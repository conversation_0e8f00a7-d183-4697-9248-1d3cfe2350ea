To extract just the first ID from each list in the nested structure returned by "$result#get('sale_transaction')#get('deliveries')", you should modify the expression to:

"$result#get('sale_transaction')#get('deliveries')#map('0.id')"

This will:
1. Get the 'sale_transaction' object from the result
2. Get the 'deliveries' array from that object
3. Map over each item in the array, extracting the 'id' property from the first element (index 0) of each inner list

So instead of getting:
[[{'id': 6454}], [{'id': 9402}], [{'id': 10197}]]

You'll get:
[6454, 9402, 10197]

Replace the line:
("print", "$result#get('sale_transaction')#get('deliveries')"),

With:
("print", "$result#get('sale_transaction')#get('deliveries')#map('0.id')"),

This will extract just the IDs you need from the nested structure.
