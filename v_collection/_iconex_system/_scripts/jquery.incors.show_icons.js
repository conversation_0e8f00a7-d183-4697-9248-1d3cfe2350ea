(function($) {

$.Incors = $.Incors || {};

$.Incors.IconsColorbox = function(options) {
  this.options = $.extend({
    onOpen: function() {},
    onCleanup: function() {},
    onComplete: function() {}
  }, options || {})
  
  
  this.hashChanged = false;
  
  this.isOpen = false;
  
  this.createIconsShadowboxOptions = {
    initialWidth: 650,
    initialHeight: 500,
    innerWidth: 650,
    innerHeight: 500,
    onOpen: this.options.onOpen,
    onComplete: $.proxy(function() {
      $('.icons_related_icon_link').colorbox(this.createIconsShadowboxOptions);
      $('#show_shadows').change(function() {
        if ($('#show_shadows:checked').val() !== undefined) {
          // checked
  	      $('.icons_without_shadows').hide();
          $('.icons_with_shadows').show();
        } else {
          // not checked
          $('.icons_with_shadows').hide();
          $('.icons_without_shadows').show();
        }
        this.isOpen = true;
      });
      this.hashChanged = true;
      window.location.hash = $('#icon_preview_icon_name').text();
      this.options.onComplete();
    }, this),
    onCleanup: $.proxy(function() {
      this.hashChanged = true;
      window.location.hash = '';
      this.isOpen = false;
      this.options.onCleanup();
    }, this)
  }
  
  if (window.location.hash) {
    $.colorbox($.extend({ href: '../icons/?icon=' + window.location.hash.substr(1) }, this.createIconsShadowboxOptions)); 	
  }
  
  /*window.onhashchange = function() {
	  console.info('changed');
    if(this.hashChanged == false) {
	    $.colorbox($.extend(this.createIconsShadowboxOptions, { href: '?icon=' + window.location.hash.substr(1) }));
  	} else {
      this.hashChanged = false;
  	}
  }*/
};

$.Incors.IconsColorbox.prototype = {
  createIconsShadowbox: function(options) {
    var options = $.extend({
      href: null,
      elems: null
    }, options || {});

    if (options.elems) {
      options.elems.colorbox(this.createIconsShadowboxOptions);  
    } else if (options.href) {
      $.colorbox($.extend({ href: options.href }, this.createIconsShadowboxOptions));
    }
  }
}

})(jQuery);