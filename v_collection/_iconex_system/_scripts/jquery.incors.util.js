(function($) {
  $.Incors = $.Incors || {};

  // The .bind method from Prototype.js 
  /*if (!Function.prototype.bind) { // check if native implementation available
    Function.prototype.bind = function() { 
      var fn = this, args = Array.prototype.slice.call(arguments),object = args.shift(); 
      return function() { 
        return fn.apply(object, args.concat(Array.prototype.slice.call(arguments))); 
      }; 
    };
  };*/
})(jQuery);