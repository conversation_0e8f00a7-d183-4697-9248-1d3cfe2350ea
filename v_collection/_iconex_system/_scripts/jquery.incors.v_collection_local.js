(function($) {

/* path to image files */
IMG_PATH = 'v_collection_png';

$.Incors.Template.detailsTmpl = function(vars) {
  return [
    '<div id="details_content">' ,
    '<div id="details_icon_name">' , vars.name , '</div>' , 
    '<div id="details_shadows"><b>shadow:</b> <input id="shadow_true" type="radio" name="shadow" value="true" />&nbsp;yes <input id="shadow_false" type="radio" name="shadow" value="false" checked />&nbsp;no</div>' ,
    '<table id="details_table"><tr>' ,
    '<td style="width: 50%;">' ,
    '<div class="details_size">16x16</div>' , 
    '<div class="details_icon"><img width="16" height="16" class="detail_img" style="position: relative; top: 4px; background: ', vars.background ,';" src="' , $.Incors.Image.imgPlainPath(vars.name, 16) , '"></div>' ,
    '</td>' ,
    '<td style="width: 50%">' ,
    '<div class="details_size">24x24</div>' ,
    '<div class="details_icon"><img width="24" height="24" class="detail_img" style="background: ', vars.background ,';" src="' , $.Incors.Image.imgPlainPath(vars.name, 24) , '"></div>' ,
    '</td>' ,
    '</tr><tr>' ,
    '<td>' ,
    '<div class="details_size">32x32</div>' ,
    '<div class="details_icon"><img width="32" height="32" class="detail_img" style="position: relative; top: 8px; background: ', vars.background ,';" src="' , $.Incors.Image.imgPlainPath(vars.name, 32) , '"></div>' ,
    '</td>' ,
    '<td>' ,
    '<div class="details_size">48x48</div>' ,
    '<div class="details_icon"><img width="48" height="48" class="detail_img" style="background: ', vars.background ,';" src="' , $.Incors.Image.imgPlainPath(vars.name, 48) , '"></div>' ,
    '</td>' ,
    '</tr><tr>' ,
    '<td>' ,
    '<div class="details_size">64x64</div>' ,
    '<div class="details_icon"><img width="64" height="64" class="detail_img" style="position: relative; top: 32px; background: ', vars.background ,';" src="' , $.Incors.Image.imgPlainPath(vars.name, 64) , '"></div>' ,
    '</td>' ,
    '<td>' ,
    '<div class="details_size">128x128</div>' ,
    '<div class="details_icon"><img width="128" height="128" class="detail_img" style="background: ', vars.background ,';" src="' , $.Incors.Image.imgPlainPath(vars.name, 128) , '"></div>' ,
    '</td>' ,
    '</tr><tr>' ,
    '<td colspan="2">' ,
    '<div class="details_size">256x256</div>' ,
    '<div class="details_icon"><img width="256" height="256" class="detail_img" style="background: ', vars.background ,';" src="' , $.Incors.Image.imgPlainPath(vars.name, 256) , '"></div>' ,
    '</td>' ,
    '</tr>' ,
    '</tr></table>' , 
    '<div style="padding: 10px 0;">' ,
      '<div style="padding: 10px 0 0 0; font-weight: bold;">Other formats:</div>' ,
      '<div class="details_size">512x512: <a href="#" onclick="$.colorbox({ html: \'<div style=\\\'background: ', vars.background , ';\\\'><div><img width=\\\'512\\\' height=\\\'512\\\' src=\\\'' , $.Incors.Image.imgPlainPath(vars.name, 512) , '\\\' /></div><div style=\\\'padding: 5px;\\\'>right click on image to save</div></div>\' }); return false;">click here</a></div>' ,
    '</div>' ,
    '<div class="details_keywords"><b>Keywords:</b><br />' , vars.keywordLinks , '</div>' ,
    '</div>'
  ].join('');
};

$.Incors.Template.detailsShadowTmpl = function(vars) {
  return [
    '<div id="details_content">' ,
    '<div id="details_icon_name">' , vars.name , '</div>' , 
    '<div id="details_shadows"><b>shadow:</b> <input id="shadow_true" type="radio" name="shadow" value="true" checked />&nbsp;yes <input id="shadow_false" type="radio" name="shadow" value="false" />&nbsp;no</div>' ,
    '<table id="details_table"><tr>' ,
    '<td style="width: 50%;">' ,
    '<div class="details_size">16x16</div>' , 
    '<div class="details_icon"><img width="16" height="16" class="detail_img" style="position: relative; top: 4px;" src="' , $.Incors.Image.imgPlainPath(vars.name, 16) , '"></div>' ,
    '</td>' ,
    '<td style="width: 50%">' ,
    '<div class="details_size">24x24</div>' ,
    '<div class="details_icon"><img width="24" height="24" class="detail_img" src="' , $.Incors.Image.imgPlainPath(vars.name, 24) , '"></div>' ,
    '</td>' ,
    '</tr><tr>' ,
    '<td>' ,
    '<div class="details_size">32x32</div>' ,
    '<div class="details_icon"><img width="32" height="32" class="detail_img" style="position: relative; top: 8px;" src="' , $.Incors.Image.imgShadowPath(vars.name, 32) , '"></div>' ,
    '</td>' ,
    '<td>' ,
    '<div class="details_size">48x48</div>' ,
    '<div class="details_icon"><img width="48" height="48" class="detail_img" src="' , $.Incors.Image.imgShadowPath(vars.name, 48) , '"></div>' ,
    '</td>' ,
    '</tr><tr>' ,
    '<td>' ,
    '<div class="details_size">64x64</div>' ,
    '<div class="details_icon"><img width="64" height="64" class="detail_img" style="position: relative; top: 32px;" src="' , $.Incors.Image.imgShadowPath(vars.name, 64) , '"></div>' ,
    '</td>' ,
    '<td>' ,
    '<div class="details_size">128x128</div>' ,
    '<div class="details_icon"><img width="128" height="128" class="detail_img" src="' , $.Incors.Image.imgShadowPath(vars.name, 128) , '"></div>' ,
    '</td>' ,
    '</tr><tr>' ,
    '<td colspan="2">' ,
    '<div class="details_size">256x256</div>' ,
    '<div class="details_icon"><img width="256" height="256" class="detail_img" src="' , $.Incors.Image.imgShadowPath(vars.name, 256) , '"></div>' ,
    '</td>' ,
    '</tr></table>' ,
    '<div style="padding: 10px 0;">' ,
      '<div style="padding: 10px 0 0 0; font-weight: bold;">Other formats:</div>' ,
      '<div class="details_size">512x512: <a href="#" onclick="$.colorbox({ html: \'<div style=\\\'background: ', vars.background , ';\\\'><div><img width=\\\'512\\\' height=\\\'512\\\' src=\\\'' , $.Incors.Image.imgShadowPath(vars.name, 512) , '\\\' /></div><div style=\\\'padding: 5px;\\\'>right click on image to save</div></div>\' }); return false;">click here</a></div>' ,
    '</div>' ,
    '<div class="details_keywords"><b>Keywords:</b><br />' , vars.keywordLinks , '</div>' ,
    '</div>'
  ].join('');
};

})(jQuery);
