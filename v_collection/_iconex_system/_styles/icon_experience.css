/********** comprehensive styles **********/
html, body {

}
html {
  overflow: -moz-scrollbars-vertical;
  overflow-y: scroll;
}
html#iex_layout_v_collection {
  background: #a9e6f8 url(/_img/v/background.jpg) repeat top center;
}
html#iex_layout_m_collection {
  background: #282828;
}
html#iex_layout_x_collection {
  background: #ffcc8b url(/_img/x/background.jpg) repeat top center;
}
html#iex_layout_incors {
  background: #788185 url(/_img/incors/background.jpg) repeat top center;
}
body {
  font-family: Tahoma, Verdana, "Helvetica Neue", Helvetica, sans-serif;
  font-size: 13px;
  line-height: 130%;
  margin: 0;  
  
}
h1 {
  font-family: "Trebuchet MS", Tahoma, Verdana, "Helvetica Neue", Helvetica, sans-serif;
}
h2 {
  font-family: "Trebuchet MS", Tahoma, Verdana, "Helvetica Neue", Helvetica, sans-serif;
}
h3 {
  font-family: "Trebuchet MS", Tahoma, Verdana, "Helvetica Neue", Helvetica, sans-serif;
}
td {
  color: #1F272D;
}
p {
  padding: 0;
  margin: 10px 0px 0px 0px;
}
a {
  outline: 0;
  text-decoration: none;
  color: #20638D;
}
a:hover, a:active {
  text-decoration: none;
  color: #3697BF;
}
img {
  border: none;
}
.iex_content {
  padding: 5px 0px 20px 0px;
}
/********** page.htm **********/
.iex_news {
  font-size: 10px;
  background-color: rgb(227, 231, 233);
  border: solid 1px rgb(80, 99, 110);
  padding: 15px 8px;
}
/********** list_item.htm **********/
.iex_list_item {
  margin: 3px 0;
}
/********** list_item_large.htm **********/
.iex_list_item_large {
  margin: 10px 0;
}
/********** menu.htm **********/
.iex_menu {
	padding: 0 0 0 400px; 
  height: 23px;
}
/********** menu_item.htm **********/
.iex_menu_item td {
  text-align: center;
  border-style: solid;
  border-width: 1px;
  padding: 4px 0;
}
.iex_menu_item_not_selected td {
  background-color: rgb(227, 231, 233);
  border-color: rgb(80, 99, 110);
}
.iex_menu_item_selected td {
  background-color: rgb(224, 227, 207);
  border-color: rgb(102, 102, 0);
}
div.iex_menu_item {
  width: 163px;
  height: 23px;
  margin: 0 2px 0 0;
  background-position: 0 0;
  background-repeat: no-repeat;
  float: left;
}
div.iex_menu_item a {
  display: block;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-position: 0 -23px;
  background-repeat: no-repeat;
}
div.iex_menu_item a:hover {
  background-image: none;
}
div.iex_menu_item a:active {
  color: black;
}
/********** menu_sub_item.htm & menu_sub_item_selected.htm **********/

.iex_menu_sub {
  margin: 0 0 0 10px;
  padding: 0 40px 0 0;
  list-style-type: none;
  text-align: right;
}
.iex_menu_sub_item {
  float: right;
  padding: 0 10px;
  font-size: 14px;
  font-weight: bold;
  line-height: 30px;
}
.iex_menu_sub_item_not_selected {
  
}
.iex_menu_sub_item_selected {
  
}
.iex_menu_sub_item_not_selected a, .iex_menu_sub_item_not_selected a:visited, .iex_menu_sub_item_selected a, .iex_menu_sub_item_selected a:visited {
  font-family: Tahoma, Helvetica;
  color: #fff;
}

.iex_menu_sub_item_selected {
  font-family: Tahoma, Helvetica;
  color: #fff;
  line-height: 22px;
  height: 22px;
  margin: 3px 0;
  padding: 0 9px;
}

.iex_menu_sub_item_not_selected a:hover {
  text-decoration: underline;
}

#iex_layout_v_collection .iex_menu_sub_item_selected {
  background-color: #5db3da;
  border: 1px solid #a6e1ee;
}

#iex_layout_m_collection .iex_menu_sub_item_selected {
  background-color: #555555;
  border: 1px solid #999999;
}

#iex_layout_x_collection .iex_menu_sub_item_selected {
  background-color: #feb650;
  border: 1px solid #fedb75;
}

#iex_layout_incors .iex_menu_sub_item_selected {
  background-color: #98b7bc;
  border: 1px solid #e3eff1;
}

/********** headings **********/
.iex_heading_table {
  table-layout: fixed;
  width: 100%;
}
.iex_heading_1 {
  margin: 20px 0px 10px 0px;
  clear: right;
}
.iex_heading_2 {
  margin: 20px 0px 10px 0px;
  clear: right;
}
.iex_heading_3 {
  margin: 20px 0px 0px 0px;
  clear: right;
}

.iex_heading_1 .iex_heading_middle {
  font-size: 20px;
}
.iex_heading_2 .iex_heading_middle {
  font-size: 16px;
}
.iex_heading_3 .iex_heading_middle {
  font-size: 14px;
}
.iex_heading_table td {
  background-repeat: repeat;
  font-family: Arial, Helvetica, Geneva, Swiss, SunSans-Regular;
  font-weight: bold;
  color: #292827;
}
.iex_heading_top_left {
  width: 0px;
  height: 0px;
}
.iex_heading_top {
  height: 0px;
}
.iex_heading_top_right {
  width: 0px;
  height: 0px;
}
.iex_heading_left {
  width: 0px;
}
.iex_heading_middle {
  padding-bottom: 7px 
}
.iex_heading_right {
  width: 0px;
}
.iex_heading_bottom_left {
   width: 0px;
  height: 1px;
}
.iex_heading_bottom {
  background-color: #70BDF0;
  height: 1px;
}
.iex_heading_bottom_right {
  width: 0px;
  height: 1px;
}

.table_row_2cols_fact_sheet td {
  padding: 10px;
}

.table_row_2cols_fact_sheet p {
  margin: 0;
  padding: 0;
}

/********** footer **********/

#iex_footer_line {
  width: 90%;
  margin: 0 auto; 
  height:0px; 
  border-top: 1px solid #F9FAFB; 
  border-bottom: 1px solid #E4E9ED;
  border-left: 0px;
  border-right:0px;
  padding: 0px;
}

#iex_footer {
  position: relative;
  height: 41px;
}

#iex_footer a {
  color: #000;
  text-decoration: none;
}

#iex_footer a:hover {
  text-decoration: underline;
}

#v_collection_search_submit {
	position: absolute;
	right: 0;
	top: 0;
  width: 119px;
  height: 29px;
  border: 0px solid #fff;
  cursor: pointer;
}

#m_collection_search_submit {
	position: absolute;
	right: 0;
	top: 0;
  width: 119px;
  height: 29px;
  border: 0px solid #fff;
  cursor: pointer;
}

/********** v_collections.php **********/
.iex_buy_now {
  width: 120px;
}

div.iex_buy_now {
  width: 120px; height: 24px;
  background-position: -120px 0;
  background-repeat: no-repeat;
  background-image: url(/_img/button_buy_now.png);
}

div.iex_buy_now a {
  display: block;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-position: top left;
  background-repeat: no-repeat;
  background-image: url(/_img/button_buy_now.png);
}

div.iex_buy_now a:hover {
  background-image: none;
}

.iex_sample_icons {
  width: 169px;
}

div.iex_sample_icons {
  width: 169px; height: 24px;
  background-position: -169px 0;
  background-repeat: no-repeat;
  background-image: url(/_img/button_download_samples.png);
}

div.iex_sample_icons a {
  display: block;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-position: top left;
  background-repeat: no-repeat;
  background-image: url(/_img/button_download_samples.png);
}

div.iex_sample_icons a:hover {
  background-image: none;
}

div.iex_features_v a {
  display: block;
  width: 285px; 
  height: 87px;
  background-repeat: no-repeat;
}

div.iex_features_v a:hover {
  background-position: -285 inherit;
  background-repeat: no-repeat;
}



/********** download_pdf.htm, download_zip.htm, weblink.htm  **********/
.iex_download_pdf, .iex_download_zip, .iex_weblink, .iex_image_link {
  line-height: 24px;
  clear: left;
}
.iex_download_pdf img, .iex_download_zip img, .iex_weblink img, .iex_image_link img {
  margin: 0 5px 0 0;
  vertical-align: middle;  
  display:block;
  border: none;
  float: left;
}
/********** info_box.php **********/
.iex_info_box {
  border: solid 2px #50636e;
  background-color: #eff1f2;
  height: 45px;
  padding: 10px;
}
.iex_info_box img {
  float: left;
  margin: 0 10px 5px 0;
  width: 46px;
  height: 45px;
}
/********** license.php **********/
.iex_license_header {
  font-weight: bold;
  text-align:center;
  margin: 0 0 20px 0;
}
/********** table_row_3cols_purchase.php **********/
div.iex_purchase_button {
  width: 134px; height: 33px;
  margin: 10px 0 10px 0;
  background-position: 0 -33px;
  background-repeat: no-repeat;
}
div.iex_purchase_button a {
  display: block;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-position: top left;
  background-repeat: no-repeat;
}
div.iex_purchase_button a:hover {
  background-image: none;
}

/********** download_table.php **********/
div.iex_download_button {
  width: 120px; height: 24px;
  margin: 10px 0 10px 0;
  background-position: -120px 0;
  background-repeat: no-repeat;
}
div.iex_download_button a {
  display: block;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-position: top left;
  background-repeat: no-repeat;
}
div.iex_download_button a:hover {
  background-image: none;
}


/**********************************************************************/
/* Version 2 */
/**********************************************************************/

/***** FAQ *****/

.iex_faq {
  margin: 0 0 10px 0;
}
.iex_faq_toggle {
  position: relative;
}
.iex_faq_toggle_close, .iex_faq_toggle_open {
  position: absolute;
  top: 5px;
  left: 0;
  width: 9px;
  height: 9px;
  cursor: pointer;
}
.iex_faq_toggle_open {
  background: url(/_img/item_collapse_expand.png) no-repeat 0 -9px;
}
.iex_faq_toggle_close {
  background: url(/_img/item_collapse_expand.png) no-repeat 0 0;
}
.iex_faq_closed  .iex_faq_toggle_close {
  display: none;
}
.iex_faq_opened  .iex_faq_toggle_open {
  display: none;
}
.iex_faq_question {
  line-height: 20px;
  vertical-align: middle;
  padding: 0 0 0 15px;
  font-weight: bold;
  cursor: pointer;
}
.iex_faq_answer {
  padding: 0 0 10px 0;
}
.iex_faq_closed .iex_faq_question:hover {
  text-decoration: underline;
}
.iex_faq_closed .iex_faq_answer {
  display: none;
}

/********** show icons **********/
.iex_icons_preview a, .iex_icons_preview_selected a {
  display: block;
  width: 248px;
  height: 93px;
  background-repeat: no-repeat;
  margin: 3px auto 15px;
}

#iex_layout_v_collection .iex_icons_preview a, #iex_layout_v_collection .iex_icons_preview_selected a {
  background-image: url(/_img/v/icons_preview_categories.png);
}

.iex_icons_preview #iex_icons_preview_v_basic {
  background-position: 0 0;
}

.iex_icons_preview_selected #iex_icons_preview_v_basic {
  background-position: 0 -186px;
}

.iex_icons_preview #iex_icons_preview_v_basic:hover {
  background-position: 0 -93px;
}

.iex_icons_preview #iex_icons_preview_v_computer {
  background-position: -248px 0;
}

.iex_icons_preview_selected #iex_icons_preview_v_computer {
  background-position: -248px -186px;
}

.iex_icons_preview #iex_icons_preview_v_computer:hover {
  background-position: -248px -93px;
}

.iex_icons_preview  #iex_icons_preview_v_finance {
  background-position: -496px 0;
}

.iex_icons_preview_selected  #iex_icons_preview_v_finance {
  background-position: -496px -186px;
}

.iex_icons_preview  #iex_icons_preview_v_finance:hover {
  background-position: -496px -93px;
}

.iex_icons_preview  #iex_icons_preview_v_science {
  background-position: -744px 0;
}

.iex_icons_preview_selected  #iex_icons_preview_v_science {
  background-position: -744px -186px;
}

.iex_icons_preview  #iex_icons_preview_v_science:hover {
  background-position: -744px -93px;
}

.iex_icons_preview  #iex_icons_preview_v_society {
  background-position: -992px 0;
}

.iex_icons_preview_selected  #iex_icons_preview_v_society {
  background-position: -992px -186px;
}

.iex_icons_preview  #iex_icons_preview_v_society:hover {
  background-position: -992px -93px;
}

.iex_icons_preview  #iex_icons_preview_v_flags {
  background-position: -1240px 0;
}

.iex_icons_preview_selected  #iex_icons_preview_v_flags {
  background-position: -1240px -186px;
}

.iex_icons_preview  #iex_icons_preview_v_flags:hover {
  background-position: -1240px -93px;
}


#iex_layout_m_collection .iex_icons_preview a, #iex_layout_m_collection .iex_icons_preview_selected a {
  background-image: url(/_img/m/icons_preview_categories.png);
}

.iex_icons_preview #iex_icons_preview_m_basic {
  background-position: 0 0;
}

.iex_icons_preview_selected #iex_icons_preview_m_basic {
  background-position: 0 -186px;
}

.iex_icons_preview #iex_icons_preview_m_basic:hover {
  background-position: 0 -93px;
}

.iex_icons_preview #iex_icons_preview_m_computer {
  background-position: -248px 0;
}

.iex_icons_preview_selected #iex_icons_preview_m_computer {
  background-position: -248px -186px;
}

.iex_icons_preview #iex_icons_preview_m_computer:hover {
  background-position: -248px -93px;
}

.iex_icons_preview  #iex_icons_preview_m_finance {
  background-position: -496px 0;
}

.iex_icons_preview_selected  #iex_icons_preview_m_finance {
  background-position: -496px -186px;
}

.iex_icons_preview  #iex_icons_preview_m_finance:hover {
  background-position: -496px -93px;
}

.iex_icons_preview  #iex_icons_preview_m_science {
  background-position: -744px 0;
}

.iex_icons_preview_selected  #iex_icons_preview_m_science {
  background-position: -744px -186px;
}

.iex_icons_preview  #iex_icons_preview_m_science:hover {
  background-position: -744px -93px;
}

.iex_icons_preview  #iex_icons_preview_m_society {
  background-position: -992px 0;
}

.iex_icons_preview_selected  #iex_icons_preview_m_society {
  background-position: -992px -186px;
}

.iex_icons_preview  #iex_icons_preview_m_society:hover {
  background-position: -992px -93px;
}

.iex_icons_preview  #iex_icons_preview_m_flags {
  background-position: -1240px 0;
}

.iex_icons_preview_selected  #iex_icons_preview_m_flags {
  background-position: -1240px -186px;
}

.iex_icons_preview  #iex_icons_preview_m_flags:hover {
  background-position: -1240px -93px;
}


#iex_layout_x_collection .iex_icons_preview a, #iex_layout_x_collection .iex_icons_preview_selected a {
  background-image: url(/_img/x/icons_preview_categories.png);
}

.iex_icons_preview #iex_icons_preview_x_application {
  background-position: 0 0;
}

.iex_icons_preview_selected #iex_icons_preview_x_application {
  background-position: 0 -186px;
}

.iex_icons_preview #iex_icons_preview_x_application:hover {
  background-position: 0 -93px;
}

.iex_icons_preview #iex_icons_preview_x_network {
  background-position: -248px 0;
}

.iex_icons_preview_selected #iex_icons_preview_x_network {
  background-position: -248px -186px;
}

.iex_icons_preview #iex_icons_preview_x_network:hover {
  background-position: -248px -93px;
}

.iex_icons_preview  #iex_icons_preview_x_software {
  background-position: -496px 0;
}

.iex_icons_preview_selected  #iex_icons_preview_x_software {
  background-position: -496px -186px;
}

.iex_icons_preview  #iex_icons_preview_x_software:hover {
  background-position: -496px -93px;
}

.iex_icons_preview  #iex_icons_preview_x_business {
  background-position: -744px 0;
}

.iex_icons_preview_selected  #iex_icons_preview_x_business {
  background-position: -744px -186px;
}

.iex_icons_preview  #iex_icons_preview_x_business:hover {
  background-position: -744px -93px;
}

.iex_icons_preview  #iex_icons_preview_x_objects {
  background-position: -992px 0;
}

.iex_icons_preview_selected  #iex_icons_preview_x_objects {
  background-position: -992px -186px;
}

.iex_icons_preview  #iex_icons_preview_x_objects:hover {
  background-position: -992px -93px;
}

.iex_icons_preview  #iex_icons_preview_x_flags {
  background-position: -1240px 0;
}

.iex_icons_preview_selected  #iex_icons_preview_x_flags {
  background-position: -1240px -186px;
}

.iex_icons_preview  #iex_icons_preview_x_flags:hover {
  background-position: -1240px -93px;
}



.iex_icons_preview a {
  cursor: pointer;
}
.iex_icons_preview_selected a {
  cursor: default;
}
.iex_icons_preview a:hover {
}

/*** Icon Page ***/
#icon_preview {
  margin: 15px 0 0 0;
}
#icon_preview h1 {
  margin-top: 0;
}
#icon_preview h2 {
  margin-top: 0;
  margin-bottom: 10px;
}
#icon_preview h3 {
  margin-top: 0;
  margin-bottom: 5px;
}
.icons_related_icons {
  float: left;
  margin: 10px 10px 0 0;
  text-align: center;
  border: 1px dashed #ccc;
  padding: 5px;
}
.icons_related_icons:hover {
  border: 1px dashed #555;
}
.icon_all_sizes_table_colorbox {
  width: 100%;
  background-color: #a5adb1;
}
.icon_all_sizes_table {
  width: 624px;
  height: 380px;
  background-color: #a5adb1;
}

.icon_all_sizes_table td, .icon_all_sizes_table_colorbox td {
  text-align: center;
  vertical-align: middle;
  padding: 15px;
  background-color: #fff;
}
.icon_pixel_size_text {
  margin: 10px 0 0 0;
  color: #777;
}

#icon_preview_colorbox {
  padding: 10px;
}
#icon_preview_colorbox h1 {
  margin-top: 0;
}
#icon_preview_colorbox h2 {
  margin-top: 0;
  margin-bottom: 10px;
}
#icon_preview_colorbox h3 {
  margin-top: 0;
  margin-bottom: 5px;
}

/* Home */
#iex_home {
  background-color: #e0e0e0;
}

#iex_home #sub_heading {
  margin: 0;
  font-weight: bold;
  font-size: 16px;
  padding: 5px 25px;
  background: #f5f5f5;
  color: #555;
  font-family: "Trebuchet MS", Helvetica, Jamrul, sans-serif;
}

#iex_home #sub_heading_line {
  height: 3px;
  color: #1F272D;
  background: url(/_img/subheading_line.png);
}

#iex_home_frame {
  position: relative;
  width: 950px;
  background: #fff;
  margin: 0 auto;
}
#iex_home_header {
  height: 35px;
  margin: 40px 0 0;
  font-size: 30px;
}
#iex_home_subheader {
  height: 30px;
  margin: 0 0 40px;
}

#iex_home .collection_preview {
  float: left;
  margin-left: 28px;
  position: relative; 
}

#iex_home .collection_preview_link {
	height: 22px;
	margin: 6px 0;
	cursor: pointer;
}

#iex_home .collection_preview_links {
	position: absolute;
	top: 200px;
	left: 15px;
}

#iex_home .collection_preview_link:hover {
  background-position: 0 -22px;
}

#iex_home_footer {
  position: relative;
  height: 41px;
  margin: 5px 0 0 0;
  padding: 0 0 5px 0;
}

#iex_home_footer a {
  color: #000;
  text-decoration: none;
}

#iex_home_footer a:hover {
  text-decoration: underline;
}

#top_reasons {
  position: relative;
  float: right;
  top: 0;
  right: 25px;
  cursor: pointer;
  width: 358px;
  height: 52px;
  background: url(/_img/top_reasons_for_iconexperience.png);
}

#top_reasons:hover {
  width: 358px;
  height: 52px;
  background: url(/_img/top_reasons_for_iconexperience.png) 0 -52px;
}

/* m-collection */
#iex_layout_m_collection td {
  color: #fff;
}

#iex_layout_m_collection #iex_footer a {
  color: #fff;
  text-decoration: none;
}

#iex_layout_m_collection a {
  color: #fff;
  text-decoration: underline;
}

#iex_layout_m_collection a:hover {
  color: #ccc;
}

#iex_layout_m_collection #iex_footer_line {
  display: none;
}

#iex_layout_m_collection .iex_heading_bottom {
  background-color: transparent;
}

#iex_layout_m_collection .show_icons_icon {
  width: 48px;
  height: 48px;
}

#iex_layout_m_collection .show_icons_image_loader_text {
  color: inherit;
}

#iex_layout_m_collection .show_icons_image_loader {
  background: transparent;
}

#iex_layout_m_collection .cboxElement {
  text-decoration: none;
}

#iex_layout_m_collection .show_icons_image_loader {
  margin-bottom: 5px;
}

#iex_layout_m_collection .show_icons_image_loader:hover {
  margin-bottom: 0px;
}

#iex_layout_m_collection .icons_related_icons {
  padding: 5px;
  border: none;
}

#iex_layout_m_collection .icon_all_sizes_table_colorbox img {
  padding: 5px;
}

#iex_layout_m_collection #icon_show_more {
  color: #202020;
}

#iex_layout_m_collection .iex_menu_sub_item a {
  text-decoration: none;
}

#iex_layout_m_collection .iex_menu_sub_item a:hover, #iex_layout_m_collection #iex_footer a:hover {
  color: #fff;
  text-decoration: underline;
}

/* collections comparison */

#collections_comparison {
  width: 600px;
}

#collections_comparison_table {
  table-layout: fixed;
  border-spacing: 2px 2px;
  margin: 0 auto;
}

#collections_comparison_table td, #collections_comparison_table th {
  background-color: #ccc;
  padding: 5px;
}

#collections_comparison_table th {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

#collections_comparison_table th:first-child {
  background-color: #fff;
  border: none;
}

#collections_comparison_table td {
  text-align: center;
  color: #2b363f;
}

#collections_comparison_table td:first-child {
  text-align: left;
  font-weight: bold;
  color: #1F272D;
}

#collections_comparison_table .col_property {
  padding-left: 10px;
}

#collections_comparison_table th.col_v_collection {
  color: #fff;
  background-color: #2E89B2; 
}

#collections_comparison_table th.col_m_collection {
  color: #fff;
  background-color: #404040; 
}

#collections_comparison_table th.col_x_collection {
  color: #fff;
  background-color: #8F1C00; 
}

#collections_comparison_table .check_yes, #collections_comparison_table .check_no {
  height: 30px;
  width: 30px;
  background-repeat: no-repeat;
  background-position: center center;
  margin: 0 auto;
}

#collections_comparison_table .check_yes {
  background-image: url(/_img/comparison_table_check_yes.png);
}

#collections_comparison_table .check_no {
  background-image: url(/_img/comparison_table_check_no.png);
}

#collections_comparison_table sup {
  font-weight: normal;
}

#collections_comparison_footer {
  margin: 5px auto 0;
  color: #555;
  font-size: 12px;
}

#collections_comparison_footer td {
  vertical-align: top;
  padding: 0 2px;
}

#collections_comparison_print {
  margin: 5px 0 0;
  text-align: right;
  cursor: pointer;
}

/* crossfade example */
#iex_crossfade_examples {
	width: 552px;
	background: #202020;
	padding: 15px;
}

#iex_crossfade_examples .tile_1_1, #iex_crossfade_examples .tile_1_2 {
	float: left;
	margin: 4px;
	cursor: pointer;
}

#iex_crossfade_examples .tile_1_1, #iex_crossfade_examples .tile_1_1 .bottom, #iex_crossfade_examples .tile_1_1 .top {
	width: 84px;
	height: 84px;
}

#iex_crossfade_examples .tile_1_2, #iex_crossfade_examples .tile_1_2 .bottom, #iex_crossfade_examples .tile_1_2 .top {
	width: 176px;
	height: 84px;
}

#iex_crossfade_examples .crossfade {
	position: relative;
}

#iex_crossfade_examples .crossfade > a:last-child img:last-child, #iex_crossfade_examples .crossfade > img:last-child, #iex_crossfade_examples .crossfade > a:last-child div:last-child, #iex_crossfade_examples .crossfade > div:last-child {
	position: absolute;
	left: 0;
	top: 0;
}