html {
}
html, body {
  height: auto; // IE 6 Fix -> gap between window bottom and search area
}
#abs_cont {
  position: absolute;
  width: 500px;
}
#icon_search {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
  margin: 0;
  padding: 0;
  table-layout: fixed;
}
#left_col {
  width: 100%;
  border: none;
  margin: 0;
  padding: 0;
  vertical-align: top;
}
#right_col {
  width: 310px;
  border: none;
  vertical-align: top;
  margin: 0;
  padding: 0;
  border-left: 1px solid #c0c0c0;
  background-color: #fff;
}

#search {
  width: 639px;
  height: 100px;
  border-bottom: 1px solid #c0c0c0;
  font-family: Verdana, Arial, Helvetica, Geneva, Swiss, SunSans-Regular;
}
#search_header {
  height: 42px;
  font-size: 18px;
  font-weight: bold;
}
#search_input {
}
#search_input input {
  width: 456px;
  height: 25px;
  font-size: 18px;
  line-height: 25px;
}
#search_type_table {
  padding: 3px 0 0 0;
  float: right;
  margin: 0 60px 0 0;
}
#search_type_table td {
  color: #fff;
}
#search_type_table_left {
  font-weight: bold;
  text-align: right;
  vertical-align: bottom;
}
#search_type_table_right {
  vertical-align: bottom
}

#results {
  overflow: scroll; /* IE 7 handels 'auto' buggy */
  overflow-x: hidden; /* IE fix */
  width: 100%;
}
#results_table {
  width: 100%;
  margin: 0px;
  padding: 0px;
  border-spacing: 0;
  border-collapse: collapse;
  table-layout: fixed;
}
#details {
  overflow: auto;
  overflow-x: hidden; /* IE fix */
  outline-style: none /* Firefox fix */
}
#details_content {
  padding: 10px;
}
#details_icon_name {
  font-size: 20px;
  font-weight: normal;
  vertical-align: bottom;
  text-align: left;
  margin: 0px 0px 6px 0px;
}
#details_icon_collection {
  font-size: 14px;
  vertical-align: bottom;
  text-align: left;
  margin: 5px 0px 6px 0px;
}
#details_shadows {
  font-size: 13px;
  font-weight: normal;
  vertical-align: middle;
  margin: 0px 0px 5px 0px
}
#details_table {
  table-layout: fixed;
  width: 100%;
  cellspacing: 1px;
  background-color: #ccc;
}
#details_table td {
  background-color: #fff;
  vertical-align: top;
}
.details_keywords {
  font-size: 14px;
  margin: 5px 0 0 0;
}
#i_icon_search {
  background-color: #fff;
  margin: 0 0 0 119px;
}
#i_icon_search:focus {
  outline: none;
}
.input {
  font-family: Arial;
  border: 1px solid silver;
  background-color: white;
}
.results_col_left {
  width: 150px;
  padding: 10px 10px 10px 13px;
  vertical-align: middle;
  font-size: 13px;
  font-weight: normal;
  font-family: Verdana;
  line-height: 18px;
  text-align: left;
}
.results_col_right {	
  padding: 0px 10px 0px 10px;
  vertical-align: middle;
  text-align: left;
  width: 100%;
}
.results_row_odd {
}
.results_row_even {
  background-color: #e6e9ea;
}
#results_row_more {
  padding: 3px;
  vertical-align: middle;
  text-align: center;
  color: #fff;
  background-color: #C0C7CB;
}
#results_row_more a {
  color: #1E3B66;	
}
#no_results {
  width: 100%;
  height: 100%;
  text-align: center;
}
#no_results #no_result_text {
  color: #555;
  font-size: 16px;
}
ul.icon_list_1 {
  list-style: none;
  display: inline;
  padding: 0px;
  margin: 0px;
}
ul.icon_list_1 li {
  display: inline;
  text-align: left;
  padding: 0;
}
#results img {
  margin: 2px;
  border: none;
  vertical-align: middle;
}
.details_icon {
  text-align: center;
}
.details_size {
  vertical-align: top;
  font-style: italic;
  font-weight: bold;
  text-align: left;
  font-size: 12px;
  color: #000;
  margin: 3px 0px 3px 0px;
  padding: 1px 5px 1px 5px;
}
.details_description {
  vertical-align: top;
  font-style: italic;
  font-weight: normal;
  text-align: center;
  font-size: 12px;
  margin: 3px 0px 3px 0px;
}
.details_tip {
  font-size: 12px;
  margin: 0;
  color: #333;
  border: 1px solid #777;
  padding: 5px;
  background-color: white;
}
.icon_result_name {
  float: left;
  margin: 10px;
  padding: 10px;
  background-color: #e6e9ea;
  text-align: center;
  height: 100px;
}
.icon_result_name div {
  padding: 5px;	
}
#icon_search img {
  margin: 3px;
}

.no_script_frame {
  width: 100%;
  text-align: center;
}
.no_script {
  width: 700px;
  margin: 20px 0;
  padding: 20px;
  font-size: 14px;
  color: #000;
  background-color: #fff;
}

.no_script_heading {
  font-size: 1.7em;
  font-weight: bold;
  margin: 10px 0;
}

.no_script_sub_heading {
  font-size: 1.2em;
  margin: 20px 0;
  text-align: left;
}

.no_script_browsers {
  
}

.no_script_browser_heading {
  font-size: 1.2em;
  font-style: italic;
  text-decoration: underline;
  margin: 10px 0;
}

.no_script_browser_content {
  font-size: 1em;
  text-align: left;
}

.no_script_list {
  font-size: 1em;
  text-align: left;
}

#collections_enabled {
  margin: 20px 40px;
}

.collection_disabled td {
  color: #aaa;
}

.keyword_for_icon {
  cursor: pointer;
}

#tooltip_right_click {
  display: none;
  background: transparent url(../../_iconex_system/_html_img/tooltip/black_arrow.png);
	height: 70px;
	width: 160px;
	padding: 25px;
}

#tooltip_right_click_content {
  height: 60px;
  width: 100%;
}

#tooltip_right_click_content td {
  font-size: 12px;
  color: #fff;
  text-align: center;
}