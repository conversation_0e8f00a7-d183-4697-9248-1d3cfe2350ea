from po_lib.constants import Integrations
import ast


def push_breakpoints(params, callback, _):
    _list = []
    _data = params["value"]
    for i in _data:
        if i.get("promr_amount") != None:
            _list.append({"qty": str(i.get("prompq_qty")), "amount": str(i.get("promr_amount")), "type": "amount"})
        else:
            _list.append({"qty": str(i.get("prompq_qty")), "amount": str(i.get("promr_percent")), "type": "percent"})
    callback(_list)


def push_bin_location(params, callback, _):
    loc_list = []
    for i in params["locations"][0]:
        loc_list.append({"aisles": i.split("-")[0],
                     "sections": i.split("-")[1],
                     "shelves": i.split("-")[2],
                     "bins": i.split("-")[3]})
    callback(loc_list)


def pull_bin_location(params, callback, _):
    loc_list = []
    for i in params["locations"]:
        count = 0
        for k, v in i.items():
            if v != "" and k != "":
                count += 1
        if count > 0:
            i.pop(None, None)
            temp = i.values()
            loc_list.append('-'.join(temp))

    callback(loc_list)


def pull_nutritional(params, callback, _):
    print(params["value"])
    _dict = {}
    for n in params["value"]:
        if n.get("nut_key") is not None:
            _dict[n.get("nut_key")] = n.get("nut_value")

    # Sort the data by nut_key before processing
    sorted_dict = sorted(params["nut_key"], key=lambda x: x.get("nut_value", ""))
    callback(sorted_dict)


def push_nutritional(params, callback, _):
    _list = []
    try:
        data = ast.literal_eval(params["value"])

        for k, v in data.items():
            _list.append({"nut_key": k, "nut_value": v})
    except:
        pass
    callback(_list)


def pull_printezones(params, callback, _):
    zones = params["zones"]
    _list = []

    for i in zones:
        if i.get("enabled") is True:
            _list.append(i.get("printing_zone_id"))

    callback(_list)


def push_printerzones(params, callback, _):
    zones = params["zones"]
    zone_list = params["zone_list"]
    _list = []
    _selected = []

    for p in zone_list:
        for z in zones:
            if p == z.get("printing_zone_id"):
                _list.append({"enabled": "True", "prtz_name": z["prtz_name"], "printing_zone_id": z["printing_zone_id"]})
                _selected.append(z.get("prtz_name"))

    for z in zones:
        if z.get("printing_zone_id") not in zone_list:
            _list.append({"enabled": "False", "prtz_name": z["prtz_name"], "printing_zone_id": z["printing_zone_id"]})

    callback([_list, _selected])


def push_pidb_sync(params, callback, _):
    _list = []

    _level = params["level"]
    _pos = params["pos"]
    _pidb = params["pidb"]
    _temp = ""
    media_id = []

    _basic = [
                {"pidb": "gtins", "pos": "prod_barcodes", "field": "barcode", "label": "Barcode", "value": "Barcode"},
                {"pidb": "description", "pos": "prod_name", "field": "desc", "label": "Description", "value": "Description"},
                {"pidb": "category", "pos": "prod_category", "field": "cat", "label": "Category", "value": "Category"},
                {"pidb": "subcategory", "pos": "prod_subcategory", "field": "sub_cat", "label": "Sub Category", "value": "Sub Category"},
                {"pidb": "manufacturer", "pos": "mfgbr_ent_name", "field": "manuf", "label": "Manufacturer", "value": "Manufacturer"},
                {"pidb": "brand", "pos": "mfgbr_brand_name", "field": "brand", "label": "Brand", "value": "Brand"},
             ]

    if _level is not None:
        if "basic" in _level:
            for b in _basic:
                if _pidb[0].get(b.get("pidb")) is not None:
                    _temp = _pidb[0].get(b.get("pidb"))
                    if b.get("pidb") == "gtins":
                        _temp = _pidb[0].get(b.get("pidb"))[0].get("value")
                    if isinstance(_temp, dict):
                        if _temp.get("name"):
                            _temp = _temp.get("name")
                else:
                    _temp = ""
                _list.append({"sync": False,
                            "pidb": _temp,
                            "pos": str(_pos.get(b.get("pos")) or "").strip("[").strip("]").strip("'").strip("'"),
                            "label": b.get("label"),
                            "field": b.get("field"),
                            "value": b.get("value")})

        if "enhanced" in _level:
            _enhanced = [
                {"pidb": "long_description", "pos": "prod_long_description", "field": None, "label": "Long Desc", "value": "LongDescription"},
                {"pidb": "images", "pos": "prod_image_count", "field": None, "label": "Images", "value": "Images"},
                {"pidb": "videos", "pos": "prod_video_count", "field": None, "label": "Videos", "value": "Videos"},
                {"pidb": "marketing_message", "pos": "marketing_message", "field": None, "label": "Marketing", "value": "MarketingMessage"},
                {"pidb": "web_info", "pos": "prod_web_info", "field": None, "label": "Product Info", "value": "WebInfo"},
                {"pidb": "tags", "pos": "prod_tags", "field": None, "label": "Tags", "value": "tags"},
                {"pidb": "ingredients", "pos": "prod_ingredients", "field": None, "label": "Ingredients", "value": "Ingredients"},
                {"pidb": "nutritional", "pos": "prod_nutritional", "field": None, "label": "Nutritional", "value": "Nutritional"},
                {"pidb": "map", "pos": "map", "field": None, "label": "MAP", "value": "MAP"},
                {"pidb": "msp", "pos": "msp", "field": None, "label": "MSP", "value": "MSP"},
                {"pidb": "msrp", "pos": "msrp", "field": None, "label": "MSRP", "value": "MSRP"},
                {"pidb": "replacement_product_ids", "pos": "", "field": None, "label": "Replacement", "value": "ReplacementProductIds"},
                {"pidb": "up_sell_product_ids", "pos": "up_sell_product_ids", "field": None, "label": "Up Sell", "value": "UpSellProductIds"},
                {"pidb": "cross_sell_product_ids", "pos": "cross_sell_product_ids", "field": None, "label": "Cross Sell", "value": "CrossSellProductIds"},
                {"pidb": "alternative_product_ids", "pos": "alternative_product_ids", "field": None, "label": "Alternative", "value": "AlternativeProductIds"},
                # {"pidb": "", "pos": "", "field": None, "label": "", "value": ""},
            ]

            for e in _enhanced:
                if not isinstance(_pidb[0].get(e.get("pidb")), list):
                    if str(_pidb[0].get(e.get("pidb"))) != "None":
                        _temp = _pidb[0].get(e.get("pidb"))
                        if isinstance(_temp, dict):
                            if _temp.get("name"):
                                _temp = _temp.get("name")
                    else:
                        _temp = ""
                else:
                    if len(_pidb[0].get(e.get("pidb"))) > 0:
                        if e.get("pidb") == "images":
                            for i in _pidb[0].get(e.get("pidb")):
                                media_id.append({"media_id": i.get("id"), "prod_id": i.get("product_id")})

                            _temp = "See PIDB Media Tab - " + str(len(_pidb[0].get(e.get("pidb")))) + " image(s)"
                        elif e.get("pidb") == "videos":
                            _temp = "See PIDB Media Tab - " + str(len(_pidb[0].get(e.get("pidb")))) + " video(s)"
                        elif e.get("pidb") == "alternative_product_ids" or e.get("pidb") == "cross_sell_product_ids" or \
                                e.get("pidb") == "replacement_product_ids" or e.get("pidb") == "up_sell_product_ids":
                            _temp = str(_pidb[0].get(e.get("pidb"))).strip("[").strip("]")
                        else:
                            _temp = _pidb[0].get(e.get("pidb"))
                    else:
                        _temp = ""

                if _pos.get(e.get("pos")) == "{}":
                    _pos[e.get("pos")] = ""

                _list.append({"sync": False,
                            "pidb": _temp,
                            "pos": str(_pos.get(e.get("pos")) or "").strip("[").strip("]"),
                            "label": e.get("label"),
                            "field": e.get("field"),
                            "value": e.get("value")})
    callback([_list, media_id])


def pull_pidb_sync(params, callback, _):
    _list = []

    fields = params["fields"]

    for field in fields:
        if field.get("sync"):
            _list.append(field.get("value"))

    callback(_list)


fpe_specs = {
    # Manage Products
    "fpe_actions": {
        "disable_buttons": [
            ("change_state", "grp_command_buttons", {"default_state": "disabled"}),
            ("change_state", "grp_barcodes_command_buttons", {"default_state": "disabled"}),
            ("change_state", "grp_attributes_command_buttons", {"default_state": "disabled"}),
            ("change_state", "grp_product_supplier_command_buttons", {"default_state": "disabled"}),
            ("change_state", "grp_product_simplebundle_command_buttons", {"default_state": "disabled"}),
            ("change_state", "grp_product_advancebundle_command_buttons", {"default_state": "disabled"}),
            ("change_state", "grp_taxes_command_buttons", {"default_state": "disabled"}),
            ("change_state", "g_viewpo_button", {"default_state": "disabled"}),
            ("change_state", "g_massupdatesalescost_button", {"default_state": "disabled"}),
            ("change_state", "g_massupdatepurchasecost_button", {"default_state": "disabled"}),

            ("change_state", "g_autoordersupplierinfo_button", {"default_state": "disabled"})
        ],

        "enable_buttons": ("change_state", "grp_command_buttons", {"default_state": "enabled"}),

        "show_errors": ("error_message", "'\n'.join($errors)"),

        "setup_form": [
            ("if", "$app.lite == True",
                [
                    ("change_state", "app_lite_disable", {"default_state": "disabled"}),
                    "$app_lite_hide.hide",
                    "$app_lite_show.show",
                    "$g_manageproducts_tabs.hide_tab('g_printerzones_tab')",
                    "$g_manageproducts_tabs.hide_tab('g_online_tab')",
                    "$g_manageproducts_tabs.hide_tab('g_attributes_widget')",
                    "$g_manageproducts_tabs.hide_tab('g_purchasinginfo_tabs')",
                ],
                [
                    "$app_lite_hide.show",
                    "$app_lite_show.hide",
                ])
        ],

        "prepare_form":  [
            "$form.set_width(1015)",
            "$grp_phase1_hidden_objects.hide",
            "$g_insights_lineedit.hide",
            "$g_insights_label.hide",

            ("change_state", "grp_disabled_start", {"default_state": "disabled"}),
            "$g_manageproducts_tabs.hide_tab('g_groupings_widget')",
            "$g_manageproducts_tabs.hide_tab('g_printerzones_tab')",
            "$g_manageproducts_tabs.hide_tab('g_pidb_tab')",
            "$g_manageproducts_tabs.hide_tab('g_pidbonline_tab')",
            "$g_manageproducts_tabs.hide_tab('g_pidbmedia_widget')",
            "$g_pidb_ingredents_tabs.hide_tab('g_pidbpidbprodinfo_tab')",
            "$g_sync_pidb_tabWidget.hide_tab('g_pidbmarket_tab')",
            "$g_crossell_tabs.hide_tab('g_modifiers_tab')",
            "$g_pidb_ingredents_tabs.hide_tab('g_pidbcrosssell_tab')",

            # Load Form Settings and Set Permissions
            # ("push", "$storage.location_id", "$form.input.location_ids[0]"),
            ("push", "$storage.location_id", "$app.get_session_data.location_id"),
            ("push", "$storage.location_name", "$app.get_session_data.location_name"),
            # Load Form Settings and Set Permissions

            "set_permissions",

            # Featured
            ("if", "$ext.releases.config['pricing_group'] == 'true'",
             "$g_manageproducts_tabs.show_tab('g_pricinggroups_tab')",
             "$g_manageproducts_tabs.hide_tab('g_pricinggroups_tab')"),

            ("push", "$g_inventory_table", []),
            ("push", "$storage.checking_inventory", False),
            "$form.set_height(680)",
            "disable_buttons",

            # Featured
            ("if", "$ext.releases.config['skuoptions'] == 'true' ",
                [
                    "$g_useserials_checkbox.show",
                    "$g_askforcost_checkbox.show",
                    # "$g_askfornotes_checkbox.show",
                    "$g_promptforcommission_checkbox.show",
                    "$g_askforproductdescription_checkbox.show",
                    "$g_promptfortip_checkbox.show",
                ],
                [
                    "$g_useserials_checkbox.hide",
                    "$g_askforcost_checkbox.hide",
                    # "$g_askfornotes_checkbox.hide",
                    "$g_promptforcommission_checkbox.hide",
                    "$g_askforproductdescription_checkbox.hide",
                    "$g_promptfortip_checkbox.hide",
                    "$g_promptfortip_checkbox.hide",
                ]),

            ("push", "$g_totalresults_label_04", ""),

            "$g_commoverride_lineedit.set_input('currency')",
            "$g_minstocklevel_lineedit.set_input('int')",
            "$g_maxstocklevel_lineedit.set_input('int')",
            "$g_minqtylevel_lineedit.set_input('int')",
            "$g_maxqtylevel_lineedit.set_input('int')",

            "$g_cost_lineedit.set_input('float4_neg')",
            "$g_markup_lineedit.set_input('float4')",
            "$g_markup_lineedit.set_enabled(False)",

            ("change_state", "g_resettaxes_button", {"default_state": "disabled"}),
            ("change_state", "g_binlocation_lineedit", {"default_state": "disabled"}),
            ("change_state", "g_manageoptions_button", {"default_state": "disabled"}),
            ("change_state", "g_massactionoptions_button", {"default_state": "disabled"}),
            ("change_state", "g_massactionseasonal_button", {"default_state": "disabled"}),
            ("change_state", "g_deleteupsell_button", {"default_state": "disabled"}),
            ("change_state", "g_deletecrosssell_button", {"default_state": "disabled"}),
            ("change_state", "g_deletealternative_button", {"default_state": "disabled"}),
            ("change_state", "g_variantgroup_lineedit", {"default_state": "disabled"}),

            ("$g_manageproducts_tabs.select_tab", "$g_basics_widget#self"),
            ("$g_purchasinginfo_tabs.select_tab", "$g_supplierinfo_widget#self"),
            ("$g_historyoption_tabs.select_tab", "$g_movements_widget#self"),

            ("push", "$g_producttype_combobox", [{"name": "Regular - Regular Products", "value": "Regular"},
                                                 {"name": "Gratuity - Tips", "value": "Gratuity"},
                                                 {"name": "Liability - Gift Cards, Abandonment Fees", "value": "Liability"},
                                                 # {"name": "Modifiers - Add Ons", "value": "Modifier"},
                                                 {"name": "Other Expense", "value": "Other Expense"},
                                                 {"name": "Other Income", "value": "Other Income"},
                                                 {"name": "Receivable - House Account Payments", "value": "Receivable"},
                                                 {"name": "Tax - Bag Tax, Usage Based Tax", "value": "Tax"}]),

            ("push", "$g_sales_type_combbox", [{"name": "All",                    "value": "Sale,Layaway,Return from Customer"},
                                               {"name": "Sales",                  "value": "Sale"},
                                               {"name": "Returns from Customers", "value": "Return from Customer"},
                                               {"name": "Layaways",               "value": "Layaway"}]),

            ("push", "$g_sales_sumbyperiod_combbox", [{"name": "Events", "value": "events"},
                                                      {"name": "Week",   "value": "week"},
                                                      {"name": "Month",  "value": "month"}]),

            ("push", "$g_movements_sumbyperiod_combbox", [{"name": "Events", "value": "events"},
                                                          {"name": "Week",   "value": "week"},
                                                          {"name": "Month",  "value": "month"}]),

            ("push", "$g_stockingrule_combobox", [{"name": "Stock", "value": "Stock"},
                                                  {"name": "Special Order", "value": "Special Order"},
                                                  {"name": "Not Available", "value": "Not Available"}]),

            ("push", "$g_sales_type_combbox.selected", "All"),
            ("push", "$g_sales_sumbyperiod_combbox.selected", "events"),
            ("push", "$g_movements_sumbyperiod_combbox.selected", "events"),

            ("push", "$g_imageratio_combobox", [{"name": "1x1", "value": "1x1"},
                                                {"name": "4x3", "value": "4x3"},
                                                # {"name": "7x11", "value": "7x11"},
                                                {"name": "16x9", "value": "16x9"},
                                                {"name": "Free Form", "value": "disable"},]),

            ("push", "$storage.invtry_qty", []),

            ("push", "$storage.sender", "None"),
            ("push", "$storage.order_update_value", False),
            ("push", "$storage.original_price", 0),
            ("push", "$storage.original_cost", 0),
            ("push", "$storage.checkprice", False),
            ("push", "$storage.recycle", False),
            ("push", "$storage.markups_increase", False),
            ("push", "$storage.loading", True),
            ("push", "$storage.imagesaved", True),

            ("push", "$storage.product_id", "$form.input.ids[$paginator.index]"),
            ("push", "$storage.product_department_id", 0),
            ("push", "$storage.product_category_id", 0),
            ("push", "$storage.product_price_id", None),
            ("push", "$storage.prdpr_product_pricing_group_id", None),
            ("push", "$storage.product_location_settings_id", None),
            ("push", "$storage.price_link_id", None),
            ("push", "$storage.product_price", None),
            ("push", "$storage.variant_id", None),

            # Customer History
            ("push", "$storage.customerhistory_user_settings", None),  # User Table settings
            ("push", "$storage.customerhistory_effects", None),
            ("push", "$storage.customerhistory_quippet_translation", None),
            ("push", "$storage.customerhistory_user_setting_id", None),
            ("push", "$storage.customerhistory_column_edit", None),
            ("push", "$storage.customerhistory_table_settings", None),
            ("push", "$storage.customerhistory_user_table_settings", None),
            ("push", "$storage.totalresults_customerhistory", 0),

            ("push", "$g_history_startdate_timeedit", "#date#beginning - #timedelta(30)"),
            ("push", "$g_history_enddate_timeedit", "#date#ending"),

            # ("push", "$g_customerhistory_startdate_timeedit", "#date#beginning"),
            # ("push", "$g_customerhistory_enddate_timeedit", "#date#ending"),

            ("case", {
                "value": "$form.input.flags",

                "create": [
                    ("push", "$paginator.total", "$form.input.ids#length"),
                    ("if", "$paginator.total < 2", "$g_pagenav_frame.hide")
                ],

                "update": [
                    ("push", "$paginator.total", "$form.input.ids#length"),
                    ("if", "$paginator.total < 2", "$g_pagenav_frame.hide")
                ]
            }),

            "$history_datetime_range.set_on_change('load_history_tab')",

            ("push", "$storage.temp", [{"type": "amount", "name": "Amount"}, {"type": "percent", "name": "Percent"}]),

            ("push", "$storage.breaktypecombobox", "#dict('type', #dict('match', 'type',"
                                                                       "'show', 'name',"
                                                                       "'data', $storage.temp))"),
            "$breakpoint_droplist.set_combox_data($storage.breaktypecombobox)",

            ("push", "$storage.aisle", [{"aisles": "00"}]),
            ("push", "$storage.section", [{"sections": "00"}]),
            ("push", "$storage.shelf", [{"shelfs": "00"}]),
            ("push", "$storage.bin", [{"bins": "00"}]),

            ("push", "$storage.prod_locations", None),

            ("push", "$storage.prodlocations", "#dict('aisles', #dict('match', 'aisles', 'show', 'aisles', 'data', $storage.aisle),"
                                                   "'sections', #dict('match', 'sections', 'show', 'sections', 'data', $storage.section),"
                                                     "'shelfs', #dict('match', 'shelfs', 'show', 'shelfs', 'data', $storage.shelf),"
                                                       "'bins', #dict('match', 'bins', 'show', 'bins', 'data', $storage.bin))"),
            "$prodlocation_droplist.set_combox_data($storage.prodlocations)",

            # Corp settings
            ("push", "$storage.corp", "$app.get_corp_data"),
            ("if", "$storage.corp == False",
                [
                    "$g_optionscorp_frame.hide",
                    "$g_seasonalcorp_frame.hide",
                    "$g_historylocations_frame.hide",
                    "$g_promolocations_frame.hide"
                    "$g_settingslocation_frame.hide"
                ],
                [
                    "$g_optionslocation_frame.hide",
                    "$g_seasonal_frame.hide",
                    ("if", "$app.permissions.has_role(-8,-9,-19)",
                        [
                            "$g_historylocations_frame.show",
                            "$g_promolocations_frame.show",
                            "$g_settingslocation_frame.show",
                        ]),
                    ("if", "$app.permissions.has_role(-9)",
                        "$g_replacementproduct_alllocations_checkbox.show")
                ]),

            ("push", "$g_sku_lineedit", "$form.input.data[0]#get('product_id')"),
            ("push", "$g_description_lineedit", "$form.input.data[0]#get('prod_name')"),
            ("push", "$g_cost_lineedit", "$form.input.data[0]#get('prdpr_cost')#currency(4,0) or '0.00'"),
            ("push", "$g_markup_lineedit", "$form.input.data[0]#get('prdpr_markup')#currency(4,0) or '0'"),
            ("push", "$g_price_lineedit", "$form.input.data[0]#get('prdpr_price')#currency(2,0) or '0.00'"),
            ("if", "$form.input.data[0]#get('prdpr_uses_markup')",
             [
                 "$g_price_radioButton.uncheck",
                 "$g_markup_radioButton.check"
             ],
             [
                 "$g_price_radioButton.check",
                 "$g_markup_radioButton.uncheck"
             ]),

            ("if", "$g_price_lineedit.data#int  != 0 and $g_cost_lineedit.data#int != 0 and $g_price_lineedit.data#int != 0",
                ("push", "$g_margin_label",
                 "((($g_price_lineedit.data#float#round(2) - $g_cost_lineedit.data#float#round(2)) / $g_price_lineedit.data#float#round(2)) *100)#currency(2,0)#str + '%'")),

            ("if", "$form.input.data[0]#get('price_link_id') != None",
                ("push", "$g_price_label", "Link Price"),
                ("push", "$g_price_label", "Price")),

            # Show Deliveries Groupbox
            ("if", "$app.integrations.is_enabled(" + str(Integrations.STORE_PICKUP) + ") == True",
                [
                    "$g_deliverytypes_groupbox.show",
                    "$g_storepickup_checkbox.show"
                ]),
            ("if", "$app.integrations.is_enabled(" + str(Integrations.SCHEDULED_DELIVERY) + ") == True",
                [
                    "$g_deliverytypes_groupbox.show",
                    "$g_scheduleddelivery_checkbox.show"
                ]),
            ("if", "$app.integrations.is_enabled(" + str(Integrations.SHIP_TO_HOME) + ") == True",
                [
                    "$g_deliverytypes_groupbox.show",
                    "$g_shiptohome_checkbox.show"
                ]),

            # PIDB
            ("push", "$storage.host", False),
            ("push", "$storage.token", False),
            ("push", "$storage.manufacturers", False),
            ("push", "$storage.brand", False),
            ("push", "$storage.categories", False),
            ("push", "$storage.subcategories", False),
            ("push", "$storage.product", False),
            ("push", "$storage.pidb_product", None),
            ("push", "$storage.product_info", None),
            ("push", "$storage.media_id", []),
            ("push", "$storage.level", "$form.input.level"),
            ("push", "$storage.pidb_field_list", None),

            # Pricing Group
            ("push", "$storage.pricinggroup_table_edit", None),

            # Subscriptions
            ("push", "$g_subscriptionqty_lineedit", 1),

            ("push", "$g_subscriptionperiod_combobox", [{"name": "Days", "value": "Days"},
                                                        {"name": "Weeks", "value": "Weeks"},
                                                        {"name": "Months", "value": "Months"},
                                                        {"name": "Years", "value": "Years"},
                                                        {"name": "Lifetime", "value": "Lifetime"},]),
            ("push", "$g_renewal_combobox", [{"name": "Prompt", "value": "Prompt"},
                                             {"name": "Auto", "value": "Auto"},
                                             {"name": "Expire", "value": "Expire"}]),

            # Product Warnings
            ("push", "$c_productwarnings_checkcombobox", [{"name": "Low Margin", "check": False},
                                                          {"name": "Negative Inventory", "check": False},
                                                          {"name": "No Cost", "check": False},
                                                          {"name": "No Supplier Available", "check": False}]),


            # Dimensions
            ("push", "$storage.old_dimension_unit", None),
            ("push", "$g_dimension_measure_combobox", [{"name": "in", "value": "in"},
                                                       {"name": "ft", "value": "ft"},
                                                       {"name": "mm", "value": "mm"},
                                                       {"name": "cm", "value": "cm"},
                                                       {"name": "dm", "value": "dm"},
                                                       {"name": "m", "value": "m"}]),

            # Weight
            ("push", "$storage.old_weight_unit", None),
            ("push", "$g_prod_weightmeasure_combobox", [{"name": "lb", "value": "lb"},
                                                        {"name": "g", "value": "g"},
                                                        {"name": "kg", "value": "kg"}]),
        ],

        "set_permissions": [
            "$g_price_lineedit.set_enabled(False)",
            ("change_state", "grp_permissions", {"default_state": "disabled"}),
            ("change_state", "g_cost_lineedit", {"default_state": "disabled"}),
            ("change_state", "g_markup_lineedit", {"default_state": "disabled"}),
            ("change_state", "g_markup_radioButton", {"default_state": "disabled"}),
            ("change_state", "g_price_radioButton", {"default_state": "disabled"}),
            ("change_state", "g_question_label", {"default_state": "enabled"}),
            ("change_state", "g_recycle_label", {"default_state": "enabled"}),

            ("if", "$app.permissions.has_permission(-1)",  # View Cost
                [
                    "$g_costlastreceived_frame.show",
                    "$g_pricelastsaledate_frame.show",
                    "$g_price_radioButton.show",
                    "$g_markup_radioButton.show",
                ]),

            ("if", "$app.permissions.has_permission_other(2, -1)",  # Modify Cost
                [
                    "$g_price_radioButton.show",
                    "$g_markup_radioButton.show",
                    "$g_costlastreceived_frame.show",
                    "$g_pricelastsaledate_frame.show",
                    "$g_price_lineedit.set_enabled(True)",
                    ("change_state", "g_cost_lineedit", {"default_state": "enabled"}),
                    ("change_state", "g_markup_lineedit", {"default_state": "enabled"}),
                    ("change_state", "g_markup_radioButton", {"default_state": "enabled"}),
                    ("change_state", "g_price_radioButton", {"default_state": "enabled"})
                ]),

            ("if", "$app.permissions.has_permission_other(0, -1)",
                "$g_customerhistory_tableView.hidden_columns('txnline_sale_cost')"),

            ("if", "$app.integrations.is_enabled(" + str(Integrations.PRINTING_ZONES) + ") == True and "
                                                                                        "$app.permissions.has_role(-9) and $app.lite != True",
                "$g_manageproducts_tabs.show_tab('g_printerzones_tab')",),

            ("if", "$app.permissions.has_role(-7)",
             [
                 "$g_description_lineedit.set_enabled(False)",
                 "$g_mfg_combobox.set_enabled(False)",
                 "$g_brand_combobox.set_enabled(False)",
                 # "$g_department_combobox.set_enabled(False)",
                 # "$g_category_combobox.set_enabled(False)",
                 "$g_barcodebuttons_frame.hide",

                 "$g_dailysalesavg_label.hide",
                 "$g_lastsaledatevalue_label.hide",
                 "$g_attributesbuttons_frame.hide",

                 "$g_manageproducts_tabs.hide_tab('g_advanced_widget')",
                 "$g_manageproducts_tabs.hide_tab('g_options_tab')",
                 "$g_manageproducts_tabs.hide_tab('g_purchasing_widget')",

                 "$g_historyoption_tabs.hide_tab('g_movements_widget')",
                 "$g_historyoption_tabs.hide_tab('g_sales_widget')",
                 "$g_historyoption_tabs.hide_tab('g_purchases_tab')",
                 "$g_historyoption_tabs.hide_tab('g_productupdates_widget')",
                 "$g_historyoption_tabs.hide_tab('g_order_tab')",
                 "$g_historyoption_tabs.hide_tab('g_transfers_tab')",
                 "$g_historyoption_tabs.hide_tab('g_inventoryadjustments_tab')",

                 ("change_state", "g_advanced_widget", {"default_state": "disabled"}),

                 "$g_upload_frame.hide",
             ]),

            ("if", "$app.permissions.has_role(-17)",
             [
                 ("change_state", "g_media_widget", {"default_state": "disabled"}),

                 "$g_manageproducts_tabs.hide_tab('g_advanced_widget')",
                 "$g_manageproducts_tabs.hide_tab('g_options_tab')",
                 "$g_manageproducts_tabs.hide_tab('g_purchasing_widget')",
                 "$g_historyoption_tabs.hide_tab('g_movements_widget')",
                 "$g_historyoption_tabs.hide_tab('g_sales_widget')",
                 "$g_historyoption_tabs.hide_tab('g_purchases_tab')",
                 "$g_historyoption_tabs.hide_tab('g_productupdates_widget')",
                 "$g_historyoption_tabs.hide_tab('g_order_tab')",
                 "$g_historyoption_tabs.hide_tab('g_transfers_tab')",
                 "$g_historyoption_tabs.hide_tab('g_inventoryadjustments_tab')",

                 "$g_description_lineedit.set_enabled(False)",
                 "$g_mfg_combobox.set_enabled(False)",
                 "$g_brand_combobox.set_enabled(False)",
                 # "$g_department_combobox.set_enabled(False)",
                 "$g_category_combobox.set_enabled(False)",

                 "$g_dailysalesavg_label.hide",
                 "$g_lastsaledatevalue_label.hide",
                 "$g_transfer_groupBox.hide",
                 "$g_barcodebuttons_frame.hide",
                 "$g_attributesbuttons_frame.hide",
                 "$g_upload_frame.hide",
                 "$g_pormobuttons_frame.hide",
                 "$g_simplebundlebuttons_frame.hide",
                 "$g_advancedbundlebuttons_frame.hide"
             ]),

            ("if", "$app.permissions.has_role(-8,-9,-19)",
             [
                 "$g_upsellcontrols_frame.show",
                 "$g_crosssellcontroles_frame.show",
                 "$g_alternativecontrols_frame.show"
             ]),

            ("if", "$app.permissions.has_permission(-10)",  # Adjust Inventory Button
                [
                    "$g_transfer_groupBox.show",
                    "$g_adjustinventory_button.show"
                ],
                "$g_adjustinventory_button.hide"),

            ("if", "$app.permissions.has_permission(-19)",  # Adjust Inventory Button
                [
                    "$g_transfer_groupBox.show",
                    "$g_transferinventory_button.show"
                ],
                "$g_transferinventory_button.hide"),

            ("if", "$app.permissions.has_permission_other(2, -25) or "
                   "$app.permissions.has_permission_other(4, -25) or "
                   "$app.permissions.has_permission_other(5, -25)",  # Modify Categories/Sub Categories
                [
                    ("change_state", "g_department_combobox", {"default_state": "enabled"}),
                    ("change_state", "g_category_combobox", {"default_state": "enabled"}),
                ]),
        ],

        "load_customerhistory_settings": [
            ("transfers", {
                "names": [
                    "get_customerhistory_user_table_settings"
                ],
                "on_success": [
                    "$paginator_customerhistory.per_page = 25",
                    "$g_customerhistory_tableView.field_per_page(25)",
                    ("transfer", "get_customerhistory_column_names",

                     [
                         ("if", "$storage.customerhistory_user_table_settings != None",
                          "$g_customerhistory_tableView.load_user_settings($storage.customerhistory_user_table_settings.get('stn_value'))",
                          "$g_customerhistory_tableView.load_user_settings()"),

                         ("if", "$storage.customerhistory_effects != None",
                          ("if", "$storage.customerhistory_user_settings != None",
                           "$paginator_customerhistory.per_page = $storage.customerhistory_user_settings#eval#get('perpage') or 25")),

                         ("if", "$storage.customerhistory_quippet_translation != None",
                          "$g_customerhistory_tableView.column_names($storage.customerhistory_quippet_translation)"),

                         ("push", "$storage.loading", False),
                         ("if", "'update' in $form.input.flags",
                          ("transfer", "get_customerhistory"))
                     ]),
                ]
            }),
        ],

        "load_data": [
            ("transfer", "get_locations", "load_after_location_change")
        ],

        "load_after_location_change": [
            ("push", "$storage.loading", True),
            ("push", "$storage.price_link_id", None),
            {"case": {
                "value": "$form.input.flags",
                "update": {
                    "transfers": {
                        "names": [
                            "get_comission_status",
                            "get_current_location",
                            "get_entity_locations",
                            "get_iamge_ratio",
                            "read_autopricemarkup",
                            "get_linked_price",
                            "get_departments",
                            "get_manufacturers",
                            "get_product_types",
                            "get_brands",
                            "get_suppliers",
                            "get_extendedactions",
                            "get_price_link",
                            "get_nutrition",
                            "get_printer_zones",
                            "get_ecommerce_integration",
                            "get_product_location",
                            "get_product_locations",
                            "get_product_attributes"
                        ],
                        "on_success": [
                            ("transfers", [
                                "get_product",
                                "get_products_barcodes",
                            ],
                             [
                                 "enable_buttons",
                                 ("transfers", [
                                     "get_inventory_totals",
                                     "get_product_suppliers",
                                     "read_commenable_settings",
                                     "read_tipssettings",
                                     "get_openorders",
                                     "get_images",
                                     "get_purchases_by_date",
                                     "get_product_sales_history",
                                     "get_promotions",
                                     "get_promotions_breaks",
                                     "get_storetaxes",
                                     "read_commenable_settings",
                                     "read_tipssettings",
                                     "get_product_history_orders",
                                     "get_products_history_transfers",
                                     "get_products_history_inventory_adjustments",
                                     "get_pricinggroup",
                                     "get_openInvoices",
                                 ],
                                  [
                                      "load_product_sales",
                                      "load_product_movements",
                                      "load_customerhistory_settings",
                                      ("if", "$storage.corp == True",
                                       [
                                           ("transfer", "get_product_options"),
                                           ("transfer", "get_product_seasonal")
                                       ]),
                                      "load_pidb",
                                  ]),
                             ]),
                        ],
                    }
                }
            }}
        ],

        "load_next": [
            ("push", "$storage.loading", True),
            ("push", "$storage.product_id", "$form.input.ids[$paginator.index]"),
            # Clear PIDB
            "$g_sync_button.hide",
            "$g_noproduct_frame.show",
            "$g_pidb_frame.hide",
            "$pidb_media_viewer.clear",
            "$g_pidb_barcodes_table.clear",
            "$g_pidb_suppliers_table.clear",
            "$sync_droplist.clear",
            "$g_pidb_images_list.clear",

            ("push", "$g_pidb_description_lineedit", ""),
            ("push", "$g_pidb_barcodes_table", ""),
            ("push", "$g_pidb_suppliers_table", ""),
            ("push", "$g_pidb_mfg_combobox.selected", None),
            ("push", "$g_pidb_brand_combobox.selected", None),
            ("push", "$g_pidb_category_combobox.selected", None),

            ("transfer", "get_product_location"),
            ("if", "$storage.corp == True", ("transfer", "get_product_options")),

            {"transfers": {
                "names": [
                    "get_entity_locations",
                    "get_product_locations",
                    "get_product_attributes",
                    "get_inventory_totals",
                    "get_storetaxes",
                    "get_product_suppliers",
                    "get_openorders",
                    "get_images",
                    "get_promotions",
                    "get_promotions_breaks",
                    "get_purchases_by_date",
                    "get_product_sales_history",
                    "get_products_barcodes",
                    "get_product_history_orders",
                    "get_products_history_transfers",
                    "get_products_history_inventory_adjustments",
                    "get_pricinggroup",
                    "get_customerhistory"
                ],
                "on_finish": [
                    "load_pidb",
                    "load_product_sales",
                    "load_product_movements",
                    ("transfer", "get_product", "enable_buttons")
                ],
            }},
        ],

        "load_pidb": [
            ("if", "$app.permissions.has_role(-9",
                ("if", "$app.integrations.is_enabled(" + str(Integrations.PIDBSUPPLIERS) + ") == True",
                    [
                        "$g_manageproducts_tabs.show_tab('g_pidb_tab')",
                        ("transfer", "get_integration")
                    ])),
        ],

        "check_save_image": [
            ("if", "$storage.imagesaved == True",
             "save_product",
             ("confirm", "Media has not been saved. Continue without saving Media?", "save_product"))
        ],

        "save_product": [
            {"validate": {
                "on_success": [
                    ("push", "$storage.cost", None),

                    ("if", "$storage.original_cost#float != 0",
                        [
                            ("if", "$g_cost_lineedit.data#float < $storage.original_cost#float",
                                ("if", "(($g_cost_lineedit.data#float / $storage.original_cost#float) * 100) < 80",
                                    ("push", "$storage.cost", "You have reduced the cost by more than 20%.  Is this correct?"))),

                            ("if", "$g_cost_lineedit.data#float > $storage.original_cost#float",
                                ("if", "((($g_cost_lineedit.data#float - $storage.original_cost#float) / $storage.original_cost#float) * 100) > 20",
                                    ("push", "$storage.cost", "You have increased the cost by more than 20%.  Is this correct?"))),

                            ("if", "$g_price_lineedit.data#float < $storage.original_price#float",
                                ("if", "(($g_price_lineedit.data#float / $storage.original_price#float) * 100) < 80",
                                    ("if", "$storage.cost == None",
                                        ("push", "$storage.cost", "You have reduced the price by more than 20%.  Is this correct?"),
                                        ("push", "$storage.cost", "You have reduced the cost and price by more than 20%.  Is this correct?")))),

                            ("if", "$g_price_lineedit.data#float > $storage.original_price#float",
                                ("if", "((($g_price_lineedit.data#float - $storage.original_price#float) / $storage.original_price#float) * 100) > 20",
                                    ("push", "$storage.cost", "You have increased the price by more than 20%.  Is this correct?")))
                        ]),

                    ("if", "$storage.cost != None",
                        {"confirm": {
                            "text": "> {$storage.cost}",
                            "on_accept": "save_product_2"}},
                        "save_product_2")
                ],
                "on_failure": "show_errors"
            }}
        ],

        "save_product_2": [
            ("if", "$g_enableaction_checkbox and $g_extendedaction_combobox.text == 'Pet Tracker'",
                ("return", {
                    "event": "product_setup",
                    "app": "Pet Tracker",
                    "product_id": "$form.input.ids[$paginator.index]"})),

            {"case": {
                "value": "$form.input.flags",

                "create": "save_and_close",
                "update": "save_and_move"
            }}
        ],

        "save_and_close": {
            "validate": {
                "on_success": {
                    "transfers": {
                        "names": [
                            "update_product"
                        ],
                        "on_success": "$form.accept"
                    }
                }
            }
        },

        "save_and_move": {
            "validate": {
                "on_success": {
                    "transfers": {
                        "names": [
                            "update_product"
                        ],
                        "on_success": [
                            ("if", "$paginator.is_last", "$form.accept"),
                            ("if", "not $paginator.is_last",
                                [
                                    "$paginator.stop_flag = False",
                                    "$paginator.next"
                                ])
                        ]
                    }
                }
            }
        },

        "on_record_move": [
            "save_when_flipping"
        ],

        "save_when_flipping": [
            ("if", "$storage.imagesaved == True",
             "save_before_flipping",
             ("confirm", {
                 "text": "Media has not been saved. Continue without saving Media?",
                 "on_reject": [
                     "enable_buttons",
                     "$paginator.stop"
                 ],
                 "on_accept": [
                     "$g_addedit_groupBox.show",
                     "$g_cropper_area.hide",
                     "$g_crop_and_save_btn.hide",
                     "$g_cropper_cancel_btn.hide",
                     ("push", "$g_uploadfilename_lineedit", ""),
                     "save_before_flipping"
                 ]
             }
              ))
        ],

        "save_before_flipping": [
            {"validate": {
                "on_success": [
                    ("push", "$storage.cost", None),
                    ("if", "$g_cost_lineedit.data#float < $storage.original_cost#float",
                        ("if", "(($g_cost_lineedit.data#float / $storage.original_cost#float) * 100) < 80",
                            ("push", "$storage.cost", "You have reduced the cost by more than 20%.  Is this correct?"))),

                    ("if", "$g_cost_lineedit.data#float > $storage.original_cost#float",
                        ("if", "((($g_cost_lineedit.data#float - ($storage.original_cost.data if $storage.original_cost.data != '' or 0)#float)"
                               " / ($storage.original_cost.data if $storage.original_cost.data != '' or 0)#float) * 100) > 20",
                            ("push", "$storage.cost", "You have increased the cost by more than 20%.  Is this correct?"))),

                    ("if", "$g_price_lineedit.data#float < $storage.original_price#float",
                        ("if", "(($g_price_lineedit.data#float / $storage.original_price#float) * 100) < 80",
                            ("if", "$storage.cost == None",
                                ("push", "$storage.cost", "You have reduced the price by more than 20%.  Is this correct?"),
                                ("push", "$storage.cost", "You have reduced the cost and price by more than 20%.  Is this correct?")))),

                    ("if", "$g_price_lineedit.data#float > $storage.original_price#float",
                        ("if", "((($g_price_lineedit.data#float - $storage.original_price#float) / $storage.original_price#float) * 100) > 20",
                            ("push", "$storage.cost", "You have increased the price by more than 20%.  Is this correct?"))),

                    ("if", "$storage.cost != None",
                        {"confirm": {
                            "text": "> {$storage.cost}",
                            "on_accept": [
                                "$paginator.stop_flag = False",
                                "save_when_flipping_2",
                            ],
                            "on_reject": [
                                "$paginator.stop",
                                "enable_buttons",
                            ]}},
                        "save_when_flipping_2")
                ],
                "on_failure": "show_errors"
            }}
        ],

        "stop_next": [

        ],

        "save_when_flipping_2": [
            ("transfer", "update_product"),
        ],

        "cancel_product": {
            "case": {
                "value": "$form.input.flags",

                "create": {
                    "transfers": {
                        "names": [
                            "delete_product"
                        ],
                        "on_success": "$form.accept"
                    }
                },

                "update": "$form.accept"
            }
        },

        "manage_extendedaction_combobox": [
            ("if", "$g_enableaction_checkbox.data",
                [
                    ("change_state", "g_extendedaction_combobox", {"default_state": "enabled"}),
                    ("if", "$g_extendedaction_combobox.selected.extended_action_id == -5",
                        [
                            "$g_subscriptionperiod_frame.show",
                            "$g_subscriptionrenewal_frame.show",
                        ],
                        [
                            "$g_subscriptionperiod_frame.hide",
                            "$g_subscriptionrenewal_frame.hide",
                        ])
                ],
                [
                    ("change_state", "g_extendedaction_combobox", {"default_state": "disabled"}),
                    "$g_subscriptionperiod_frame.hide",
                    "$g_subscriptionrenewal_frame.hide",
                ]),


        ],

        "load_openorders": [
            ("transfer", {
                "name": "get_openorders"
            })
        ],

        "load_product_update": ('transfer', 'get_product_updates'),

        "update_pricing": [
            ("if", "$g_markup_radioButton",
                [
                    ("if", "$g_markup_lineedit.data#is_int or $g_markup_lineedit.data#is_float ",
                         ("transfer", "get_markup",
                             ("push", "$g_margin_label", "((($g_price_lineedit.data#float#round(2) - $g_cost_lineedit.data#float#round(2)) / "
                                         "$g_price_lineedit.data#float#round(2)) *100)#currency(2,0)#str + '%'")),
                        [
                            ("push", "$g_price_lineedit", "$storage.original_price"),
                            ("push", "$g_margin_label", ""),
                            ("if", "$g_cost_lineedit.data#currency#str != '0.00' and $g_price_lineedit.data#currency#str != '0.00'",
                                        ("push", "$g_margin_label", "((($g_price_lineedit.data#float#round(2) - $g_cost_lineedit.data#float#round(2)) / $g_price_lineedit.data#float#round(2)) *100)#currency(2,0)#str + '%'"),
                                        ("push", "$g_margin_label", ""))
                        ]),
                ],
                [
                    "update_markup_from_price"
                ])

        ],

        "update_markup_from_price": [
            ("if", "$g_price_lineedit.data#is_int or $g_price_lineedit.data#is_float ",
             ("if", "$g_cost_lineedit.data#float > 0",
              [
                  ("push", "$g_markup_lineedit",
                   ("($g_price_lineedit.data#float#round(2) / $g_cost_lineedit.data#float#round(2))#currency(2,0)")),
                  ("push", "$g_margin_label",
                   "((($g_price_lineedit.data#float#round(2) - $g_cost_lineedit.data#float#round(2)) / "
                   "$g_price_lineedit.data#float#round(2)) *100)#currency(2,0)#str + '%'"),
              ],
              [
                  ("if", "$g_price_lineedit == ''",
                   [("push", "$g_price_lineedit", '0'), ("push", "$g_margin_label", ""),
                    ("push", "$g_markup_lineedit", "0")],
                   [
                       ("if",
                        "$g_cost_lineedit.data#currency#str != '0.00' and $g_price_lineedit.data#currency#str != '0.00'",
                        ("if", "$g_price_lineedit.data#float > 0",
                         ("push", "$g_margin_label",
                          "((($g_price_lineedit.data#float#round(2) - $g_cost_lineedit.data#float#round(2)) / $g_price_lineedit.data#float#round(2)) *100)#currency(2,0)#str + '%'"),
                         ("push", "$g_margin_label", "")),
                        ("push", "$g_margin_label", ""))
                   ])
              ])),
        ],

        "pull_product_information": [
            ("call", pull_bin_location, {"locations": "$prodlocation_droplist.data"},
                {"pull": {"qp_prlocs_bin_location": "$result"}}),

            ("call", pull_nutritional, {"value": "$nutrition_droplist.data"},
                {"pull": {"qp_prod_nutritional": "$result"}}),

            ("if", "$storage.corp != True",
                {"pull": {
                    # Purchase information
                    "qp_prlocs_min_safety_stock_days": "$g_minstocklevel_lineedit or None",
                    "qp_prlocs_max_safety_stock_days": "$g_maxstocklevel_lineedit or None",
                    "qp_prlocs_min_location_qty": "$g_minqtylevel_lineedit or None",
                    "qp_prlocs_max_location_qty": "$g_maxqtylevel_lineedit or None",
                    "qp_prlocs_auto_order": "$g_autoorder_checkbox",

                    # Seasonal
                    "qp_prlocs_product_is_seasonal": "$g_isseasonal_checkbox",

                    "qp_prlocs_jan_is_seasonal": "$g_isseasonaljan_checkbox",
                    "qp_prlocs_feb_is_seasonal": "$g_isseasonalfeb_checkbox",
                    "qp_prlocs_mar_is_seasonal": "$g_isseasonalmar_checkbox",
                    "qp_prlocs_apr_is_seasonal": "$g_isseasonalapr_checkbox",
                    "qp_prlocs_may_is_seasonal": "$g_isseasonalmay_checkbox",
                    "qp_prlocs_jun_is_seasonal": "$g_isseasonaljun_checkbox",
                    "qp_prlocs_jul_is_seasonal": "$g_isseasonaljul_checkbox",
                    "qp_prlocs_aug_is_seasonal": "$g_isseasonalaug_checkbox",
                    "qp_prlocs_sep_is_seasonal": "$g_isseasonalsep_checkbox",
                    "qp_prlocs_oct_is_seasonal": "$g_isseasonaloct_checkbox",
                    "qp_prlocs_nov_is_seasonal": "$g_isseasonalnov_checkbox",
                    "qp_prlocs_dec_is_seasonal": "$g_isseasonaldec_checkbox",
                }}),

            ("call", pull_printezones, {"zones": "$printerzone_list.checked_data"},
             ("if", "$result", [
                 ("if", "$result#length > 0",
                    {"pull": {"qp_printing_zone_ids": "$result"}})
             ],
                ("if", "$result#str != '[]'",
                    ("info_message", "Error in combining print zone list"),
                    {"pull": {"qp_printing_zone_ids": []}}))
             ),

            ("push", "$storage.deliverytypes", ""),

            ("if", "$g_storepickup_checkbox == False",
                [
                    ('if', "$storage.deliverytypes != ''",
                        ("push", "$storage.deliverytypes", "$storage.deliverytypes + '@~'")),
                    ("push", "$storage.deliverytypes", "$storage.deliverytypes + 'Pickup'")
                ]),

            ("if", "$g_scheduleddelivery_checkbox == False",
                [
                    ('if', "$storage.deliverytypes != ''",
                        ("push", "$storage.deliverytypes", "$storage.deliverytypes + '@~'")),
                    ("push", "$storage.deliverytypes", "$storage.deliverytypes + 'Scheduled Delivery'"),
                ]),

            ("if", "$g_shiptohome_checkbox == False",
                [
                    ('if', "$storage.deliverytypes != ''",
                        ("push", "$storage.deliverytypes", "$storage.deliverytypes + '@~'")),
                    ("push", "$storage.deliverytypes", "$storage.deliverytypes + 'Ship to Home'"),
                ]),

            {"pull": {"qp_prlocs_excluded_delivery_types": "($storage.deliverytypes if $storage.deliverytypes#length > 0) or '@null'"}},

            ("if", "$g_replacementproduct_alllocations_checkbox == True",
                {"pull": {"qp_all_prlocs_replaced_by_product_id": "($g_replacementprod_id_lineedit.data if $g_replacementprod_id_lineedit.data != '') or None"}},
                {"pull": {"qp_prlocs_replaced_by_product_id": "($g_replacementprod_id_lineedit.data if $g_replacementprod_id_lineedit.data != '') or None "}})



        ],

        "get_page_results_customerhistory": [
            ("push", "$g_totalresults_customerhistory_label",
             "> {$storage.totalresults_customerhistory} results found.  {$paginator_customerhistory.limit} per page."),
        ],

        # History Tab
        "load_history_tab": [
            "load_product_movements",
            "load_product_sales",
            "load_puchases_by_date",
            ("transfers", {
                "names": [
                    "get_customerhistory",
                    "get_openInvoices",
                    "get_products_history_transfers",
                    "get_products_history_inventory_adjustments",
                    "get_products_history_inventory_adjustments",
                ],
            })
        ],

        "load_product_movements": [
            ("push", "$g_movements_table", []),
            ("if", "$g_movements_sumbyperiod_combbox.selected.value == 'events'",
             ("transfer", "get_product_movements_by_event"),
             ("transfer", "get_product_movements_summary"))
        ],

        "load_product_sales": [
            ("push", "$g_sales_table", []),
            ("if", "$g_sales_sumbyperiod_combbox.selected.value == 'events'",
                [
                    "$g_massupdatesalescost_button.show",
                    ("transfer", "get_product_sales_by_event"),
                ],
                [
                    "$g_massupdatesalescost_button.hide",
                    ("transfer", "get_product_sales_summary")
                ])
        ],

        "load_puchases_by_date": ('transfer', 'get_purchases_by_date'),

        "update_sales_history_cost": [
            ("popup", "price", "$g_sales_table.selected.txnline_sale_cost#currency",
                [
                    ("push", "$storage.updated_sales_history_cost", "$result"),
                    ("transfer", "updated_sales_history_cost")
                ], "Update Cost", 0, 99999)
        ],

        "update_purchase_history_cost": [
            ("popup", "price", "$g_purchases_table.selected.txnline_amount#currency",
             [
                 ("push", "$storage.update_purchase_history_cost", "$result"),
                 ("transfer", "update_purchase_history_cost")
             ], "Update Cost", 0, 99999)
        ],

        "update_order_history_cost": [
            ("popup", "price", "$g_orders_table.selected.txnline_amount#currency",
             [
                 ("push", "$storage.update_products_history_orders_cost", "$result"),
                 ("transfer", "update_products_history_orders_cost")
             ], "Update Cost", 0, 99999)
        ],

        "update_transfers_history_cost": [
            ("popup", "price", "$g_transfers_table.selected.txnline_amount#currency",
             [
                 ("push", "$storage.updated_sales_history_transfers_cost", "$result"),
                 ("transfer", "update_product_history_transfers_cost")
             ], "Update Cost", 0, 99999)
        ],

        "update_invadj_history_cost": [
            ("popup", "price", "$g_inventoryadjustments_table.selected.txnline_amount#currency",
             [
                 ("push", "$storage.update_invadj_history_cost", "$result"),
                 ("transfer", "update_invadj_history_cost")
             ], "Update Cost", 0, 99999)
        ],

        # Promotions
        "open_promotion": [
            ("if", "$app.permissions.has_role(-8,-9,-19)",
                ("transfer", "update_promotion_form"))
        ],

        # Inventory
        "show_inventory_popup": [
            ("transfer", "show_inventory_popup")
        ],

        # Print Product Label
        "print_product_label": [
            ("popup", "quantity", "'1'",
             [
                 ("push", "$storage.popup_qty", "$result"),
                 ("if", "$ext.reports.config['printer_unit'] and $ext.reports.config['label_unit']",
                  ("transfer", "get_unit_label", [
                      ("transfers", ["get_unit_template_data", "get_unit_template_file"], [
                          (
                              "$ext.label_printer.print",
                              "$ext.reports.config['printer_unit']",
                              "$result[1][0].flin_contents",
                              "$result[0]*$storage.popup_qty#int"
                          )
                      ])]),
                  ("error_message", "Please setup labels and printers in settings screen"))
             ], "Print Qty")
        ],

        # Print Shelf Label
        "print_shelf_label": [
             ("if", "$ext.reports.config['printer_shelf'] and $ext.reports.config['label_shelf']",
              ("transfer", "get_shelf_label",
               [
                   ("transfers", ["get_shelf_template_data", "get_shelf_template_file"], [
                       (
                           "$ext.label_printer.print",
                           "$ext.reports.config['printer_shelf']",
                           "$result[1][0].flin_contents",
                           "$result[0]*1"
                       )])]),
              ("error_message", "Please setup labels and printers in settings screen"))
        ],

        # PIDB
        "sync_pos": [
            ("call", pull_pidb_sync, {"fields": "$sync_droplist.data"},
                ("if", "$result",
                    [
                        ("push", "$storage.pidb_field_list", "$result"),

                        ("transfer", "update_pidb_product"),
                    ],
                    [
                        ("info_message", "No selections have selected to sync"),
                ])),
        ],

        "close_after_pidb_sync": [
            ("if", "$paginator.is_last", [
                ("transfer", "update_product", "$form.accept"),

             ]),
            ("if", "not $paginator.is_last",
                [
                    "$paginator.stop_flag = False",
                    ("transfer", "update_product", "$paginator.next"),

                ])
        ],

        # Pricing Group Tab
        "verify_pricinggroup_before_update": [
            ("push", "$storage.prdpr_cost", None),
            ("push", "$storage.prdpr_markup", None),
            ("push", "$storage.prdpr_price", None),
            ("push", "$storage.price_new", None),
            ("push", "$storage.product_price_id", None),
            ("push", "$storage.original_cost", "$g_pricinggroup_tableView.currentrowdata.prdpr_cost"),
            ("push", "$storage.original_price", "$g_pricinggroup_tableView.currentrowdata.prdpr_price"),
            ("push", "$storage.currentrowdata", "$g_pricinggroup_tableView.currentrowdata"),

            ("push", "$storage.update", "$g_pricinggroup_tableView.updated_data"),
            ("push", "$storage.row_data", "$storage.update.data"),
            ("push", "$storage.product_price_id", "$storage.row_data.product_price_id"),
            ("push", "$storage.product_id", "$storage.update.product_id"),

            ("push", "$storage.prdpr_markup", "('0' if $storage.row_data.get('prdpr_markup') == '-' or "
                                              "$storage.row_data.get('prdpr_markup') == '') or "
                                              "$storage.row_data.get('prdpr_markup')#currency(4,0)"),
            ("push", "$storage.prdpr_cost", "$storage.row_data.prdpr_cost#currency(4,0)"),
            ("push", "$storage.prdpr_price", "$storage.row_data.prdpr_price#currency(2,0)"),
            ("push", "$storage.prdpr_uses_markup", "$storage.row_data.prdpr_uses_markup"),

            ("if", "$storage.prdpr_markup == None or $storage.prdpr_markup#float == 0",
                [
                    ("if", "$storage.prdpr_price#float < $storage.prdpr_cost#float or $storage.prdpr_price#float == 0",
                        [
                            {"confirm": {
                                "text": "Price is less then cost or is Zero.  Are you sure you wish to continue?",
                                "on_accept": [
                                    ("push", "$storage.prdpr_markup", 0),
                                    ("push", "$storage.prdpr_uses_markup", False),
                                    "check_before_update",
                                ],

                                "on_reject": [
                                    "reset_text",
                                ]
                            }}
                        ],
                        [
                            ("push", "$storage.prdpr_markup", 0),
                            ("push", "$storage.prdpr_uses_markup", False),
                            "check_before_update",
                        ]),
                ],
                [
                    ("transfer", "get_pricinggroup_markup",
                     [("if", "$storage.price_new#float < $storage.prdpr_cost#float",
                       [{"confirm": {
                           "text": "Price is less then cost.  Are you sure you wish to continue?",
                           "on_accept": [
                               ("push", "$storage.prdpr_uses_markup", True),
                               ("push", "$storage.prdpr_price", "$storage.price_new#currency(2,0)"),
                               "check_before_update"
                           ],
                           "on_reject": [
                               "reset_text",
                           ]
                       }
                       }],
                       [
                           ("push", "$storage.prdpr_uses_markup", True),
                           ("push", "$storage.prdpr_price", "$storage.price_new#currency(2,0)"),
                           "check_before_update"
                       ]
                       )]
                     )
                ],
            )
        ],

        "check_before_update": [
            ("push", "$storage.cost", None),
            "update_group_pricing"
        ],

        "reset_text": [
            ("push", "$storage.new_data", {
                "field": {
                    "prdpr_markup": "$storage.currentrowdata.prdpr_markup#currency(4,0)#float",
                    "prdpr_cost": "$storage.currentrowdata.prdpr_cost#currency(4,0)#float",
                    "prdpr_price": "$storage.currentrowdata.prdpr_price#currency(2,0)#float",
                    "prdpr_uses_markup": "$storage.currentrowdata.prdpr_uses_markup or False"},

                "cell": "('-' if $storage.currentrowdata.prdpr_uses_markup == 0) or "
                        "$storage.currentrowdata.prdpr_uses_markup#currency(4,0)#float",
                "row": "$storage.update.row",
                "type": "Currency"}),
            "$g_pricinggroup_tableView.reset_text($storage.new_data)",
        ],

        "update_group_pricing": [
            ("push", "$storage.new_data", {
                "field": {
                    "prdpr_markup": "$storage.prdpr_markup#currency(4,0)#float",
                    "prdpr_cost": "$storage.prdpr_cost#currency(4,0)#float",
                    "prdpr_price": "$storage.prdpr_price#currency(2,0)#float",
                    "prdpr_uses_markup": "$storage.prdpr_uses_markup or False"},

                    "cell": "('-' if $storage.prdpr_markup == 0) or $storage.prdpr_markup#currency(2,0)#float",
                    "row": "$storage.update.row",
                    "type": "Currency"}),

            ("transfer", "update_pricinggroup")
        ],

        # Calculate Weights "$storage.product_info"
        "calculate_weights": [
            ("if", "$g_prod_weight_lineedit.data#is_int or $g_prod_weight_lineedit.data#is_float",
                [
                    ("push", "$g_prod_weight_lineedit", "#update_weight($g_prod_weight_lineedit.data, $storage.old_weight_unit ,$g_prod_weightmeasure_combobox.selected.value)"),
                    ("push", "$storage.old_weight_unit", "$g_prod_weightmeasure_combobox.selected.value"),
                ])
        ],

        # Calculate Dimensions "$storage.product_info"
        "calculate_dimensions": [
            ("if", "$g_prod_height_lineedit.data#is_int or $g_prod_height_lineedit.data#is_float",
                ("push", "$g_prod_height_lineedit", "#update_dimension($g_prod_height_lineedit.data, $storage.old_dimension_unit ,$g_dimension_measure_combobox.selected.value)")),

            ("if", "$g_prod_width_lineedit.data#is_int or $g_prod_width_lineedit.data#is_float",
                 ("push", "$g_prod_width_lineedit", "#update_dimension($g_prod_width_lineedit.data, $storage.old_dimension_unit ,$g_dimension_measure_combobox.selected.value)")),

            ("if", "$$g_prod_length_lineedit.data#is_int or $g_prod_length_lineedit.data#is_float",
                    ("push", "$g_prod_length_lineedit", "#update_dimension($g_prod_length_lineedit.data, $storage.old_dimension_unit ,$g_dimension_measure_combobox.selected.value)")),

            ("push", "$storage.old_dimension_unit", "$g_dimension_measure_combobox.selected.value"),
        ],
    },

    "fpe_transfers": {
        "get_current_location": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__entities",

            "convert": "$result[0]",

            "pull": {
                "qp_entity_id": "$storage.location_id",
                "columns": "ent_roles,ent_location"
            },

            "on_success": [
                ("if", "$app.integrations.is_enabled(" + str(Integrations.CENTRALWAREHOUSE) + ") == True and "
                                          "-18 in $result.ent_roles#get('erole_role_id')",
                    "$g_historyoption_tabs.show_tab('g_order_tab')")
            ],
        },

        "get_comission_status": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__vw_applicable_settings",

            "convert": "$result[0]",

            "pull": {
                "qp_stn_name": "commission_enabled"
            },

            "on_finish": [
                ("if", "$result#get('stn_value')#tojsonloads#get('enabled') == True",
                 "$g_commoverride_groupbox.show")
            ]
        },

        "read_autopricemarkup": {
            "method": "post",
            "resource": "/apps/cash_register/queries/read__qpt__vw_applicable_settings",

            "pull": {
                "qp_stn_name": "markups_increase_price_only",
                "columnsn": "setting_id,stn_name,stn_value"
            },

            "push": {
                "$storage.markups_increase": "$result[0]#get('stn_value')"
            }
        },

        "get_ecommerce_integration": {
            "method": "get",
            "resource": "/apps/any/integration_settings",

            "pull": {
                "qp_intstn_integration_id": 7  # E-Commerce
            },

            "on_success": [
                ("if", "$result",
                 ("if", "$result[0]#length > 1",
                    [
                        ("if", "$result[0].intstn_is_enabled == True and "
                            "$result[0].intstn_attrs#get('ecommerce') == 'True'",
                            [
                                "$g_manageproducts_tabs.show_tab('g_ecommerce_tab')"
                            ]),
                        #
                        # ("if", "$result[0].intstn_is_enabled == True and "
                        #        "$storage.location_id in $result[0].intstn_attrs#get('locations')#get('location_id') and "
                        #        "$result[0].intstn_attrs#get('locations')#where('location_id', $storage.location_id)#get('enabled')[0] == True",
                        #     [
                        #         "$g_manageproducts_tabs.show_tab('g_online_tab')",
                        #         "$g_prodtags_frame.show"
                        #     ])
                    ]
                )),
            ]

        },

        "get_linked_price": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__vw_applicable_settings",

            "pull": {
                "qp_stn_name": "gui.prod.link_price",
            },

            "on_success": [
                ("if", "$result#str != '[]'",
                    ("if", "$result[0].stn_value#eval#get('linked') == True",
                        "$g_pricelink_groupBox.show",
                        "$g_pricelink_groupBox.hide"))
            ]
        },

        "get_product_location": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__product_location_settings",

            "pull": {
                "qp_prlocs_loc_entity_id": "$storage.location_id",
                "qp_prlocs_product_id": "$storage.product_id"
            },

            "on_start": [
                ("push", "$storage.product_id", "$form.input.ids[$paginator.index]"),
            ],

            "push": {
                "$storage.productentityid": "$result.prlocs_loc_entity_id",
                "$storage.productlocationsettingsid": "$result.product_location_settings_id"
            },
        },

        "get_product_locations": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_bin_locations",

            "pull": {
                "qp_loc_entity_id": "$storage.location_id"
            },

            "on_success": [
                ("push", "$storage.prodlocations", "#dict('aisles', #dict('match', 'aisles', 'show', 'aisles', 'data', $result[0].aisles),"
                                                       "'sections', #dict('match', 'sections', 'show', 'sections', 'data', $result[0].sections),"
                                                        "'shelves', #dict('match', 'shelves', 'show', 'shelves', 'data', $result[0].shelves),"
                                                           "'bins', #dict('match', 'bins', 'show', 'bins', 'data', $result[0].bins))"),
                "$prodlocation_droplist.set_combox_data($storage.prodlocations)",
            ],
        },

        "get_product": {
            "method": "get",
            "resource": "/apps/any/products/{id}",

            "query": {
                "id": "$form.input.ids[$paginator.index]"
            },
            
            "pull": {
                "qp_loc_entity_id": "$storage.location_id"
            },

            "push": {
                "$storage.product_info": "$result",
                "$storage.productid": "$result.product_id",
                "$storage.extended_action_id": "$result.prod_extended_action_id",
                "$storage.product_qtyonhand": "$result.invtry_qty",

                "$g_sku_lineedit": "$result.product_id",
                "$g_description_lineedit": "$result.prod_name or ''",

                "$g_mfg_combobox.selected": "$result.prod_mfgr_entity_id or 'default_option'",
                "$g_brand_combobox.selected": "$result.prod_mfg_brand_id or 'default_option'",

                "$g_department_combobox.selected": "$result.prod_product_category_id",

                "$g_asksaleprice_checkbox": "$result.prod_ask_sale_price",
                "$q_askforquantity_checkBox": "$result.prod_ask_quantity",
                "$g_donotinventory_checkbox": "$result.prod_do_not_track_inventory",
                "$g_requirecustomer_checkbox": "$result.prod_sale_requires_customer",
                "$g_requiredaddress_checkbox": "$result.prod_sale_requires_address",
                "$g_requiredphoneemail_checkbox": "$result.prod_sale_requires_phone_or_email",
                "$g_promptfortip_checkbox": "$result.prod_ask_tip",
                "$g_promptforcommission_checkbox": "$result.prod_ask_commission",
                "$g_markedfordeletion_checkbox": "$result.prod_is_marked_for_deletion",
                "$g_askforcost_checkbox": "$result.prod_ask_for_cost",
                "$g_askfornotes_checkbox": "$result.prod_ask_for_notes",
                "$g_askforproductdescription_checkbox": "$result.prod_ask_for_description",
                "$g_useserials_checkbox": "$result.prod_has_serial_numbers",

                "$g_usescale_checkbox": "$result.prod_use_scale",

                "$g_enableaction_checkbox": "$result.prod_extended_action_id != None",
                "$g_extendedaction_combobox.selected": "$result.prod_extended_action_id",
                "$g_stockingrule_combobox.selected": "$result.prod_location_settings#any#get('prlocs_stocking_rule')",

                "$g_producttype_combobox.selected": "$result.prod_type",
                "$g_receiptdisclaimer_textedit": "$result.prod_disclaimer_text",

                "$g_subscriptionperiod_combobox.selected": "$result.ptsubprd_period",
                "$g_subscriptionqty_lineedit": "$result.ptsubprd_period_length or '1'",
                "$g_renewal_combobox.selected": "$result.ptsubprd_renewal",
                "$g_subscriptionlabel_lineedit": "$result.ptsubprd_label or ''",

                # Product Price
                "$g_price_radioButton" : "True if $result.prdpr_uses_markup == False",
                "$g_markup_radioButton" : "True if $result.prdpr_uses_markup == True",
                "$storage.product_price_id": "$result.product_price_id",
                "$storage.prdpr_product_pricing_group_id": "$result.prdpr_product_pricing_group_id",
                "$g_pricelink_combobox.selected": "$result.prod_price_link_id or 'default_option'",
                "$storage.price_link_id": "$result.prod_price_link_id",

                "$g_cost_lineedit": "$result.prdpr_cost#currency(4,0) or 0",
                "$g_price_lineedit": "$result.prdpr_price#currency or 0",
                "$storage.product_price": "$result.prdpr_price#currency or 0",
                "$g_markup_lineedit": "$result.prdpr_markup#currency(4,0) or 0",
                "$storage.original_price": "$result.prdpr_price#currency or 0",
                "$storage.original_cost": "$result.prdpr_cost#currency or 0",

                # Commission Percentage Override
                "$g_commoverride_lineedit": "($result.prdpr_commission_pct if $result.prdpr_commission_pct != None) or ''",

                # # Purchase information
                "$g_minstocklevel_lineedit": "$result.prod_location_settings#any#get('prlocs_min_safety_stock_days','')",
                "$g_maxstocklevel_lineedit": "$result.prod_location_settings#any#get('prlocs_max_safety_stock_days','')",
                "$g_minqtylevel_lineedit": "($result.prod_location_settings#any#get('prlocs_min_location_qty', '')#float#quantity "
                                           "if $result.prod_location_settings#any#get('prlocs_min_location_qty', '')#float != 0) or ''",
                "$g_maxqtylevel_lineedit": "($result.prod_location_settings#any#get('prlocs_max_location_qty', '')#float#quantity "
                                           "if $result.prod_location_settings#any#get('prlocs_max_location_qty', '')#float != 0) or ''",
                "$g_autoorder_checkbox": "(not $result.prod_location_settings) or $result.prod_location_settings#any#get('prlocs_auto_order', False)",

                # # Seasonal information 
                "$storage.product_location_settings_id": "$result.prod_location_settings#any#get('product_location_settings_id',None)",

                "$g_isseasonal_checkbox": "$result.prod_location_settings#any#get('prlocs_product_is_seasonal',False)",

                "$g_isseasonaljan_checkbox": "$result.prod_location_settings#any#get('prlocs_jan_is_seasonal',False)",
                "$g_isseasonalfeb_checkbox": "$result.prod_location_settings#any#get('prlocs_feb_is_seasonal',False)",
                "$g_isseasonalmar_checkbox": "$result.prod_location_settings#any#get('prlocs_mar_is_seasonal',False)",
                "$g_isseasonalapr_checkbox": "$result.prod_location_settings#any#get('prlocs_apr_is_seasonal',False)",
                "$g_isseasonalmay_checkbox": "$result.prod_location_settings#any#get('prlocs_may_is_seasonal',False)",
                "$g_isseasonaljun_checkbox": "$result.prod_location_settings#any#get('prlocs_jun_is_seasonal',False)",
                "$g_isseasonaljul_checkbox": "$result.prod_location_settings#any#get('prlocs_jul_is_seasonal',False)",
                "$g_isseasonalaug_checkbox": "$result.prod_location_settings#any#get('prlocs_aug_is_seasonal',False)",
                "$g_isseasonalsep_checkbox": "$result.prod_location_settings#any#get('prlocs_sep_is_seasonal',False)",
                "$g_isseasonaloct_checkbox": "$result.prod_location_settings#any#get('prlocs_oct_is_seasonal',False)",
                "$g_isseasonalnov_checkbox": "$result.prod_location_settings#any#get('prlocs_nov_is_seasonal',False)",
                "$g_isseasonaldec_checkbox": "$result.prod_location_settings#any#get('prlocs_dec_is_seasonal',False)",

                # Promotional
                "$g_promotions_checkBox": "$result.prod_do_not_allow_promotions",

                # Discounts
                "$g_nodiscounts_checkBox": "$result.prod_do_not_allow_discounts",

                # E-Commerse
                "$g_showonline_checkbox": "$result.prod_is_available_on_web",
                "$g_longdescription_lineedit": "$result.prod_long_description or ''",
                # "$g_onlinemessage_plaintextedit": "$result.prod_marketing_message or ''",
                "$g_marketing_textEdit": "$result.prod_marketing_message or ''",
                "$g_prodinfo_textEdit": "$result.prod_web_info or ''",
                "$g_ingredients_plaintextedit": "$result.prod_ingredients#str.replace('[', '').replace(']', '').replace(\"'\", '')",
                "$g_prodtags_lineedit": "$result.prod_tags#str.replace('[', '').replace(']', '').replace(\"'\", '')",

                # Up-Sell,Cross-Sell,Alternative
                "$storage.upsell_product_ids": "$result.prod_up_sell_product_ids",
                "$storage.crosssell_product_ids": "$result.prod_cross_sell_product_ids",
                "$storage.alternative_product_ids": "$result.prod_alternative_product_ids",

                # Sell only as modifier
                "$g_sellasmodifieronly_checkbox": "(True if $result.prod_location_settings.prlocs_is_saleable_as_modifier_only[0] == True) or False",

                # Replacment prodict id
                "$g_replacementprod_id_lineedit": "($result.prod_location_settings.prlocs_replaced_by_product_id[0] "
                                                  "if $result.prod_location_settings.prlocs_replaced_by_product_id[0] != 'None') or ''",
                
                # Variant
                "$storage.variant_id": "$result.prod_product_variation_id or None",

                # Price Research
                "$g_msrp_lineedit": "$result.prod_msrp or ''",
                "$g_map_lineedit": "$result.prod_minimum_advertised_price or ''",
                "$g_msp_lineedit": "$result.prod_minimal_selling_price or ''",

                # Weights and Dimensions
                "$g_prod_weightmeasure_combobox.selected": "$result.prod_weight_unit",
                "$storage.old_weight_unit": "$result.prod_weight_unit",
                "$g_dimension_measure_combobox.selected": "$result.prod_dimension_unit",
                "$storage.old_dimension_unit": "$result.prod_dimension_unit",
                "$g_prod_weight_lineedit": "$result.prod_tags#listtostring",

            },

            "on_start": [
                "disable_buttons",
                ("push", "$storage.product_id", "$form.input.ids[$paginator.index]")
            ],

            "on_success": [
                # Product Warnings
                "$c_productwarnings_checkcombobox.set_all_unchecked()",
                ("if", "$result.prod_location_settings.prlocs_disabled_warnings != None",
                    [
                        "$c_productwarnings_checkcombobox.set_checked($result.prod_location_settings.prlocs_disabled_warnings[0], True)"
                    ]),

                ("if", "$result.prdpr_uses_markup == True",
                    [
                        "$g_markup_lineedit.set_enabled(True)",
                        "$g_price_lineedit.set_enabled(False)",
                    ],
                    [
                        "$g_markup_lineedit.set_enabled(False)"
                        "$g_price_lineedit.set_enabled(True)"
                    ]),
                ("if", "$storage.variant_id != None and $app.integrations.is_enabled(" + str(Integrations.VARIANTS) + ") == True",
                    [
                        ("if", "'variant' not in $form.input.flags",
                            "$g_variantgroup_button.show",
                            "$g_variantgroup_button.hide"),
                        "$g_variantgroup_frame.show",
                        ("transfer", "get_variant")
                    ],
                    [
                        "$g_variantgroup_frame.hide",
                    ]),

                "$prodlocation_droplist.clear_data(False)",

                ("if", "$result.prod_location_settings[0]#get('prlocs_bin_location') != None and $app.lite != True",
                    [
                        "$g_binlocation_lineedit.show",
                        "$g_binlocation_label.show",
                        ("if", "$result.prod_location_settings[0]#get('prlocs_bin_location')#str != '[]'",
                            [
                                ("push", "$g_binlocation_lineedit" ,"$result.prod_location_settings[0]#get('prlocs_bin_location')[0]"),
                                ("call", push_bin_location, {"locations": "$result.prod_location_settings#get('prlocs_bin_location')"},
                                    ("if", "$result", ("push", "$prodlocation_droplist", "$result"),
                                    [
                                        ("if", "$result#str != '[]'",
                                            ("info_message", "Error in combining lists"))
                                    ]))
                            ])
                    ],
                    [
                        "$g_binlocation_lineedit.hide",
                        "$g_binlocation_label.hide",
                    ]),

                ("if", "$storage.extended_action_id",
                    ("change_state", "g_extendedaction_combobox", {"default_state": "enabled"}),
                    ("change_state", "g_extendedaction_combobox", {"default_state": "disabled"})),

                ("if", "$result.prdpr_uses_markup",
                    [
                        "$g_price_lineedit.set_enabled(False)",
                        ("if", "$app.permissions.has_permission_other(2, -1)",  # Modify Cost
                            [
                                "$g_recycle_label.show",
                                "$g_question_label.show"
                            ],
                            [
                                "$g_recycle_label.hide",
                                "$g_question_label.hide"
                            ])
                    ]),

                ("if", "$g_autoorder_checkbox",
                    "$g_autoorder_label.hide",
                    "$g_autoorder_label.show"),

                ("if", "$g_cost_lineedit.data#currency#str != '0.00' and $g_price_lineedit.data#currency#str != '0.00'",
                    ("push", "$g_margin_label",
                     "((($g_price_lineedit.data#float#round(2) - $g_cost_lineedit.data#float#round(2)) / $g_price_lineedit.data#float#round(2)) *100)#currency(2,0)#str + '%'")),

                ("if", "$storage.price_link_id != None",
                    ("push", "$g_price_label", "Link Price"),
                    ("push", "$g_price_label", "Price")),

                # E-Commerse
                ("if", "$result.prod_nutritional != '{}'",
                    ("call", push_nutritional, {"value": "$result.prod_nutritional"},
                        ("if", "$result", ("push", "$nutrition_droplist", "$result"),
                            ("info_message", "Error in combining lists")))),

                ("if", "$app.integrations.is_enabled(" + str(Integrations.PRINTING_ZONES) + ") == True",
                    ("call", push_printerzones, {"zones": "$storage.print_zones", "zone_list": "$result.printing_zone_ids"},
                        ("if", "$result",
                            [
                                ("if", "$result[0]#length > 0",
                                    [
                                        ("push", "$printerzone_list", "$result[0]"),
                                        "$printerzone_list.selected($result[1])",
                                        ("if", "$app.lite == True",
                                         [
                                             "$g_manageproducts_tabs.hide_tab('g_advanced_widget')",
                                         ]),
                                    ])
                            ],
                            ("info_message", "Error in combining lists"))),
                 ),

                # Up-Sell, Cross-Sell, Alternative
                ("if", "$storage.upsell_product_ids != None",
                    ("if", "$storage.upsell_product_ids#length > 0",
                        ("transfer", "get_products_upsell"))),

                ("if", "$storage.crosssell_product_ids != None",
                    ("if", "$storage.crosssell_product_ids#length > 0",
                        ("transfer", "get_products_crosssell"))),

                ("if", "$storage.alternative_product_ids != None",
                    ("if", "$storage.alternative_product_ids#length > 0",
                        ("transfer", "get_products_alternative"))),

                # Ship to
                ("if", "$result.prod_location_settings.prlocs_excluded_delivery_types[0] != None",
                    [
                        ("push", "$g_storepickup_checkbox", "(True if 'Pickup' not in $result.prod_location_settings.prlocs_excluded_delivery_types[0]) or False"),
                        ("push", "$g_scheduleddelivery_checkbox", "(True if 'Scheduled Delivery' not in $result.prod_location_settings.prlocs_excluded_delivery_types[0]) or False"),
                        ("push", "$g_shiptohome_checkbox", "(True if 'Ship to Home' not in $result.prod_location_settings.prlocs_excluded_delivery_types[0]) or False")
                    ]),

                ("if", "$g_replacementprod_id_lineedit.data != ''",
                    "$g_replaced_label.show",
                    "$g_replaced_label.hide"),

                # Calculate Weight and Dimensions
                ("push", "$g_prod_weight_lineedit", "(#set_weight($storage.product_info.prod_weight, "
                                                    "$g_prod_weightmeasure_combobox.selected.value)#quantity if $storage.product_info.prod_weight != None) or ''"),

                ("push", "$g_prod_height_lineedit", "(#set_dimension($storage.product_info.prod_dimension_height, "
                                                    "$g_dimension_measure_combobox.selected.value)#quantity if $storage.product_info.prod_dimension_height != None) or ''"),
                ("push", "$g_prod_width_lineedit", "(#set_dimension($storage.product_info.prod_dimension_width, "
                                                    "$g_dimension_measure_combobox.selected.value)#quantity if $storage.product_info.prod_dimension_width != None) or ''"),
                ("push", "$g_prod_length_lineedit", "(#set_dimension($storage.product_info.prod_dimension_length, "
                                                    "$g_dimension_measure_combobox.selected.value)#quantity if $storage.product_info.prod_dimension_length != None) or ''"),

                    ("if", "$g_price_radioButton", "update_markup_from_price"),

            ],

            "on_finish": [
         # Show Warning Information
                ("if", "$form.input.data[$paginator.index]#get('warnings')",
                    [
                        ("if", "'No Cost' in $form.input.data[$paginator.index]#get('warnings')#keys",
                            "$g_notcost_frame.show",
                            "$g_notcost_frame.hide"),
                        ("if", "'Negative Inventory' in $form.input.data[$paginator.index]#get('warnings')#keys",
                            "$g_lowinventory_frame.show",
                            "$g_lowinventory_frame.hide"),
                        ("if", "'Low Margin' in $form.input.data[$paginator.index]#get('warnings')#keys",
                            "$g_lowmargin_frame.show",
                            "$g_lowmargin_frame.hide"),

                        ("if", "$result.has_suppliers",
                            "$g_nosupplier_frame.hide",
                            "$g_nosupplier_frame.show"),
                    ]),


                ("push", "$storage.loading", False),
                # "enable_buttons"
                ("push", "$g_category_combobox.selected", "$storage.product_info.prod_product_sub_category_id")
            ],

            "on_failure": [
                ("error_message", "'\n'.join($errors)"),
                "enable_buttons"
            ]
        },

        "get_promotions_breaks": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_product_promotions",

            "pull": {
                "qp_prom_is_temp_record": False,

                "qp_product_id": "$storage.product_id",
                "qp_prom_target": "Product",
                "qp_prom_type": "Percent Off,Amount Off",
            },

            "on_success": [
                ("if", "$result",
                    ("call", push_breakpoints,
                        {"value": "$result"},
                            ("if", "$result", ("push", "$breakpoint_droplist", "$result"),
                            ("info_message", "Error in combining lists"))),
                 "$breakpoint_droplist.clear_data(False)"),
            ],

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "get_product_sales_history": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__product_sales_info",

            "pull": {
                "qp_product_id": "$storage.product_id",
                "qp_loc_entity_id": "$storage.location_id"
            },

            "on_start": [
                ("push", "$g_qtysold_30_label", "'0'"),
            ],

            "on_success": [
                ("if", "$result#str != '[]'",
                    [
                        ("push", "$g_qtysold_30_label", "($result[0].txnline_qty_sold_30d#str) or '0'"),
                        ("push", "$g_lastsaledatevalue_label", "($result[0].txn_closed_last#datetime.strftime('%b-%d-%Y')) or 'N/A'")
                    ],
                    [
                        ("push", "$g_qtysold_30_label", "'0'"),
                        ("push", "$g_lastsaledatevalue_label", "N/A"),
                    ]
                ),
            ],

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "get_markup": {
            "method": "post",
            "resource": "/apps/any/product_markup_prices",

            "pull": {
                "cost": "$g_cost_lineedit.data#float or 0",
                "markup": "$g_markup_lineedit.data#float or 0"
            },

            "on_success": [
                ("push", "$storage.checkprice", True),
                ("if", "$storage.markups_increase == 'False' or $storage.recycle == True",
                    [
                        ("push", "$g_price_lineedit", "$result.price"),
                        ("push", "$storage.recycle", False),
                    ]),

                    # ("if", "$g_price_lineedit.data#float#round(2) < $result.price#float#round(2)",
                    #     ("push", "$g_price_lineedit", "$result.price"),
                    #     ("push", "$g_price_lineedit", "$storage.original_price"))),
                ("push", "$storage.checkprice", False),

            ],

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "update_product": {
            "method": "put",
            "resource": "/apps/any/products/{id}",
            "content_type": "json",

            "query": {
                "id": "$form.input.ids[$paginator.index]",
            },

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "qp_prod_name": "$g_description_lineedit.data.lstrip().rstrip()",

                "qp_prod_product_id": "$form.input.ids[$paginator.index]",

                "qp_prod_mfgr_entity_id": "(None if $g_mfg_combobox.text == '') or "
                                          "($g_mfg_combobox.selected.entity_id if $g_mfg_combobox.text != 'Select...')",
                "qp_prod_mfg_brand_id": "(None if $g_mfg_combobox.text == '') or "
                                        "($g_brand_combobox.selected.mfg_brand_id if $g_brand_combobox.text != 'Select...')",

                "_qp_ent_name": "$g_mfg_combobox.text.lstrip().rstrip() if not $g_mfg_combobox.is_selected and $g_mfg_combobox.text != 'Select...'",
                "_qp_mfgbr_brand_name": "$g_brand_combobox.text.lstrip().rstrip() if not $g_brand_combobox.is_selected and $g_brand_combobox.text != 'Select...'",

                "qp_prod_ask_sale_price": "$g_asksaleprice_checkbox",
                "qp_prod_ask_quantity": "$q_askforquantity_checkBox",
                "qp_prod_do_not_track_inventory": "$g_donotinventory_checkbox",
                "qp_prod_sale_requires_customer": "$g_requirecustomer_checkbox",
                "qp_prod_sale_requires_address": "$g_requiredaddress_checkbox",
                "qp_prod_sale_requires_phone_or_email": "$g_requiredphoneemail_checkbox",
                "qp_prod_ask_tip": "$g_promptfortip_checkbox",
                "qp_prod_ask_commission": "$g_promptforcommission_checkbox",
                "qp_prod_is_marked_for_deletion": "$g_markedfordeletion_checkbox",

                "qp_prod_ask_for_cost": "$g_askforcost_checkbox",
                "qp_prod_ask_for_notes": "$g_askfornotes_checkbox",
                "qp_prod_ask_for_description": "$g_askforproductdescription_checkbox",
                "qp_prod_has_serial_numbers": "$g_useserials_checkbox",

                "qp_prod_use_scale": "$g_usescale_checkbox",

                "qp_prod_extended_action_id": "($g_extendedaction_combobox.selected.extended_action_id if ($g_extendedaction_combobox.is_selected and $g_enableaction_checkbox)) or None",

                "qp_prod_disclaimer_text": "$g_receiptdisclaimer_textedit",

                # Subscriptions
                "qp_ptsubprd_period": "($g_subscriptionperiod_combobox.selected.value if $g_extendedaction_combobox.selected.extended_action_id == -5) or None",
                "qp_ptsubprd_period_length": "((($g_subscriptionqty_lineedit if $g_subscriptionperiod_combobox.selected.value != 'Lifetime') or None) if $g_extendedaction_combobox.selected.extended_action_id == -5) or None",
                "qp_ptsubprd_renewal": "((($g_renewal_combobox.selected.value if $g_subscriptionperiod_combobox.selected.value != 'Lifetime') or None) if $g_extendedaction_combobox.selected.extended_action_id == -5) or None",
                "qp_ptsubprd_label": "((($g_subscriptionlabel_lineedit if $g_subscriptionperiod_combobox.selected.value != 'Lifetime') or None) if $g_extendedaction_combobox.selected.extended_action_id == -5) or None",

                # Location Settings
                "qp_product_location_settings_id": "$storage.productlocationsettingsid or None",

                # Product Price
                "qp_product_price_id": "$storage.product_price_id",
                # "qp_prdpr_product_pricing_group_id": "$storage.prdpr_product_pricing_group_id",
                "qp_prdpr_product_id": "$form.input.ids[$paginator.index]",
                "qp_prod_price_link_id": "($g_pricelink_combobox.selected.price_link_id if $g_pricelink_combobox.text != 'Select...') or None",

                "qp_prdpr_cost": "$g_cost_lineedit or 0",
                "qp_prdpr_price": "$g_price_lineedit or 0",
                "qp_prdpr_markup": "($g_markup_lineedit if $g_markup_radioButton) or 0",
                "qp_prod_type": "$g_producttype_combobox.selected.value",

                "qp_prdpr_uses_markup": "$g_markup_radioButton",

                # Product Category and Sub-Category
                "qp_prod_category": "($g_department_combobox.text.lstrip().rstrip() if $g_department_combobox.text != 'Select...') or ''",
                "qp_prod_sub_category": "($g_category_combobox.text.lstrip().rstrip() if $g_category_combobox.text != 'Select...') or ''",

                # "_qp_prod_product_category_id ": "($g_department_combobox.product_category_id if ($g_department_combobox.text != 'Select...'))",
                # "_qp_prod_product_sub_category_id": "($g_category_combobox.product_category_id if ($g_category_combobox.text != 'Select...'))",

                "qp_prlocs_stocking_rule": "$g_stockingrule_combobox.selected.value",

                # Commission Percentage Override
                "qp_prdpr_commission_pct": "$g_commoverride_lineedit or None",

                # Seasonal information 
                "qp_prlocs_product_id": "$form.input.ids[$paginator.index]",
                "qp_prlocs_loc_entity_id": "$storage.location_id",

                # Pormotions
                "qp_prod_do_not_allow_promotions": "$g_promotions_checkBox",

                # Discounts
                "qp_prod_do_not_allow_discounts": "$g_nodiscounts_checkBox",

                # Breakpoints
                "discount_breaks": "$breakpoint_droplist.breakpoint",

                # Online
                "qp_prod_is_available_on_web": "$g_showonline_checkbox.data",
                "qp_prod_long_description": "$g_longdescription_lineedit",
                "qp_prod_marketing_message": "$g_marketing_textEdit",
                "qp_prod_web_info": "$g_prodinfo_textEdit.data",
                "qp_prod_ingredients": "($g_ingredients_plaintextedit.data.replace(', ',',').split(',') if $g_ingredients_plaintextedit.data != '') or ''",
                # "qp_prod_ingredients": "$g_ingredients_plaintextedit",
                "qp_prod_tags": "$g_prodtags_lineedit",

                # Up-Sell,Cross-Sell,Alternative
                "qp_prod_up_sell_product_ids": "$g_upsell_table.data#get('product_id')",
                "qp_prod_cross_sell_product_ids": "$g_crosssell_table.data#get('product_id')",
                "qp_prod_alternative_product_ids": "$g_alternative_table.data#get('product_id')",

                # Sale only as Modifier
                # "qp_prlocs_is_saleable_as_modifier_only": "$g_sellasmodifieronly_checkbox.data",

                # Price Research
                "qp_prod_msrp": "$g_msrp_lineedit.data or None",
                "qp_prod_minimum_advertised_price": "$g_map_lineedit.data or None",
                "qp_prod_minimal_selling_price": "$g_msp_lineedit.data or None",

                # Product Warning
                "qp_prlocs_disabled_warnings": ["$c_productwarnings_checkcombobox.checked.name"],

                # Weight and Dimensions
                "qp_prod_weight": "(#weight_to_grams($g_prod_weight_lineedit.data, $g_prod_weightmeasure_combobox.selected.value) "
                                  "if $g_prod_weight_lineedit.data != '') or None",
                "qp_prod_weight_unit": "$g_prod_weightmeasure_combobox.selected.value",

                "qp_prod_dimension_height": "(#dimensions_to_m($g_prod_height_lineedit.data, $g_dimension_measure_combobox.selected.value) "
                                                "if $g_prod_height_lineedit.data != '') or None",
                "qp_prod_dimension_width": "(#dimensions_to_m($g_prod_width_lineedit.data, $g_dimension_measure_combobox.selected.value) "
                                                "if $g_prod_width_lineedit.data != '') or None",
                "qp_prod_dimension_length": "(#dimensions_to_m($g_prod_length_lineedit.data, $g_dimension_measure_combobox.selected.value) "
                                                "if $g_prod_length_lineedit.data != '') or None",
                "qp_prod_dimension_unit": "$g_dimension_measure_combobox.selected.value",
            },

            "on_start": [
                "disable_buttons",
                ("push", "$storage.product_id", "$form.input.ids[$paginator.index]"),
                "pull_product_information"
            ],

            "on_finish": [
                ("push", "$g_replacementproduct_alllocations_checkbox", False),
            ],

            "on_failure": [
                ("if", "$result",
                    ("if", "$result#get('data')#get('diag')#get('table_name') == 'product_location_settings'",
                        [
                            ("info_message", "> Another product already located in Bin Location."),
                        ],
                        ("error_message", "An error occurred when saving the record!")),
                    ("error_message", "An error occurred when saving the record!")),

                "enable_buttons"
            ]
        },

        "delete_product": {
            "method": "post",
            "resource": "/apps/products/queries/delete__tbl__products",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "get_locations": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__list_locations",
            "cache": ("app_cache", "all_locations", {"days": 1}),

            "pull": {
                "order_by": "> 'loc_report_code'",
                "columns": "loc_entity_id,ent_business_name,ent_name,loc_receipt_name,loc_report_code,loc_slug,"
                           "loc_is_enabled,ppg_name,loc_show_regional"
            },

            "push": {
                "$storage.locations": "$result",
                "$c_historylocations_checkcombobox": "$result",
                "$c_promolocations_checkcombobox": "$result",
                "$g_location_combobox": "$result"
            },

            "on_success": [
                ("if", "$result#find_where('loc_entity_id', $storage.location_id).loc_show_regional or False",
                    [("push", "$storage.is_regionalpricing", True),
                     ("push", "$g_basicdetails_groupbox.title", "Regional Pricing")],
                    [("push", "$storage.is_regionalpricing", False),
                     ("push", "$g_basicdetails_groupbox.title", "Pricing")]),

                "$c_historylocations_checkcombobox.set_checked($storage.locations#where('loc_entity_id', $storage.location_id).loc_report_code, True)",
                "$c_promolocations_checkcombobox.set_checked($storage.locations#where('loc_entity_id', $storage.location_id).loc_report_code, True)",
                ("push", "$g_location_combobox.selected", "$storage.location_id")
            ],

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "get_suppliers": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__list_suppliers",

            "pull": {
                "columnsn": "entity_id,ent_name,cnum_number,eml_email"
            },

            "push": {
                "$storage.suppliers": "$result"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "get_manufacturers": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__list_manufacturers",

            "pull": {
                "order_by": "> 'ent_name'",
                "columnsn": "entity_id,ent_name"
            },

            "push": {
                "$g_mfg_combobox": "$result"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "get_product_types": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__mfg_brands",

            "pull": {
                "order_by": "> 'mfgbr_brand_name'",
                "columnsn": "mfg_brand_id,mfgbr_entity_id,mfgbr_brand_name"
            },

            "push": {
                "$g_productupdates_type_combobox": "$result"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "get_brands": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__mfg_brands",

            "pull": {
                "order_by": "> 'mfgbr_brand_name'",
                "columnsn": "mfg_brand_id,mfgbr_entity_id,mfgbr_brand_name"
            },

            "push": {
                "$g_brand_combobox": "$result"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "get_extendedactions": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__extended_actions",

            "pull": {
                # "qp_extact_app_id": 3, # Pet Tracker
                "columnsn": "extended_action_id,extact_name"
            },

            "push": {
                "$storage.extendedactions": "$result",
                "$g_extendedaction_combobox": "$result"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },


        # Variant
        "get_variant": {
            "method": "get",
            "resource": "/api/product_variations/{id}",

            "query": {
                "id": "$storage.variant_id",
            },

            "push": {
                "$g_variantgroup_lineedit": "$result.name"
            },

            "on_success": [
                ("if", "$result.category != None",
                    [
                        ("change_state", "g_department_combobox", {"default_state": "disabled"}),
                        ("change_state", "g_category_combobox", {"default_state": "disabled"}),
                    ],
                    [
                        ("change_state", "g_department_combobox", {"default_state": "enabled"}),
                        ("change_state", "g_category_combobox", {"default_state": "enabled"}),
                    ]),
                ("if", "$result.manufacturer != None",
                    [
                        ("change_state", "g_mfg_combobox", {"default_state": "disabled"}),
                        ("change_state", "g_brand_combobox", {"default_state": "disabled"}),
                    ],
                    [
                        ("change_state", "g_mfg_combobox", {"default_state": "enabled"}),
                        ("change_state", "g_brand_combobox", {"default_state": "enabled"}),
                    ]),
                ("if", "$result.ingredients != None",
                    ("change_state", "g_ingredients_plaintextedit", {"default_state": "disabled"}),
                    ("change_state", "g_ingredients_plaintextedit", {"default_state": "enabled"})),
                ("if", "$result.nutritional != None",
                    ("change_state", "g_nutritional_listWidget", {"default_state": "disabled"}),
                    ("change_state", "g_nutritional_listWidget", {"default_state": "enabled"})),
                ("if", "$result.marketing_message != None",
                    ("change_state", "g_marketing_textEdit", {"default_state": "disabled"}),
                    ("change_state", "g_marketing_textEdit", {"default_state": "enabled"})),
                ("if", "$result.is_available_on_web != None",
                    ("change_state", "g_showonline_checkbox", {"default_state": "disabled"}),
                    ("change_state", "g_showonline_checkbox", {"default_state": "enabled"})),
                ("if", "$result.web_info != None",
                    ("change_state", "g_prodinfo_textEdit", {"default_state": "disabled"}),
                    ("change_state", "g_prodinfo_textEdit", {"default_state": "enabled"}))
            ]
        },

        "update_variant_form": {
            "dialog": {
                "name": "ManageVariantDialog",

                "data": {
                    "flags": ["update", "products"],
                    "ids":  ["$storage.variant_id"],
                    # "selected": "$g_variant_tableView.all_selected",
                    "form_status": "loading"
                },
            },

            "on_finish": ("transfer", "get_product")
        },

        # Commissions
        "read_commenable_settings": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__vw_applicable_settings",

            "pull": {
                "qp_stn_name": "commission_enabled",
            },

            "on_finish": [
                ("if", "$result#get('stn_value')[0]#tojsonloads#get('type') != 'default'",
                    [
                        ("if", "$result#get('stn_value')[0]#tojsonloads#get('enabled') == False",
                         "$g_commoverride_groupbox.hide",
                         [
                             ("if",
                              "$result#get('stn_value')[0]#tojsonloads#get('locations')#find_where('loc_id', $storage.location_id).enabled == False",
                              "$g_commoverride_groupbox.hide"),
                             ("if",
                              "$result#get('stn_value')[0]#tojsonloads#get('locations')#find_where('loc_id', $storage.location_id).enabled == True",
                              "$g_commoverride_groupbox.show")

                         ])
                    ],
                    [
                     ("if", "$result#get('stn_value')[0]#tojsonloads#get('enabled') == True",
                      "$g_commoverride_groupbox.show")

                ])
            ]
        },

        # Tips
        "read_tipssettings": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__vw_applicable_settings",

            "pull": {
                "qp_stn_name": "tip_enabled",
            },

            "on_finish": [
                # Featured
                ("if", "$ext.releases.config['skuoptions'] == 'true' ",
                    [
                        ("if", "$result.stn_value[0] == 'True'",
                            "$g_promptfortip_checkbox.show",
                            "$g_promptfortip_checkbox.hide")
                    ],
                    "$g_promptfortip_checkbox.hide"),
            ]
        },

        # Linked Price
        "get_price_link": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__price_links",

            "push": {
                "$g_pricelink_combobox": "$result"
            },

            "on_success": [
                ("if", "$storage.price_link_id != None",
                    ("push", "$g_pricelink_combobox.selected", "$storage.price_link_id"))
            ],

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "manage_price_link": {
            "dialog": {
                "name": "ManagePriceLinkDialog",
            },

            "on_finish": ("transfer", "get_price_link"),

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        # Taxes
        "get_storetaxes": {
            "method": "post",
            "resource": "/apps/products/queries/read__vw__vw_applicable_taxes",

            "pull": {
                "qp_loc_entity_id": "$storage.location_id",
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "qp_taxrt_valid_during_contains": "#date",
                "qp_taxrt_valid_values_contains": "1.0",

                "order_by": "> [('{$g_taxes_table.sort_field}', '{$g_taxes_table.sort_order}')]",
                "columnsn": "tax_chart_id,taxch_description,taxrt_percent,taxrl_kind"
            },

            "push": {
                "$g_taxes_table": "$result#where('taxrl_kind', 'Product at Location')"
            },

            "on_finish": [
                ("if", "'Product at Location' in $result.taxrl_kind ",
                    ("change_state", "g_resettaxes_button", {"default_state": "enabled"}),
                    ("change_state", "g_resettaxes_button", {"default_state": "disabled"}))
            ],

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "customize_taxes": {
            "dialog": {
                "name": "ManageTaxes",

                "data": {
                    "flags": ["update", "producttax"],
                    "location_id": "$storage.location_id",
                    "product_id": "$form.input.ids[$paginator.index]",
                    "tax_name": "$g_taxes_table.all_selected.taxch_description",
                    "tax_chart_id": "$g_taxes_table.all_selected.tax_chart_id"
                }
            },

            "on_finish": ("transfer", "get_storetaxes")
        },

        "reset_taxes": {
            "method": "post",
            "resource": "/apps/products/queries/update__gen__tax_rules",

            "pull": {
                "qp_uc_taxrl_product_id": "$form.input.ids[$paginator.index]",
                "qp_uc_taxrl_loc_entity_id": "$storage.location_id",
                "qp_taxrl_is_mfd": True
            },

            "on_finish": ("transfer", "get_storetaxes"),

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        # Bar Codes
        "get_products_barcodes": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__product_barcodes",

            "pull": {
                "qp_prodbar_product_id": "$form.input.ids[$paginator.index]",
                "order_by": "> 'prodbar_order'",
                "columnsn": "product_barcode_id,prodbar_barcode,prodbar_qty,prodbar_order"
            },

            "push": {
                "$g_barcodes_table": "$result"
            }
        },

        "delete_product_barcode": {
            "method": "post",
            "resource": "/apps/products/queries/delete__tbl__product_barcodes",

            "pull": {
                "qp_product_barcode_id": "$g_barcodes_table.all_selected.product_barcode_id",
            },

            "on_finish": ("transfers", ["get_products_barcodes", "get_pidb_products"])
        },

        "update_product_barcode": {
            "dialog": {
                "name": "ManageProductBarcodes",

                "data": {
                    "flags": ["update", "productbarcode"],
                    "ids": "$g_barcodes_table.all_selected.product_barcode_id"
                }
            },

            "on_finish": ("transfers", ["get_products_barcodes", "get_pidb_products"])
        },

        "create_product_barcode": {
            "dialog": {
                "name": "ManageProductBarcodes",

                "data": {
                    "flags": ["create", "productbarcode"],
                    "ids": "$form.input.ids[$paginator.index]"
                }
            },

            "on_finish": ("transfers", ["get_products_barcodes", "get_pidb_products"])
        },

        "make_primary_barcode": {
            "method": "post",
            "resource": "/apps/products/queries/update__gen__product_barcodes",

            "pull": {
                "qp_product_barcode_id": "$g_barcodes_table.data.product_barcode_id",
                "qp_prodbar_order": "$g_barcodes_table.row_order"
            },

            "on_finish": ("transfers", ["get_products_barcodes", "get_pidb_products"])
        },

        # Category and Sub-Category
        "get_departments": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_product_categories",

            "pull": {
                "order_by": "> 'prodcat_name'",
                "qp_prodcat_parent_product_category_id": None,
                "columnsn": "prodcat_name,product_category_id,prodcat_parent_product_category_id"

            },

            "push": {
                "$g_department_combobox": "$result"
            },

            "on_error": "show_errors"
        },

        "get_sub_category": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_product_categories",

            "pull": {
                "qp_prodcat_parent_product_category_id": "$g_department_combobox.selected.product_category_id",
                "order_by": "> 'prodcat_name'",
                "columnsn": "prodcat_name,product_category_id,prodcat_parent_product_category_id"

            },

            "push": {
                "$g_category_combobox": "$result"
            },

            "on_success": [
                ("if", "$result#length > 0",
                    ("push", "$g_category_combobox.selected", "$storage.product_info.prod_product_sub_category_id"))
            ],

            "on_error": "show_errors"
        },

        # Attributes
        "get_product_attributes": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__product_attributes",

            "pull": {
                "qp_prodattr_product_id": "$storage.product_id",

                "order_by": "> [('{$g_attributes_table.sort_field}', '{$g_attributes_table.sort_order}')]",
                
                "columnsn": "product_attribute_id,prodattr_name,prodattr_value, prodattr_product_id"
            },

            "push": {
                "$g_attributes_table": "$result#reject('prodattr_name', 'Category', 'Sub-Category')"
            }
        },

        "delete_product_attribute": {
            "method": "post",
            "resource": "/apps/products/queries/delete__tbl__product_attributes",

            "pull": {
                "qp_product_attribute_id": "$g_attributes_table.all_selected.product_attribute_id"
            },

            "on_start": ("$g_attributes_table.remove_row", "$g_attributes_table.all_selected"),
            "on_finish": ("transfer", "get_product_attributes")
        },

        "update_product_attribute": {
            "dialog": {
                "name": "ManageProductAttributes",

                "data": {
                    "flags": ["update", "productattribute"],
                    "ids": "$g_attributes_table.all_selected.product_attribute_id"
                }
            },

            "on_finish": ("transfer", "get_product_attributes")
        },

        "create_product_attribute": {
            "dialog": {
                "name": "ManageProductAttributes",

                "data": {
                    "flags": ["create", "productattribute"],
                    "ids": "$form.input.ids[$paginator.index]"
                }
            },

            "on_finish": ("transfer", "get_product_attributes")
        },

        # Inventory
        "get_entity_locations": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__entities",

            "convert": "$result[0]",

            "pull": {
                "qp_entity_id": "$storage.location_id",
                "columns": "ent_location"
            },

            "push": {
                "$storage.store_location": "#list($result.ent_location.loc_latitude, $result.ent_location.loc_longitude)"
            },

            "on_finish": ("transfer", "get_inventory")
        },

        "get_inventory": {
            "method": "post",
            "resource": "/apps/products/queries/read__pvt__inventory",

            "pull": {
                "qp_invtry_product_id": "$form.input.ids[$paginator.index]",
                "_qp_lat_long": "$storage.store_location if $storage.store_location#all_same != True"
            },

            "on_finish": [
                ("if", "$result and $result[0].loc_show_regional == True",
                    [("change_state", "g_inventory_table", {

                        "selection_mode": "multi_selection",
                        "sections_movable": False,

                        "bind": {
                            "columns": [
                                ("loc_report_code", {"default": "Location", "halign": "left"}),
                                ("invtry_numbers", {"default": "Available", "halign": "right"}),
                                ("invtry_numbers", {"default": "On Hold", "halign": "right"}),
                                ("invtry_numbers", {"default": "On Layaway", "halign": "right"}),
                                ("invtry_numbers", {"default": "In Receiving", "halign": "right"}),
                                ("invtry_numbers", {"default": "On PO", "halign": "right"}),
                                ("prdpr_price", {"default": "Price", "halign": "right"})
                            ],

                            "show_fields": [
                                ("$row.loc_report_code or ''", {"on_link_click": "show_inventory_popup", "halign": "left"}),
                                ("(' ' if $row.invtry_numbers['Available']#is_string) or ($row.invtry_numbers['Available'] or 0)#quantity", {"halign": "right"}),
                                ("(' ' if $row.invtry_numbers['Available']#is_string) or ($row.invtry_numbers['Committed to Sale'] or 0)#quantity", {"halign": "right"}),
                                ("($row.invtry_numbers['Available'] if $row.invtry_numbers['Available']#is_string) or ($row.invtry_numbers['On Layaway'] or 0)#quantity", {"halign": "right"}),
                                ("(' ' if $row.invtry_numbers['Available']#is_string) or ($row.invtry_numbers['In Receiving'] or 0)#quantity", {"halign": "right"}),
                                ("(' ' if $row.invtry_numbers['Available']#is_string) or ($row.invtry_numbers['On Order'] or 0)#quantity",{"halign": "right"}),
                                ("($row.prdpr_price or 0)#currency", {"halign": "right"})
                            ]
                        }
                    }),
                     ("if", "$app.permissions.has_role(-17,-7)",
                        "$g_inventory_table.set_can_open_click(False)"),
                     ("push", "$g_inventory_table", "$result")],

                    [("change_state", "g_inventory_table", {
                        "selection_mode": "multi_selection",
                        "sections_movable": False,

                        "bind": {
                             "columns": [
                                ("loc_report_code", {"default": "Location", "halign": "left"}),
                                ("invtry_numbers", {"default": "Available", "halign": "right"}),
                                ("invtry_numbers", {"default": "On Hold", "halign": "right"}),
                                ("invtry_numbers", {"default": "On Layaway", "halign": "right"}),
                                ("invtry_numbers", {"default": "In Receiving", "halign": "right"}),
                                ("invtry_numbers", {"default": "On PO", "halign": "right"})
                            ],

                            "show_fields": [
                                ("$row.loc_report_code or ''", {"on_link_click": "show_inventory_popup", "halign": "left"}),
                                ("(' ' if $row.invtry_numbers['Available']#is_string) or ($row.invtry_numbers['Available'] or 0)#quantity", {"halign": "right"}),
                                ("(' ' if $row.invtry_numbers['Available']#is_string) or ($row.invtry_numbers['Committed to Sale'] or 0)#quantity", {"halign": "right"}),
                                ("($row.invtry_numbers['Available'] if $row.invtry_numbers['Available']#is_string) or ($row.invtry_numbers['On Layaway'] or 0)#quantity", {"halign": "right"}),
                                ("(' ' if $row.invtry_numbers['Available']#is_string) or ($row.invtry_numbers['In Receiving'] or 0)#quantity", {"halign": "right"}),
                                ("(' ' if $row.invtry_numbers['Available']#is_string) or ($row.invtry_numbers['On Order'] or 0)#quantity", {"halign": "right"})
                            ]
                        }
                    }),
                     ("if", "$app.permissions.has_role(-17,-7)",
                      "$g_inventory_table.set_can_open_click(False)"),
                     ("push", "$g_inventory_table", "$result")]
                ),
                "c.set_selection('None')",
            ]
        },

        "get_inventory_totals": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__inventory",

            "pull": {
                "qp_invtry_product_id": "$form.input.ids[$paginator.index]",
                "columns": "invtry_qty"
            },

            "on_finish": [
                ("if", "$result",
                    ("push", "$storage.invtry_qty", "$result.invtry_qty#remove_from_list(0.0)"),
                    ("push", "$storage.invtry_qty", []))
            ]
        },

        "show_inventory_popup": {
            "dialog": {
                "name": "InventoryListPopup",

                "data": {
                    "data": ["show"],
                    "loc_id": "$g_inventory_table.selected"
                }
            }
        },

        "adjust_inventory": {
            "dialog": {
                "name": "AdjustInventory",
                "data": {
                    "flags": ["create", "adjustinventory"],
                    "ids": "$form.input.ids[$paginator.index]",
                    "location_id": "$storage.location_id",
                    "product_description": "$g_description_lineedit",
                    "product_qtyonhand": "$storage.product_qtyonhand",
                }
            },
            "on_accept": [
                ("push", "$storage.product_qtyonhand", "$dialog.output.new_quantity")
            ],
            "on_finish": ("transfers", ["get_inventory", "get_inventory_totals"])
        },

        "transfer_inventory": {
            "dialog": {
                "name": "TransferInventory",
                "data": {
                    "flags": ["create", "transferinventory"],
                    "ids": "$form.input.ids[$paginator.index]"
                }
            },
            "on_finish": ("transfers", ["get_inventory", "get_inventory_totals"])
        },

        "check_inventory": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__inventory",

            "pull": {
                "qp_invtry_product_id": "$form.input.ids[$paginator.index]",
                "qp_invtry_inv_qty_type": "Available,Committed to Sale,On Layaway,On Order,In Receiving,In Return to Supplier,Send Transfer,Destination Receiving,Committed to Order",
                "qp_invtry_qty_non_zero": True
            },

            "push": {
                "$storage.check": "$result"
            },

            "on_success": [
                ("if", "$result#length > 0",
                    [
                        ("push", "$storage.checking_inventory", True),
                        ("push", "$g_donotinventory_checkbox", False),
                        ("info_message","You must have inventory set to 0 at all locations before 'Do Not Track in Inventory' is enabled."),
                        ("transfer", "show_check_inventory_popup"),
                    ]),


            ],

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "show_check_inventory_popup": {
            "dialog": {
                "name": "InventoryListPopup",

                "data": {
                    "data": ["check"],
                    "loc_id": "$storage.check"
                }
            },

            "on_finish":  ("push", "$storage.checking_inventory", False),
        },

        # Purchasing
        "get_product_suppliers": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_product_suppliers",

            "pull": {
                "_qp_loc_entity_id": "($storage.location_id if $storage.corp != True) or None",
                "qp_product_id": "$form.input.ids[$paginator.index]",

                "order_by": "> [('{$g_supplierinfo_table.sort_field}', '{$g_supplierinfo_table.sort_order}')]",
            },

            "on_success": [
                ("if", "$storage.corp == True",
                 [
                     ("push", "$storage.temp", ["corp"]),
                    "$g_supplierinfo_table.table_views($storage.temp)",
                     "$g_supplierinfo_table.field_bind"
                 ],
                 [
                     ("push", "$storage.temp", []),
                     "$g_supplierinfo_table.table_views($storage.temp)",
                     "$g_supplierinfo_table.field_bind"
                 ]),
                ("push", "$g_supplierinfo_table", "$result")
            ]
        },

        "delete_product_suppliers": {
            "method": "post",
            "resource": "/apps/products/queries/delete__tbl__product_location_suppliers",

            "pull": {
                "qp_product_location_supplier_id": "$g_supplierinfo_table.all_selected.product_location_supplier_id"
            },

            "on_start": ("$g_supplierinfo_table.remove_row", "$g_supplierinfo_table.all_selected"),
            "on_finish": ("transfer", "get_product_suppliers"),

            "on_failure": [
                ("if", "$result",
                 ("if", "$result#get('data')#get('diag')#get('constraint_name') == 'check_supplier_order_qty'",
                  ("info_message", "Cannot delete supplier. There are PO's for this product."),
                  ("error_message", "An error occurred when deleting supplier.")),
                 ("error_message", "An error occurred when deleting supplier.")),
            ],
        },

        "update_product_supplier": {
            "dialog": {
                "name": "ManageProductSupplier",

                "data": {
                    "flags": ["update", "productsupplier"],
                    "suppliers": "$g_supplierinfo_table.all_selected",
                    "ids": "$g_supplierinfo_table.all_selected.splr_entity_id",
                    "loc_sup_ids": "$g_supplierinfo_table.all_selected.product_location_supplier_id",
                    "loc_entity_id": "$g_supplierinfo_table.all_selected.loc_entity_id",
                    "product_location_supplier_id": "$g_supplierinfo_table.all_selected.product_location_supplier_id",
                    "splr_entity_id": "$g_supplierinfo_table.all_selected.splr_entity_id",
                    "product_id": "$form.input.ids[$paginator.index]",
                    "corp": "$storage.corp",
                    "location_id": "$storage.location_id"
                }
            },

            "on_finish": ("transfer", "get_product_suppliers")
        },

        "massaction_product_supplier": {
            "dialog": {
                "name": "ManageProductSupplier",

                "data": {
                    "flags": ["massaction", "update", "productsupplier"],
                    "ids": "$g_supplierinfo_table.all_selected.splr_entity_id",
                    "suppliers": "$g_supplierinfo_table.all_selected",
                    "loc_sup_ids": "$g_supplierinfo_table.all_selected.product_location_supplier_id",
                    "loc_entity_id": "$g_supplierinfo_table.all_selected.loc_entity_id",
                    "product_location_supplier_id": "$g_supplierinfo_table.all_selected.product_location_supplier_id",
                    "product_id": "$form.input.ids[$paginator.index]",
                    "corp": "$storage.corp",
                    "location_id": "$storage.location_id"
                }
            },

            "on_finish": ("transfer", "get_product_suppliers")
        },

        "create_product_supplier": {
            "dialog": {
                "name": "ManageProductSupplier",

                "data": {
                    "flags": ["create", "productsupplier"],
                    "ids": "$form.input.ids[$paginator.index]",
                    "corp": "$storage.corp",
                    "location_id": "$storage.location_id"
                }
            },

            "on_finish": ("transfer", "get_product_suppliers")
        },

        # Purchasing - Options
        "get_product_options": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_product_location_settings",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",

                "order_by": "> [('{$g_options_table.sort_field}', '{$g_options_table.sort_order}')]",

                "columns": "loc_receipt_name,loc_report_code,prlocs_auto_order,prlocs_max_location_qty,"
                           "prlocs_max_safety_stock_days,prlocs_min_location_qty,prlocs_min_safety_stock_days,"
                           "product_location_settings_id"
            },

            "push": {
                "$g_options_table": "$result"
            }
        },

        "update_product_options": {
            "dialog": {
                "name": "ManageProductOptions",

                "data": {
                    "flags": ["update"],
                    "product_id": "$form.input.ids[$paginator.index]",
                    "settings": "$g_options_table.all_selected"
                }
            },

            "on_finish": ("transfer", "get_product_options")
        },

        "massaction_product_options": {
            "dialog": {
                "name": "ManageProductOptions",

                "data": {
                    "flags": ["massaction_product"],
                    "product_id": "$form.input.ids[$paginator.index]",
                    "settings": "$g_options_table.all_selected"
                }
            },

            "on_finish": ("transfer", "get_product_options")
        },

        # Purchasing - Seasonal
        "get_product_seasonal": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_product_location_settings",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",

                "order_by": "> [('{$g_options_table.sort_field}', '{$g_options_table.sort_order}')]",

                "columns": "loc_receipt_name,loc_report_code,prlocs_apr_is_seasonal,prlocs_aug_is_seasonal,"
                           "prlocs_dec_is_seasonal,prlocs_feb_is_seasonal,prlocs_jan_is_seasonal,"
                           "prlocs_jul_is_seasonal,prlocs_jun_is_seasonal,prlocs_mar_is_seasonal,"
                           "prlocs_may_is_seasonal,prlocs_nov_is_seasonal,prlocs_oct_is_seasonal,"
                           "prlocs_sep_is_seasonal,prlocs_product_is_seasonal,product_location_settings_id,"
                           "loc_entity_id"
            },

            "push": {
                "$g_seasonal_table": "$result"
            }
        },

        "update_product_seasonal": {
            "dialog": {
                "name": "ManageProductSeasonal",

                "data": {
                    "flags": ["update"],
                    "product_id": "$form.input.ids[$paginator.index]",
                    "settings": "$g_seasonal_table.all_selected"
                }
            },

            "on_finish": ("transfer", "get_product_seasonal")
        },

        "massaction_product_seasonal": {
            "dialog": {
                "name": "ManageProductSeasonal",

                "data": {
                    "flags": ["massaction_product"],
                    "product_id": "$form.input.ids[$paginator.index]",
                    "settings": "$g_seasonal_table.all_selected"
                }
            },

            "on_finish": ("transfer", "get_product_seasonal")
        },

        # Purchasing - Open Orders 
        "get_openorders": {
            "type": "row_by_row",

            "table": "g_openorders_table",
            "match": "transaction_id",

            "method": "post",
            "resource": "/apps/purchasing_and_receiving/queries/read__qpt__prod_transaction_detail_info",

            "pull": {
                "qp_loc_entity_id": "$storage.location_id",
                "qp_product_id": "$storage.product_id", 
                "qp_txn_status": "Opened,Receiving,Submitted",
                "qp_txn_type":  "Purchase",
                 
                "order_by": "> [('{$g_openorders_table.sort_field}', '{$g_openorders_table.sort_order}')]",

                "limit": "$openorders_paginator.limit",
                "offset": "$openorders_paginator.offset"
            },

            "on_start": [
                ("push", "$g_totalresults_label_04", "Loading results"),
            ],

            "push": {
                "$openorders_paginator.total": "$total"
            },

            "row_pull": {
                "qp_loc_entity_id": "$storage.location_id",
                "qp_transaction_id": "$iter"
            },

            "on_finish": [
                ("if", "$total > 0",
                    ("push", "$g_totalresults_label_04", "> {$total} results found."),
                    ("push", "$g_totalresults_label_04", "No results found.")),
                ("push", "$openorders_paginator.total", "$total"),
                ("if", "$openorders_paginator.total < 26", "$g_openorderspagination_frame.hide",
                 "$g_openorderspagination_frame.show")
            ]
        },

        # History - Movements 
        "get_product_movements_by_event": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__product_movement",

            "pull": {  
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "qp_date_range": "$history_datetime_range.extended_range",
                "qp_loc_entity_id": "$c_historylocations_checkcombobox.checked.loc_entity_id",
                "order_by": "> [('{$g_movements_table.sort_field}', '{$g_movements_table.sort_order}')]"
            },

            "push": {
                "$g_movements_table": "$result"
            },

            "on_start": [
                ("if", "$storage.corp == False or $c_historylocations_checkcombobox.checked.loc_entity_id#length == 1",
                    ("change_state", "g_movements_table", {
                        "remote_sort": ["inv_changed_on", "txn_type", "txn_type_number",
                                        "user_display_name", "inv_delta", "inv_accum"],

                        "default_sort_field": "inv_changed_on",
                        "default_sort_order": "desc",

                        "bind": {
                            "columns": [
                                ("inv_changed_on", {"default": "Date"}),
                                ("txn_type", {"default": "Type"}),
                                ("txn_type_number", {"default": "Number"}),
                                ("user_display_name", {"default": "Employee"}),
                                ("inv_delta", {"default": "Movement Qty", "halign": "right"}),
                                ("inv_accum", {"default": "Available", "halign": "right"}),
                            ],

                            "show_fields": [
                                "$row.inv_changed_on#datetime.strftime('%Y-%m-%d %H:%M')",
                                "$row.txn_type or ''",
                                "$row.txn_type_number or ''",
                                "$row.user_display_name or ''",
                                ("$row.inv_delta#quantity or ''", {"halign": "right"}),
                                ("$row.inv_accum#quantity or ''", {"halign": "right"}),
                            ]
                        },
                    }),
                 ("change_state", "g_movements_table", {
                     "remote_sort": ["inv_changed_on", "txn_type", "txn_type_number",
                                     "user_display_name", "inv_delta", "loc_report_code"],

                     "default_sort_field": "inv_changed_on",
                     "default_sort_order": "desc",

                     "bind": {
                         "columns": [
                             ("inv_changed_on", {"default": "Date"}),
                             ("txn_type", {"default": "Type"}),
                             ("txn_type_number", {"default": "Number"}),
                             ("user_display_name", {"default": "Employee"}),
                             ("inv_delta", {"default": "Movement Qty"}),
                             # ("inv_accum", {"default": "Balance Qty", "halign": "right"}),
                             ("loc_report_code", {"default": "Location"}),
                         ],

                         "show_fields": [
                             "$row.inv_changed_on#datetime.strftime('%Y-%m-%d %H:%M')",
                             "$row.txn_type or ''",
                             "$row.txn_type_number or ''",
                             "$row.user_display_name or ''",
                             ("$row.inv_delta#quantity or ''", {"halign": "right"}),
                             # ("$row.inv_accum#quantity or ''", {"halign": "right"}),
                             "$row.loc_report_code or ''"
                         ]
                     },
                 }))
            ]
        },

        "get_product_movements_summary": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__product_movement_summary",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "qp_loc_entity_id": "$c_historylocations_checkcombobox.checked.loc_entity_id",
                "qp_date_range": "$history_datetime_range.extended_range",
                "qp_date_group": "($g_movements_sumbyperiod_combbox.selected.value == 'week')#switch('week','month')",

                "order_by": "> [('{$g_movements_table.sort_field}', '{$g_movements_table.sort_order}')]"
            },

            "push": {
                "$g_movements_table": "$result"
            },

            "on_start": [
                ("if", "$storage.corp == False or $c_historylocations_checkcombobox.checked.loc_entity_id#length == 1",
                    ("change_state", "g_movements_table", {
                        "remote_sort": ["period", "txn_type", "inv_delta", "inv_accum"],

                        "default_sort_field": "period_start",
                        "default_sort_order": "desc",

                        "bind": {
                             "columns": [
                                ("period", {"default": "Period"}),
                                ("txnline_txn_type", {"default": "Type"}),
                                ("inv_delta", {"default": "Movement Qty", "halign": "right"}),
                                ("inv_accum", {"default": "Balance Qty", "halign": "right"}),
                                # ("loc_report_code", {"default": "Location"})
                            ],

                            "show_fields": [
                                "$row.period",
                                "$row.txnline_txn_type or ''",
                                ("$row.inv_delta#quantity or ''", {"halign": "right"}),
                                ("$row.inv_accum#quantity or ''", {"halign": "right"}),
                                # "$row.loc_report_code"
                            ]
                        },
                    }),
                     ("change_state", "g_movements_table", {
                         "remote_sort": ["period", "txn_type", "inv_delta", "loc_report_code"],

                         "default_sort_field": "period_start",
                         "default_sort_order": "desc",

                         "bind": {
                             "columns": [
                                 ("period", {"default": "Period"}),
                                 ("txnline_txn_type", {"default": "Type"}),
                                 ("inv_delta", {"default": "Movement Qty"}),
                                 # ("inv_accum", {"default": "Balance Qty", "halign": "right"}),
                                 ("loc_report_code", {"default": "Location"})
                             ],

                             "show_fields": [
                                 "$row.period",
                                 "$row.txnline_txn_type or ''",
                                 ("$row.inv_delta#quantity or ''", {"halign": "right"}),
                                 # ("$row.inv_accum#quantity or ''", {"halign": "right"}),
                                 "$row.loc_report_code or ''"
                             ]
                         },
                     }))
            ],
        },

        # History - Sales
        "get_product_sales_by_event": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__product_movement",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "qp_date_range": "$history_datetime_range.extended_range",
                "_qp_txn_type": "$g_sales_type_combbox.selected.value",
                # "qp_loc_entity_id": "$storage.location_id",
                "qp_loc_entity_id": "$c_historylocations_checkcombobox.checked.loc_entity_id",
                "order_by": "> [('{$g_sales_table.sort_field}', '{$g_sales_table.sort_order}')]"
            },

            "push": {
                "$g_sales_table": "$result"
            },

            "on_start": [
                ("change_state", "g_sales_table", {
                    "remote_sort": ["inv_changed_on", "txn_type", "txn_type_number",
                                    "user_display_name", "inv_delta", "txnline_sale_cost", "txnline_amount",
                                    "txnline_discount", "loc_report_code"],

                    "default_sort_field": "inv_changed_on",
                    "default_sort_order": "desc",

                    "bind": {
                        "columns": [
                            ("inv_changed_on", {"default": "Date"}),
                            ("txn_type", {"default": "Type"}),
                            ("txn_type_number", {"default": "Number"}),
                            ("user_display_name", {"default": "Employee"}),
                            ("inv_delta", {"default": "Movement Qty", "halign": "right"}),
                            ("txnline_sale_cost", {"default": "Cost", "halign": "right"}),
                            ("txnline_amount", {"default": "Price", "halign": "right"}),
                            ("txnline_discount", {"default": "Discount", "halign": "right"}),
                            ("loc_report_code", {"default": "Location"})
                        ],

                        "show_fields": [
                            "$row.inv_changed_on#datetime.strftime('%Y-%m-%d %H:%M')",
                            "$row.txn_type or ''",
                            ("$row.txn_type_number", {"on_link_click": ("transfer", "show_invoice")}),
                            "$row.user_display_name or ''",
                            ("$row.inv_delta#quantity", {"halign": "right"}),
                            ("$row.txnline_sale_cost#currency(2,0) or '0.00'", {"halign": "right", "on_link_click": "update_sales_history_cost"}),
                            ("$row.txnline_amount#currency or 0.00", {"halign": "right"}),
                            ("$row.txnline_discount#currency(2,0) or 0.00", {"halign": "right"}),
                            "$row.loc_report_code"
                        ]
                    },
                }),
                ("if", "$storage.corp == False",
                    "$g_sales_table.hidden_columns('loc_report_code')")
            ],
        },

        "get_product_sales_summary": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__product_movement_summary",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "qp_date_range": "$history_datetime_range.extended_range",
                "qp_date_group": "($g_sales_sumbyperiod_combbox.selected.value == 'week')#switch('week','month')",
                # "qp_loc_entity_id": "$storage.location_id",
                "qp_loc_entity_id": "$c_historylocations_checkcombobox.checked.loc_entity_id",

                "qp_product_movement_summary_grouping": "(16 if $storage.corp == False) or 0",

                "_qp_txn_type": "$g_sales_type_combbox.selected.value",

                "order_by": "> [('{$g_sales_table.sort_field}', '{$g_sales_table.sort_order}')]"
            },

            "push": {
                "$g_sales_table": "$result"
            },

            "on_start": [
                ("if", "$storage.corp == False or $c_historylocations_checkcombobox.checked.loc_entity_id#length == 1",
                    ("change_state", "g_sales_table", {
                        "remote_sort": ["period", "txnline_txn_type", "txnline_amount", "txnline_discount",
                                        "inv_delta", "inv_accum"],

                        "default_sort_field": "period_start",
                        "default_sort_order": "desc",

                        "bind": {
                            "columns": [
                                ("period", {"default": "Period"}),
                                ("txnline_txn_type", {"default": "Type"}),
                                ("txnline_amount", {"default": "Amount", "halign": "right"}),
                                ("txnline_discount", {"default": "Discount", "halign": "right"}),
                                ("inv_delta", {"default": "Movement Qty", "halign": "right"}),
                                ("inv_accum", {"default": "Balance Qty", "halign": "right"})
                            ],

                            "show_fields": [
                                "$row.period",
                                "$row.txnline_txn_type",

                                ("$row.txnline_amount#currency or 0.00", {"halign": "right"}),
                                ("$row.txnline_discount#currency(2,0) or 0.00", {"halign": "right"}),

                                ("$row.inv_delta#quantity or ''", {"halign": "right"}),
                                ("$row.inv_accum#quantity or ''", {"halign": "right"})
                            ]
                        },
                    }),
                    ("change_state", "g_sales_table", {
                        "remote_sort": ["period", "txnline_txn_type", "txnline_amount", "txnline_discount",
                                        "inv_delta", "loc_report_code"],

                        "default_sort_field": "period_start",
                        "default_sort_order": "desc",

                        "bind": {
                            "columns": [
                                ("period", {"default": "Period"}),
                                ("txnline_txn_type", {"default": "Type"}),
                                ("txnline_amount", {"default": "Amount", "halign": "right"}),
                                ("txnline_discount", {"default": "Discount", "halign": "right"}),
                                ("inv_delta", {"default": "Movement Qty", "halign": "right"}),
                                ("loc_report_code or ''", {"default": "Location"})
                            ],

                            "show_fields": [
                                "$row.period",
                                "$row.txnline_txn_type",
                                ("$row.txnline_amount#currency or 0.00", {"halign": "right"}),
                                ("$row.txnline_discount#currency(2,0) or 0.00", {"halign": "right"}),

                                ("$row.inv_delta#quantity or ''", {"halign": "right"}),
                                "$row.loc_report_code"
                            ]
                        },
                    }),
                )
            ],
        },

        "show_invoice": {
            "dialog": {
                "name": "InvoiceHistory",

                "data": {
                    "flags": ["invoice"],
                    "ids": "$g_sales_table.selected.transaction_id#list",
                    "invoice_type": "$g_sales_table.selected.txn_type#list",
                    "hidebuttons": True
                }
            }
        },

        "updated_sales_history_cost": {
            "method": "post",
            "resource": "/apps/products/queries/update__tbl__transaction_lines",

            "pull": {
                "qp_transaction_line_id": "$g_sales_table.selected.transaction_line_id",
                "qp_txnline_sale_cost": "$storage.updated_sales_history_cost"
            },

            "on_success": ("transfer", "get_product_sales_by_event"),

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        "mass_update_sales_history_cost": {
            "method": "post",
            "resource": "/apps/products/queries/update__gen__transaction_lines",

            "pull": {
                "qp_transaction_line_id": "$g_sales_table.all_selected.transaction_line_id",
                "qp_txnline_sale_cost": "$storage.updated_sales_history_cost"
            },

            "on_success": ("transfer", "get_product_sales_by_event"),

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        # History - Product Updates
        "get_product_updates": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_product_suppliers",

            "pull": {
                # "qp_loc_entity_id": "$storage.location_id",
                "qp_loc_entity_id": "$c_historylocations_checkcombobox.checked.loc_entity_id",
                "qp_product_id": "$form.input.ids[$paginator.index]",

                "order_by": "> [('{$g_productupdates_table.sort_field}', '{$g_productupdates_table.sort_order}')]",

                # "columnsn": "prlsplr_min_order_qty,prlsplr_supplier_sku,prlsplr_cost,prlsplr_last_updated_on,loc_report_code"
            },

            "push": {
                "$g_productupdates_table": "$result"
            },

            "on_start": [
                ("if", "$storage.corp == False or $c_historylocations_checkcombobox.checked.loc_entity_id#length == 1",
                    "$g_productupdates_table.hidden_columns('loc_report_code')")
            ],
        },

        # History - Purchase
        "get_purchases_by_date": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__product_purchases",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "qp_date_range": "$history_datetime_range.extended_range",
                "qp_loc_entity_id": "$c_historylocations_checkcombobox.checked.loc_entity_id",
                "order_by": "> [('{$g_purchases_table.sort_field}', '{$g_purchases_table.sort_order}')]"
            },

            "push": {
                "$g_purchases_table": "$result",
            },

            "on_start": [
                ("if", "$storage.corp == False",
                    "$g_purchases_table.hidden_columns('loc_report_code')")
            ],
        },

        "view_receiving_order_form": {
            "dialog": {
                "name": "ManagePurchaseOrder",

                "data": {
                    "flags": ["update", "view_only", "receiving_order", "products"],
                    "ids": "$g_purchases_table.all_selected.transaction_id",
                    "loc_id": "$storage.location_id",
                    "ent_name": None,
                    "corp": False
                }
            }
        },

        "update_purchase_history_cost": {
            "method": "post",
            "resource": "/apps/products/queries/update__tbl__transaction_lines",

            "pull": {
                "qp_transaction_line_id": "$g_purchases_table.selected.transaction_line_id",
                "qp_txnline_amount": "$storage.update_purchase_history_cost"
            },

            "on_success": ("transfer", "get_purchases_by_date"),

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        "mass_update_purchase_history_cost": {
            "method": "post",
            "resource": "/apps/products/queries/update__gen__transaction_lines",

            "pull": {
                "qp_transaction_line_id": "$g_purchases_table.all_selected.transaction_line_id",
                "qp_txnline_amount": "$storage.update_purchase_history_cost"
            },

            "on_success": ("transfer", "get_purchases_by_date"),

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        # History - Orders
        "get_product_history_orders": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__product_orders",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "qp_date_range": "$history_datetime_range.extended_range",
                "qp_loc_entity_id": "$c_historylocations_checkcombobox.checked.loc_entity_id",
                "order_by": "> [('{$g_orders_table.sort_field}', '{$g_orders_table.sort_order}')]"
            },

            "push": {
                "$g_orders_table": "$result"
            },
        },

        "update_products_history_orders_cost": {
            "method": "post",
            "resource": "/apps/products/queries/update__tbl__transaction_lines",

            "pull": {
                "qp_transaction_line_id": "$g_orders_table.selected.transaction_line_id",
                "qp_txnline_amount": "$storage.update_products_history_orders_cost"
            },

            "on_success": ("transfer", "get_product_history_orders"),

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        "mass_products_history_orders_cost": {
            "method": "post",
            "resource": "/apps/products/queries/update__gen__transaction_lines",

            "pull": {
                "qp_transaction_line_id": "$g_orders_table.all_selected.transaction_line_id",
                "qp_txnline_amount": "$storage.update_products_history_orders_cost"
            },

            "on_success": ("transfer", "get_product_history_orders"),

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        # History - Transfers
        "get_products_history_transfers": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__product_transfers",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "qp_date_range": "$history_datetime_range.extended_range",
                "qp_loc_entity_id": "$c_historylocations_checkcombobox.checked.loc_entity_id",
                "order_by": "> [('{$g_transfers_table.sort_field}', '{$g_transfers_table.sort_order}')]"
            },

            "push": {
                "$g_transfers_table": "$result"
            },
        },

        "update_product_history_transfers_cost": {
            "method": "post",
            "resource": "/apps/products/queries/update__tbl__transaction_lines",

            "pull": {
                "qp_transaction_line_id": "$g_transfers_table.selected.transaction_line_id",
                "qp_txnline_amount": "$storage.updated_sales_history_transfers_cost"
            },

            "on_success": ("transfer", "get_products_history_transfers"),

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        "mass_update_product_history_transfers_cost": {
            "method": "post",
            "resource": "/apps/products/queries/update__gen__transaction_lines",

            "pull": {
                "qp_transaction_line_id": "$g_transfers_table.all_selected.transaction_line_id",
                "qp_txnline_amount": "$storage.updated_sales_history_transfers_cost"
            },

            "on_success": ("transfer", "get_products_history_transfers"),

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        # History - Inventory Adjustments
        "get_products_history_inventory_adjustments": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__product_adjustments",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "qp_date_range": "$history_datetime_range.extended_range",
                "qp_loc_entity_id": "$c_historylocations_checkcombobox.checked.loc_entity_id",
                "order_by": "> [('{$g_inventoryadjustments_table.sort_field}', '{$g_inventoryadjustments_table.sort_order}')]"
            },

            "push": {
                "$g_inventoryadjustments_table": "$result"
            },
        },

        "update_invadj_history_cost": {
            "method": "post",
            "resource": "/apps/products/queries/update__tbl__transaction_lines",

            "pull": {
                "qp_transaction_line_id": "$g_inventoryadjustments_table.selected.transaction_line_id",
                "qp_txnline_amount": "$storage.update_invadj_history_cost"
            },

            "on_success": ("transfer", "get_products_history_inventory_adjustments"),

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        "mass_invadj_history_cost": {
            "method": "post",
            "resource": "/apps/products/queries/update__gen__transaction_lines",

            "pull": {
                "qp_transaction_line_id": "$g_inventoryadjustments_table.all_selected.transaction_line_id",
                "qp_txnline_amount": "$storage.update_invadj_history_cost"
            },

            "on_success": ("transfer", "get_products_history_inventory_adjustments"),

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        # Open Invoices
        "get_openInvoices": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__transaction_info",

            "pull": {
                # "qp_txn_expiration_date_is_null": False,
                "qp_txn_status": "Opened",
                "qp_txn_type": "Sale,Return from Customer,Layaway",
                "qp_txn_loc_entity_id": "$storage.location_id",
                "qp_opened_date_range": "$history_datetime_range.extended_range",
                "qp_product_id": "$form.input.ids[$paginator.index]",

                "order_by": "> [('{$g_openinvoices_table.sort_field}', '{$g_openinvoices_table.sort_order}')]",

                "columnsn": "txn_type,txn_type_number,txn_opened_on,combined_person_name,txn_subtotal," +
                            "txn_grand_total"
            },

            "push": {
                "$g_openinvoices_table": "$result",
            }
        },

        # Lables
        "get_unit_label": {
            "resource": "/apps/any/labels/{id}",
            "method": "get",

            "query": {
                "id": "$ext.reports.config['label_unit'] or ''"
            },

            "on_success": [
                ("push", "$storage.unit_label", "$result")
            ]
        },

        "get_shelf_label": {
            "resource": "/apps/any/labels/{id}",
            "method": "get",

            "query": {
                "id": "$ext.reports.config['label_shelf'] or ''"
            },

            "on_success": [
                ("push", "$storage.shelf_label", "$result")
            ]
        },

        "get_unit_template_data": {
            "resource": "/apps/any/labels/{id}/template_data",
            "method": "get",

            "query": {
                "id": "$storage.unit_label['label_id'] or ''"
            },

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]"
            }
        },

        "get_unit_template_file": {
            "resource": "/apps/pet_tracker/queries/read__tbl__vw_files_with_in_band_contents",
            "method": "post",

            "pull": {
                "qp_file_id": "$storage.unit_label['lbl_file_id']"
            }
        },

        "get_shelf_template_data": {
            "resource": "/apps/any/labels/{id}/template_data",
            "method": "get",

            "query": {
                "id": "$storage.shelf_label['label_id'] or ''"
            },

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]"
            }
        },

        "get_shelf_template_file": {
            "resource": "/apps/pet_tracker/queries/read__tbl__vw_files_with_in_band_contents",
            "method": "post",

            "pull": {
                "qp_file_id": "$storage.shelf_label['lbl_file_id']"
            }
        },

        "change_autoorder_status": {
            "method": "post",
            "resource": "/apps/products/queries/update__gen__product_location_suppliers",

            "pull": {
                "qp_product_location_supplier_id": "$g_supplierinfo_table.all_selected.product_location_supplier_id",
                "qp_prlsplr_splr_entity_id": "$g_supplierinfo_table.all_selected.splr_entity_id",
                "qp_prlsplr_auto_order": "$storage.order_update_value"
            },

            "on_finish": ("transfer", "get_product_suppliers")
        },

        # Bundles 
        "get_product_simplebundle": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_product_suppliers",

            "pull": {
                "qp_loc_entity_id": "$storage.location_id",
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "order_by": "> [('{$g_simplebundle_table.sort_field}', '{$g_simplebundle_table.sort_order}')]",
                "columnsn": "splr_ent_name,prlsplr_min_order_qty,prlsplr_supplier_sku,prlsplr_cost"
            },

            "push": {
                "$g_simplebundle_table": "$result"
            }
        },

        "get_product_advancebundle": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__product_location_suppliers",

            "pull": {
                "qp_loc_entity_id": "$storage.location_id",
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "order_by": "> [('{$g_advancedbundle_table.sort_field}', '{$g_advancedbundle_table.sort_order}')]",
                "columnsn": "splr_ent_name,prlsplr_min_order_qty,prlsplr_supplier_sku,prlsplr_cost"
            },

            "push": {
                "$g_advancedbundle_table": "$result"
            }
        },

        "manage_simplebungle_inventory": {
            "dialog": {
                "name": "ManageBundle",
                "data": {
                    "flags": ["update", "simple"],
                    "ids": "$form.input.ids[$paginator.index]"
                }
            },
            "on_finish": ("transfer", "get_product_simplebundle")
        },

        "manage_advancedbungle_inventory": {
            "dialog": {
                "name": "ManageBundle",
                "data": {
                    "flags": ["update", "advanced"],
                    "ids": "$form.input.ids[$paginator.index]"
                }
            },
            "on_finish": ("transfer", "get_product_advancebundle")
        },

        # Media
        "get_iamge_ratio": {
            "method": "post",
            "resource": "/apps/any/queries/read__tbl__settings",

            "convert": "$result[0]",

            "pull": {
                "qp_stn_name": "image_ratio",
            },

            "on_success": [
                ("push", "$storage.ratio", "$result.stn_value"),
                ("if", "$result.stn_value  == '4x3'", "$cropper.set_ratio(1.333)"),  # ratio 1.333
                # ("if", "$result.stn_value  == '7x11'", "$cropper.set_ratio(0.618)"),
                ("if", "$result.stn_value  == '16x9'", "$cropper.set_ratio(1.777)"),  # ratio 1.777
                ("push", "$g_imageratio_combobox.selected", "$result.stn_value")

            ]
        },

        "get_images": {
            "method": "get",
            "resource": "/api/products/{product_id}/media",

            "query": {
                "product_id": "$form.input.ids[$paginator.index]",
            },

            "push": {
                "$media_viewer": "$result",
                "$storage.media": "$result",
                "$storage.ids": "$result#reject('type', 'video').id"
            },

            "on_success": [
                ("transfer", "get_data_for_images")
            ],
            "on_error": "show_errors"
        },

        "get_data_for_images": {
            "type": "iterator",

            "iterator": "$storage.ids",
            "transfer": "get_data_for_image",

            "on_iter_success": [
                ("if", "$result", "$media_viewer.update($result)")
            ]
        },

        "get_data_for_image": {
            "method": "get",
            "resource": "/api/products/{product_id}/images/{image_id}",

            "cache": ("app_cache", "> product_image_{$iter}", {"days": 1}),

            "query": {
                "product_id": "$form.input.ids[$paginator.index]",
                "image_id": "$iter",
            },

            "pull": {
                "include_contents": True
            }
        },

        "create_image": {
            "method": "post",
            "resource": "/api/products/{product_id}/images",

            "query": {
                "product_id": "$form.input.ids[$paginator.index]",
            },

            "pull": {
                "description": "$g_image_description_lineedit or None",
                "filedata": "$cropper.data",
                "filename": "$storage.filename"
            },

            "on_success": [
                "$g_cropper_area.hide",
                "$g_crop_and_save_btn.hide",
                "$g_cropper_cancel_btn.hide",
                "$g_addedit_groupBox.show",
                ("push", "$storage.product_media_id", "#list($result.product_image_id)"),
                ("push", "$g_uploadfilename_lineedit", ""),
                ("transfer", "get_images")
            ],

            "on_failure": ("error_message", "An error occurred when uploading the file"),

            "on_finish": [
                ("push", "$storage.imagesaved", True),
                "$g_cropper_area.hide",
                "$g_crop_and_save_btn.hide",
                "$g_cropper_cancel_btn.hide",

                "$g_addedit_groupBox.show",

                ("push", "$g_image_description_lineedit", ""),
                ("push", "$g_title_lineedit", ""),
                ("push", "$g_tags_lineedit", "")
            ]
        },

        "remove_image": {
            "method": "delete",
            "resource": "/api/products/{product_id}/images/{image_id}",

            "query": {
                "product_id": "$form.input.ids[$paginator.index]",
                "image_id": "$item.id",
            },

            "on_success": "$media_viewer.remove($result)"
        },

        "remove_video": {
            "method": "delete",
            "resource": "/api/products/{product_id}/videos/{image_id}",

            "query": {
                "product_id": "$form.input.ids[$paginator.index]",
                "image_id": "$item.id",
            },

            "on_success": "$media_viewer.remove($result)"
        },

        "change_image_title": {
            "method": "put",
            "resource": "/api/products/{product_id}/images/{image_id}",

            "query": {
                "product_id": "$form.input.ids[$paginator.index]",
                "image_id": "$storage.media_id"
            },

            "pull": {
                "description": "$g_image_description_lineedit or None",
                "tags": "",
            },

            "on_success": [
                ("push", "$storage.cache", "> product_image_{$storage.media_id}"),
                "$app.cache.delete($storage.cache)",
                ("transfer", "get_images")
            ],

            "on_finish": [
                "$g_cropper_area.hide",
                "$g_crop_and_save_btn.hide",
                "$g_cropper_cancel_btn.hide",

                "$g_addedit_groupBox.show",

                ("push", "$g_image_description_lineedit", ""),
                ("push", "$g_title_lineedit", ""),
                ("push", "$g_tags_lineedit", "")
            ]
        },

        "change_video_title": {
            "method": "put",
            "resource": "/api/products/{product_id}/videos/{video_id}",

            "query": {
                "product_id": "$form.input.ids[$paginator.index]",
                "video_id": "$storage.media_id"
            },

            "pull": {
                "description": "$g_image_description_lineedit or None",
                "tags": "$g_tags_lineedit or None",
                "title": "$g_title_lineedit or None"
            },

            "on_success": [
                ("transfer", "get_images")
            ],

            "on_finish": [
                "$g_cropper_area.hide",
                "$g_crop_and_save_btn.hide",
                "$g_cropper_cancel_btn.hide",

                "$g_addedit_groupBox.show",

                ("push", "$g_image_description_lineedit", ""),
                ("push", "$g_title_lineedit", ""),
                ("push", "$g_tags_lineedit", "")
            ]
        },

        "update_media_order": {
            "method": "put",
            "resource": "/api/products/{product_id}/media",

            "query": {
                "product_id": "$form.input.ids[$paginator.index]",
            },

            "pull": {
                "sort_order": "$media_viewer.sort_order"
            },
        },

        "create_video": {
            "method": "post",
            "resource": "/api/products/{product_id}/videos",

            "query": {
                "product_id": "$form.input.ids[$paginator.index]",
            },

            "pull": {
                "filename": "$file_uploader.file",
                "description": "$file_uploader.file",
                "title": "",
                "filedata": "$file_uploader.data",
                "tags": ""
            },

            "on_finish": [
                ("push", "$storage.imagesaved", True),
                ("push", "$g_image_description_lineedit", ""),
                ("push", "$g_title_lineedit", ""),
                ("push", "$g_tags_lineedit", ""),
                ("transfer", "get_images")
            ],

            "on_error": "show_errors"
        },

        # Promotions
        "get_promotions": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_product_promotions",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "qp_loc_entity_id": "$c_promolocations_checkcombobox.checked.loc_entity_id",
                "order_by": "> [('{$g_promo_table.sort_field}', '{$g_promo_table.sort_order}')]",
                "qp_prom_target": "Item,Sale",
            },

            "push": {
                "$g_promo_table": "$result"
            },

            "on_start": [
                ("if", "$storage.corp == False",
                    "$g_promo_table.hidden_columns('loc_report_code')")
            ]
        },

        "update_promotion_form": {
            "dialog": {
                "name": "ManagePromotion",

                "data": {
                    "flags": ["update", "promotion"],
                    "ids": ["$g_promo_table.selected.promotion_id"],
                    "limitby": ["$g_promo_table.selected.prom_product_target"],
                    "form_status": "loading"
                }
            },

            "on_finish": ("transfer", "get_promotions")
        },

        "delete_promotion": {
            "method": "post",
            "resource": "/apps/settings_and_configuration/queries/update__qpt__promotion_product_qualifiers",

            "pull": {
                "qp_promotion_id": "$g_promotions_table.selected.promotion_id",
                "prompq_in_product_ids": "$g_promotions_table.selected.promotion_id",
                "_qp_prompq_in_product_ids_action": "'del' if $g_promotions_table.prom_product_target == 'Products'",
                "_qp_prompq_out_product_ids_action": "'del' if"
            },

            "on_finish": ("transfer", "get_promotions")
        },

        # E-Commerce
        "get_nutrition": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__product_nutritional_keys",

            "on_success": [
                ("push", "$storage.nutrion_keys",
                 "#dict('nut_key', #dict('match', 'nut_key', 'show', 'nut_key', 'data', $result.prod_nutritional_key))"),
                "$nutrition_droplist.set_combox_data($storage.nutrion_keys)",
            ]
        },

        # Printer Zones
        "get_printer_zones": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__printing_zones",

            "pull": {
                "qp_prtz_loc_entity_id": "$storage.location_id"
            },

            "push": {
                "$storage.print_zones": "$result"
            },

            "on_success": [
            ]
        },

        "get_product_printer_zones": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__printing_zones",

            "pull": {
                "qp_prtz_loc_entity_id": "$storage.location_id"
            },

            "push": {
                "$storage.print_zones": "$result",
            }
        },

        # Up-Sell
        "get_products_upsell": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_products",

            "pull": {
                "qp_loc_entity_id": "$storage.location_id",
                "order_by": "> [('{$g_products_tableView.sort_field}', '{$g_products_tableView.sort_order}')]",
                "optimize": "true",
                "qp_product_id": "$storage.upsell_product_ids",

                "columnsn": "product_id,prod_name"
            },

            "push": {
                "$g_upsell_table": "$result"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "add_product_to_upsell": {
            "dialog": {
                "name": "AddProductToAny",

                "data": {
                    "flags": ["return_many"]
                }
            },

            "on_accept": [
                ("if", "$dialog.output.products",
                 "$g_upsell_table.append_rows($dialog.output.products)",
                 "$g_upsell_table.populate_data")
            ]
        },

        # Cross-Sell
        "get_products_crosssell": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_products",

            "pull": {
                "qp_loc_entity_id": "$storage.location_id",
                "order_by": "> [('{$g_products_tableView.sort_field}', '{$g_products_tableView.sort_order}')]",
                "optimize": "true",
                "qp_product_id": "$storage.crosssell_product_ids",

                "columnsn": "product_id,prod_name"
            },

            "push": {
                "$g_crosssell_table": "$result"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "add_product_to_crosssell": {
            "dialog": {
                "name": "AddProductToAny",

                "data": {
                    "flags": ["return_many"]
                }
            },

            "on_accept": [
                ("if", "$dialog.output.products",
                 "$g_crosssell_table.append_rows($dialog.output.products)",
                 "$g_crosssell_table.populate_data")
            ]
        },

        # # Alternative Products
        # "get_products_alternative": {
        #     "method": "post",
        #     "resource": "/apps/products/queries/read__qpt__list_products",
        #
        #     "pull": {
        #         "qp_loc_entity_id": "$storage.location_id",
        #         "order_by": "> [('{$g_products_tableView.sort_field}', '{$g_products_tableView.sort_order}')]",
        #         "optimize": "true",
        #         "qp_product_id": "$storage.alternative_product_ids",
        #
        #         "columnsn": "product_id,prod_name"
        #     },
        #
        #     "push": {
        #         "$g_alternative_table": "$result"
        #     },
        #
        #     "on_failure": ("error_message", "'\n'.join($errors)")
        # },

        # Alternative Products
        "get_products_alternative": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_products",

            "pull": {
                "qp_loc_entity_id": "$storage.location_id",
                "order_by": "> [('{$g_products_tableView.sort_field}', '{$g_products_tableView.sort_order}')]",
                "optimize": "true",
                "qp_product_id": "$storage.alternative_product_ids",

                "columnsn": "product_id,prod_name"
            },

            "push": {
                "$g_alternative_table": "$result"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "add_product_to_alternative": {
            "dialog": {
                "name": "AddProductToAny",

                "data": {
                    "flags": ["return_many"]
                }
            },

            "on_accept": [
                ("if", "$dialog.output.products",
                 "$g_alternative_table.append_rows($dialog.output.products)",
                 "$g_alternative_table.populate_data")
            ]
        },

        "select_replacement_product": {
            "dialog": {
                "name": "AddProductToAny",

                "data": {
                    "flags": ["return"]
                }
            },

            "on_accept": [
                ("if", "$dialog.output.products",
                    [
                        ("push", "$g_replacementprod_id_lineedit", "$dialog.output.products#get('product_id')[0]"),
                        "$g_replaced_label.show"
                    ],
                    [
                        ("if", "$g_replacementprod_id_lineedit.data != ''",
                            "$g_replaced_label.show",
                            "$g_replaced_label.hide")
                    ])
            ]
        },

        # Customer History
        "get_customerhistory_user_table_settings": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__vw_applicable_settings",

            "pull": {
                "qp_stn_name": "table.products.customerhistory",
            },

            "on_finish": [
                ("if", "$result",
                 [
                     ("push", "$storage.customerhistory_user_table_settings", "$result[0]"),
                     ("push", "$storage.customerhistory_user_settings", "$result[0].stn_value"),
                     ("push", "$storage.customerhistory_user_setting_id", "$result[0].setting_id"),
                     ("push", "$storage.customerhistory_effects", "$result[0].stn_value#eval#get('effects')")
                 ],
                 [
                     ("push", "$storage.customerhistory_user_settings", None),
                     ("push", "$storage.customerhistory_user_table_settings", None),
                     "update_customerhistory_settings"
                 ])
            ]
        },

        "get_customerhistory_column_names": {
            "method": "get",
            "resource": "/apps/products/queries/read__qpt__list_sold_items",
            "cache": ("app_cache", "Man_prod_customerhistory_Col_Names", {"days": 1}),
            "on_finish": [
                ("push", "$storage.customerhistory_quippet_translation", "$result")
            ]

        },

        "get_customerhistory": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_sold_items",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",
                # "qp_loc_entity_id": "$storage.location_id",
                "qp_loc_entity_id": "$c_historylocations_checkcombobox.checked.loc_entity_id",

                "order_by": "> [('{$g_customerhistory_tableView.sort_field}', '{$g_customerhistory_tableView.sort_order}')]",

                "limit": "$paginator_customerhistory.limit",
                "offset": "$paginator_customerhistory.offset",

                "qp_date_range": "$history_datetime_range.extended_range",

                "_$unisearch.key": "$unisearch.value",

                "columnsn": "$g_customerhistory_tableView.report_columns",
            },

            "push": {
                "$g_customerhistory_tableView": "$result",
                "$paginator_customerhistory": "$total",
                "$storage.totalresults_customerhistory": "$total",
            },

            "on_start": [
                ("if", "$storage.corp == False",
                    "$g_customerhistory_tableView.hidden_columns('loc_report_code')"),
            ],

            "on_finish": "get_page_results_customerhistory"
        },

        # Settings for Customer History Table.
        "update_customerhistory_settings": {
            "dialog": {
                "name": "ManageTableSettings",

                "data": {
                    "flags": ["table.products.customerhistory"],
                    "columns_names": "$g_customerhistory_tableView.get_headers",
                    "column_order": "$g_customerhistory_tableView.order_of_columns",
                    "sort_field": "$g_customerhistory_tableView.sort_field",
                    "sort_order": "$g_customerhistory_tableView.sort_order",
                    "column_required": None,
                    "column_required_headers": None
                }
            },

            "on_accept": [
                "load_customerhistory_settings"
            ]
        },

        "update_customerhistory_table_settings": {
            "method": "post",
            "resource": "/apps/any/queries/update__tbl__settings",

            "pull": {
                "qp_setting_id": "$storage.customerhistory_user_setting_id",
                "qp_stn_name": "table.products.customerhistory",
                "qp_stn_value": "#dict('effects', $storage.customerhistory_user_settings#eval#get('effects'),"
                                "'perpage', $paginator_customerhistory.limit,"
                                "'column_order', $g_customerhistory_tableView.order_of_columns,"
                                "'sort_field', $g_customerhistory_tableView.sort_field,"
                                "'sort_order', $g_customerhistory_tableView.sort_order)"
            },
            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        "create_customerhistory_table_settings": {
            "method": "post",
            "resource": "/apps/any/queries/create__tbl__settings",

            "pull": {
                "qp_stn_user_entity_id": "$storage.user_id",
                "qp_stn_name": "table.products.customerhistory",
                "qp_stn_allow_override": True,
                "qp_stn_value": "#dict('effects', 'onlyme',"
                                "'perpage', $paginator_customerhistory.limit,"
                                "'column_order', $g_customerhistory_tableView.order_of_columns,"
                                "'sort_field', $g_customerhistory_tableView.sort_field,"
                                "'sort_order', $g_customerhistory_tableView.sort_order)_"
            },
            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        "select_customerhistory_export": {
            "dialog": {
                "name": "SelectExportPopup",

                "data": {
                    "id": "$g_customerhistory_tableView.all_selected#length or None"
                }
            },

            "on_accept": [
                ("push", "$storage.export_selection", "$dialog.output.selection"),
                ("transfer", "get_customerhistory_export")
            ]
        },

        "get_customerhistory_export": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_sold_items",
            "output_format": "excel",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "qp_loc_entity_id": "$c_historylocations_checkcombobox.checked.loc_entity_id",

                "order_by": "> [('{$g_customerhistory_tableView.sort_field}', '{$g_customerhistory_tableView.sort_order}')]",
                "qp_date_range": "$history_datetime_range.extended_range",
                "use_map": "True",
                "format": "excel",

                "_qp_txn_type_number": "($g_customerhistory_tableView.all_selected.txn_type_number if $storage.export_selection == 'selected')",

                "_limit": "$paginator_customerhistory.limit if $storage.export_selection == 'screen'",
                "_offset": "$paginator_customerhistory.offset if $storage.export_selection == 'screen'",

                "_$unisearch.key": "$unisearch.value",

                "columnsn": "$g_customerhistory_tableView.report_columns",
            },

            "on_success": [
                ("save_excel_export", "$result.document",
                 "> products_sold_list_{#datetime.strftime('%m%d%Y_%H%M%S')}")
            ],

            "on_failure": [
                ("if", "$result", ("error_message", "An error occurred when print the report!")),
            ]
        },

        # PIDB
        "get_integration": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__integration_settings",

            "pull": {
                "qp_intstn_integration_id": 10
            },

            "push": {
                "$storage.host": "$result.intstn_attrs.host[0]",
                "$storage.token": "$result.intstn_attrs.token[0]",
            },

            "on_success": [
                ("if", "$result.intstn_attrs.setup[0]#get('suppliers') == True",
                    ("push", "$storage.level", ["basic", "suppliers"])),
                ("if", "$result.intstn_attrs.setup[0]#get('enhanced') == True",
                    [
                        ("push", "$storage.level", ["basic", "suppliers", "enhanced"]),
                        "$g_manageproducts_tabs.show_tab('g_pidbmedia_tab')",
                        "$g_manageproducts_tabs.show_tab('g_pidbonline_tab')"
                    ]),
                ("if", "$result.intstn_attrs.setup[0]#get('insights') == True",
                    [
                        ("push", "$storage.level", ["basic", "suppliers", "enhanced", "insights"]),
                        "$g_manageproducts_tabs.show_tab('g_pidbmedia_tab')"
                    ]),

                ("if", "$storage.level == None",
                    ("push", "$storage.level", ["basic", ""])),

                ("push", "$storage.manufacturers", "> {$storage.host}/manufacturers"),
                ("push", "$storage.brand", "> {$storage.host}/brands"),
                ("push", "$storage.categories", "> {$storage.host}/categories"),
                ("push", "$storage.subcategories", "> {$storage.host}/subcategories"),
                ("push", "$storage.product", "> {$storage.host}/pos/products"),

                {"transfers": {
                    "names": [
                        "pidb_manufacturer",
                        "pidb_brand",
                        "pidb_category"
                    ],
                    "on_success": [
                        ("if", "$g_barcodes_table.data#length > 0",
                            ("transfer", "get_pidb_products"))
                    ],
                }}
            ]
        },

        "get_pidb_products": {
            "method": "get",
            "resource": "$storage.product",
            "content_type": "json",

            "pull": {
                "user_headers": {"token": "$storage.token"},
                # "_with_suppliers_for_location": "$storage.location_id",

                "gtin": "> ={$g_barcodes_table.data[0].prodbar_barcode}"
            },

            "push": {
                "$storage.pidb_product": "$result"
            },

            "on_success": [
                "$storage.product_info['mfgbr_ent_name'] = $g_mfg_combobox.text if $g_mfg_combobox.text != 'Select...' or ''",
                "$storage.product_info['mfgbr_brand_name'] = $g_brand_combobox.text if $g_brand_combobox.text != 'Select...' or ''",
                ("if", "$result#length > 0",
                    ("call", push_pidb_sync, {"pos": "$storage.product_info", "pidb": "$result", "level": "$storage.level"},
                        ("if", "$result",
                            [
                                ("push", "$sync_droplist", "$result[0]"),
                                ("push", "$storage.media_id", "$result[1]#get('media_id')"),
                                "$g_sync_button.show",
                                "$g_noproduct_frame.hide",
                                "$g_pidb_frame.show",
                                ("transfer", "get_data_for_pidb_images"),

                                ("push", "$g_pidb_description_lineedit", "$storage.pidb_product[0]#get('description')"),
                                ("push", "$g_pidb_barcodes_table", "$storage.pidb_product[0]#get('gtins')"),

                                ("push", "$g_pidb_suppliers_table", "$storage.pidb_product[0]#get('supplier_products')#sort_listofdict('cost', True, 'supplier_name', False)"),

                                ("push", "$g_pidb_onlinemessage_plaintextedit", "($storage.pidb_product[0]#get('marketing_message') if "
                                                                                "$storage.pidb_product[0]#get('marketing_message') != 'None') or ''"),

                                ("push", "$g_pidb_webinfo_plaintextedit", "($storage.pidb_product[0]#get('long_description') if "
                                                                     "$storage.pidb_product[0]#get('long_description') != 'None') or ''"),

                                ("push", "$g_pidb_webinfo_plaintextedit", "($storage.pidb_product[0]#get('long_description') if "
                                                                          "$storage.pidb_product[0]#get('long_description') != 'None') or ''"),

                                ("push", "$g_pidb_ingredients_plaintextedit", "($storage.pidb_product[0]#get('ingredients') if "
                                                                              "$storage.pidb_product[0]#get('ingredients') != 'None') or ''"),

                                "$g_pidb_nutritional_listWidget.clear",
                                ("if", "$storage.pidb_product[0]#get('nutritional') != None",
                                    ("push", "$g_pidb_nutritional_listWidget", "$storage.pidb_product[0]#get('nutritional')")),

                                ("if", "$storage.pidb_product[0]#get('manufacturer') != None",
                                    ("push", "$g_pidb_mfg_combobox.selected", "$storage.pidb_product[0]#get('manufacturer')#get('id')"),
                                    ("push", "$g_pidb_mfg_combobox.selected", None)),

                                ("if", "$storage.pidb_product[0]#get('brand') != None",
                                    ("push", "$g_pidb_brand_combobox.selected", "$storage.pidb_product[0]#get('brand')#get('id')"),
                                    ("push", "$g_pidb_brand_combobox.selected", None)),

                                ("if", "$storage.pidb_product[0]#get('category') != None",
                                    ("push", "$g_pidb_category_combobox.selected", "$storage.pidb_product[0]#get('category')#get('id')"),
                                    ("push", "$g_pidb_category_combobox.selected", None)),
                            ],
                            [
                                "$sync_droplist.clear_data(True)",
                                ("push", "$g_pidb_description_lineedit", ""),
                                "$pidb_media_viewer.clear",
                                "$g_pidb_barcodes_table.clear",
                                "$g_pidb_suppliers_table.clear",
                                "$sync_droplist.clear",
                                "$g_pidb_images_list.clear",
                                # "$g_pidb_nutritional_listWidget.clear",

                                # ("push", "$g_pidb_onlinemessage_plaintextedit", ""),
                                # ("push", "$g_pidb_webinfo_plaintextedit", ""),
                                # ("push", "$g_pidb_ingredients_plaintextedit", ""),
                                # ("push", "$g_pidb_description_lineedit", ""),
                                ("push", "$g_pidb_barcodes_table", ""),
                                ("push", "$g_pidb_suppliers_table", ""),

                                "$g_pidb_mfg_combobox.set_inde(-1)x",
                                "$g_pidb_brand_combobox.set_index(-1)",
                                "$g_pidb_category_combobox.set_index(-1)",
                                "$g_pidb_sub_category_combobox.set_index(-1)",
                            ]))),

            ],

            "on_finish": [
            ]
        },

        "update_pidb_product": {
            "method": "post",
            "resource": "/api/pidb_product_load",
            "content_type": "json",

            "pull": {
                "barcodes": ["$g_barcodes_table.data[0].prodbar_barcode"],
                "fields": "$storage.pidb_field_list"
            },

            "on_success": [
                ("info_message", "Product sync has been queued and will be completed in the next few minutes."),
                "close_after_pidb_sync"
            ],

            "on_failure": [
                ("error_message", "An error occurred when saving the record!")
            ]
        },

        # "update_local_product": {
        #     "method": "put",
        #     "resource": "/apps/any/products/{id}",
        #     "content_type": "json",
        #
        #     "query": {
        #         "id": "$form.input.ids[$paginator.index]",
        #     },
        #
        #     "pull": {
        #         "qp_product_id": "$form.input.ids[$paginator.index]",
        #         "_qp_prod_name": "$g_pidb_description_lineedit.data.lstrip().rstrip() if 'Description' in $storage.pidb_field_list",
        #
        #         "qp_prod_product_id": "$form.input.ids[$paginator.index]",
        #
        #         "_qp_ent_name": "$g_mfg_combobox.text.lstrip().rstrip() if 'Manufacturer' in $storage.pidb_field_list",
        #         "_qp_mfgbr_brand_name": "$g_brand_combobox.text.lstrip().rstrip() if 'Brand' in $storage.pidb_field_list",
        #         "_qp_prod_category": "($g_department_combobox.text.lstrip().rstrip() if 'Category' in $storage.pidb_field_list",
        #         "_qp_prod_sub_category": "($g_category_combobox.text.lstrip().rstrip() if 'Sub Category' in $storage.pidb_field_list",
        #
        #         # Online
        #         "_qp_prod_marketing_message": "$g_pidb_onlinemessage_plaintextedit if 'WebInfo' in $storage.pidb_field_list",
        #         "_qp_prod_web_info": "$g_pidb_webinfo_plaintextedit.data if 'Description' in $storage.pidb_field_list",
        #         "_qp_prod_ingredients": "$g_pidb_ingredients_plaintextedit if 'Ingredients' in $storage.pidb_field_list",
        #
        #         # Up-Sell,Cross-Sell,Alternative
        #         # "qp_prod_up_sell_product_ids": "$g_upsell_table.data#get('product_id')",
        #         # "qp_prod_cross_sell_product_ids": "$g_crosssell_table.data#get('product_id')",
        #         # "qp_prod_alternative_product_ids": "$g_alternative_table.data#get('product_id')",
        #
        #         # Sale only as Modifier
        #         # "qp_prlocs_is_saleable_as_modifier_only": "$g_sellasmodifieronly_checkbox.data",
        #         # "qp_prlocs_replaced_by_product_id": "($g_replacementprod_id_lineedit.data if $g_replacementprod_id_lineedit.data != '') or None ",
        #
        #     },
        #
        #     "on_start": [
        #         "disable_buttons",
        #         ("push", "$storage.product_id", "$form.input.ids[$paginator.index]")
        #     ],
        #
        #     "on_success": [
        #         ("if", "'Marketing' in $storage.pidb_field_list",
        #             ("push", "$g_marketing_textEdit", "$g_pidb_marketing_textEdit")),
        #         ("if", "'Description' in $storage.pidb_field_list",
        #             ("push", "$g_description_lineedit", "$g_pidb_description_lineedit.data.lstrip().rstrip()")),
        #         ("if", "'Ingredients' in $storage.pidb_field_list",
        #             ("push", "$g_ingredients_plaintextedit", "$g_pidb_ingredients_plaintextedit")),
        #         ("if", "'LongDescription' in $storage.pidb_field_list",
        #             ("push", "$g_longdescription_lineedit", "$storage.pidb_product[0]#get('long_description')"))
        #     ],
        #
        #     "on_finish": [
        #         "enable_buttons"
        #     ],
        #
        #     "on_failure": [
        #         ("error_message", "An error occurred when saving the record!"),
        #         "enable_buttons"
        #     ]
        # },

        # PIDB Manufacturer and Brand
        "pidb_manufacturer": {
            "method": "get",
            "resource": "$storage.manufacturers",
            "cache": ("app_cache", "product_manage_pidb_manufacturer", {"days": 1}),
            "content_type": "json",

            "pull": {
                "user_headers": {"token": "$storage.token"},
                "limit": 10000
            },

            "push": {
                "$g_pidb_mfg_combobox": "$result"
            }
        },

        "pidb_brand": {
            "method": "get",
            "resource": "$storage.brand",
            "cache": ("app_cache", "product_manage_pidb_brand", {"days": 1}),
            "content_type": "json",

            "pull": {
                "user_headers": {"token": "$storage.token"},
                "limit": 10000
            },

            "push": {
                "$g_pidb_brand_combobox": "$result"
            },
        },

        # PIDB Category and Sub-Category
        "pidb_category": {
            "method": "get",
            "resource": "$storage.categories",
            "cache": ("app_cache", "product_manage_pidb_category", {"days": 1}),
            "content_type": "json",

            "pull": {
                "user_headers": {"token": "$storage.token"},
                "limit": 10000
            },

            "push": {
                "$g_pidb_category_combobox": "$result"
            }
        },

        # PIDB Media
        "get_data_for_pidb_images": {
            "type": "iterator",

            "iterator": "$storage.media_id",
            "transfer": "get_data_for_pidb_image",

            "on_start": [
                ("push", "$storage.counter", 0)
            ],
            "on_iter_success": [
                     ("push", "$storage.counter", "$storage.counter + 1"),
                     "$pidb_media_viewer.update(#dict('id', $storage.counter, 'filedata', $response_data.b64encoded, 'description', '', 'sort_order', $storage.counter, 'type', 'image'))"
            ]
        },

        "get_data_for_pidb_image": {
            "method": "get",
            "resource": "{host}/products/{id}/images/{image_id}",
            "content_type": "json",

            "query": {
                "id": "$storage.pidb_product[0]#get('id')",
                "image_id": "$iter",
                "host": "$storage.host"
            },

            "pull": {
                "user_headers": {"token": "$storage.token"}
            },
        },

        # Pricing Group
        "get_pricinggroup": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__list_product_prices",

            "pull": {
                "qp_product_id": "$form.input.ids[$paginator.index]",
                "order_by": "> [('{$g_pricinggroup_tableView.sort_field}', '{$g_pricinggroup_tableView.sort_order}')]",

                "columnsn": "$g_pricinggroup_tableView.transfer_columns"
            },

            "push": {
                # "$paginator.total": "$total",
                # "$storage.totalresults": "$total",
            },

            "on_success": [
                "$g_pricinggroup_tableView.set_source($result)",
            ],

            "on_finish": [
                # "get_page_results"
            ],

            "on_failure": [
                # "enable_buttons"
            ],
        },

        # Edit Selected Columns
        "edit_pricinggroup_columns": {
            "dialog": {
                "name": "SelectEditColumn",

                "data": {
                    "columns": "$g_pricinggroup_tableView.edit_columns"
                }
            },

            "on_accept": [
                ("push", "$storage.pricinggroup_table_edit", True),
                "$g_pricinggroup_tableView.edit_table($storage.pricinggroup_table_edit, $dialog.output.field)",
                "$g_editpricinggroup_table_label.stylesheet('background-color: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);')"
            ],
        },

        "get_pricinggroup_markup": {
            "method": "post",
            "resource": "/apps/any/product_markup_prices",

            "pull": {
                "cost": "$storage.prdpr_cost#float or 0",
                "markup": "$storage.prdpr_markup#float or 0"
            },

            "on_success": [
                     ("push", "$storage.price_new", "$result.price"),
            ],

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "update_pricinggroup": {
            "method": "post",
            "resource": "/apps/any/queries/update__tbl__product_prices",

            "pull": {
                "qp_product_price_id": "$storage.product_price_id",
                "qp_prdpr_price": "$storage.prdpr_price",
                "qp_prdpr_markup": "$storage.prdpr_markup",
                "qp_prdpr_cost": "$storage.prdpr_cost",
                "qp_prdpr_uses_markup": "$storage.prdpr_uses_markup",
            },

            "on_success": [
                "$g_pricinggroup_tableView.edit_finish(True, $storage.new_data)"
            ],

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

    },

    "fpe_custom_widgets": [
        {
            "type": "paginator",
            "name": "paginator",

            "left_arrow": "g_previouspage_label",
            "right_arrow": "g_nextpage_label",
            "page_input": "g_currentpage_lineedit",
            "total_label": "g_totalpages_label",

            "initial_page": 1,
            "per_page": 1,

            "on_change": [
                "disable_buttons",
                "on_record_move"
            ],

            "on_changed": [
                "disable_buttons",
                "load_next"
            ]
        },

        {
            "type": "progress_bar",
            "name": "progress_bar",

            "frame": "g_progress_frame",
            "completed": "g_completed_frame"
        },

        # History Tab
        {
            "type": "datetime_range",
            "name": "history_datetime_range",

            "start": "g_history_startdate_timeedit",
            "end": "g_history_enddate_timeedit"
        },

        # {
        #     "type": "datetime_range",
        #     "name": "customerhistory_datetime_range",
        #
        #     "start": "g_customerhistory_startdate_timeedit",
        #     "end": "g_customerhistory_enddate_timeedit",
        #
        #     "on_change": [
        #         ("if", "'update' in $form.input.flags and $storage.loading == False",
        #          ("transfer", "get_customerhistory"))
        #     ]
        # },

        # Open Orders
        {
            "type": "paginator",
            "name": "openorders_paginator", 

            "left_arrow": "g_previouspage_label_04",
            "right_arrow": "g_nextpage_label_04",
            "page_input": "g_currentpage_lineedit_04",
            "total_label": "g_totalpages_label_04",

            "initial_page": 1,
            "per_page": 25,

            "on_changed": "load_openorders"
        },

        # Print Labels Qty
        # {
        #     "type": "quantity_selector",
        #     "name": "qsetter",
        #     "inc_button": "g_spinplus_button",
        #     "dec_button": "g_spinminus_button",
        #     "input_field": "g_spin_lineEdit",
        #
        #     "start_value": 1,
        #     "min_value": 1,
        #     "max_value": 99,
        #     "step": 1,
        # },

        # Media
        {
            "type": "media_viewer_list",
            "name": "media_viewer",

            "list": "g_product_images_list",

            "key": "id",
            "key2": "id",
            "data": "filedata",
            "description": "description",
            "position": "sort_order",
            "tags": "tags",
            "title": "title",


            "placeholder": "Picture Description",

            "on_remove_image": ("transfer", "remove_image"),

            "on_remove_video": ("transfer", "remove_video"),

            "on_edit": [
                ("push", "$storage.imagesaved", False),
                ("push", "$storage.type", "$item.type",),
                ("if", "$item.type == 'image'",
                    [
                        "$g_mediatitle_frame.hide",

                        "$g_cropper_area.show",
                        "$g_crop_and_save_btn.show",
                        "$g_cropper_cancel_btn.show",
                        "$g_size_label.show",
                        "$g_addedit_groupBox.hide",

                        "$storage.filename = $item.filename",
                        "$cropper.updated = False",
                        "$cropper.data = $item.filedata",

                        "$cropper.crop_updated(False)",
                    ],
                    [
                        "$g_cropper_area.show",
                        "$g_crop_and_save_btn.show",
                        "$g_cropper_cancel_btn.show",
                        "$g_size_label.hide",
                        "$g_addedit_groupBox.hide",
                        "$g_mediatitle_frame.show",

                        "$cropper.crop_updated(False)",
                        "$cropper.data = None",
                        ("push", "$g_title_lineedit", "($item.video.meta.title if $item.video.meta.title != '@null') or ''"),
                        ("push", "$g_tags_lineedit", "(','.join($item.tags) if ','.join($item.tags) != '@null') or ''")
                    ]),
                "$storage.media_id = $item.id",
                ("push", "$g_image_description_lineedit", "($item.description if $item.description != '@null') or ''"),
            ],

            "on_image_description_changed": [
                ("transfer", "change_image_title"),
            ],

            "on_video_description_changed": [
                ("transfer", "change_video_title"),
            ],

            "on_sort": [
                ("transfer", "update_media_order"),
            ],

            "on_click": [
                ("$ext.system.open_html", "$media_viewer.link")
            ]
            # "on_click": ("$ext.system.open_html", "<html> <body> Hello world! </body> </html>")
        },

        {
            "type": "file_uploader",
            "name": "file_uploader",

            "mask": "Images (*.jpg *.jpeg *.mp4 *.mov *.mpeg4 *.avi *.wmv *.mpegps *.flv *.3gpp *.png *.PNG " +
                    "*.JPG *.JPEG *.MP4 *.MOV *.MPEG4 *.AIV *.WMV *.MPEGPS *.FLV *.3GPP *.webp *.jfif)",
            "title": "Select image",

            "browse": "g_selectfile_button",
            "display": "g_uploadfilename_lineedit",

            "on_upload": [
                ("if", "'.jpg' in $file_uploader.file.lower() or"
                       " '.jpeg' in $file_uploader.file.lower() or"
                       " '.jfif' in $file_uploader.file.lower() or"
                       " '.webp' in $file_uploader.file.lower() or"
                       " '.png' in $file_uploader.file.lower()",
                    [
                        ("push", "$storage.imagesaved", False),
                        "$g_mediatitle_frame.hide",
                        ("push", "$storage.type", "image"),
                        "$g_cropper_area.show",
                        "$g_cropper_cancel_btn.show",
                        "$g_crop_and_save_btn.show",
                        "$cropper.crop_updated(True)",
                        "$g_addedit_groupBox.hide",
                        "$g_size_label.show",

                        "$storage.filename = $file_uploader.file",
                        "$cropper.data = $file_uploader.data",
                        ("push", "$g_image_description_lineedit", "$g_description_lineedit.data or ''"),
                    ],
                    [
                        "$g_mediatitle_frame.show",
                        ("push", "$storage.type", "video"),
                        ("transfer", "create_video")
                    ]),
            ]
        },

        {
            "type": "cropper",
            "name": "cropper",

            "frame": "g_cropper_frame",
            "size_label": "g_size_label",

            "ratio": 1,
            # "ratio": 1.333,  #  size 4x3
            # "ratio": 1.777,  #  size 16x9

            "width": 600,
            "height": 300,

            "return_width": 800,
            "return_height": 800
        },

        # Discount Breaks
        {
            "type": "droplist",
            "name": "breakpoint_droplist",

            "container": "g_breakdiscount_listWidget",

            "add": ":/entity_dialog/add",
            "delete": ":/entity_dialog/delete",

            "default": {"qty": "0", "amount": "0", "type": "amount"},

            "layout": [
                {"field": "qty", "data": {"type": "lineedit", "label": "Qty", "reg": "float", "name": "g_qty_lineedit", "blank": "0"}},
                {"field": "amount", "data": {"type": "lineedit", "label": "Amount Off Each", "label_width": 100, "reg": "float4_neg", "name": "g_amount_lineedit", "blank": "0"}},
                {"field": "type", "data": {"type": "combobox", "label": "Type", "update": "only", "name": "g_type_combobox"}}
                # {"field": "qty", "data": {"type": "lineedit", "label": "Qty", "reg": "float", "name": "g_qty_lineedit", "width": 65, "blank": "0"}},
                # {"field": "amount", "data": {"type": "lineedit", "label": "Amount", "reg": "float", "name": "g_amount_lineedit", "width": 65, "blank": "0"}},
                # {"field": "type", "data": {"type": "combobox", "label": "Type", "update": "only", "name": "g_type_combobox", "width": 80}}
            ]
        },

        # Product Locations
        {
            "type": "droplist",
            "name": "prodlocation_droplist",

            "container": "g_productlocation_listWidget",

            "add": ":/entity_dialog/add",
            "delete": ":/entity_dialog/delete",

            "default": {"aisles": "", "sections": "", "shelves": "", "bins": ""},

            "layout": [
                {"field": "aisles", "data": {"type": "combobox", "name": "g_aisle_combobox", "valid": "int",
                                             "update": "only", "editable": True, "text_length": 3, "text_entry": True}},
                {"field": "sections", "data": {"type": "combobox", "name": "g_section_combobox", "valid": "int",
                                             "update": "only", "editable": True, "text_length": 3, "text_entry": True}},
                {"field": "shelves", "data": {"type": "combobox", "name": "g_shelf_combobox", "valid": "int",
                                             "update": "only", "editable": True, "text_length": 3, "text_entry": True}},
                {"field": "bins", "data": {"type": "combobox", "name": "g_bin_combobox", "valid": "int",
                                             "update": "only", "editable": True, "text_length": 3, "text_entry": True}}
            ]
        },

        # E-Commerce
        {
            "type": "droplist",
            "name": "nutrition_droplist",

            "container": "g_nutritional_listWidget",

            "add": ":/custom_fields/add",
            "delete": ":/custom_fields/delete",

            "default": {"nut_key": None, "nut_value": None},

            "layout": [
                {"field": "nut_key", "data": {"type": "combobox", "name": "g_nut_key_combobox", "width": 300,
                                             "update": "only", "editable": True, "text_entry": True}},

                {"field": "nut_value",
                 "data": {"type": "lineedit", "required": True, "name": "g_nut_value_lineedit"}}
            ]
        },

        # Printer Zones
        {
            "type": "single_list",
            "name": "printerzone_list",
            "selection_type": "checkbox",

            "drag_drop": True,

            "source_list": "g_printerzones_listWidget",
            "checked": "enabled",

            "up_arrow": "g_printerzonesuparrow_button",
            "down_arrow": "g_printerzonesdownarrow_button",
        },

        # Sold
        {
            "type": "unisearch",
            "name": "unisearch",

            "input": "g_customerhistory_searchtext_lineedit",
            "cross": "g_customerhistory_clearsearch_button",
            "select": "g_customerhistory_search_combobox",

            "conf": [
                {"Smart Search": "qp_smart_search"},
            ],

            "ean8_convert": [
                {"Smart Search": "qp_smart_search"},
            ],

            "on_change": ("transfer", "get_customerhistory")
        },

        {
            "type": "paginator",
            "name": "paginator_customerhistory",
            "left_arrow": "g_customerhistory_previouspage_label",
            "right_arrow": "g_customerhistory_nextpage_label",
            "page_input": "g_customerhistory_currentpage_lineedit",
            "total_label": "g_customerhistory_totalpages_label",

            "initial_page": 1,
            "per_page": 25,

            "on_changed": ("transfer", "get_customerhistory")
        },

        # Purchasing Locations
        {
            "type": "check_combobox",
            "name": "c_historylocations_checkcombobox",
            "widget": "g_historylocations_checkcombobox",
        },

        # Promotions Locations
        {
            "type": "check_combobox",
            "name": "c_promolocations_checkcombobox",
            "widget": "g_promolocations_checkcombobox",
        },

        # PIDB Sync Product
        {
            "type": "droplist",
            "name": "sync_droplist",

            "container": "g_sync_listWidget",

            "layout": [
                {"field": "label", "data": {"type": "label", "label_width": 115, "align": "right"}},
                {"field": "pidb", "data": {"type": "lineedit", "enable": "disable"}},
                {"field": "sync", "data": {"type": "checkbox", "text": "PIDB -> POS",
                                           "color_true": "green", "color_false": "red",
                                           "rules": {"disable": True, "field": "pidb", "value": ""}}},
                {"field": "pos", "data": {"type": "lineedit", "enable": "disable"}},
            ]
        },

        # Media
        {
            "type": "media_viewer_list",
            "name": "pidb_media_viewer",

            "list": "g_pidb_images_list",

            "key": "id",
            "key2": "id",
            "data": "filedata",
            "description": "description",
            "position": "sort_order",
            "tags": "tags",
            "title": "title",
            "is_editable": False,

            "placeholder": "Picture Description"
        },

        # Product Warnings
        {
            "type": "check_combobox",
            "name": "c_productwarnings_checkcombobox",
            "widget": "g_productwarnings_checkcombobox",
        },
    ],

    "fpe_validations": {
        "g_description_lineedit": {
            "rule": {"type": "string", "min": 3, "max": 50},
            "message": "Description length should be in range 3..50"
        },

        "g_cost_lineedit": {
            "rule": {"type": "currency", "min": 0, "blank_ok": False},
            "message": "A valid cost is required"
        },

        "g_markup_lineedit": {
            "rule": "($g_markup_lineedit.data != '' and $g_markup_radioButton.data == True) or $g_markup_radioButton.data == False",
            "message": "Markup Required"
        },

        "g_price_lineedit": {
            "rule": {"type": "currency", "blank_ok": False},
            "message": "A valid price is required" 
        },

        "g_department_combobox": {
            "rule": "$g_department_combobox.has_value",
            "message": "Category is required"
        },

        "g_minstocklevel_lineedit": {
            "rule": "$g_minstocklevel_lineedit.data == '' or $g_minstocklevel_lineedit.data#int > 0",
            "message": "Minimum Stock should be 1 or greater or blank."
        },

        "g_maxstocklevel_lineedit": {
            "rule": "$g_maxstocklevel_lineedit.data == '' or $g_maxstocklevel_lineedit.data#int >= $g_minstocklevel_lineedit.data#int",
            "message": "> Maximum Stock should be {$g_minstocklevel_lineedit.data} or greater."
        },

        "g_minqtylevel_lineedit": {
            "rule": "($g_minqtylevel_lineedit.data == '' or $g_minqtylevel_lineedit.data#int > 0)",
            "message": "Minimum Quantity must be 0 or greater."
        },

        "g_maxqtylevel_lineedit": {
            "rule": "$g_maxqtylevel_lineedit.data == ''  or $g_maxqtylevel_lineedit.data#int >= $g_minqtylevel_lineedit.data#int",
            "message": "> Maximum Quantity should be {$g_minqtylevel_lineedit.data} or greater."
        },

        # Cutomer Pets Subsciptions
        "g_subscriptionqty_lineedit": {
            "rule": "(($g_subscriptionqty_lineedit.data != '' and $g_subscriptionperiod_combobox.selected.value != 'Lifetime') "
                    "and $g_extendedaction_combobox.selected.extended_action_id == -5)  or "
                    "($g_subscriptionperiod_combobox.selected.value == 'Lifetime' "
                    "and $g_extendedaction_combobox.selected.extended_action_id == -5) "
                    "or $g_extendedaction_combobox.selected.extended_action_id != -5",
            "message": "Qty Required"
        },

        "g_renewal_combobox": {
            "rule": "(($g_renewal_combobox.has_value and $g_subscriptionperiod_combobox.selected.value != 'Lifetime') "
                    "and $g_extendedaction_combobox.selected.extended_action_id == -5) or "
                    "($g_subscriptionperiod_combobox.selected.value == 'Lifetime' "
                    "and $g_extendedaction_combobox.selected.extended_action_id == -5)  "
                    "or $g_extendedaction_combobox.selected.extended_action_id != -5",
            "message": "Renewal is required"
        },

        # "g_subscriptionlabel_lineedit": {
        #     "rule": "$g_maxstocklevel_lineedit.data != '' and $g_extendedaction_combobox.selected.extended_action_id == -5 or $g_extendedaction_combobox.selected.extended_action_id != -5",
        #     "message": "> Maximum Stock should be {$g_minstocklevel_lineedit.data} or greater."
        # },

        "g_subscriptionperiod_combobox": {
            "rule": "$g_subscriptionperiod_combobox.has_value and $g_extendedaction_combobox.selected.extended_action_id == -5  or $g_extendedaction_combobox.selected.extended_action_id != -5",
            "message": "Period Required"
        },

        "$breakpoint_droplist.fields('g_amount_lineedit')": {
            "value": "$widget.data",
            "rule": "$value#float > 0",
            "message": "Discount Breaks must be greater than zero"
        },

        "$breakpoint_droplist.fields('g_qty_lineedit')": {
            "value": "$widget.data",
            "rule": "$value#float > 0",
            "message": "Discount Qty must be greater than zero"
        },
    },

    "fpe_groups": {
        "app_lite_hide": [
            "g_binlocation_label",
            "g_binlocation_lineedit",
            "g_transfer_groupBox",
            "g_promolocations_frame",
            "g_promo_table",
            "g_pormobuttons_frame",
            "g_purchasinginfo_tabs",
            "g_productlocation_groupBox",
            "g_commoverride_groupbox",
            "g_breaks_groupBox",
            "g_extendedaction_groupbox",
            "g_markedfordeletion_checkbox",
            "g_promotions_checkBox",
            "g_nodiscounts_checkBox",
            "g_askfornotes_checkbox",
            "g_promolocations_frame",
            "g_donotinventory_checkbox"

        ],

        "app_lite_show":[
            "g_purchasing_not_available_frame",
            "g_promotion_not_available_frame",
            "g_product_type_spacer_frame",
            "g_purchasing_not_available_frame",
        ],

        "app_lite_disable": [
            "g_stockingrule_combobox",
            "g_replacementprod_id_lineedit",
            "g_replacementprod_select_button",
            "g_replacementproduct_alllocations_checkbox",
            "g_donotinventory_checkbox",
            "g_requirecustomer_checkbox",
            "g_requiredaddress_checkbox",
            "g_requiredphoneemail_checkbox",
        ],

        "grp_phase1_hidden_objects": [
            # Main Location Combobox Selection
            "g_settingslocation_frame",
            "g_insights_label",
            "g_insights_lineedit",
            "g_lowinventory_frame",
            "g_lowmargin_frame",
            "g_nosupplier_frame",
            "g_notcost_frame",

            "g_groupings_widget",
            "g_productupdates_widget",
            "g_purchasehistory_widget",
            "g_projectedqtys_widget",
            "g_usescale_checkbox",
            "g_massactionsupplierinfo_button",
            "g_pormobuttons_frame",
            "g_question_label",
            "g_recycle_label",

            "g_cropper_area",
            "g_crop_and_save_btn",
            "g_cropper_cancel_btn",
            "g_mediatitle_frame",

            "g_usescale_checkbox",
            "g_useserials_checkbox",
            "g_askforcost_checkbox",
            # "g_askfornotes_checkbox",
            "g_askforproductdescription_checkbox",

            "g_seasonalcalendar_frame",
            "g_askforsaleprice_label",
            "g_binlocation_lineedit",
            "g_binlocation_label",

            "g_autoorder_label",

            # Permissions
            "g_adjustinventory_button",
            "g_costlastreceived_frame",
            "g_pricelastsaledate_frame",
            "g_price_radioButton",
            "g_markup_radioButton",

            "g_pricelink_groupBox",

            "g_printerzones_tab",

            "g_replaced_label",

            "g_deliverytypes_groupbox",
            "g_storepickup_checkbox",
            "g_scheduleddelivery_checkbox",
            "g_shiptohome_checkbox",

            "g_upsellcontrols_frame",
            "g_crosssellcontroles_frame",
            "g_alternativecontrols_frame",
            "g_locationskuoptions_groupbox",
            # "g_prodtags_frame",

            "g_replacementproduct_alllocations_checkbox",

            # History Tab
            "g_historylocations_frame",
            "g_order_tab",

            # Promotion
            "g_promolocations_frame",

            # PIDB
            'g_pidb_frame',
            "g_sync_button",

            "g_pidbmfglinklabel_label",
            "g_pidbmfglink_label",

            "g_variantgroup_frame",

            # Subscriptions
            "g_subscriptionperiod_frame",
            "g_subscriptionrenewal_frame",

            # commissions
            "g_commoverride_groupbox",

        ],

        "grp_permissions": [
            "g_department_combobox",
            "g_category_combobox",
        ],

        # PIDB
        "grp_disabled_start": [
            "g_pidb_description_lineedit",
            "g_pidb_mfg_combobox",
            "g_pidb_brand_combobox",
            "g_pidb_category_combobox",
            "g_pidb_sub_category_combobox"
        ],

        "grp_command_buttons": [
            "g_cancel_button",
            "g_save_button",
            "g_pagenav_frame",
            "g_nextpage_label",
            "g_previouspage_label",
            "g_currentpage_lineedit",
            "g_location_combobox",
            "g_printnewlabel_groupbox",
            "g_transfer_groupBox",
            "g_barcodebuttons_frame"
        ],

        "grp_barcodes_command_buttons": [
            "g_removebarcode_button",
            "g_managebarcode_button"
        ],

        "grp_attributes_command_buttons": [
            "g_removeattribute_button",
            "g_manageattribute_button"
        ],

        "grp_product_supplier_command_buttons": [
            "g_removesupplierinfo_button",
            "g_managesupplierinfo_button"
        ],

        "grp_product_simplebundle_command_buttons": [
            "g_managesimplebundle_button"
        ],

        "grp_product_advancebundle_command_buttons": [
            "g_manageadvancedbundle_button"
        ],

        "grp_taxes_command_buttons": [
            # "g_resettaxes_button"
        ]
    },

    # Widgets
    "form": {
        "title": "Manage Product",
        "resizable": "true",

        "on_load": [
            "setup_form",
            "prepare_form",
            "load_data"
        ],

        "on_close": {
            "case": {
                "value": "$form.input.flags",
                "create": ("if", "$storage.sender != 'Save'", ("transfer", "delete_product"))
            }
        }
    },

    # "g_disable_ratio_checkbox":{
    #     "on_change": [
    #         ("if", "$g_disable_ratio_checkbox",
    #             "$cropper.set_ratio(None)",
    #             [
    #             ]),
    #     ]
    # },

    "g_imageratio_combobox": {
        "blank": False,

        "on_change": [
            ("if", "$g_imageratio_combobox.selected.value  == '1x1'", "$cropper.set_ratio(1)"),
            ("if", "$g_imageratio_combobox.selected.value  == '4x3'", "$cropper.set_ratio(1.333)"),
            ("if", "$g_imageratio_combobox.selected.value  == '16x9'", "$cropper.set_ratio(1.777)"),
            ("if", "$g_imageratio_combobox.selected.value  == '7x11'", "$cropper.set_ratio(0.618)"),
            ("if", "$g_imageratio_combobox.selected.value  == 'disable'", "$cropper.set_ratio(None)")
        ],

        "bind": {
            "show": "name",
            "match": "value"
        }
    },

    #  Select location
    "g_location_combobox": {
        "clear": True,
        "blank": False,
        "select": "Required",

        "bind": {
            "show": "loc_report_code",
            "match": "loc_entity_id",
        },

        "on_change": [
            ("if", "$storage.loading == False",
                ("transfer", "update_product",
                    [
                        ("push", "$storage.location_id", "$g_location_combobox.selected.loc_entity_id"),
                        "load_after_location_change"
                    ]))
        ]
    },

    "g_description_lineedit": {
        "on_change": [
            ("if", "$g_description_lineedit.data#length > 50",
                "$g_description_lineedit.set_red",
                "$g_description_lineedit.set_normal")
        ]
    },

    "g_product_images_list": {
        "selection_type": "rows",
    },

    "g_enableaction_checkbox": {
        "on_change": "manage_extendedaction_combobox"
    },

    "g_mfg_combobox": {
        "clear": True,
        "editable": True,

        "bind": {
            "show": "ent_name",
            "match": "entity_id",
        },

        "on_change": ("$g_brand_combobox.filter", {"mfgbr_entity_id": "$g_mfg_combobox.selected.entity_id"})
    },

    "g_brand_combobox": {
        "clear": True,
        "editable": True,

        "bind": {
            "show": "mfgbr_brand_name",
            "match": "mfg_brand_id",
        }
    },

    "g_extendedaction_combobox": {
        "clear": True,

        "bind": {
            "show": "extact_name",
            "match": "extended_action_id",
        },

        "on_change": [
            "manage_extendedaction_combobox"
        ]
    },

    "g_stockingrule_combobox": {
        "editable": False,
        "clear": True,
        "blank": False,

        "bind": {
            "show": "name",
            "match": "value",
        }
    },

    "g_department_combobox": {
        "clear": True,
        "editable": True,

        "bind": {
            "show": "prodcat_name",
            "match": "product_category_id"
        },

        "on_change": [
            ("if", "$g_department_combobox.selected",
                ("transfer", "get_sub_category"))
        ],

        "on_focus_out": [
            ("if", "$g_department_combobox.selected == None",
                ("push", "$g_category_combobox", [])),
        ]
    },

    "g_category_combobox": {
        "clear": True,
        "editable": True,

        "bind": {
            "show": "prodcat_name",
            "match": "product_category_id"
        }
    },

    "g_productlocation_listWidget": {
        "selection_type": "rows",
    },

    "g_breakdiscount_listWidget": {
        "selection_type": "rows",
    },

    "g_nutritional_listWidget": {
        "selection_type": "rows",
    },

    "g_pidb_nutritional_listWidget": {
        "selection_type": "rows",
    },

    # Online
    # "g_longdescription_lineedit": {
    #     "max_length": 80,
    #     "on_change": [
    #         ("push", "$g_onlinedescmaxlength_label", "> {$g_longdescription_lineedit.get_length}/80")
    #     ],
    # },

    "g_marketing_textEdit": {
        "max_length": 600,
        "on_change": [
            ("push", "$g_onlinemessagemaxlength_label", "> {$g_marketing_textEdit.get_length}/600")
        ],
    },

    # Promotions
    "g_subscriptionperiod_combobox": {
        "clear": True,

        "bind": {
            "show": "name",
            "match": "value",
        },

        "on_change": [
            ("if", "$g_subscriptionperiod_combobox.selected.value == 'Lifetime'",
                [
                    "$g_subscriptionqty_lineedit.hide",
                    ("push", "$g_subscriptionqty_lineedit", ""),
                    ("push", "$g_subscriptionlabel_lineedit", ""),
                    "$g_subscriptionqty_label.hide",
                    "$g_subscriptionrenewal_frame.hide",
                    ("push", "$g_renewal_combobox.selected", None),
                ],
                [
                    "$g_subscriptionqty_lineedit.show",
                    "$g_subscriptionqty_label.show",
                    "$g_subscriptionrenewal_frame.show",
                ])
        ]
    },

    "g_renewal_combobox": {
        "clear": True,

        "bind": {
            "show": "name",
            "match": "value",
        }
    },

    # Pricing Group
    "g_pricinggroup_tableView": {
        "selection_mode": "single_selection",
        "sections_movable": False,
        "default_sort_field": "prdpr_product_id",
        "default_sort_order": "asc",
        "per_page": 25,

        "bind": {
            "table": "multiple_edit",
            "primary_id": "product_id",
            "no_export": [],
            "required_hidden_fields": ["product_price_id", "product_pricing_group_id", "prdpr_product_id",
                                       "prdpr_uses_markup"],

            "required_fields": [],

            "column_order": ["ppg_name", "prdpr_cost", "prdpr_markup", "prdpr_price"],

            "columns": [
                {"value": "ppg_name", "name": "Name"},
                {"value": "prdpr_cost", "name": "Cost", "halign": "right", "edit": "true"},
                {"value": "prdpr_markup", "name": "Markup", "halign": "right", "edit": "true"},
                {"value": "prdpr_price", "name": "Price", "halign": "right", "edit": "true"},
            ],

            "show_fields": [
                {"value": "ppg_name"},
                {"value": "$row.prdpr_cost#currency or ''", "regex": "float4", "edit": "lineedit"},
                {"value": "($row.prdpr_markup#currency(4,0) if $row.prdpr_markup#str != '0.0' and "
                          "$row.prdpr_markup#str != '0.0000') or '-'", "regex": "float4", "edit": "lineedit"},
                {"value": "$row.prdpr_price#currency or ''", "regex": "float4", "edit": "lineedit"},
            ]
        },

        "on_column_move": [],

        "on_double_click_row": { },

        "on_row_select": [],

        "on_selection_clear": [],

        "on_sort": [],

        "on_edit": [
            "verify_pricinggroup_before_update",
        ]
    },

    "g_editpricinggroup_table_label": {
        "on_release": [
            ("if", "$storage.pricinggroup_table_edit",
             [
                 ("push", "$storage.pricinggroup_table_edit", False),
                 "enable_edit_buttons",
                 "$g_pricinggroup_tableView.edit_table($storage.pricinggroup_table_edit)",
                 "$g_editpricinggroup_table_label.stylesheet('background-color: transparent;')",
             ],
             [
                 ("transfer", "edit_pricinggroup_columns"),
             ]
             )
        ]
    },

    # History Tab Location Control
    "c_historylocations_checkcombobox": {
        "status": False,
        "delay": True,
        "bind": {
            "show": "loc_report_code",
            "check": "check"
        },

        "on_hide_list": [
            ("if", "$storage.loading == False",
                [
                    "load_product_movements",
                    "load_product_sales",
                    "load_puchases_by_date",
                    # ("transfer", "get_product_updates"),
                    ("transfer", "get_customerhistory")
                ]),
        ]
    },

    # Weights and Dimensions
    "g_prod_weightmeasure_combobox": {
        "clear": True,
        "blank": False,

        "bind": {
            "show": "name",
            "match": "value",
        },

        "on_change": [
            ('if', "$g_prod_weight_lineedit.data != ''",
                "calculate_weights")
        ]
    },

    "g_dimension_measure_combobox": {
        "clear": True,
        "blank": False,

        "bind": {
            "show": "name",
            "match": "value",
        },

        "on_change": [
            "calculate_dimensions"
        ]


    },

    # Variant
    "g_variantgroup_button": {
        "on_release": [
            ("transfer", "update_variant_form")
        ]
    },

    # Printer Zones
    "printerzone_list": {
        "clear": True,

        "bind": {
            "show": "prtz_name"
        }
    },

    # Price Link
    "g_pricelink_combobox": {
        "blank": True,
        "clear": True,
        "select": "Select...",

        "bind": {
            "show": "plnk_name",
            "match": "price_link_id",
        },

        "on_change": [
            ("if", "$storage.loading == False",
                ("if", "$g_pricelink_combobox.text != 'Select...'",
                    ("push", "$g_price_label", "Link Price"),
                    ("push", "$g_price_label", "Price")))
        ]
    },

    "g_managepricelink_button": {
        "on_release": [
            ("transfer", "manage_price_link")
        ]
    },

    # Promotions
    "g_promo_table": {
        "clear": True,

        "selection_mode": "single_selection",
        "sections_movable": False,

        "remote_sort": ["prom_name", "prom_type", "txn_type_number", "loc_report_codes"],

        "default_sort_field": "prom_name",
        "default_sort_order": "asc",

        "bind": {
            "columns": [
                ("inv_changed_date", {"default": "Name"}),
                ("txn_type", {"default": "Type"}),
                ("txn_type_number", {"default": "Active"}),
                ("loc_report_codes", {"default": "Location"})
            ],

            "show_fields": [
                ("$row.prom_name", {"on_link_click": "open_promotion"}),
                "$row.prom_type",
                "$row.prom_is_active_today",
                "$row.loc_report_codes or ''",
            ]
        },

        # "on_row_select": [
            # ("change_state", "grp_product_supplier_command_buttons", {"default_state": "enabled"}),
        # ],

        # "on_double_click_row": ("link_to", "g_managesupplierinfo_button", "on_release"),

        "on_sort": ("transfer", "get_promotions")
    },

    # Product Warnings
    "c_productwarnings_checkcombobox": {
        "status": False,
        "delay": True,
        "bind": {
            "show": "name",
            "check": "check"
        },

        "on_hide_list": [
            # ("if", "$storage.loading == False",
            #  [
            #      ("transfer", "get_promotions")
            #  ]),
        ]
    },

    # Sku Options
    "g_requirecustomer_checkbox": {
        "on_change": [
            ("if", "$g_requirecustomer_checkbox == False",
                [
                    ("push", "$g_requiredaddress_checkbox", False),
                    ("push", "$g_requiredphoneemail_checkbox", False)
                ])
        ]
    },

    "g_requiredaddress_checkbox": {
        "on_change": [
            ("if", "$g_requiredaddress_checkbox",
                ("push", "$g_requirecustomer_checkbox", True))
        ]
    },

    "g_requiredphoneemail_checkbox": {
        "on_change": [
            ("if", "$g_requiredphoneemail_checkbox",
                ("push", "$g_requirecustomer_checkbox", True))
        ]
    },

    # "g_managepromo_button": {
    #     "on_release": [
    #         ("transfer", "update_promotion_form")
    #     ]
    # },

    "c_promolocations_checkcombobox": {
        "status": False,
        "delay": True,
        "bind": {
            "show": "loc_report_code",
            "check": "check"
        },

        "on_hide_list": [
            ("if", "$storage.loading == False",
                [
                    ("transfer", "get_promotions")
                ]),
        ]
    },

    # Media
    "g_crop_and_save_btn": {
        "on_release": [
            ("if", "$storage.type == 'image'",
                [
                   ("if", "$cropper.is_updated == True",
                       ("if", "$cropper.area_width < 400",
                           ("error_message",
                               "The cropped image size is too small. Please make sure your saved image size is at least 400 pixels wide."),
                           ("transfer", "create_image")),
                       [
                            ("transfer", "change_image_title")
                       ])
                ],
                ("transfer", "change_video_title")),
        ]
    },

    "g_cropper_cancel_btn": {
        "on_release": [
            "$g_addedit_groupBox.show",
            "$g_cropper_area.hide",
            "$g_crop_and_save_btn.hide",
            "$g_cropper_cancel_btn.hide",
            ("push", "$g_uploadfilename_lineedit", "")
        ]
    },

    # PIDB
    "g_pidb_barcodes_table": {
        "clear": True,

        "selection_mode": "single_selection",
        "sections_movable": False,

        "bind": {
            "show_fields": [
                "$row.value",
                "$row.label or ''",
                ("$row.qty#quantity", {"halign": "right"})
            ]
        }
    },

    "g_pidb_suppliers_table": {
        "clear": True,

        "sort": ["string_ignore_case", "string_ignore_case", "float"],

        "selection_mode": "single_selection",
        "sections_movable": False,

        "bind": {
            "show_fields": [
                # "$row.sku",
                "$row.supplier_name",
                ("$row.cost#currency or 'No Account'", {"halign": "right"}),
                # ("$row.wholesale_list#currency", {"halign": "right"}),
            ]
        },

        # "on_sort": "load_suppliers"
    },

    "g_pidb_mfg_combobox": {
        "clear": True,

        "bind": {
            "show": "name",
            "match": "id",
        }
    },

    "g_pidb_brand_combobox": {
        "clear": True,

        "bind": {
            "show": "name",
            "match": "id",
        }
    },

    "g_pidb_category_combobox": {
        "clear": True,

        "bind": {
            "show": "name",
            "match": "id"
        },

        "on_change": [
            ("if", "$g_pidb_category_combobox.selected#get('subcategories') != None",
             [
                 ("push", "$g_pidb_sub_category_combobox", "$g_pidb_category_combobox.selected#get('subcategories')"),
                 ("if", "$storage.pidb_product[0] != None",
                    ("push", "$g_pidb_sub_category_combobox.selected", "$storage.pidb_product[0]#get('subcategory')#get('id')"))
             ],
             [
                 ("push", "$g_pidb_sub_category_combobox", None)
             ])
        ]
    },

    "g_pidb_sub_category_combobox": {
        "clear": True,

        "bind": {
            "show": "name",
            "match": "id"
        }
    },

    "g_sync_button": {
        "on_release": [
            "sync_pos"
        ]
    },

    "g_selectallsync_checkBox": {
        "on_change": [
            "$sync_droplist.set_check('sync', $g_selectallsync_checkBox.data)"
        ]
    },

    "g_pidb_images_list": {
        "selection_type": "rows",
    },

    "g_sync_listWidget": {
        "selection_type": "rows",
    },

    # Advanced 
    "g_taxes_table": {
        "clear": True,

        "selection_mode": "single_selection",
        "sections_movable": False,

        "remote_sort": ["taxch_description", "taxrt_percent"],

        "default_sort_field": "taxch_description",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.taxch_description",
                "$row.taxrt_percent"
            ]
        },

        "on_row_select": ("change_state", "grp_taxes_command_buttons", {"default_state": "enabled"}),

        "on_selection_clear": ("change_state", "grp_taxes_command_buttons", {"default_state": "disabled"}),

        "on_double_click_row": ("link_to", "g_customizetaxes_button", "on_release"),

        "on_sort": ("transfer", "get_storetaxes")
    },

    "g_resettaxes_button": {
        "on_release": {
            "confirm": {
                "on_accept": ("transfer", "reset_taxes")
            }
        }
    },

    "g_customizetaxes_button": {
        "on_release": ("transfer", "customize_taxes")
    },

    "g_overridepercentage_button": {
        "on_release": [
            ("popup", "price", "$g_commoverride_lineedit.data",
             ("push", "$g_commoverride_lineedit", "$result or 0"), "Override", 250, 50)
        ]
    },

    "g_minstocklevel_lineedit": {
        "on_change": [
            ("if", "$g_minstocklevel_lineedit == ''",
                [("change_state", "g_maxstocklevel_lineedit", {"default_state": "disabled"}),
                 ("push", "$g_maxstocklevel_lineedit", "")],
                ("change_state", "g_maxstocklevel_lineedit", {"default_state": "enabled"}))
        ]
    },

    # Bar Codes group
    "g_barcodes_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "default_sort_field": "prodbar_barcode",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.prodbar_order",
                "$row.prodbar_barcode",
                "$row.prodbar_qty#quantity"
            ]
        },

        "on_row_select": [
            ("change_state", "grp_barcodes_command_buttons", {"default_state": "enabled"}),
            ("if", "$g_barcodes_table.all_selected#length == 1",
                [
                    ("push", "$g_addbarcode_button", "Make\nPrimary"),
                    ("change_state", "g_addbarcode_button", {"default_state": "enabled"}),
                ],
                [
                    ("change_state", "g_addbarcode_button", {"default_state": "disabled"}),
                    ("push", "$g_addbarcode_button", "Add")
                ])
        ],

        "on_selection_clear": [
            ("change_state", "grp_barcodes_command_buttons", {"default_state": "disabled"}),
            ("change_state", "g_addbarcode_button", {"default_state": "enabled"}),
            ("push", "$g_addbarcode_button", "Add")
        ],

        "on_double_click_row": [
            ("if", "$app.permissions.has_role(-7, -17) == False",
                ("link_to", "g_managebarcode_button", "on_release"))
        ]
    },

    "g_removebarcode_button": {
        "on_release": {
            "confirm": {
                "on_accept": ("transfer", "delete_product_barcode")
            }
        }
    },

    "g_managebarcode_button": {
        "on_release": ("transfer", "update_product_barcode")
    },

    "g_addbarcode_button": {
        "on_release": [
            ("if", "$g_addbarcode_button.data == 'Add'",
                ("transfer", "create_product_barcode"),
                [
                    "$g_barcodes_table.move_first($g_barcodes_table.all_selected[0])",
                    ("transfer", "make_primary_barcode")
                ])
        ]
    },

    # Attributes Tab
    "g_attributes_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["prodattr_name", "prodattr_value"],

        "default_sort_field": "prodattr_name",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.get('pan_attr_name', $row.get('prodattr_name'))",
                "$row.get('prodattr_value', '')"
            ]
        },

        "on_row_select": [
            ("change_state", "grp_attributes_command_buttons", {"default_state": "enabled"})
        ],
        "on_selection_clear": ("change_state", "grp_attributes_command_buttons", {"default_state": "disabled"}),
        "on_double_click_row": [
            ("if", "$app.permissions.has_role(-7,-17) == False",
                ("link_to", "g_manageattribute_button", "on_release"))
        ],

        "on_sort": ("transfer", "get_product_attributes")
    },

    "g_removeattribute_button": {
        "on_release": [
            ("if", "$g_attributes_table.selected.prodattr_name == 'Category' or $g_attributes_table.selected.prodattr_name == 'Sub-Category'",
                ("error_message", "Remove Category or Sub-Category is not allowed."),
                {"confirm": {"on_accept": ("transfer", "delete_product_attribute")}})
        ]
    },

    "g_manageattribute_button": {
        "on_release": [
            ("if", "$g_attributes_table.selected.prodattr_name == 'Category' or $g_attributes_table.selected.prodattr_name == 'Sub-Category'",
                ("error_message", "Remove Category or Sub-Category is not allowed."),
                ("transfer", "update_product_attribute"))
        ]
    },

    "g_addattribute_button": {
        "on_release": ("transfer", "create_product_attribute")
    },

    # Inventory
    "g_donotinventory_checkbox": {
        "on_change": [
            ("if", "$g_donotinventory_checkbox",
                [
                    ("change_state", "g_adjustinventory_button", {"default_state": "disabled"}),
                    ("if", "$storage.checking_inventory == False",
                        ("transfer", "check_inventory"))
                ],
                [
                    ("change_state", "g_adjustinventory_button", {"default_state": "enabled"})
                ]
            )
        ]
    },

    "show_inventory_popup": [

    ],

    "g_adjustinventory_button": {
        "on_release": ("transfer", "adjust_inventory")
    },

    "g_transferinventory_button": {
        "on_release": ("transfer", "transfer_inventory")
    },

    # Purchasing - Supplier Info
    "g_supplierinfo_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["splr_ent_name", "prlsplr_min_order_qty", "prlsplr_supplier_sku", "prlsplr_cost",
                        "prlsplr_last_updated_on", "loc_ent_name"],

        "default_sort_field": "splr_ent_name",
        "default_sort_order": "asc",

        "bind": {
            "columns": [
                ("splr_ent_name", {"default": "Supplier"}),
                ("prlsplr_min_order_qty", {"default": "Min Order Qty"}),
                ("prlsplr_supplier_sku", {"default": "Supplier Sku"}),
                ("prlsplr_cost", {"default": "Cost", "halign": "right"}),
                ("prlsplr_last_updated_on", {"default": "Last Update"}),
                ("loc_report_code", {"default": "Location", "view": ["corp"]})
            ],

            "show_fields": [
                "$row.splr_ent_name or ''",
                ("$row.prlsplr_min_order_qty#quantity", {"halign": "right"}),
                "$row.prlsplr_supplier_sku",
                ("$row.prlsplr_cost#currency", {"halign": "right"}),
                "$row.prlsplr_last_updated_on#date",
                "$row.loc_report_code"
            ],

            "show_rows": {
                "color": "$row.prlsplr_auto_order#switch('#007F00','#FF0000')"
            }
        },

        "on_row_select": [
            ("change_state", "grp_product_supplier_command_buttons", {"default_state": "enabled"}),
            ("if", "$storage.corp == True",
                [
                    "$g_addsupplierinfo_button.hide",
                    "$g_massactionsupplierinfo_button.show",
                ],
                "$g_addsupplierinfo_button.hide"),

            ("if", "$g_supplierinfo_table.all_selected.prlsplr_auto_order" +
                "#unite($g_supplierinfo_table.all_selected.prlsplr_auto_order)#length > 1",
                [("change_state", "g_autoordersupplierinfo_button", {"default_state": "disabled"})],
                [
                    ("change_state", "g_autoordersupplierinfo_button", {"default_state": "enabled"}),

                    ("change_state", "g_autoordersupplierinfo_button", {
                        "text": "($g_supplierinfo_table.all_selected.prlsplr_auto_order" +
                        "#unite($g_supplierinfo_table.all_selected.prlsplr_auto_order) == " +
                        "#list(True))#switch('Do Not \nAuto Order', 'Auto \nOrder')"
                    }),

                    ("push", "$storage.order_update_value", 
                        "($g_supplierinfo_table.all_selected.prlsplr_auto_order" +
                        "#unite($g_supplierinfo_table.all_selected.prlsplr_auto_order) == " +
                        "#list(True))#switch(False, True)")
                ]
            )
        ],

        "on_selection_clear": [
            ("change_state", "grp_product_supplier_command_buttons", {"default_state": "disabled"}),
            ("change_state", "g_autoordersupplierinfo_button", {"default_state": "disabled"}),
            "$g_addsupplierinfo_button.show",
            "$g_massactionsupplierinfo_button.hide",
        ],

        "on_double_click_row": [
            ("link_to", "g_managesupplierinfo_button", "on_release"),
            ("if", "$storage.corp == True",
                [
                    "$g_addsupplierinfo_button.show",
                    "$g_massactionsupplierinfo_button.hide",
                ],
                "$g_addsupplierinfo_button.show",),

        ],

        "on_sort": ("transfer", "get_product_suppliers")
    },

    "g_autoordersupplierinfo_button": {
        "on_release": ("transfer", "change_autoorder_status")
    },

    "g_removesupplierinfo_button": {
        "on_release": {
            "confirm": {
                "on_accept": ("transfer", "delete_product_suppliers")
            }
        }
    },

    "g_managesupplierinfo_button": {
        "on_release": ("transfer", "update_product_supplier")
    },

    "g_massactionsupplierinfo_button": {
        "on_release": [
            ("if", "$g_supplierinfo_table.all_selected.splr_ent_name" +
                "#unite($g_supplierinfo_table.all_selected.splr_ent_name)#length > 1",
                {"confirm": {
                    "text": "You have selected multiple suppliers to edit at the same time.\nAre you sure you want to continue.",
                    "on_accept":
                        [
                             ("transfer", "massaction_product_supplier")
                        ]
                }},
                ("transfer", "massaction_product_supplier"))
        ]
    },

    "g_addsupplierinfo_button": {
        "on_release": ("transfer", "create_product_supplier")
    },

    # Purchasing Option Corp
    "g_options_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["loc_report_code", "prlocs_min_safety_stock_days", "prlocs_max_safety_stock_days",
                        "prlocs_auto_order", "prlocs_min_location_qty"],

        "default_sort_field": "loc_report_code",
        "default_sort_order": "asc",

        "bind": {
            "columns": [
                ("loc_report_code", {"default": "Location"}),
                ("prlocs_auto_order", {"default": "Auto Order Product"}),
                ("prlocs_min_safety_stock_days", {"default": "Min Stock Days"}),
                ("prlocs_max_safety_stock_days", {"default": "Max Stock Days"}),
                ("prlocs_min_location_qty", {"default": "Min Stock Quantity"}),
                ("prlocs_max_location_qty", {"default": "Max Stock Quantity"})
            ],

            "show_fields": [
                "$row.loc_report_code",
                "$row.prlocs_auto_order or 'No'",
                "$row.prlocs_min_safety_stock_days#quantity or ''",
                "$row.prlocs_max_safety_stock_days#quantity or ''",
                "$row.prlocs_min_location_qty#quantity or ''",
                "$row.prlocs_max_location_qty#quantity or ''"
            ]
        },

        "on_row_select": [
            ("change_state", "g_manageoptions_button", {"default_state": "enabled"}),
            ("if", "$g_options_table.all_selected#length > 1",
                ("change_state", "g_massactionoptions_button", {"default_state": "enabled"}),
                ("change_state", "g_massactionoptions_button", {"default_state": "disabled"})),
        ],

        "on_selection_clear": [
            ("change_state", "g_manageoptions_button", {"default_state": "disabled"}),
            ("change_state", "g_massactionoptions_button", {"default_state": "disabled"}),
        ],

        "on_double_click_row": [
            ("link_to", "g_manageoptions_button", "on_release"),
        ],

        "on_sort": ("transfer", "get_product_options")
    },

    "g_manageoptions_button": {
        "on_release": ("transfer", "update_product_options")
    },

    "g_massactionoptions_button": {
        "on_release": ("transfer", "massaction_product_options")
    },

    # Purchasing Seasonal Corp
    "g_seasonal_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["loc_report_code", "prlocs_product_is_seasonal", "prlocs_jan_is_seasonal", "prlocs_feb_is_seasonal",
                        "prlocs_mar_is_seasonal", "prlocs_apr_is_seasonal", "prlocs_may_is_seasonal",
                        "prlocs_jun_is_seasonal", "prlocs_jul_is_seasonal", "prlocs_aug_is_seasonal",
                        "prlocs_sep_is_seasonal", "prlocs_oct_is_seasonal", "prlocs_nov_is_seasonal",
                        "prlocs_dec_is_seasonal"],

        "default_sort_field": "loc_report_code",
        "default_sort_order": "asc",

        "bind": {
            "columns": [
                ("loc_report_code", {"default": "Location"}),
                ("prlocs_product_is_seasonal", {"default": "Is Seasonal"}),
                ("prlocs_jan_is_seasonal", {"default": "Jan"}),
                ("prlocs_feb_is_seasonal", {"default": "Feb"}),
                ("prlocs_mar_is_seasonal", {"default": "Mar"}),
                ("prlocs_apr_is_seasonal", {"default": "Apr"}),
                ("prlocs_may_is_seasonal", {"default": "May"}),
                ("prlocs_jun_is_seasonal", {"default": "Jun"}),
                ("prlocs_jul_is_seasonal", {"default": "Jul"}),
                ("prlocs_aug_is_seasonal", {"default": "Aug"}),
                ("prlocs_sep_is_seasonal", {"default": "Sep"}),
                ("prlocs_oct_is_seasonal", {"default": "Oct"}),
                ("prlocs_nov_is_seasonal", {"default": "Nov"}),
                ("prlocs_dec_is_seasonal", {"default": "Dec"}),
            ],

            "show_fields": [
                "$row.loc_report_code",
                "$row.prlocs_product_is_seasonal#switch('Yes', 'No')",
                "$row.prlocs_jan_is_seasonal#switch('Yes', 'No')",
                "$row.prlocs_feb_is_seasonal#switch('Yes', 'No')",
                "$row.prlocs_mar_is_seasonal#switch('Yes', 'No')",
                "$row.prlocs_apr_is_seasonal#switch('Yes', 'No')",
                "$row.prlocs_may_is_seasonal#switch('Yes', 'No')",
                "$row.prlocs_jun_is_seasonal#switch('Yes', 'No')",
                "$row.prlocs_jul_is_seasonal#switch('Yes', 'No')",
                "$row.prlocs_aug_is_seasonal#switch('Yes', 'No')",
                "$row.prlocs_sep_is_seasonal#switch('Yes', 'No')",
                "$row.prlocs_oct_is_seasonal#switch('Yes', 'No')",
                "$row.prlocs_nov_is_seasonal#switch('Yes', 'No')",
                "$row.prlocs_dec_is_seasonal#switch('Yes', 'No')",
            ]
        },

        "on_row_select": [
            ("change_state", "g_manageseasonal_button", {"default_state": "enabled"}),
            ("if", "$g_seasonal_table.all_selected#length > 1",
                ("change_state", "g_massactionseasonal_button", {"default_state": "enabled"}),
                ("change_state", "g_massactionseasonal_button", {"default_state": "disabled"})),
        ],

        "on_selection_clear": [
            ("change_state", "g_manageseasonal_button", {"default_state": "disabled"}),
            ("change_state", "g_massactionseasonal_button", {"default_state": "disabled"}),
        ],

        "on_double_click_row": [
            ("link_to", "g_manageseasonal_button", "on_release"),
        ],

        "on_sort": ("transfer", "get_product_seasonal")
    },

    "g_manageseasonal_button": {
        "on_release": ("transfer", "update_product_seasonal")
    },

    "g_massactionseasonal_button": {
        "on_release": ("transfer", "massaction_product_seasonal")
    },

    # Print labels
    "g_printshelflabel_button": {
        "on_release": [
            ("if", "$storage.product_price#float != $g_price_lineedit.data#float",
                {"confirm": {
                    "text": "Price has changed. Save and Continue?",
                    "on_accept": [
                        ("transfer", "update_product", ["enable_buttons", "print_shelf_label"])]}},
             "print_shelf_label")
        ]
    },

    "g_printproductlabel_button": {
        "on_release": [
            ("if", "$storage.product_price#float != $g_price_lineedit.data#float",
                {"confirm": {
                    "text": "Price has changed. Save and Continue?",
                    "on_accept": [
                        ("transfer", "update_product", ["enable_buttons", "print_product_label"])]}},
                "print_product_label")
        ]
    },

    "g_autoorder_checkbox": {
        "on_change": ("if", "$g_autoorder_checkbox",
            "$g_autoorder_label.hide",
            "$g_autoorder_label.show")
    },

    # Pricing
    "g_question_label": {
        "tooltip": "The current cost * markup for this product would result in a X.XX price.\n" +
                   "Your price is different because the cost has dropped since the price was set.\n" +
                   "To review prior cost and price changes, go to the Price Review section.\n"
                   "To force the price to recalculate, click on the recycle icon next to markup."
    },

    "g_recycle_label": {
        "on_release": [
            ("push", "$storage.recycle", True),
            "update_pricing",
        ]
    },

    "g_price_radioButton": {
        "on_change": [
            ("if", "$storage.loading == False",
                ("if", "$g_price_radioButton.data == True",
                    [
                        "$g_markup_lineedit.set_enabled(False)",
                        ("if", "$app.permissions.has_permission_other(2, -1)",  # Modify Cost
                            "$g_price_lineedit.set_enabled(True)"),
                        "$g_markup_radioButton.uncheck",
                        "$g_recycle_label.hide",
                        "$g_question_label.hide",
                        ("if", "$storage.loading == False",
                            "update_pricing")
                    ]))
        ]
    },

    "g_markup_radioButton": {
        "on_change": [
            ("if", "$storage.loading == False",
                ("if", "$g_markup_radioButton.data == True",
                    [
                        "$g_price_lineedit.set_enabled(False)",
                        ("if", "$app.permissions.has_permission_other(2, -1)",  # Modify Cost
                            [
                                "$g_recycle_label.show",
                                "$g_question_label.show"
                            ],
                            [
                                "$g_recycle_label.hide",
                                "$g_question_label.hide"
                            ]),
                        "$g_markup_lineedit.set_enabled(False)",
                        ("if", "$app.permissions.has_permission_other(2, -1)",  # Modify Cost
                            "$g_markup_lineedit.set_enabled(True)"),
                        "$g_price_radioButton.uncheck",
                        ("if", "$storage.loading == False",
                            "update_pricing")
                    ]))
        ]
    },

    "g_cost_lineedit": {
        "on_change": [
            ("if", "$storage.loading == False",
                [
                    ("if", "$g_price_radioButton.data == True",
                        ("push", "$storage.recycle", True)),
                    "update_pricing"
                ])
        ]
    },

    "g_price_lineedit": {
        "on_change": [
            ("if", "$storage.loading == False",
                ("if", "$storage.checkprice == False",
                    [
                        ("push", "$storage.recycle", True),
                        "update_pricing"
                    ]))
        ]
    },

    "g_markup_lineedit": {
        "on_change": [
            ("if", "$storage.loading == False",
                [
                    ("push", "$storage.recycle", True),
                    # ("push", "$storage.markups_increase", "False"),
                    "update_pricing"
                ])
        ]
    },

    "g_asksaleprice_checkbox": {
        "on_change": [
            ("if", "$g_asksaleprice_checkbox",
             [
                 "$g_askforsaleprice_label.show",
                 "$g_pricelastsaledate_frame.hide",
                 "$g_price_lineedit.hide",
                 "$g_price_label.hide",
                 "$g_markup_label.hide",
                 "$g_price_radioButton.hide",
                 "$g_question_label.hide",
             ],
             [
                 "$g_askforsaleprice_label.hide",
                 "$g_pricelastsaledate_frame.show",
                 "$g_price_lineedit.show",
                 "$g_price_label.show",
                 "$g_markup_label.show",
                 "$g_price_radioButton.show",
                 "$g_question_label.show",
             ])
        ]
    },

    # Purchasing - Seasonal 
    "g_isseasonal_checkbox": {
        "on_change": [
            ("if", "$g_isseasonal_checkbox.data == True",
                "$g_seasonalcalendar_frame.show",
                "$g_seasonalcalendar_frame.hide")
        ]
    },

    # Open Orders
    "g_openorders_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": True,

        "remote_sort": ["txn_type_number", "txn_opened_on", "ent_name", "txnline_qty",
                        "txnline_qty_received", "txnline_expected_cost"],

        "default_sort_field": "txn_type_number",
        "default_sort_order": "asc",

        "bind": { 
            "show_fields": [
                "$row.txn_type_number",
                "$row.txn_opened_on#date",
                "$row.ent_name",
                "$row.txnline_qty#int",
                "$row.txnline_qty_received#int",
                "$row.txnline_expected_cost#currency",
                # "$row.txnline_amount#currency or 0.00",  # Price
                "$row.txn_status or ''" 
            ]
        },

        "on_sort": "load_openorders"
    },

    # History - Movements
    "g_movements_table": {
        "clear": True,

        "selection_mode": "single_selection",
        "sections_movable": False,

        "bind": {"show_fields": []}, 

        "on_row_select": [],

        "on_double_click_row": [],

        "on_sort": "load_product_movements"
    },

    "g_movements_sumbyperiod_combbox": {
        "blank": False,
        "clear": True,
        "bind": {
            "show": "name",
            "match": "value",
        },

        "on_change": "load_product_movements"
    },

    # History - Sales 
    "g_sales_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "bind": {"show_fields": []},

        "on_row_select": [
            ("change_state", "g_massupdatesalescost_button", {"default_state": "enabled"}),
        ],

        "on_selection_clear": [
            ("change_state", "g_massupdatesalescost_button", {"default_state": "disabled"}),
        ],

        "on_double_click_row": [
        ],

        "on_sort": "load_product_sales"
    },

    "g_massupdatesalescost_button": {
        "on_release": [
            ("push", "$storage.cost", "0.00"),
            ("popup", "price", "$storage.cost#currency",
             [
                 ("push", "$storage.updated_sales_history_cost", "$result"),
                 ("transfer", "mass_update_sales_history_cost")
             ], "Update Cost", 0, 99999)
        ]
    },

    "g_sales_type_combbox": {
        "blank": False,
        "clear": True,
        "bind": {
            "show": "name",
            "match": "value",
        },

        "on_change": "load_product_sales"
    },

    "g_producttype_combobox": {
        "blank": False,
        "clear": True,
        "bind": {
            "show": "name",
            "match": "value",
        }
    },

    "g_sales_sumbyperiod_combbox": {
        "blank": False,
        "clear": True,
        "bind": {
            "show": "name",
            "match": "value",
        },

        "on_change": "load_product_sales"
    },

    # History - Product Updates
    "g_productupdates_table": {
        "clear": True,

        "selection_mode": "single_selection",
        "sections_movable": False,

        "remote_sort": ["prlsplr_min_order_qty", "prlsplr_supplier_sku", "prlsplr_cost",
                        "inv_changed_on_max", "loc_report_code"],

        "default_sort_field": "prlsplr_min_order_qty",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.prlsplr_min_order_qty#quantity",
                "$row.prlsplr_supplier_sku",
                "$row.prlsplr_cost#currency",
                "$row.inv_changed_on_max#date",
                "$row.loc_report_code"
            ]
        },

        "on_row_select": [
        ],

        "on_double_click_row": [
        ],

        "on_sort": ("transfer", "get_product_updates")
    },

    "g_productupdates_type_combobox": {
        "clear": True,
        "editable": True,

        "bind": {
            "show": "mfgbr_brand_name",
            "match": "mfg_brand_id",
        },

        "on_change": ("transfer", "get_product_updates")
    },

    # History - Order
    "g_orders_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["inv_changed_date", "txnline_qty_received", "txnline_amount", "loc_report_code"],

        "default_sort_field": "inv_changed_date",
        "default_sort_order": "desc",

        "bind": {
            "columns": [
                ("txn_closed_date", {"default": "Date"}),
                # ("txn_type_number", {"default": "Number"}),
                ("txnline_qty_received", {"default": "Movement Qty", "halign": "right"}),
                ("txnline_amount", {"default": "Cost", "halign": "right"}),
                ("loc_report_code", {"default": "Location"})
            ],

            "show_fields": [
                "$row.txn_closed_date#date",
                # "$row.txn_type_number",
                ("$row.txnline_qty_received#quantity", {"halign": "right"}),
                ("$row.txnline_amount#currency(2,0) or '0.00'",
                 {"halign": "right", "on_link_click": "update_order_history_cost"}),
                "$row.loc_report_code"
            ]
        },

        "on_row_select": [
            ("change_state", "g_massupdateorders_button", {"default_state": "enabled"}),
        ],

        "on_selection_clear": [
            ("change_state", "g_massupdateorders_button", {"default_state": "disabled"}),
        ],

        "on_double_click_row": [
        ],

        "on_sort": ("transfer", "get_product_history_orders")
    },

    "g_massupdateorders_button": {
        "on_release": [
            ("push", "$storage.cost", "0.00"),
            ("popup", "price", "$storage.cost#currency",
             [
                 ("push", "$storage.update_products_history_orders_cost", "$result"),
                 ("transfer", "mass_products_history_orders_cost")
             ], "Update Cost", 0, 99999)
        ]
    },

    # History - Transfers
    "g_transfers_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["txn_closed_date", "txnline_qty", "txnline_amount", "src_loc_report_code",
                        "dst_loc_report_code"],

        "default_sort_field": "txn_closed_date",
        "default_sort_order": "desc",

        "bind": {
            "columns": [
                ("txn_closed_on", {"default": "Date"}),
                # ("transaction_id", {"default": "Number"}),
                ("txnline_qty", {"default": "Movement Qty", "halign": "right"}),
                ("txnline_amount", {"default": "Cost", "halign": "right"}),
                ("src_loc_report_code", {"default": "Send Location"}),
                ("dst_loc_report_code", {"default": "Receive Location"}),
            ],

            "show_fields": [
                "$row.txn_closed_on#datetime.strftime('%Y-%m-%d %H:%M')",
                ("$row.txnline_qty#quantity", {"halign": "right"}),
                ("$row.txnline_amount#currency(2,0) or '0.00'",
                 {"halign": "right", "on_link_click": "update_transfers_history_cost"}),
                "$row.src_loc_report_code",
                "$row.dst_loc_report_code"
            ]
        },

        "on_row_select": [
            ("change_state", "g_massupdatetransfers_button", {"default_state": "enabled"}),
        ],

        "on_selection_clear": [
            ("change_state", "g_massupdatetransfers_button", {"default_state": "disabled"}),
        ],

        "on_double_click_row": [
        ],

        "on_sort": ("transfer", "get_products_history_transfers")
    },

    "g_massupdatetransfers_button": {
        "on_release": [
            ("push", "$storage.cost", "0.00"),
            ("popup", "price", "$storage.cost#currency",
             [

                 ("push", "$storage.updated_sales_history_transfers_cost", "$result"),
                 ("transfer", "mass_update_product_history_transfers_cost")
             ], "Update Cost", 0, 99999)
        ]
    },

    # History - Inventory Adjustments
    "g_inventoryadjustments_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["inv_changed_date", "txn_type_number", "txnline_qty", "txnline_amount", "txn_reason",
                        "loc_report_code"],

        "default_sort_field": "txn_closed_on",
        "default_sort_order": "desc",

        "bind": {
            "columns": [
                ("txn_closed_on", {"default": "Date"}),
                ("txn_type_number", {"default": "Number"}),
                ("txnline_qty", {"default": "Movement Qty", "halign": "right"}),
                ("txnline_amount", {"default": "Cost", "halign": "right"}),
                ("txn_reason", {"default": "Reason"}),
                ("loc_report_code", {"default": "Location"})
            ],

            "show_fields": [
                "$row.txn_closed_on#datetime.strftime('%Y-%m-%d %H:%M')",
                "$row.txn_type_number",
                ("$row.txnline_qty#quantity", {"halign": "right"}),
                ("$row.txnline_amount#currency(2,0) or '0.00'",
                 {"halign": "right", "on_link_click": "update_invadj_history_cost"}),
                "$row.txn_reason",
                "$row.loc_report_code"
            ]
        },

        "on_row_select": [
            ("change_state", "g_massupdateinvadjustments_button", {"default_state": "enabled"}),
        ],

        "on_selection_clear": [
            ("change_state", "g_massupdateinvadjustments_button", {"default_state": "disabled"}),
        ],

        "on_double_click_row": [
        ],

        "on_sort": ("transfer", "get_products_history_inventory_adjustments")
    },

    "g_massupdateinvadjustments_button": {
        "on_release": [
            ("push", "$storage.cost", "0.00"),
            ("popup", "price", "$storage.cost#currency",
             [
                 ("push", "$storage.update_invadj_history_cost", "$result"),
                 ("transfer", "mass_invadj_history_cost")
             ], "Update Cost", 0, 99999)
        ],
    },

    # History Purchases
    "g_purchases_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["txn_type_number", "txn_opened_on", "txn_closed_on", "txnline_qty_ordered", "txnline_qty_received",
                        "txnline_qty_variance", "txnline_amount", "txnline_amount_extended", "ent_name", "loc_report_code"],

        "default_sort_field": "txnline_posted_on_ordered",
        "default_sort_order": "asc",

        "bind": {
            "columns": [
                ("txn_type_number", {"default": "Invoice #", "halign": "left"}),
                ("txn_opened_on", {"default": "Date Purchased", "halign": "right"}),
                ("txn_closed_on", {"default": "Date Received", "halign": "right"}),
                ("txnline_qty_ordered", {"default": "Ordered Qty", "halign": "right"}),
                ("txnline_qty_received", {"default": "Received Qty", "halign": "right"}),
                ("txnline_qty_variance", {"default": "Variance", "halign": "right"}),
                ("txnline_amount", {"default": "Cost", "halign": "right"}),
                ("txnline_amount_extended", {"default": "Extended Cost", "halign": "right"}),
                ("ent_name", {"default": "Supplier Name"}),
                ("loc_report_code or ''", {"default": "Location"})
            ],

            "show_fields": [
                ("$row.txn_type_number or ''", {"halign": "left"}),
                ("$row.txn_opened_on#datetime.strftime('%Y-%m-%d %H:%M')", {"halign": "right"}),
                ("$row.txn_closed_on#datetime.strftime('%Y-%m-%d %H:%M')", {"halign": "right"}),
                ("$row.txnline_qty_ordered#quantity", {"halign": "right"}),
                ("$row.txnline_qty_received#quantity", {"halign": "right"}),
                ("$row.txnline_qty_variance#quantity", {"halign": "right"}),
                ("$row.txnline_amount#currency(2,0) or '0.00'", {"halign": "right", "on_link_click": "update_purchase_history_cost"}),
                ("$row.txnline_amount_extended#currency", {"halign": "right"}),
                "$row.ent_name",
                "$row.loc_report_code"
            ]
        },

        "on_row_select": [
            ("change_state", "g_massupdatepurchasecost_button", {"default_state": "enabled"}),
            ("change_state", "g_viewpo_button", {"default_state": "enabled"}),
        ],
        "on_selection_clear": [
            ("change_state", "g_massupdatepurchasecost_button", {"default_state": "disabled"}),
            ("change_state", "g_viewpo_button", {"default_state": "disabled"}),
        ],

        "on_double_click_row": ("link_to", "g_viewpo_button", "on_release"),

        "on_sort": ("transfer", "get_purchases_by_date")
    },

    "g_viewpo_button": {
        "on_release": [
            ("transfer", "view_receiving_order_form")
        ]
    },

    "g_massupdatepurchasecost_button": {
        "on_release": [
            ("push", "$storage.cost", "0.00"),
            ("popup", "price", "$storage.cost#currency",
             [
                 ("push", "$storage.update_purchase_history_cost", "$result"),
                 ("transfer", "mass_update_purchase_history_cost")
             ], "Update Cost", 0, 99999)
        ]
    },

    # Open Invoices
    "g_openinvoices_table": {
        "clear": True,

        "selection_mode": "single_selection",
        "sections_movable": True,

        "remote_sort": ["txn_type", "txn_type_number", "txn_opened_on",
                        "customer_full_name", "txn_subtotal", "txn_grand_total"],

        "default_sort_field": "txn_opened_on",
        "default_sort_order": "desc",

        "bind": {
            "show_fields": [
                "$row.txn_type",
                "$row.txn_type_number",
                "$row.txn_opened_on#datetime.strftime('%Y-%m-%d %H:%M:%S')",
                "$row.combined_person_name or ''",
                ("$row.txn_subtotal#currency or '0.00'", {"halign": "right"}),
                ("$row.txn_grand_total#currency or '0.00'", {"halign": "right"}),
            ],
        },
        "on_sort": "get_openInvoices"
    },

    # Bundles
    "g_simplebundle_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["splr_ent_name", "prlsplr_min_order_qty", "prlsplr_supplier_sku", "prlsplr_cost"],

        "default_sort_field": "splr_ent_name",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.splr_ent_name",
                "$row.prlsplr_min_order_qty#quantity",
                "$row.prlsplr_supplier_sku",
                "$row.prlsplr_cost#currency"
            ]
        },

        "on_row_select": ("change_state", "grp_product_simplebundle_command_buttons", {"default_state": "enabled"}),

        "on_double_click_row": [
            ("if", "$app.permissions.has_role(-17) == False",
                ("link_to", "g_managesimplebundle_button", "on_release"))
        ],

        "on_sort": ("transfer", "get_product_simplebundle")
    },

    "g_advancedbundle_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,
        
        "remote_sort": ["splr_ent_name", "prlsplr_min_order_qty", "prlsplr_supplier_sku", "prlsplr_cost"],

        "default_sort_field": "splr_ent_name",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.splr_ent_name",
                "$row.prlsplr_min_order_qty#quantity",
                "$row.prlsplr_supplier_sku",
                "$row.prlsplr_cost#currency"
            ]
        },

        "on_row_select": ("change_state", "grp_product_advancebundle_command_buttons", {"default_state": "enabled"}),

        "on_double_click_row": [
            ("if", "$app.permissions.has_role(-17) == False",
                ("link_to", "g_manageadvancedbundle_button", "on_release"))
        ],

        "on_sort": ("transfer", "get_product_advancebundle")
    },

    "g_managesimplebundle_button": {
        "on_release": ("transfer", "manage_simplebungle_inventory")
    }, 

    "g_manageadvancedbundle_button": {
        "on_release": ("transfer", "manage_advancedbungle_inventory")
    },

    # Up-Sell
    "g_upsell_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["product_id", "prod_name"],

        "default_sort_field": "product_id",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.product_id",
                "$row.prod_name"
            ]
        },

        "on_row_select": ("change_state", "g_deleteupsell_button", {"default_state": "enabled"}),

        "on_selection_clear": ("change_state", "g_deleteupsell_button", {"default_state": "disabled"}),

        "on_sort": ("transfer", "get_products_up_sell")
    },

    "g_addupsell_button": {
        "on_release": [
            ("transfer", "add_product_to_upsell")
        ]
    },

    "g_deleteupsell_button": {
        "on_release": [
            {"confirm": {
                "text": "Delete selected product(s) from Up-Sell. Continue?",
                "on_accept": ("$g_upsell_table.remove_rows", "$g_upsell_table.all_selected"),
            }}
        ]
    },

    # Cross-Sell
    "g_crosssell_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["product_id", "prod_name"],

        "default_sort_field": "product_id",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.product_id",
                "$row.prod_name"
            ]
        },

        "on_row_select": ("change_state", "g_deletecrosssell_button", {"default_state": "enabled"}),

        "on_selection_clear": ("change_state", "g_deletecrosssell_button", {"default_state": "disabled"}),

        "on_sort": ("transfer", "get_products_to_crosssell")
    },

    "g_addcrosssell_button": {
        "on_release": [
            ("transfer", "add_product_to_crosssell")
        ]
    },

    "g_deletecrosssell_button": {
        "on_release": [
            {"confirm": {
                "text": "Delete selected product(s) from Cross-Sell. Continue?",
                "on_accept": ("$g_crosssell_table.remove_rows", "$g_crosssell_table.all_selected"),
            }}
        ]
    },

    # Alternative
    "g_alternative_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["product_id", "prod_name"],

        "default_sort_field": "product_id",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.product_id",
                "$row.prod_name"
            ]
        },

        "on_row_select": ("change_state", "g_deletealternative_button", {"default_state": "enabled"}),

        "on_selection_clear": ("change_state", "g_deletealternative_button", {"default_state": "disabled"}),

        "on_sort": ("transfer", "get_products_alternative")
    },

    "g_addalternative_button": {
        "on_release": [
            ("transfer", "add_product_to_alternative")
        ]
    },

    "g_deletealternative_button": {
        "on_release": [
            {"confirm": {
                "text": "Delete selected product(s) from Alternatives. Continue?",
                "on_accept": ("$g_alternative_table.remove_rows", "$g_alternative_table.all_selected"),
            }}
        ]
    },

    # Replacement Product
    "g_replacementprod_select_button": {
        "on_release": [
            ("transfer", "select_replacement_product")
        ]
    },

    # Customer History
    "g_customerhistory_tableView": {
        "selection_mode": "multi_selection",
        "default_sort_field": "txn_closed_on",
        "default_sort_order": "desc",
        "per_page": 25,

        "bind": {
            "primary_id": "transaction_id",
            "required_hidden_fields": [],
            "required_fields": ["transaction_id", "txn_type_number", "transaction_line_id"],
            "no_export": ["transaction_id", "transaction_line_id"],

            "column_order": ["txn_ent_name", "txnline_product_description", "txnline_qty",
                             "txn_closed_on", "txn_type_number", "txn_type", "txnline_amount", "txnline_net", "loc_report_code"],

            "columns": [
                {"value": "txn_ent_name", "name": "Customer"},
                {"value": "txnline_product_description", "name": "Description"},
                {"value": "txnline_qty", "name": "Qty", "halign": "right"},
                {"value": "txn_closed_on", "name": "Invoice Date"},
                {"value": "txn_type_number", "name": "Invoice #"},
                {"value": "txn_type", "name": "Invoice Type"},
                {"value": "txnline_amount", "name": "Price", "halign": "right"},
                {"value": "txnline_net", "name": "Net", "halign": "right"},
                {"value": "txn_addr_address_1", "name": "Address 1"},
                {"value": "txn_addr_address_2", "name": "Address 2"},
                {"value": "txn_addr_city", "name": "City"},
                {"value": "txn_prgn_iso_code", "name": "State"},
                {"value": "txn_addr_postal_code", "name": "Zip"},
                {"value": "txn_cnum_number", "name": "Phone"},
                {"value": "txn_eml_email", "name": "Email"},
                {"value": "product_id", "name": "Sku", "halign": "right"},
                {"value": "loc_report_code", "name": "Location"},
                {"value": "txnline_sale_cost", "name": "Cost", "halign": "right"},

            ],

            "show_fields": [
                {"value": "$row.txn_ent_name or ''"},
                {"value": "$row.txnline_product_description or ''"},
                {"value": "$row.txnline_qty", "halign": "right"},
                {"value": "$row.txn_closed_on#datetime.strftime('%Y/%m/%d %H%M')"},
                {"value": "$row.txn_type_number", "halign": "right"},
                {"value": "$row.txn_type"},
                {"value": "$row.txnline_amount#currency or '0.00'"},
                {"value": "$row.txnline_net#currency or '0.00'"},

                {"value": "$row.txn_addr_address_1 or ''"},
                {"value": "$row.txn_addr_address_2 or ''"},
                {"value": "$row.txn_addr_city or ''"},
                {"value": "$row.txn_prgn_iso_code or ''"},
                {"value": "$row.txn_addr_postal_code or ''"},
                {"value": "$row.txn_cnum_number or ''"},
                {"value": "$row.txn_eml_email or ''"},
                {"value": "$row.product_id or ''", "halign": "right"},
                {"value": "$row.loc_report_code"},
                {"value": "$row.txnline_sale_cost#currency(2,0) or '0.00'", "halign": "right"},
            ]
        },

        "on_column_move": [
            ("if", "$storage.customerhistory_user_setting_id",
                ("transfer", "update_customerhistory_table_settings"))
        ],

        "on_row_select": [
            "get_page_results",
        ],

        "on_selection_clear": [
            "get_page_results",
        ],

        "on_sort": [
            ("if", "$storage.customerhistory_user_setting_id",
                ("transfer", "update_customerhistory_table_settings", ("transfer", "get_customerhistory")),
                ("transfer", "get_customerhistory"))
        ],

        "load_table": "get_customerhistory"
    },

    "g_customerhistory_tablesettings_label": {
        "on_release": [
            ("transfer", "update_customerhistory_settings")
        ]
    },

    "g_customerhistory_export_label": {
        "on_release": [
            ("transfer", "select_customerhistory_export"),
        ]
    },

    # Command Buttons
    "g_cancel_button": {
        "on_release": [
            ("push", "$storage.sender", "Cancel"),
            "cancel_product"
        ]
    },

    "g_save_button": {
        "on_release": [
            ("push", "$storage.sender", "Save"),
            "check_save_image"
        ]
    }
}
