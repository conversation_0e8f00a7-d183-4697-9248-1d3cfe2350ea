# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'selectsupplier_popup.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_SelectSupplier(object):
    def setupUi(self, SelectSupplier):
        SelectSupplier.setObjectName("SelectSupplier")
        SelectSupplier.resize(563, 480)
        SelectSupplier.setMinimumSize(QtCore.QSize(0, 480))
        SelectSupplier.setMaximumSize(QtCore.QSize(16777215, 16777215))
        SelectSupplier.setStyleSheet("#SelectSupplier {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_selectsupplier_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border:1px solid #073c83;\n"
"    border-radius:10px;\n"
"    background-color:#DDE4E9;\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
".QScrollArea {\n"
"    background-color: #f4f4f4;\n"
"    border-radius:5px;\n"
"    border:1px solid #073c83;\n"
"    padding:4px;\n"
"}\n"
"\n"
"QScrollBar:horizontal {\n"
"height:0px;\n"
"}\n"
"\n"
"#scrollAreaWidgetContents {\n"
"background-color: #f4f4f4;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"#g_tabs QTabWidget::tab-bar {\n"
"     left: 5px; \n"
" }\n"
"\n"
"#g_managelitter_tabs, #__g_managelitter_tabs QTabBar::tab,\n"
"#g_registries_tabs, #__g_registries_tabs QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"#g_managelitter_tabs, #__g_managelitter_tabs QTabBar::tab:selected, QTabBar::tab:hover,\n"
"#g_registries_tabs, #__g_registries_tabs QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"#g_managelitter_tabs, #__g_managelitter_tabs QTabBar::tab:selected,\n"
"#g_registries_tabs, #__g_registries_tabs QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"#g_cancel_button, #__g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button, #__g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_select_button, #__g_select_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_select_button:pressed, #__g_select_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button, #__g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed, #__g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_search_frame, #__g_search_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_search_box_frame, #__g_search_box_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_clearsearch_button, #__g_clearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_search_lineedit, #__g_search_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_search_combobox, #__g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_search_combobox::drop-down, #__g_search_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"#g_searchtext_lineedit, #__g_searchtext_lineedit {\n"
"    border: 0px;\n"
"}\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(SelectSupplier)
        self.verticalLayout_2.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_progress_frame = QtWidgets.QFrame(SelectSupplier)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.verticalLayout_2.addWidget(self.g_progress_frame)
        self.g_selectsupplier_frame = QtWidgets.QFrame(SelectSupplier)
        self.g_selectsupplier_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_selectsupplier_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_selectsupplier_frame.setObjectName("g_selectsupplier_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_selectsupplier_frame)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_main_label = QtWidgets.QLabel(self.g_selectsupplier_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setStyleSheet("")
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        self.g_search_frame = QtWidgets.QFrame(self.g_selectsupplier_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_search_frame.sizePolicy().hasHeightForWidth())
        self.g_search_frame.setSizePolicy(sizePolicy)
        self.g_search_frame.setMinimumSize(QtCore.QSize(200, 41))
        self.g_search_frame.setMaximumSize(QtCore.QSize(166546, 59))
        self.g_search_frame.setStyleSheet("")
        self.g_search_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_search_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_search_frame.setObjectName("g_search_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_search_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(14)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_search_combobox = QtWidgets.QComboBox(self.g_search_frame)
        self.g_search_combobox.setMaximumSize(QtCore.QSize(30, 31))
        self.g_search_combobox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_search_combobox.setStyleSheet("")
        self.g_search_combobox.setFrame(False)
        self.g_search_combobox.setObjectName("g_search_combobox")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.horizontalLayout_6.addWidget(self.g_search_combobox)
        self.__g_search_box_frame = QtWidgets.QFrame(self.g_search_frame)
        self.__g_search_box_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.__g_search_box_frame.setStyleSheet("")
        self.__g_search_box_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_search_box_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_search_box_frame.setObjectName("__g_search_box_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.__g_search_box_frame)
        self.horizontalLayout_5.setContentsMargins(2, 0, 5, 0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_searchtext_lineedit = QtWidgets.QLineEdit(self.__g_search_box_frame)
        self.g_searchtext_lineedit.setStyleSheet("")
        self.g_searchtext_lineedit.setText("")
        self.g_searchtext_lineedit.setObjectName("g_searchtext_lineedit")
        self.horizontalLayout_5.addWidget(self.g_searchtext_lineedit)
        self.g_clearsearch_button = QtWidgets.QPushButton(self.__g_search_box_frame)
        self.g_clearsearch_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_clearsearch_button.setStyleSheet("")
        self.g_clearsearch_button.setText("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/clear"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_clearsearch_button.setIcon(icon)
        self.g_clearsearch_button.setIconSize(QtCore.QSize(18, 18))
        self.g_clearsearch_button.setObjectName("g_clearsearch_button")
        self.horizontalLayout_5.addWidget(self.g_clearsearch_button)
        self.horizontalLayout_6.addWidget(self.__g_search_box_frame)
        self.verticalLayout.addWidget(self.g_search_frame)
        spacerItem = QtWidgets.QSpacerItem(20, 5, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout.addItem(spacerItem)
        self.g_selectsupplier_table = QtWidgets.QTableWidget(self.g_selectsupplier_frame)
        self.g_selectsupplier_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_selectsupplier_table.setAlternatingRowColors(True)
        self.g_selectsupplier_table.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.g_selectsupplier_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_selectsupplier_table.setShowGrid(False)
        self.g_selectsupplier_table.setObjectName("g_selectsupplier_table")
        self.g_selectsupplier_table.setColumnCount(3)
        self.g_selectsupplier_table.setRowCount(14)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setVerticalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_selectsupplier_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_selectsupplier_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_selectsupplier_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(2, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(3, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(4, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectsupplier_table.setItem(4, 2, item)
        self.g_selectsupplier_table.horizontalHeader().setVisible(True)
        self.g_selectsupplier_table.horizontalHeader().setDefaultSectionSize(130)
        self.g_selectsupplier_table.horizontalHeader().setStretchLastSection(True)
        self.g_selectsupplier_table.verticalHeader().setVisible(False)
        self.verticalLayout.addWidget(self.g_selectsupplier_table)
        self.g_bottombar_frame = QtWidgets.QFrame(self.g_selectsupplier_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.g_bottombar_frame.setSizePolicy(sizePolicy)
        self.g_bottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_bottombar_frame.setStyleSheet("")
        self.g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottombar_frame.setObjectName("g_bottombar_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_bottombar_frame)
        self.horizontalLayout.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_controlbuttons_frame = QtWidgets.QFrame(self.g_bottombar_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem1)
        self.g_manage_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_manage_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/edit"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_manage_button.setIcon(icon1)
        self.g_manage_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manage_button.setObjectName("g_manage_button")
        self.horizontalLayout_11.addWidget(self.g_manage_button)
        self.g_new_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_new_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_new_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_new_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/entity_dialog/icons/flat_add_white_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_new_button.setIcon(icon2)
        self.g_new_button.setIconSize(QtCore.QSize(24, 24))
        self.g_new_button.setObjectName("g_new_button")
        self.horizontalLayout_11.addWidget(self.g_new_button)
        self.g_select_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_select_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_select_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_select_button.setIcon(icon3)
        self.g_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_select_button.setObjectName("g_select_button")
        self.horizontalLayout_11.addWidget(self.g_select_button)
        self.horizontalLayout.addWidget(self.g_controlbuttons_frame)
        self.verticalLayout.addWidget(self.g_bottombar_frame)
        self.verticalLayout_2.addWidget(self.g_selectsupplier_frame)

        self.retranslateUi(SelectSupplier)
        QtCore.QMetaObject.connectSlotsByName(SelectSupplier)

    def retranslateUi(self, SelectSupplier):
        _translate = QtCore.QCoreApplication.translate
        SelectSupplier.setWindowTitle(_translate("SelectSupplier", "Select Supplier"))
        self.g_main_label.setText(_translate("SelectSupplier", "Select Supplier"))
        self.g_search_combobox.setItemText(0, _translate("SelectSupplier", "Smart Search"))
        self.g_search_combobox.setItemText(1, _translate("SelectSupplier", "Search Name"))
        self.g_search_combobox.setItemText(2, _translate("SelectSupplier", "Search Phone"))
        self.g_search_combobox.setItemText(3, _translate("SelectSupplier", "Search Email"))
        self.g_search_combobox.setItemText(4, _translate("SelectSupplier", "Search Loyalty Card"))
        self.g_search_combobox.setItemText(5, _translate("SelectSupplier", "Search All Roles"))
        self.g_searchtext_lineedit.setPlaceholderText(_translate("SelectSupplier", "Enter your search terms then press Enter..."))
        self.g_selectsupplier_table.setSortingEnabled(False)
        item = self.g_selectsupplier_table.verticalHeaderItem(0)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(1)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(2)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(3)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(4)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(5)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(6)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(7)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(8)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(9)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(10)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(11)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(12)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.verticalHeaderItem(13)
        item.setText(_translate("SelectSupplier", "New Row"))
        item = self.g_selectsupplier_table.horizontalHeaderItem(0)
        item.setText(_translate("SelectSupplier", "Name"))
        item = self.g_selectsupplier_table.horizontalHeaderItem(1)
        item.setText(_translate("SelectSupplier", "Email"))
        item = self.g_selectsupplier_table.horizontalHeaderItem(2)
        item.setText(_translate("SelectSupplier", "Phone"))
        __sortingEnabled = self.g_selectsupplier_table.isSortingEnabled()
        self.g_selectsupplier_table.setSortingEnabled(False)
        item = self.g_selectsupplier_table.item(0, 0)
        item.setText(_translate("SelectSupplier", "Erick Armstrong"))
        item = self.g_selectsupplier_table.item(0, 1)
        item.setText(_translate("SelectSupplier", "<EMAIL>"))
        item = self.g_selectsupplier_table.item(0, 2)
        item.setText(_translate("SelectSupplier", "************"))
        item = self.g_selectsupplier_table.item(1, 0)
        item.setText(_translate("SelectSupplier", "Nick Cook"))
        item = self.g_selectsupplier_table.item(1, 1)
        item.setText(_translate("SelectSupplier", "<EMAIL>"))
        item = self.g_selectsupplier_table.item(1, 2)
        item.setText(_translate("SelectSupplier", "************"))
        item = self.g_selectsupplier_table.item(2, 0)
        item.setText(_translate("SelectSupplier", "Doug Sans Puppies"))
        item = self.g_selectsupplier_table.item(2, 1)
        item.setText(_translate("SelectSupplier", "<EMAIL>"))
        item = self.g_selectsupplier_table.item(2, 2)
        item.setText(_translate("SelectSupplier", "789-456-1230"))
        item = self.g_selectsupplier_table.item(3, 0)
        item.setText(_translate("SelectSupplier", "Daniel\'s Puppy Emporium"))
        item = self.g_selectsupplier_table.item(3, 1)
        item.setText(_translate("SelectSupplier", "<EMAIL>"))
        item = self.g_selectsupplier_table.item(3, 2)
        item.setText(_translate("SelectSupplier", "159-357-7524"))
        item = self.g_selectsupplier_table.item(4, 0)
        item.setText(_translate("SelectSupplier", "Rafal Distributes Dogs"))
        item = self.g_selectsupplier_table.item(4, 1)
        item.setText(_translate("SelectSupplier", "<EMAIL>"))
        item = self.g_selectsupplier_table.item(4, 2)
        item.setText(_translate("SelectSupplier", "987-159-7530"))
        self.g_selectsupplier_table.setSortingEnabled(__sortingEnabled)
        self.g_manage_button.setText(_translate("SelectSupplier", " Manage"))
        self.g_new_button.setText(_translate("SelectSupplier", " New"))
        self.g_select_button.setText(_translate("SelectSupplier", " Select"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    SelectSupplier = QtWidgets.QDialog()
    ui = Ui_SelectSupplier()
    ui.setupUi(SelectSupplier)
    SelectSupplier.show()
    sys.exit(app.exec_())
