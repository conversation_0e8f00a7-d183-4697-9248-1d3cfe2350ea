# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'datacapcardentry_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_DatacapCardEntryDialog(object):
    def setupUi(self, DatacapCardEntryDialog):
        DatacapCardEntryDialog.setObjectName("DatacapCardEntryDialog")
        DatacapCardEntryDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        DatacapCardEntryDialog.resize(540, 300)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(DatacapCardEntryDialog.sizePolicy().hasHeightForWidth())
        DatacapCardEntryDialog.setSizePolicy(sizePolicy)
        DatacapCardEntryDialog.setMinimumSize(QtCore.QSize(540, 300))
        DatacapCardEntryDialog.setMaximumSize(QtCore.QSize(795, 310))
        DatacapCardEntryDialog.setStyleSheet("\n"
"QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QPushButton {\n"
"    color: white;\n"
"    font-weight: bold;\n"
"    font-size: 11pt;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border: 2px solid #287599;\n"
"    font-weight: bold;\n"
"    font-size: 15px\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QPushButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: None;\n"
"}\n"
"\n"
"#g_cancel_button,\n"
"#__g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border: 2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed,\n"
"       #__g_cancel_button:pressed {\n"
"       background: #c80319;\n"
"       }\n"
"\n"
"       #g_close_button {\n"
"       background: #009c00;\n"
"       border:2px solid #007f00;\n"
"       }\n"
"\n"
"       #g_close_button:pressed {\n"
"       background: #007f00;\n"
"       }\n"
"\n"
"       #g_close_button:disabled {\n"
"       background: rgb(164, 164, 164);\n"
"       border: None;\n"
"       }\n"
"\n"
"       #g_message_label {\n"
"       font-size: 17px;\n"
"       font-weight: bold;\n"
"}\n"
"\n"
"#label {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_warning_label {\n"
"    font-size: 13px;\n"
"}\n"
"\n"
"#g_textresponse_lineedit {\n"
"    font-size: 13px;\n"
"}")
        DatacapCardEntryDialog.setModal(True)
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(DatacapCardEntryDialog)
        self.verticalLayout_3.setContentsMargins(5, 5, 5, 5)
        self.verticalLayout_3.setSpacing(3)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_top_frame = QtWidgets.QFrame(DatacapCardEntryDialog)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_top_frame)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_message_label = QtWidgets.QLabel(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_message_label.sizePolicy().hasHeightForWidth())
        self.g_message_label.setSizePolicy(sizePolicy)
        self.g_message_label.setMinimumSize(QtCore.QSize(0, 33))
        self.g_message_label.setMaximumSize(QtCore.QSize(16777215, 45))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_message_label.setFont(font)
        self.g_message_label.setTextFormat(QtCore.Qt.PlainText)
        self.g_message_label.setScaledContents(True)
        self.g_message_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_message_label.setWordWrap(True)
        self.g_message_label.setObjectName("g_message_label")
        self.verticalLayout.addWidget(self.g_message_label)
        self.g_waiting_progressbar = QtWidgets.QProgressBar(self.g_top_frame)
        self.g_waiting_progressbar.setEnabled(True)
        self.g_waiting_progressbar.setMinimumSize(QtCore.QSize(0, 30))
        font = QtGui.QFont()
        font.setPointSize(10)
        self.g_waiting_progressbar.setFont(font)
        self.g_waiting_progressbar.setMinimum(0)
        self.g_waiting_progressbar.setMaximum(0)
        self.g_waiting_progressbar.setProperty("value", -1)
        self.g_waiting_progressbar.setTextVisible(False)
        self.g_waiting_progressbar.setObjectName("g_waiting_progressbar")
        self.verticalLayout.addWidget(self.g_waiting_progressbar)
        self.verticalLayout_3.addWidget(self.g_top_frame)
        self.g_textresponse_frame = QtWidgets.QFrame(DatacapCardEntryDialog)
        self.g_textresponse_frame.setMinimumSize(QtCore.QSize(0, 70))
        self.g_textresponse_frame.setObjectName("g_textresponse_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_textresponse_frame)
        self.verticalLayout_2.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.label = QtWidgets.QLabel(self.g_textresponse_frame)
        self.label.setMinimumSize(QtCore.QSize(0, 33))
        self.label.setMaximumSize(QtCore.QSize(16777215, 33))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.label.setFont(font)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.verticalLayout_2.addWidget(self.label)
        self.g_textresponse_lineedit = QtWidgets.QLineEdit(self.g_textresponse_frame)
        self.g_textresponse_lineedit.setMinimumSize(QtCore.QSize(0, 30))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_textresponse_lineedit.setFont(font)
        self.g_textresponse_lineedit.setText("")
        self.g_textresponse_lineedit.setFrame(True)
        self.g_textresponse_lineedit.setReadOnly(True)
        self.g_textresponse_lineedit.setObjectName("g_textresponse_lineedit")
        self.verticalLayout_2.addWidget(self.g_textresponse_lineedit)
        self.verticalLayout_3.addWidget(self.g_textresponse_frame)
        self.g_bottom_frame = QtWidgets.QFrame(DatacapCardEntryDialog)
        self.g_bottom_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_bottom_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottom_frame.setObjectName("g_bottom_frame")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_bottom_frame)
        self.verticalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_4.setSpacing(0)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_warning_label = QtWidgets.QLabel(self.g_bottom_frame)
        self.g_warning_label.setMinimumSize(QtCore.QSize(0, 33))
        self.g_warning_label.setMaximumSize(QtCore.QSize(16777215, 33))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_warning_label.setFont(font)
        self.g_warning_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_warning_label.setObjectName("g_warning_label")
        self.verticalLayout_4.addWidget(self.g_warning_label)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setContentsMargins(-1, 0, -1, -1)
        self.horizontalLayout.setSpacing(9)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_voice_auth_button = QtWidgets.QPushButton(self.g_bottom_frame)
        self.g_voice_auth_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_voice_auth_button.setMaximumSize(QtCore.QSize(120, 40))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/datacapcardentry_dialog/phone"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_voice_auth_button.setIcon(icon)
        self.g_voice_auth_button.setIconSize(QtCore.QSize(24, 24))
        self.g_voice_auth_button.setObjectName("g_voice_auth_button")
        self.horizontalLayout.addWidget(self.g_voice_auth_button)
        self.g_manualentry_button = QtWidgets.QPushButton(self.g_bottom_frame)
        self.g_manualentry_button.setEnabled(True)
        self.g_manualentry_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manualentry_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_manualentry_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/datacapcardentry_dialog/manual_entry"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_manualentry_button.setIcon(icon1)
        self.g_manualentry_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manualentry_button.setObjectName("g_manualentry_button")
        self.horizontalLayout.addWidget(self.g_manualentry_button)
        self.g_swipecard_button = QtWidgets.QPushButton(self.g_bottom_frame)
        self.g_swipecard_button.setEnabled(True)
        self.g_swipecard_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_swipecard_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_swipecard_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/datacapcardentry_dialog/swipe_card"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_swipecard_button.setIcon(icon2)
        self.g_swipecard_button.setIconSize(QtCore.QSize(24, 24))
        self.g_swipecard_button.setObjectName("g_swipecard_button")
        self.horizontalLayout.addWidget(self.g_swipecard_button)
        self.g_savedcards_button = QtWidgets.QPushButton(self.g_bottom_frame)
        self.g_savedcards_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_savedcards_button.setMaximumSize(QtCore.QSize(120, 40))
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/datacapcardentry_dialog/credit_cards"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_savedcards_button.setIcon(icon3)
        self.g_savedcards_button.setIconSize(QtCore.QSize(24, 24))
        self.g_savedcards_button.setObjectName("g_savedcards_button")
        self.horizontalLayout.addWidget(self.g_savedcards_button)
        self.g_close_button = QtWidgets.QPushButton(self.g_bottom_frame)
        self.g_close_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_close_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_close_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/datacapcardentry_dialog/check"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_close_button.setIcon(icon4)
        self.g_close_button.setIconSize(QtCore.QSize(24, 24))
        self.g_close_button.setObjectName("g_close_button")
        self.horizontalLayout.addWidget(self.g_close_button)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.verticalLayout_4.addLayout(self.horizontalLayout)
        self.verticalLayout_3.addWidget(self.g_bottom_frame)

        self.retranslateUi(DatacapCardEntryDialog)
        QtCore.QMetaObject.connectSlotsByName(DatacapCardEntryDialog)

    def retranslateUi(self, DatacapCardEntryDialog):
        _translate = QtCore.QCoreApplication.translate
        DatacapCardEntryDialog.setWindowTitle(_translate("DatacapCardEntryDialog", "Credit/Debit Card Transaction"))
        DatacapCardEntryDialog.setProperty("swipe_card_msg_text", _translate("DatacapCardEntryDialog", "Please swipe,  insert,  or tap a card using the PIN pad now ..."))
        DatacapCardEntryDialog.setProperty("manual_entry_msg_text", _translate("DatacapCardEntryDialog", "Please enter card information using the PIN pad now ..."))
        DatacapCardEntryDialog.setProperty("preparing_pinpad_msg_text", _translate("DatacapCardEntryDialog", "Preparing PIN pad for new transaction. Please wait ..."))
        DatacapCardEntryDialog.setProperty("pinpad_busy_msg_text", _translate("DatacapCardEntryDialog", "PIN pad appears to be busy. Please cancel the current transaction or wait for it to timeout, then try again."))
        DatacapCardEntryDialog.setProperty("pinpad_timeout_msg_text", _translate("DatacapCardEntryDialog", "PIN pad time out."))
        DatacapCardEntryDialog.setProperty("pinpad_error_msg_text", _translate("DatacapCardEntryDialog", "PIN pad returned an unexpected response. Please contact Pinogy support at ************."))
        DatacapCardEntryDialog.setProperty("transaction_not_complete_msg_text", _translate("DatacapCardEntryDialog", "PIN pad transaction did not complete."))
        DatacapCardEntryDialog.setProperty("pinpad_setup_msg_text", _translate("DatacapCardEntryDialog", "The PIN pad has not been setup for processing transactions. Please wait for this to complete, then re-try your transaction."))
        DatacapCardEntryDialog.setProperty("transaction_cancelled_msg_text", _translate("DatacapCardEntryDialog", "PIN pad transaction was cancelled."))
        DatacapCardEntryDialog.setProperty("pinpad_not_detected_msg_text", _translate("DatacapCardEntryDialog", "PIN pad was not detected. Verify that it is powered on and connected properly, then retry your transaction."))
        DatacapCardEntryDialog.setProperty("transaction_declined_msg_text", _translate("DatacapCardEntryDialog", "PIN pad transaction was declined."))
        DatacapCardEntryDialog.setProperty("current_session_url_text", _translate("DatacapCardEntryDialog", "/apps/any/sessions/mine"))
        DatacapCardEntryDialog.setProperty("printing_receipts_msg_text", _translate("DatacapCardEntryDialog", "Printing transaction receipts ..."))
        DatacapCardEntryDialog.setProperty("read_payment_gateways_url_text", _translate("DatacapCardEntryDialog", "/apps/cash_register/queries/read__tbl__payment_gateways"))
        DatacapCardEntryDialog.setProperty("error_title_text", _translate("DatacapCardEntryDialog", "Error"))
        DatacapCardEntryDialog.setProperty("retrieving_gateways_msg_text", _translate("DatacapCardEntryDialog", "Retrieving configured payment gateways ..."))
        DatacapCardEntryDialog.setProperty("success_title_text", _translate("DatacapCardEntryDialog", "Success"))
        DatacapCardEntryDialog.setProperty("initialization_failed_msg_text", _translate("DatacapCardEntryDialog", "The following PIN pad failed to initialize correctly ...\n"
"\n"
"%(device_id)s\n"
"\n"
"Please double-check your configuration and retry\n"
"this operation or contact Pinogy support at \n"
"************."))
        DatacapCardEntryDialog.setProperty("missing_credentials_msg_text", _translate("DatacapCardEntryDialog", "One or more credentials for this Vantiv/Mercury\n"
"payment gateway are missing.\n"
"\n"
"Please provide all required Vantiv/Mercury\n"
"credentials using the Settings and Configuration\n"
"app then retry this operation."))
        DatacapCardEntryDialog.setProperty("missing_gateway_msg_text", _translate("DatacapCardEntryDialog", "The Vantiv/Mercury Payment Gateway is not configured for this location.\n"
"\n"
"This form needs to be saved from the location(s) where the Trancloud devices are located."))
        DatacapCardEntryDialog.setProperty("pinpads_initializing_msg_text", _translate("DatacapCardEntryDialog", "The PIN-pads are now being initialized.\n"
"\n"
"This process may take 1-2 minutes for \n"
"each PIN-pad. Please wait for it to \n"
"complete.\n"
"\n"
"Initializing PIN pad %(device_id)s ...\n"
"(%(count)s of %(total)s)"))
        DatacapCardEntryDialog.setProperty("pinpads_initialized_msg_text", _translate("DatacapCardEntryDialog", "PIN pads were successfully initialized and are now ready to process transactions."))
        DatacapCardEntryDialog.setProperty("read_payment_processors_url_text", _translate("DatacapCardEntryDialog", "/apps/cash_register/queries/read__tbl__payment_processors"))
        DatacapCardEntryDialog.setProperty("processor_misconfigured_msg_text", _translate("DatacapCardEntryDialog", "The Vantiv/Mercury payment processor is not \n"
"configured correctly.\n"
"\n"
"Please contact Pinogy support at ************."))
        DatacapCardEntryDialog.setProperty("try_again_button_text", _translate("DatacapCardEntryDialog", "Try Again"))
        DatacapCardEntryDialog.setProperty("swipe_card_button_text", _translate("DatacapCardEntryDialog", "Swipe Card"))
        DatacapCardEntryDialog.setProperty("transaction_retry_required_msg_text", _translate("DatacapCardEntryDialog", "PIN pad transaction must be retried."))
        DatacapCardEntryDialog.setProperty("pindebit_prompt_msg_text", _translate("DatacapCardEntryDialog", "Please select Credit or Debit using the PIN pad now ..."))
        self.g_message_label.setText(_translate("DatacapCardEntryDialog", "Please swipe,  insert,  or tap a card using the PIN pad now ..."))
        self.label.setText(_translate("DatacapCardEntryDialog", "Text Response from Last Request"))
        self.g_warning_label.setText(_translate("DatacapCardEntryDialog", "To cancel this charge,  press cancel on the Pin Pad (Red Button)."))
        self.g_voice_auth_button.setText(_translate("DatacapCardEntryDialog", "Voice Auth"))
        self.g_manualentry_button.setText(_translate("DatacapCardEntryDialog", "  Manual\n"
"    Entry"))
        self.g_swipecard_button.setText(_translate("DatacapCardEntryDialog", " Swipe\n"
" Card"))
        self.g_savedcards_button.setText(_translate("DatacapCardEntryDialog", "Saved\n"
"Cards"))
        self.g_close_button.setText(_translate("DatacapCardEntryDialog", "Close"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    DatacapCardEntryDialog = QtWidgets.QDialog()
    ui = Ui_DatacapCardEntryDialog()
    ui.setupUi(DatacapCardEntryDialog)
    DatacapCardEntryDialog.show()
    sys.exit(app.exec_())
