# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managebrandmedia_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_ManageBrandMedia(object):
    def setupUi(self, ManageBrandMedia):
        ManageBrandMedia.setObjectName("ManageBrandMedia")
        ManageBrandMedia.resize(969, 500)
        ManageBrandMedia.setMinimumSize(QtCore.QSize(0, 0))
        ManageBrandMedia.setMaximumSize(QtCore.QSize(16777215, 500))
        ManageBrandMedia.setStyleSheet("#ManageBrandMedia {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QLabel {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font: 17px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/pet_tracker/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/pet_tracker/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
".QListWidget {\n"
"    padding: 2px;\n"
"    background: transparent;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"#g_crop_and_save_btn,\n"
"#g_close_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_crop_and_save_btn:pressed,\n"
"#g_close_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cropper_cancel_btn  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"##g_cropper_cancel_btn:pressed, ##g_cropper_cancel_btn:pressed  {\n"
"    background: #c80319\n"
"}\n"
"")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(ManageBrandMedia)
        self.verticalLayout_2.setContentsMargins(9, 0, 9, 9)
        self.verticalLayout_2.setSpacing(6)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_progress_frame = QtWidgets.QFrame(ManageBrandMedia)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(6, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.verticalLayout_2.addWidget(self.g_progress_frame)
        self.g_titlepagenav_frame = QtWidgets.QFrame(ManageBrandMedia)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_titlepagenav_frame.sizePolicy().hasHeightForWidth())
        self.g_titlepagenav_frame.setSizePolicy(sizePolicy)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_33 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_33.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_33.setSpacing(6)
        self.horizontalLayout_33.setObjectName("horizontalLayout_33")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout_24 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout_24.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_24.setObjectName("verticalLayout_24")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_24.addWidget(self.g_main_label)
        self.horizontalLayout_33.addWidget(self.g_title_frame)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout.setSpacing(6)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout.addItem(spacerItem)
        self.horizontalLayout_33.addWidget(self.g_pagenav_frame)
        self.verticalLayout_2.addWidget(self.g_titlepagenav_frame)
        self.g_cropper_area = QtWidgets.QFrame(ManageBrandMedia)
        self.g_cropper_area.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cropper_area.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cropper_area.setObjectName("g_cropper_area")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_cropper_area)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_description_frame = QtWidgets.QFrame(self.g_cropper_area)
        self.g_description_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_description_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_description_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_description_frame.setObjectName("g_description_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_description_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setSpacing(6)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_description_label = QtWidgets.QLabel(self.g_description_frame)
        self.g_description_label.setMinimumSize(QtCore.QSize(140, 0))
        self.g_description_label.setMaximumSize(QtCore.QSize(140, 16777215))
        self.g_description_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_description_label.setObjectName("g_description_label")
        self.horizontalLayout_2.addWidget(self.g_description_label)
        self.g_description_lineedit = QtWidgets.QLineEdit(self.g_description_frame)
        self.g_description_lineedit.setObjectName("g_description_lineedit")
        self.horizontalLayout_2.addWidget(self.g_description_lineedit)
        self.verticalLayout_3.addWidget(self.g_description_frame)
        self.g_cropper_frame = QtWidgets.QFrame(self.g_cropper_area)
        self.g_cropper_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cropper_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cropper_frame.setObjectName("g_cropper_frame")
        self.horizontalLayout_19 = QtWidgets.QHBoxLayout(self.g_cropper_frame)
        self.horizontalLayout_19.setSpacing(6)
        self.horizontalLayout_19.setObjectName("horizontalLayout_19")
        self.verticalLayout_3.addWidget(self.g_cropper_frame)
        self.g_size_label = QtWidgets.QLabel(self.g_cropper_area)
        self.g_size_label.setMinimumSize(QtCore.QSize(90, 0))
        self.g_size_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_size_label.setFont(font)
        self.g_size_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_size_label.setObjectName("g_size_label")
        self.verticalLayout_3.addWidget(self.g_size_label)
        spacerItem1 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_3.addItem(spacerItem1)
        self.verticalLayout_2.addWidget(self.g_cropper_area)
        self.g_addedit_groupBox = QtWidgets.QGroupBox(ManageBrandMedia)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_addedit_groupBox.setFont(font)
        self.g_addedit_groupBox.setObjectName("g_addedit_groupBox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_addedit_groupBox)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_pet_images_list = QtWidgets.QListWidget(self.g_addedit_groupBox)
        self.g_pet_images_list.setWordWrap(False)
        self.g_pet_images_list.setObjectName("g_pet_images_list")
        self.verticalLayout.addWidget(self.g_pet_images_list)
        self.g_upload_frame = QtWidgets.QFrame(self.g_addedit_groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_upload_frame.sizePolicy().hasHeightForWidth())
        self.g_upload_frame.setSizePolicy(sizePolicy)
        self.g_upload_frame.setMinimumSize(QtCore.QSize(0, 50))
        self.g_upload_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_upload_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_upload_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_upload_frame.setObjectName("g_upload_frame")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.g_upload_frame)
        self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_13.setSpacing(6)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.g_uploadnew_label = QtWidgets.QLabel(self.g_upload_frame)
        self.g_uploadnew_label.setMinimumSize(QtCore.QSize(80, 31))
        self.g_uploadnew_label.setMaximumSize(QtCore.QSize(0, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_uploadnew_label.setFont(font)
        self.g_uploadnew_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_uploadnew_label.setObjectName("g_uploadnew_label")
        self.horizontalLayout_13.addWidget(self.g_uploadnew_label)
        self.g_uploadfilename_lineedit = QtWidgets.QLineEdit(self.g_upload_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_uploadfilename_lineedit.sizePolicy().hasHeightForWidth())
        self.g_uploadfilename_lineedit.setSizePolicy(sizePolicy)
        self.g_uploadfilename_lineedit.setMinimumSize(QtCore.QSize(100, 31))
        self.g_uploadfilename_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_uploadfilename_lineedit.setFont(font)
        self.g_uploadfilename_lineedit.setReadOnly(False)
        self.g_uploadfilename_lineedit.setObjectName("g_uploadfilename_lineedit")
        self.horizontalLayout_13.addWidget(self.g_uploadfilename_lineedit)
        self.g_selectfile_button = QtWidgets.QPushButton(self.g_upload_frame)
        self.g_selectfile_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_selectfile_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_selectfile_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/products/search"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_selectfile_button.setIcon(icon)
        self.g_selectfile_button.setIconSize(QtCore.QSize(24, 24))
        self.g_selectfile_button.setObjectName("g_selectfile_button")
        self.horizontalLayout_13.addWidget(self.g_selectfile_button)
        self.verticalLayout.addWidget(self.g_upload_frame)
        self.verticalLayout_2.addWidget(self.g_addedit_groupBox)
        self.__g_bottombar_frame = QtWidgets.QFrame(ManageBrandMedia)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_bottombar_frame.setSizePolicy(sizePolicy)
        self.__g_bottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_bottombar_frame.setStyleSheet("")
        self.__g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_bottombar_frame.setObjectName("__g_bottombar_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.__g_bottombar_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.__g_controlbuttons_frame = QtWidgets.QFrame(self.__g_bottombar_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.__g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.__g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.__g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_controlbuttons_frame.setObjectName("__g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.__g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(6)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem2 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem2)
        self.g_cropper_cancel_btn = QtWidgets.QPushButton(self.__g_controlbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_cropper_cancel_btn.sizePolicy().hasHeightForWidth())
        self.g_cropper_cancel_btn.setSizePolicy(sizePolicy)
        self.g_cropper_cancel_btn.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cropper_cancel_btn.setMaximumSize(QtCore.QSize(120, 16777215))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/products/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cropper_cancel_btn.setIcon(icon1)
        self.g_cropper_cancel_btn.setIconSize(QtCore.QSize(24, 24))
        self.g_cropper_cancel_btn.setObjectName("g_cropper_cancel_btn")
        self.horizontalLayout_11.addWidget(self.g_cropper_cancel_btn)
        self.g_crop_and_save_btn = QtWidgets.QPushButton(self.__g_controlbuttons_frame)
        self.g_crop_and_save_btn.setMinimumSize(QtCore.QSize(120, 40))
        self.g_crop_and_save_btn.setMaximumSize(QtCore.QSize(120, 16777215))
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/products/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_crop_and_save_btn.setIcon(icon2)
        self.g_crop_and_save_btn.setIconSize(QtCore.QSize(24, 24))
        self.g_crop_and_save_btn.setObjectName("g_crop_and_save_btn")
        self.horizontalLayout_11.addWidget(self.g_crop_and_save_btn)
        self.g_close_button = QtWidgets.QPushButton(self.__g_controlbuttons_frame)
        self.g_close_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_close_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_close_button.setStyleSheet("")
        self.g_close_button.setIcon(icon2)
        self.g_close_button.setIconSize(QtCore.QSize(24, 24))
        self.g_close_button.setObjectName("g_close_button")
        self.horizontalLayout_11.addWidget(self.g_close_button)
        self.horizontalLayout.addWidget(self.__g_controlbuttons_frame)
        self.verticalLayout_2.addWidget(self.__g_bottombar_frame)

        self.retranslateUi(ManageBrandMedia)
        QtCore.QMetaObject.connectSlotsByName(ManageBrandMedia)

    def retranslateUi(self, ManageBrandMedia):
        _translate = QtCore.QCoreApplication.translate
        ManageBrandMedia.setWindowTitle(_translate("ManageBrandMedia", "Manage Pet Media"))
        self.g_main_label.setText(_translate("ManageBrandMedia", "Manage Brand Media"))
        self.g_description_label.setText(_translate("ManageBrandMedia", "Alternative Description"))
        self.g_size_label.setText(_translate("ManageBrandMedia", "0x0"))
        self.g_addedit_groupBox.setTitle(_translate("ManageBrandMedia", "Add / Edit Existing Brand Media"))
        self.g_uploadnew_label.setText(_translate("ManageBrandMedia", "Upload New"))
        self.g_uploadfilename_lineedit.setPlaceholderText(_translate("ManageBrandMedia", "No file chosen."))
        self.g_selectfile_button.setText(_translate("ManageBrandMedia", "Select File"))
        self.g_cropper_cancel_btn.setText(_translate("ManageBrandMedia", "Cancel"))
        self.g_crop_and_save_btn.setText(_translate("ManageBrandMedia", "Crop and\n"
"   Save"))
        self.g_close_button.setText(_translate("ManageBrandMedia", "Close"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageBrandMedia = QtWidgets.QDialog()
    ui = Ui_ManageBrandMedia()
    ui.setupUi(ManageBrandMedia)
    ManageBrandMedia.show()
    sys.exit(app.exec_())
