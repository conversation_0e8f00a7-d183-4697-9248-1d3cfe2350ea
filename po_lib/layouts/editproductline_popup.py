# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'editproductline_popup.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_EditProductLinePopup(object):
    def setupUi(self, EditProductLinePopup):
        EditProductLinePopup.setObjectName("EditProductLinePopup")
        EditProductLinePopup.resize(440, 785)
        EditProductLinePopup.setStyleSheet("\n"
"QPushButton {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border: 2px solid #287599;\n"
"    color: white;\n"
"    min-height: 40px;\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border: 2px solid #287599;\n"
"}\n"
"\n"
"#g_cover_frame, \n"
"#__g_cover_frame {\n"
"    background-color: rgb(0,0,0, 25%);\n"
"}\n"
"\n"
"#g_container_frame, \n"
"#__g_container_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border: 1px solid #073c83;\n"
"    border-radius: 10px;\n"
"    background-color: #DDE4E9;\n"
"}\n"
"\n"
"#g_cancel_button,\n"
"#__g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border: 2px solid #c80319;\n"
"    min-width: 120px;\n"
"}\n"
"\n"
"#g_cancel_button:pressed,\n"
"#__g_cancel_button:pressed {\n"
"    background: #c80319;\n"
"}\n"
"\n"
"#g_title_label {\n"
"    font-weight: bold;\n"
"    font-size: 16px;\n"
"}")
        self.verticalLayout = QtWidgets.QVBoxLayout(EditProductLinePopup)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_cover_frame = QtWidgets.QFrame(EditProductLinePopup)
        self.g_cover_frame.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.g_cover_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cover_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cover_frame.setObjectName("g_cover_frame")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_cover_frame)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        spacerItem = QtWidgets.QSpacerItem(20, 67, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem1)
        self.g_container_frame = QtWidgets.QFrame(self.g_cover_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_container_frame.sizePolicy().hasHeightForWidth())
        self.g_container_frame.setSizePolicy(sizePolicy)
        self.g_container_frame.setMinimumSize(QtCore.QSize(250, 0))
        self.g_container_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_container_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_container_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_container_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_container_frame.setObjectName("g_container_frame")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_container_frame)
        self.verticalLayout_3.setContentsMargins(11, 11, 11, 11)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_title_label = QtWidgets.QLabel(self.g_container_frame)
        self.g_title_label.setMaximumSize(QtCore.QSize(16777215, 19))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_title_label.setFont(font)
        self.g_title_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_title_label.setObjectName("g_title_label")
        self.verticalLayout_3.addWidget(self.g_title_label)
        spacerItem2 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_3.addItem(spacerItem2)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout()
        self.verticalLayout_2.setContentsMargins(18, -1, 18, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_changenote_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_changenote_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_changenote_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_changenote_button.setObjectName("g_changenote_button")
        self.horizontalLayout.addWidget(self.g_changenote_button)
        self.verticalLayout_2.addLayout(self.horizontalLayout)
        self.g_changecommission_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_changecommission_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_changecommission_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_changecommission_button.setObjectName("g_changecommission_button")
        self.verticalLayout_2.addWidget(self.g_changecommission_button)
        self.g_changetax_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_changetax_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_changetax_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_changetax_button.setObjectName("g_changetax_button")
        self.verticalLayout_2.addWidget(self.g_changetax_button)
        self.g_nonresalable_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_nonresalable_button.setObjectName("g_nonresalable_button")
        self.verticalLayout_2.addWidget(self.g_nonresalable_button)
        self.g_qrtag_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_qrtag_button.setObjectName("g_qrtag_button")
        self.verticalLayout_2.addWidget(self.g_qrtag_button)
        self.g_customer_pet_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_customer_pet_button.setObjectName("g_customer_pet_button")
        self.verticalLayout_2.addWidget(self.g_customer_pet_button)
        self.g_change_variation_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_change_variation_button.setObjectName("g_change_variation_button")
        self.verticalLayout_2.addWidget(self.g_change_variation_button)
        self.g_sign_on_tablet_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_sign_on_tablet_button.setObjectName("g_sign_on_tablet_button")
        self.verticalLayout_2.addWidget(self.g_sign_on_tablet_button)
        self.g_print_ppw_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_print_ppw_button.setObjectName("g_print_ppw_button")
        self.verticalLayout_2.addWidget(self.g_print_ppw_button)
        self.g_manage_product_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_manage_product_button.setObjectName("g_manage_product_button")
        self.verticalLayout_2.addWidget(self.g_manage_product_button)
        self.verticalLayout_3.addLayout(self.verticalLayout_2)
        spacerItem3 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_3.addItem(spacerItem3)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setContentsMargins(-1, -1, 0, -1)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem4)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setFocusPolicy(QtCore.Qt.NoFocus)
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/editproductline_popup/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_2.addWidget(self.g_cancel_button)
        self.verticalLayout_3.addLayout(self.horizontalLayout_2)
        self.horizontalLayout_3.addWidget(self.g_container_frame)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem5)
        self.verticalLayout_4.addLayout(self.horizontalLayout_3)
        spacerItem6 = QtWidgets.QSpacerItem(20, 66, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem6)
        self.verticalLayout.addWidget(self.g_cover_frame)

        self.retranslateUi(EditProductLinePopup)
        QtCore.QMetaObject.connectSlotsByName(EditProductLinePopup)

    def retranslateUi(self, EditProductLinePopup):
        _translate = QtCore.QCoreApplication.translate
        EditProductLinePopup.setWindowTitle(_translate("EditProductLinePopup", "Dialog"))
        self.g_title_label.setText(_translate("EditProductLinePopup", "Select Action"))
        self.g_changenote_button.setText(_translate("EditProductLinePopup", "Change Note"))
        self.g_changecommission_button.setText(_translate("EditProductLinePopup", "Change Commission"))
        self.g_changetax_button.setText(_translate("EditProductLinePopup", "Change Tax"))
        self.g_nonresalable_button.setText(_translate("EditProductLinePopup", "Non Resalable"))
        self.g_qrtag_button.setText(_translate("EditProductLinePopup", "Change QR Tag"))
        self.g_customer_pet_button.setText(_translate("EditProductLinePopup", "Change Customer Pet"))
        self.g_customer_pet_button.setShortcut(_translate("EditProductLinePopup", "Ctrl+S"))
        self.g_change_variation_button.setText(_translate("EditProductLinePopup", "Change Variation"))
        self.g_sign_on_tablet_button.setText(_translate("EditProductLinePopup", "Complete on Tablet"))
        self.g_print_ppw_button.setText(_translate("EditProductLinePopup", "Print Paperwork to Sign"))
        self.g_manage_product_button.setText(_translate("EditProductLinePopup", "Manage Product"))
        self.g_cancel_button.setText(_translate("EditProductLinePopup", "  Cancel"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    EditProductLinePopup = QtWidgets.QDialog()
    ui = Ui_EditProductLinePopup()
    ui.setupUi(EditProductLinePopup)
    EditProductLinePopup.show()
    sys.exit(app.exec_())
