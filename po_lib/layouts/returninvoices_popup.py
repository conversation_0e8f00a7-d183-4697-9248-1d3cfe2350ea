# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'returninvoices_popup.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ReturnInvoicesPopup(object):
    def setupUi(self, ReturnInvoicesPopup):
        ReturnInvoicesPopup.setObjectName("ReturnInvoicesPopup")
        ReturnInvoicesPopup.setEnabled(True)
        ReturnInvoicesPopup.resize(640, 484)
        ReturnInvoicesPopup.setStyleSheet("\n"
"\n"
"/*************************************************************/\n"
"/* Default styling for line-edit widgets */\n"
"/*************************************************************/\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    background-color: #fff;\n"
"    max-height: 31px;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"\n"
"/*************************************************************/\n"
"/* Default styling for pushbuttons */\n"
"/*************************************************************/\n"
"\n"
"QPushButton {\n"
"\n"
"    /* Applies a blue background color. */\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    border: 2px solid #287599;\n"
"    color: white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"}\n"
"\n"
"QPushButton:disabled {\n"
"    background: grey;\n"
"    border: grey;\n"
"}\n"
"\n"
"\n"
"/*************************************************************/\n"
"/* Styling for the visual elements creating the popup itself */\n"
"/*************************************************************/\n"
"\n"
"#g_cover_frame, \n"
"#__g_cover_frame {\n"
"\n"
"    /* Applies a black background that is 25% transparent. */\n"
"    background-color: rgb(0,0,0, 25%);\n"
"}\n"
"\n"
"#g_container_frame, \n"
"#__g_container_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border: 1px solid #073c83;\n"
"    border-radius: 10px;\n"
"    background-color: #dde4e9;\n"
"}\n"
"\n"
"\n"
"/*************************************************************/\n"
"/* Styling for widgets appearing within the popup */\n"
"/*************************************************************/\n"
"\n"
"#g_cancel_button,\n"
"#__g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed,\n"
"#__g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_startreturn_button,\n"
"#__g_startreturn_button {\n"
"    border: 2px solid #007f00;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #278938, stop: 1 #3e9e27);\n"
"}\n"
"\n"
"#g_startreturn_button:pressed,\n"
"#__g_startreturn_button:pressed {\n"
"    background: 2px solid #007f00;\n"
"}\n"
"\n"
"#g_invoicetype_combobox,\n"
"#__g_invoicetype_combobox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"#g_invoicetype_combobox:disabled,\n"
"#__g_invoicetype_combobox:disabled {\n"
"    color: rgb(187, 187, 187);\n"
"}\n"
"\n"
"#g_invoicetype_combobox::drop-down,\n"
"#__g_invoicetype_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    width: 20px;\n"
"}\n"
"\n"
"#g_invoicetype_combobox::drop-down,\n"
"#__g_invoicetype_combobox::drop-down {\n"
"    background-image: url(:/returninvoices_popup/combobox_down_arrow);\n"
"    height: 31px;\n"
"}\n"
"\n"
"#g_invoicetype_combobox::drop-down:disabled,\n"
"#__g_invoicetype_combobox::drop-down:disabled {\n"
"    background-image: url(:/returninvoices_popup/disabled_combobox_down_arrow);\n"
"    height: 31px;\n"
"}\n"
"\n"
"\n"
"/*************************************************************/\n"
"/* Style directives for table widgets */\n"
"/*************************************************************/\n"
"\n"
"QHeaderView::section {\n"
"    text-align: left;\n"
"    spacing: 15px;\n"
"    background-color: #ffffff;\n"
"    color: #000000;\n"
"    font-weight: bold;\n"
"    border: 0px;\n"
"    height: 15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size: 12px;\n"
"    padding-top: 5px;\n"
"    padding-bottom: 5px;\n"
"    padding-left: 3px;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border: none;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableView {\n"
"\n"
"    /* Color of the table text.\n"
"\n"
"    Must be defined explicitly because the text color of the focused\n"
"    (:focus) item will be set to this. */\n"
"    color: black;\n"
"\n"
"    /* Colors of the selection text and background respectively.\n"
"\n"
"    These must be defined explicitly because the text/background color\n"
"    of the :focus:selected item will be set to this. */\n"
"    selection-color: white;\n"
"    selection-background-color: rgb(51, 153, 255);\n"
"\n"
"    /* Removes the dotted border from selected cells in a table widget. */\n"
"    outline: 0;\n"
"}\n"
"\n"
"QTableWidget::item:focus:selected,\n"
"QTableWidget::item:focus {\n"
"\n"
"    /* Do not display a border around any focused item whether it\'s part\n"
"    of a selection or not. */\n"
"    border: none;\n"
"}\n"
"\n"
"QTableWidget::item:focus:selected {\n"
"\n"
"    /* Set the text and background colors to be the same as the selection\n"
"    colors defined earlier.\n"
"\n"
"    Pseudo-states can be chained, in which case a logical AND is\n"
"    implied. */\n"
"    color: white;\n"
"    background-color: rgb(51, 153, 255);\n"
"}\n"
"\n"
"QTableWidget::item:focus {\n"
"\n"
"    /* Set the focused item text to be the same as the unfocused\n"
"    text color.\n"
"\n"
"    No \'unselected\' pseudo-state exists, only \'selected\'. */\n"
"    color: black;\n"
"\n"
"    /* Alternating row colors can also be styled, but \'transparent\'\n"
"    allows the default alternating row colors to be used without\n"
"    having to explicitly define new ones. */\n"
"    background-color: transparent;\n"
"}\n"
"\n"
"#label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/returninvoices_popup/x_normal"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        ReturnInvoicesPopup.setProperty("g_xnormal_icon", icon)
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/returninvoices_popup/x_grey"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        ReturnInvoicesPopup.setProperty("g_xgrey_icon", icon1)
        ReturnInvoicesPopup.setProperty("invoice_types_list", ['Sale:SALE', 'Layaway:LAYAWAY'])
        self.verticalLayout = QtWidgets.QVBoxLayout(ReturnInvoicesPopup)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_cover_frame = QtWidgets.QFrame(ReturnInvoicesPopup)
        self.g_cover_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_cover_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cover_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cover_frame.setObjectName("g_cover_frame")
        self.g_container_frame = QtWidgets.QFrame(self.g_cover_frame)
        self.g_container_frame.setGeometry(QtCore.QRect(20, 20, 591, 431))
        self.g_container_frame.setMinimumSize(QtCore.QSize(271, 345))
        self.g_container_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_container_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_container_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_container_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_container_frame.setObjectName("g_container_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_container_frame)
        self.verticalLayout_2.setContentsMargins(27, 27, 27, 27)
        self.verticalLayout_2.setSpacing(27)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.label = QtWidgets.QLabel(self.g_container_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.label.setFont(font)
        self.label.setObjectName("label")
        self.verticalLayout_2.addWidget(self.label)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setSpacing(6)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_invoicetype_combobox = QtWidgets.QComboBox(self.g_container_frame)
        self.g_invoicetype_combobox.setEnabled(True)
        self.g_invoicetype_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_invoicetype_combobox.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_invoicetype_combobox.setMinimumContentsLength(0)
        self.g_invoicetype_combobox.setObjectName("g_invoicetype_combobox")
        self.g_invoicetype_combobox.addItem("")
        self.g_invoicetype_combobox.addItem("")
        self.horizontalLayout_2.addWidget(self.g_invoicetype_combobox)
        self.g_invoiceentry_lineedit = QtWidgets.QLineEdit(self.g_container_frame)
        self.g_invoiceentry_lineedit.setContextMenuPolicy(QtCore.Qt.NoContextMenu)
        self.g_invoiceentry_lineedit.setAcceptDrops(False)
        self.g_invoiceentry_lineedit.setText("")
        self.g_invoiceentry_lineedit.setPlaceholderText("Enter invoice number(s), then press Enter to add them to this return.")
        self.g_invoiceentry_lineedit.setClearButtonEnabled(True)
        self.g_invoiceentry_lineedit.setObjectName("g_invoiceentry_lineedit")
        self.horizontalLayout_2.addWidget(self.g_invoiceentry_lineedit)
        self.verticalLayout_2.addLayout(self.horizontalLayout_2)
        self.g_invoicenumbers_table = QtWidgets.QTableWidget(self.g_container_frame)
        self.g_invoicenumbers_table.viewport().setProperty("cursor", QtGui.QCursor(QtCore.Qt.ArrowCursor))
        self.g_invoicenumbers_table.setMouseTracking(False)
        self.g_invoicenumbers_table.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_invoicenumbers_table.setContextMenuPolicy(QtCore.Qt.NoContextMenu)
        self.g_invoicenumbers_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_invoicenumbers_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_invoicenumbers_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_invoicenumbers_table.setTabKeyNavigation(False)
        self.g_invoicenumbers_table.setProperty("showDropIndicator", False)
        self.g_invoicenumbers_table.setDragDropOverwriteMode(False)
        self.g_invoicenumbers_table.setAlternatingRowColors(True)
        self.g_invoicenumbers_table.setSelectionMode(QtWidgets.QAbstractItemView.MultiSelection)
        self.g_invoicenumbers_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_invoicenumbers_table.setShowGrid(False)
        self.g_invoicenumbers_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_invoicenumbers_table.setWordWrap(False)
        self.g_invoicenumbers_table.setCornerButtonEnabled(False)
        self.g_invoicenumbers_table.setObjectName("g_invoicenumbers_table")
        self.g_invoicenumbers_table.setColumnCount(6)
        self.g_invoicenumbers_table.setRowCount(3)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoicenumbers_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoicenumbers_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoicenumbers_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_invoicenumbers_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_invoicenumbers_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_invoicenumbers_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_invoicenumbers_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoicenumbers_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoicenumbers_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoicenumbers_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoicenumbers_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoicenumbers_table.setItem(2, 0, item)
        self.g_invoicenumbers_table.horizontalHeader().setVisible(False)
        self.g_invoicenumbers_table.horizontalHeader().setDefaultSectionSize(100)
        self.g_invoicenumbers_table.horizontalHeader().setHighlightSections(False)
        self.g_invoicenumbers_table.horizontalHeader().setMinimumSectionSize(31)
        self.g_invoicenumbers_table.horizontalHeader().setSortIndicatorShown(False)
        self.g_invoicenumbers_table.horizontalHeader().setStretchLastSection(False)
        self.g_invoicenumbers_table.verticalHeader().setVisible(False)
        self.verticalLayout_2.addWidget(self.g_invoicenumbers_table)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(27)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setFocusPolicy(QtCore.Qt.NoFocus)
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/returninvoices_popup/close"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon2)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_startreturn_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_startreturn_button.setMinimumSize(QtCore.QSize(180, 40))
        self.g_startreturn_button.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_startreturn_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_startreturn_button.setFocusPolicy(QtCore.Qt.NoFocus)
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/returninvoices_popup/start_return"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_startreturn_button.setIcon(icon3)
        self.g_startreturn_button.setIconSize(QtCore.QSize(24, 24))
        self.g_startreturn_button.setObjectName("g_startreturn_button")
        self.horizontalLayout.addWidget(self.g_startreturn_button)
        self.verticalLayout_2.addLayout(self.horizontalLayout)
        self.verticalLayout.addWidget(self.g_cover_frame)

        self.retranslateUi(ReturnInvoicesPopup)
        QtCore.QMetaObject.connectSlotsByName(ReturnInvoicesPopup)

    def retranslateUi(self, ReturnInvoicesPopup):
        _translate = QtCore.QCoreApplication.translate
        ReturnInvoicesPopup.setWindowTitle(_translate("ReturnInvoicesPopup", "Dialog"))
        ReturnInvoicesPopup.setProperty("error_title_text", _translate("ReturnInvoicesPopup", "Error"))
        ReturnInvoicesPopup.setProperty("retrieving_invoice_msg_text", _translate("ReturnInvoicesPopup", "Retrieving invoice ..."))
        ReturnInvoicesPopup.setProperty("read_transaction_returns_url_text", _translate("ReturnInvoicesPopup", "/apps/cash_register/queries/read__qpt__sale_transaction_line_returns"))
        self.label.setText(_translate("ReturnInvoicesPopup", "Return Invoice(s)"))
        self.g_invoicetype_combobox.setItemText(0, _translate("ReturnInvoicesPopup", "Sale"))
        self.g_invoicetype_combobox.setItemText(1, _translate("ReturnInvoicesPopup", "Layaway"))
        self.g_invoicenumbers_table.setSortingEnabled(False)
        item = self.g_invoicenumbers_table.verticalHeaderItem(0)
        item.setText(_translate("ReturnInvoicesPopup", "0"))
        item = self.g_invoicenumbers_table.verticalHeaderItem(1)
        item.setText(_translate("ReturnInvoicesPopup", "1"))
        item = self.g_invoicenumbers_table.verticalHeaderItem(2)
        item.setText(_translate("ReturnInvoicesPopup", "2"))
        item = self.g_invoicenumbers_table.horizontalHeaderItem(0)
        item.setText(_translate("ReturnInvoicesPopup", "Invoice #"))
        item = self.g_invoicenumbers_table.horizontalHeaderItem(1)
        item.setText(_translate("ReturnInvoicesPopup", "Customer Name"))
        item = self.g_invoicenumbers_table.horizontalHeaderItem(2)
        item.setText(_translate("ReturnInvoicesPopup", "Date/Time"))
        item = self.g_invoicenumbers_table.horizontalHeaderItem(3)
        item.setText(_translate("ReturnInvoicesPopup", "Total Amount"))
        __sortingEnabled = self.g_invoicenumbers_table.isSortingEnabled()
        self.g_invoicenumbers_table.setSortingEnabled(False)
        item = self.g_invoicenumbers_table.item(0, 0)
        item.setText(_translate("ReturnInvoicesPopup", "1111"))
        item = self.g_invoicenumbers_table.item(1, 0)
        item.setText(_translate("ReturnInvoicesPopup", "2222"))
        item = self.g_invoicenumbers_table.item(2, 0)
        item.setText(_translate("ReturnInvoicesPopup", "3333"))
        self.g_invoicenumbers_table.setSortingEnabled(__sortingEnabled)
        self.g_cancel_button.setText(_translate("ReturnInvoicesPopup", "  Cancel"))
        self.g_startreturn_button.setText(_translate("ReturnInvoicesPopup", "  Start Return"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ReturnInvoicesPopup = QtWidgets.QDialog()
    ui = Ui_ReturnInvoicesPopup()
    ui.setupUi(ReturnInvoicesPopup)
    ReturnInvoicesPopup.show()
    sys.exit(app.exec_())
