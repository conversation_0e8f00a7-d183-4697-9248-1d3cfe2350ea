# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'passwordauthorization_popup.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_PasswordAuthorizationPopup(object):
    def setupUi(self, PasswordAuthorizationPopup):
        PasswordAuthorizationPopup.setObjectName("PasswordAuthorizationPopup")
        PasswordAuthorizationPopup.resize(461, 303)
        PasswordAuthorizationPopup.setStyleSheet("\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    color: white;\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"#g_ok_button:disabled,\n"
"#__g_ok_button:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}\n"
"\n"
"#g_error_label, \n"
"#__g_error_label {\n"
"    color: red;\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"#g_cover_frame, \n"
"#__g_cover_frame {\n"
"    background-color: rgb(0,0,0, 25%);\n"
"}\n"
"\n"
"#g_container_frame, \n"
"#__g_container_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border:1px solid #073c83;\n"
"    border-radius: 10px;\n"
"    background-color: #DDE4E9;\n"
"}\n"
"\n"
"#g_ok_button,\n"
"#__g_ok_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_ok_button:pressed, \n"
"#__g_ok_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button, \n"
"#__g_cancel_button {\n"
"    background-color: #ee1111;\n"
"    border:2px solid #CA0E0E;\n"
"}\n"
"\n"
"#g_cancel_button:pressed, \n"
"#__g_cancel_button:pressed {\n"
"    background-color:#CA0E0E;\n"
"}\n"
"\n"
"#g_title_label {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_instructions_label, #g_status_label {\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"#g_password_lineedit {\n"
"    font-size: 12px;\n"
"}")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(PasswordAuthorizationPopup)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_cover_frame = QtWidgets.QFrame(PasswordAuthorizationPopup)
        self.g_cover_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_cover_frame.setStyleSheet("")
        self.g_cover_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cover_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cover_frame.setObjectName("g_cover_frame")
        self.g_container_frame = QtWidgets.QFrame(self.g_cover_frame)
        self.g_container_frame.setGeometry(QtCore.QRect(40, 50, 381, 201))
        self.g_container_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_container_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_container_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_container_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_container_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_container_frame.setObjectName("g_container_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_container_frame)
        self.verticalLayout.setContentsMargins(16, 16, 16, 14)
        self.verticalLayout.setSpacing(12)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_title_label = QtWidgets.QLabel(self.g_container_frame)
        self.g_title_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_title_label.setFont(font)
        self.g_title_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_title_label.setWordWrap(True)
        self.g_title_label.setObjectName("g_title_label")
        self.verticalLayout.addWidget(self.g_title_label)
        self.g_instructions_label = QtWidgets.QLabel(self.g_container_frame)
        self.g_instructions_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_instructions_label.setFont(font)
        self.g_instructions_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_instructions_label.setWordWrap(True)
        self.g_instructions_label.setObjectName("g_instructions_label")
        self.verticalLayout.addWidget(self.g_instructions_label)
        self.g_error_label = QtWidgets.QLabel(self.g_container_frame)
        self.g_error_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_error_label.setFont(font)
        self.g_error_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_error_label.setWordWrap(True)
        self.g_error_label.setObjectName("g_error_label")
        self.verticalLayout.addWidget(self.g_error_label)
        self.g_status_label = QtWidgets.QLabel(self.g_container_frame)
        self.g_status_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_status_label.setFont(font)
        self.g_status_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_status_label.setObjectName("g_status_label")
        self.verticalLayout.addWidget(self.g_status_label)
        self.g_waiting_progressbar = QtWidgets.QProgressBar(self.g_container_frame)
        self.g_waiting_progressbar.setMinimumSize(QtCore.QSize(0, 31))
        self.g_waiting_progressbar.setMaximum(0)
        self.g_waiting_progressbar.setProperty("value", 0)
        self.g_waiting_progressbar.setTextVisible(False)
        self.g_waiting_progressbar.setInvertedAppearance(False)
        self.g_waiting_progressbar.setTextDirection(QtWidgets.QProgressBar.TopToBottom)
        self.g_waiting_progressbar.setObjectName("g_waiting_progressbar")
        self.verticalLayout.addWidget(self.g_waiting_progressbar)
        self.g_password_lineedit = QtWidgets.QLineEdit(self.g_container_frame)
        self.g_password_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_password_lineedit.setFont(font)
        self.g_password_lineedit.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.g_password_lineedit.setFrame(False)
        self.g_password_lineedit.setEchoMode(QtWidgets.QLineEdit.Password)
        self.g_password_lineedit.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_password_lineedit.setReadOnly(False)
        self.g_password_lineedit.setPlaceholderText("")
        self.g_password_lineedit.setClearButtonEnabled(True)
        self.g_password_lineedit.setObjectName("g_password_lineedit")
        self.verticalLayout.addWidget(self.g_password_lineedit)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(12)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setFocusPolicy(QtCore.Qt.NoFocus)
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/passwordauthorization_popup/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_ok_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_ok_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_ok_button.setMaximumSize(QtCore.QSize(120, 40))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_ok_button.setFont(font)
        self.g_ok_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_ok_button.setFocusPolicy(QtCore.Qt.NoFocus)
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/passwordauthorization_popup/ok"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_ok_button.setIcon(icon1)
        self.g_ok_button.setIconSize(QtCore.QSize(24, 24))
        self.g_ok_button.setObjectName("g_ok_button")
        self.horizontalLayout.addWidget(self.g_ok_button)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.verticalLayout_2.addWidget(self.g_cover_frame)

        self.retranslateUi(PasswordAuthorizationPopup)
        QtCore.QMetaObject.connectSlotsByName(PasswordAuthorizationPopup)

    def retranslateUi(self, PasswordAuthorizationPopup):
        _translate = QtCore.QCoreApplication.translate
        PasswordAuthorizationPopup.setWindowTitle(_translate("PasswordAuthorizationPopup", "Dialog"))
        PasswordAuthorizationPopup.setProperty("password_incorrect_msg_text", _translate("PasswordAuthorizationPopup", "The password you entered is incorrect"))
        PasswordAuthorizationPopup.setProperty("welcome_user_msg_text", _translate("PasswordAuthorizationPopup", "Welcome %(username)s"))
        PasswordAuthorizationPopup.setProperty("enter_password_msg_text", _translate("PasswordAuthorizationPopup", "Please enter a password to continue"))
        PasswordAuthorizationPopup.setProperty("inactivity_lockout_title_text", _translate("PasswordAuthorizationPopup", "As a security precaution, your login session has been locked due to inactivity"))
        PasswordAuthorizationPopup.setProperty("validating_password_msg_text", _translate("PasswordAuthorizationPopup", "Validating password. Please wait ..."))
        PasswordAuthorizationPopup.setProperty("autologout_busy_title_text", _translate("PasswordAuthorizationPopup", "Auto-logout in progress"))
        PasswordAuthorizationPopup.setProperty("please_wait_msg_text", _translate("PasswordAuthorizationPopup", "Please wait ..."))
        PasswordAuthorizationPopup.setProperty("autologout_finished_title_text", _translate("PasswordAuthorizationPopup", "Your login session has ended"))
        PasswordAuthorizationPopup.setProperty("enter_manager_password_msg_text", _translate("PasswordAuthorizationPopup", "Please enter a Manager password to continue"))
        PasswordAuthorizationPopup.setProperty("processing_request_msg_text", _translate("PasswordAuthorizationPopup", "Processing request ..."))
        PasswordAuthorizationPopup.setProperty("approval_success_msg_text", _translate("PasswordAuthorizationPopup", "Approval request succeeded"))
        PasswordAuthorizationPopup.setProperty("manager_approval_title_text", _translate("PasswordAuthorizationPopup", "This action requires the approval of a manager"))
        PasswordAuthorizationPopup.setProperty("location_disallowed_msg_text", _translate("PasswordAuthorizationPopup", "This user account cannot be used from this location."))
        PasswordAuthorizationPopup.setProperty("login_disallowed_msg_text", _translate("PasswordAuthorizationPopup", "This user account cannot be used with this application."))
        self.g_title_label.setText(_translate("PasswordAuthorizationPopup", "Authorization Required"))
        self.g_instructions_label.setText(_translate("PasswordAuthorizationPopup", "Please enter a password to continue"))
        self.g_error_label.setText(_translate("PasswordAuthorizationPopup", "The password you entered is incorrect"))
        self.g_status_label.setText(_translate("PasswordAuthorizationPopup", "Logging in ..."))
        self.g_cancel_button.setText(_translate("PasswordAuthorizationPopup", "  Cancel"))
        self.g_ok_button.setText(_translate("PasswordAuthorizationPopup", "  Ok"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    PasswordAuthorizationPopup = QtWidgets.QDialog()
    ui = Ui_PasswordAuthorizationPopup()
    ui.setupUi(PasswordAuthorizationPopup)
    PasswordAuthorizationPopup.show()
    sys.exit(app.exec_())
