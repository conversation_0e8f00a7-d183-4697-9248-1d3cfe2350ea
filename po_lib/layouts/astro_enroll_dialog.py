# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'astro_enroll_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_AstroEnrollDialog(object):
    def setupUi(self, AstroEnrollDialog):
        AstroEnrollDialog.setObjectName("AstroEnrollDialog")
        AstroEnrollDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        AstroEnrollDialog.resize(418, 307)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(AstroEnrollDialog.sizePolicy().hasHeightForWidth())
        AstroEnrollDialog.setSizePolicy(sizePolicy)
        AstroEnrollDialog.setMinimumSize(QtCore.QSize(0, 40))
        AstroEnrollDialog.setMaximumSize(QtCore.QSize(802, 16777215))
        AstroEnrollDialog.setStyleSheet("#g_yes_button, #g_link_button,  #g_search_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_yes_button:pressed, #g_link_button:pressed, #g_search_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_no_button, #g_cancel_button {\n"
"    background-color: #ee1111;\n"
"    border:2px solid #CA0E0E;\n"
"}\n"
"\n"
"#g_no_button:pressed, #g_cancel_button:pressed {\n"
"    background-color:#CA0E0E;\n"
"}\n"
"\n"
"#g_start_search_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"#g_start_search_button:pressed {\n"
"    background: #287599;\n"
"}\n"
"\n"
"#AstroEnrollDialog QPushButton, QPushButton {\n"
"    color:white;\n"
"    font-weight:bold;\n"
"    font-size:15px;\n"
"       }\n"
"\n"
"       #AstroEnrollDialog {\n"
"       background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"       }\n"
"\n"
"\n"
"       QLabel {\n"
"       font-weight:bold;\n"
"       font-size:16px;\n"
"       }\n"
"\n"
"       #g_yes_button:disabled,\n"
"       #g_link_button:disabled,\n"
"       #g_search_button:disabled,\n"
"       #g_no_button:disabled,\n"
"       #g_cancel_button:disabled,\n"
"       #g_start_search_button:disabled {\n"
"       background-color: rgb(164, 164, 164);\n"
"       border: None;\n"
"       }\n"
"   ")
        AstroEnrollDialog.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(AstroEnrollDialog)
        self.verticalLayout.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_message_frame = QtWidgets.QFrame(AstroEnrollDialog)
        self.g_message_frame.setMaximumSize(QtCore.QSize(400, 16777215))
        self.g_message_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_message_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_message_frame.setObjectName("g_message_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_message_frame)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_message_label = QtWidgets.QLabel(self.g_message_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_message_label.sizePolicy().hasHeightForWidth())
        self.g_message_label.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_message_label.setFont(font)
        self.g_message_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_message_label.setWordWrap(True)
        self.g_message_label.setObjectName("g_message_label")
        self.verticalLayout_2.addWidget(self.g_message_label)
        self.verticalLayout.addWidget(self.g_message_frame)
        self.g_search_frame = QtWidgets.QFrame(AstroEnrollDialog)
        self.g_search_frame.setMaximumSize(QtCore.QSize(400, 16777215))
        self.g_search_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_search_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_search_frame.setObjectName("g_search_frame")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_search_frame)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.label = QtWidgets.QLabel(self.g_search_frame)
        self.label.setObjectName("label")
        self.verticalLayout_3.addWidget(self.label)
        self.g_search_lineedit = QtWidgets.QLineEdit(self.g_search_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_search_lineedit.sizePolicy().hasHeightForWidth())
        self.g_search_lineedit.setSizePolicy(sizePolicy)
        self.g_search_lineedit.setMinimumSize(QtCore.QSize(0, 40))
        self.g_search_lineedit.setObjectName("g_search_lineedit")
        self.verticalLayout_3.addWidget(self.g_search_lineedit)
        self.verticalLayout.addWidget(self.g_search_frame)
        self.g_phone_frame = QtWidgets.QFrame(AstroEnrollDialog)
        self.g_phone_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_phone_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_phone_frame.setObjectName("g_phone_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_phone_frame)
        self.horizontalLayout_3.setContentsMargins(-1, 1, -1, 1)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.label_2 = QtWidgets.QLabel(self.g_phone_frame)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_3.addWidget(self.label_2)
        self.g_phone_lineedit = QtWidgets.QLineEdit(self.g_phone_frame)
        self.g_phone_lineedit.setMinimumSize(QtCore.QSize(0, 40))
        self.g_phone_lineedit.setObjectName("g_phone_lineedit")
        self.horizontalLayout_3.addWidget(self.g_phone_lineedit)
        self.verticalLayout.addWidget(self.g_phone_frame)
        self.g_searchresult_frame = QtWidgets.QFrame(AstroEnrollDialog)
        self.g_searchresult_frame.setMaximumSize(QtCore.QSize(400, 16777215))
        self.g_searchresult_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_searchresult_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_searchresult_frame.setObjectName("g_searchresult_frame")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_searchresult_frame)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_searchresult_label = QtWidgets.QLabel(self.g_searchresult_frame)
        self.g_searchresult_label.setWordWrap(True)
        self.g_searchresult_label.setObjectName("g_searchresult_label")
        self.verticalLayout_4.addWidget(self.g_searchresult_label)
        self.verticalLayout.addWidget(self.g_searchresult_frame)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem)
        self.g_buttons_frame = QtWidgets.QFrame(AstroEnrollDialog)
        self.g_buttons_frame.setMinimumSize(QtCore.QSize(0, 50))
        self.g_buttons_frame.setMaximumSize(QtCore.QSize(400, 16777215))
        self.g_buttons_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_buttons_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_buttons_frame.setObjectName("g_buttons_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_buttons_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(6)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_buttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(100, 40))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/astro_enroll_dialog/no"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_no_button = QtWidgets.QPushButton(self.g_buttons_frame)
        self.g_no_button.setMinimumSize(QtCore.QSize(100, 40))
        self.g_no_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/confirm_dialog/no"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_no_button.setIcon(icon1)
        self.g_no_button.setIconSize(QtCore.QSize(24, 24))
        self.g_no_button.setObjectName("g_no_button")
        self.horizontalLayout.addWidget(self.g_no_button)
        spacerItem2 = QtWidgets.QSpacerItem(0, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem2)
        self.g_start_search_button = QtWidgets.QPushButton(self.g_buttons_frame)
        self.g_start_search_button.setMinimumSize(QtCore.QSize(100, 40))
        self.g_start_search_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/confirm_dialog/add"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_start_search_button.setIcon(icon2)
        self.g_start_search_button.setIconSize(QtCore.QSize(24, 24))
        self.g_start_search_button.setObjectName("g_start_search_button")
        self.horizontalLayout.addWidget(self.g_start_search_button)
        self.g_search_button = QtWidgets.QPushButton(self.g_buttons_frame)
        self.g_search_button.setMinimumSize(QtCore.QSize(100, 40))
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/astro_enroll_dialog/search"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_search_button.setIcon(icon3)
        self.g_search_button.setIconSize(QtCore.QSize(24, 24))
        self.g_search_button.setObjectName("g_search_button")
        self.horizontalLayout.addWidget(self.g_search_button)
        self.g_yes_button = QtWidgets.QPushButton(self.g_buttons_frame)
        self.g_yes_button.setMinimumSize(QtCore.QSize(100, 40))
        self.g_yes_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/confirm_dialog/yes"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_yes_button.setIcon(icon4)
        self.g_yes_button.setIconSize(QtCore.QSize(24, 24))
        self.g_yes_button.setObjectName("g_yes_button")
        self.horizontalLayout.addWidget(self.g_yes_button)
        self.g_link_button = QtWidgets.QPushButton(self.g_buttons_frame)
        self.g_link_button.setMinimumSize(QtCore.QSize(100, 40))
        icon5 = QtGui.QIcon()
        icon5.addPixmap(QtGui.QPixmap(":/astro_enroll_dialog/yes"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_link_button.setIcon(icon5)
        self.g_link_button.setIconSize(QtCore.QSize(24, 24))
        self.g_link_button.setObjectName("g_link_button")
        self.horizontalLayout.addWidget(self.g_link_button)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem3)
        self.horizontalLayout_2.addLayout(self.horizontalLayout)
        self.verticalLayout.addWidget(self.g_buttons_frame)

        self.retranslateUi(AstroEnrollDialog)
        QtCore.QMetaObject.connectSlotsByName(AstroEnrollDialog)

    def retranslateUi(self, AstroEnrollDialog):
        _translate = QtCore.QCoreApplication.translate
        AstroEnrollDialog.setWindowTitle(_translate("AstroEnrollDialog", "Astro Loyalty Enrollment"))
        AstroEnrollDialog.setProperty("error_title_text", _translate("AstroEnrollDialog", "Error"))
        AstroEnrollDialog.setProperty("success_title_text", _translate("AstroEnrollDialog", "Success"))
        AstroEnrollDialog.setProperty("link_created_msg_text", _translate("AstroEnrollDialog", "Customer succesfully linked"))
        AstroEnrollDialog.setProperty("link_error_msg_text", _translate("AstroEnrollDialog", "Failed to link customer with their Astro Loyalty account."))
        self.g_message_label.setText(_translate("AstroEnrollDialog", "This customer is not enrolled with Astro Loyalty. Do you want to create enrollment?"))
        self.label.setText(_translate("AstroEnrollDialog", "Type your phone or email to search:"))
        self.label_2.setText(_translate("AstroEnrollDialog", "Mobile Phone"))
        self.g_searchresult_label.setText(_translate("AstroEnrollDialog", "Search results"))
        self.g_cancel_button.setText(_translate("AstroEnrollDialog", "Cancel"))
        self.g_no_button.setText(_translate("AstroEnrollDialog", "  No"))
        self.g_no_button.setShortcut(_translate("AstroEnrollDialog", "Enter"))
        self.g_start_search_button.setToolTip(_translate("AstroEnrollDialog", "Link customer with existing Astro account"))
        self.g_start_search_button.setText(_translate("AstroEnrollDialog", "Already\n"
"in Astro"))
        self.g_search_button.setText(_translate("AstroEnrollDialog", "Search"))
        self.g_yes_button.setToolTip(_translate("AstroEnrollDialog", "Will create Astro account for a customer"))
        self.g_yes_button.setText(_translate("AstroEnrollDialog", "  Yes"))
        self.g_link_button.setToolTip(_translate("AstroEnrollDialog", "Link customer with existing Astro account"))
        self.g_link_button.setText(_translate("AstroEnrollDialog", "Link"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    AstroEnrollDialog = QtWidgets.QDialog()
    ui = Ui_AstroEnrollDialog()
    ui.setupUi(AstroEnrollDialog)
    AstroEnrollDialog.show()
    sys.exit(app.exec_())
