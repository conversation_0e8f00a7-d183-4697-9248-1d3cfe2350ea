# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'pricekeypad_popup.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_PriceKeypadPopup(object):
    def setupUi(self, PriceKeypadPopup):
        PriceKeypadPopup.setObjectName("PriceKeypadPopup")
        PriceKeypadPopup.resize(271, 345)
        PriceKeypadPopup.setStyleSheet("#g_cover_frame, #__g_cover_frame {\n"
"    background-color: rgb(0,0,0, 0%);\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"#g_unit_price_subtract_button, #__g_unit_price_subtract_button {\n"
"    border-top-left-radius:5px;\n"
"    border-bottom-left-radius:5px;\n"
"}\n"
"\n"
"#g_unit_price_add_button, #__g_unit_price_add_button {\n"
"    border-top-right-radius:5px;\n"
"    border-bottom-right-radius:5px;\n"
"}\n"
"\n"
"\n"
"#g_container_frame, #__g_container_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border:1px solid #073c83;\n"
"    border-radius:10px;\n"
"    background-color:#DDE4E9;\n"
"}\n"
"\n"
"/*unit_price Keypad LineEdit*/\n"
".QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"\n"
"/*unit_price Keypad Frame*/\n"
"#g_keypad_unit_price_buttons_frame, #__g_keypad_unit_price_buttons_frame {\n"
"    border:0px;\n"
"}\n"
"\n"
"/*unit_price Keypad Buttons */\n"
"#g_keypad_unit_price_buttons_frame, #__g_keypad_unit_price_buttons_frame .QPushButton {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"    border-radius: 5px;\n"
"    margin:1px;\n"
"    font-weight:bold;\n"
"    font-size:15px;\n"
"}\n"
"\n"
"#g_keypad_unit_price_buttons_frame, #__g_keypad_unit_price_buttons_frame .QPushButton:hover {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #2980a9, stop: 1 #2980a9);\n"
"    border:2px solid #215f7c;\n"
"}\n"
"\n"
"#g_keypad_unit_price_buttons_frame, #__g_keypad_unit_price_buttons_frame .QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:1px solid #287599;\n"
"}\n"
"\n"
"#g_title_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"")
        self.verticalLayout = QtWidgets.QVBoxLayout(PriceKeypadPopup)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_cover_frame = QtWidgets.QFrame(PriceKeypadPopup)
        self.g_cover_frame.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.g_cover_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_cover_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_cover_frame.setLineWidth(0)
        self.g_cover_frame.setObjectName("g_cover_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_cover_frame)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_container_frame = QtWidgets.QFrame(self.g_cover_frame)
        self.g_container_frame.setMinimumSize(QtCore.QSize(271, 345))
        self.g_container_frame.setMaximumSize(QtCore.QSize(271, 345))
        self.g_container_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_container_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_container_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_container_frame.setLineWidth(0)
        self.g_container_frame.setObjectName("g_container_frame")
        self.g_readout_lineedit = QtWidgets.QLineEdit(self.g_container_frame)
        self.g_readout_lineedit.setGeometry(QtCore.QRect(10, 41, 250, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_readout_lineedit.setFont(font)
        self.g_readout_lineedit.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_readout_lineedit.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_readout_lineedit.setReadOnly(True)
        self.g_readout_lineedit.setObjectName("g_readout_lineedit")
        self.__g_keypad_unit_price_buttons_frame = QtWidgets.QFrame(self.g_container_frame)
        self.__g_keypad_unit_price_buttons_frame.setEnabled(True)
        self.__g_keypad_unit_price_buttons_frame.setGeometry(QtCore.QRect(10, 86, 250, 250))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_keypad_unit_price_buttons_frame.sizePolicy().hasHeightForWidth())
        self.__g_keypad_unit_price_buttons_frame.setSizePolicy(sizePolicy)
        self.__g_keypad_unit_price_buttons_frame.setMinimumSize(QtCore.QSize(200, 250))
        self.__g_keypad_unit_price_buttons_frame.setMaximumSize(QtCore.QSize(400, 500))
        self.__g_keypad_unit_price_buttons_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.__g_keypad_unit_price_buttons_frame.setStyleSheet("")
        self.__g_keypad_unit_price_buttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_keypad_unit_price_buttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_keypad_unit_price_buttons_frame.setObjectName("__g_keypad_unit_price_buttons_frame")
        self.gridLayout = QtWidgets.QGridLayout(self.__g_keypad_unit_price_buttons_frame)
        self.gridLayout.setContentsMargins(0, 0, 0, 0)
        self.gridLayout.setSpacing(5)
        self.gridLayout.setObjectName("gridLayout")
        self.g_five_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_five_button.sizePolicy().hasHeightForWidth())
        self.g_five_button.setSizePolicy(sizePolicy)
        self.g_five_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_five_button.setObjectName("g_five_button")
        self.gridLayout.addWidget(self.g_five_button, 1, 1, 1, 1)
        self.g_four_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_four_button.sizePolicy().hasHeightForWidth())
        self.g_four_button.setSizePolicy(sizePolicy)
        self.g_four_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_four_button.setObjectName("g_four_button")
        self.gridLayout.addWidget(self.g_four_button, 1, 0, 1, 1)
        self.g_nine_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_nine_button.sizePolicy().hasHeightForWidth())
        self.g_nine_button.setSizePolicy(sizePolicy)
        self.g_nine_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_nine_button.setObjectName("g_nine_button")
        self.gridLayout.addWidget(self.g_nine_button, 0, 2, 1, 1)
        self.g_eight_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_eight_button.sizePolicy().hasHeightForWidth())
        self.g_eight_button.setSizePolicy(sizePolicy)
        self.g_eight_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_eight_button.setObjectName("g_eight_button")
        self.gridLayout.addWidget(self.g_eight_button, 0, 1, 1, 1)
        self.g_seven_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_seven_button.sizePolicy().hasHeightForWidth())
        self.g_seven_button.setSizePolicy(sizePolicy)
        self.g_seven_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_seven_button.setObjectName("g_seven_button")
        self.gridLayout.addWidget(self.g_seven_button, 0, 0, 1, 1)
        self.g_six_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_six_button.sizePolicy().hasHeightForWidth())
        self.g_six_button.setSizePolicy(sizePolicy)
        self.g_six_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_six_button.setObjectName("g_six_button")
        self.gridLayout.addWidget(self.g_six_button, 1, 2, 1, 1)
        self.g_one_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_one_button.sizePolicy().hasHeightForWidth())
        self.g_one_button.setSizePolicy(sizePolicy)
        self.g_one_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_one_button.setObjectName("g_one_button")
        self.gridLayout.addWidget(self.g_one_button, 2, 0, 1, 1)
        self.g_two_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_two_button.sizePolicy().hasHeightForWidth())
        self.g_two_button.setSizePolicy(sizePolicy)
        self.g_two_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_two_button.setObjectName("g_two_button")
        self.gridLayout.addWidget(self.g_two_button, 2, 1, 1, 1)
        self.g_three_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_three_button.sizePolicy().hasHeightForWidth())
        self.g_three_button.setSizePolicy(sizePolicy)
        self.g_three_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_three_button.setObjectName("g_three_button")
        self.gridLayout.addWidget(self.g_three_button, 2, 2, 1, 1)
        self.g_enter_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_enter_button.sizePolicy().hasHeightForWidth())
        self.g_enter_button.setSizePolicy(sizePolicy)
        palette = QtGui.QPalette()
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(187, 226, 187))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ToolTipText, brush)
        self.g_enter_button.setPalette(palette)
        self.g_enter_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_enter_button.setObjectName("g_enter_button")
        self.gridLayout.addWidget(self.g_enter_button, 2, 3, 2, 1)
        self.g_zero_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_zero_button.sizePolicy().hasHeightForWidth())
        self.g_zero_button.setSizePolicy(sizePolicy)
        self.g_zero_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_zero_button.setObjectName("g_zero_button")
        self.gridLayout.addWidget(self.g_zero_button, 3, 0, 1, 1)
        self.g_doublezero_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_doublezero_button.sizePolicy().hasHeightForWidth())
        self.g_doublezero_button.setSizePolicy(sizePolicy)
        self.g_doublezero_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_doublezero_button.setObjectName("g_doublezero_button")
        self.gridLayout.addWidget(self.g_doublezero_button, 3, 1, 1, 1)
        self.g_decimal_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_decimal_button.sizePolicy().hasHeightForWidth())
        self.g_decimal_button.setSizePolicy(sizePolicy)
        self.g_decimal_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_decimal_button.setObjectName("g_decimal_button")
        self.gridLayout.addWidget(self.g_decimal_button, 3, 2, 1, 1)
        self.g_backspace_button = QtWidgets.QPushButton(self.__g_keypad_unit_price_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_backspace_button.sizePolicy().hasHeightForWidth())
        self.g_backspace_button.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_backspace_button.setFont(font)
        self.g_backspace_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_backspace_button.setText("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/pricekeypad_popup/backspace"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_backspace_button.setIcon(icon)
        self.g_backspace_button.setIconSize(QtCore.QSize(32, 32))
        self.g_backspace_button.setObjectName("g_backspace_button")
        self.gridLayout.addWidget(self.g_backspace_button, 0, 3, 2, 1)
        self.g_decimal_button.raise_()
        self.g_enter_button.raise_()
        self.g_nine_button.raise_()
        self.g_five_button.raise_()
        self.g_eight_button.raise_()
        self.g_seven_button.raise_()
        self.g_two_button.raise_()
        self.g_six_button.raise_()
        self.g_three_button.raise_()
        self.g_four_button.raise_()
        self.g_zero_button.raise_()
        self.g_one_button.raise_()
        self.g_doublezero_button.raise_()
        self.g_backspace_button.raise_()
        self.g_title_label = QtWidgets.QLabel(self.g_container_frame)
        self.g_title_label.setGeometry(QtCore.QRect(10, 10, 241, 21))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_title_label.setFont(font)
        self.g_title_label.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.g_title_label.setObjectName("g_title_label")
        self.verticalLayout_2.addWidget(self.g_container_frame)
        self.verticalLayout.addWidget(self.g_cover_frame, 0, QtCore.Qt.AlignHCenter)

        self.retranslateUi(PriceKeypadPopup)
        QtCore.QMetaObject.connectSlotsByName(PriceKeypadPopup)

    def retranslateUi(self, PriceKeypadPopup):
        _translate = QtCore.QCoreApplication.translate
        PriceKeypadPopup.setWindowTitle(_translate("PriceKeypadPopup", "Dialog"))
        self.g_readout_lineedit.setPlaceholderText(_translate("PriceKeypadPopup", "0"))
        self.g_five_button.setText(_translate("PriceKeypadPopup", "5"))
        self.g_four_button.setText(_translate("PriceKeypadPopup", "4"))
        self.g_nine_button.setText(_translate("PriceKeypadPopup", "9"))
        self.g_eight_button.setText(_translate("PriceKeypadPopup", "8"))
        self.g_seven_button.setText(_translate("PriceKeypadPopup", "7"))
        self.g_six_button.setText(_translate("PriceKeypadPopup", "6"))
        self.g_one_button.setText(_translate("PriceKeypadPopup", "1"))
        self.g_two_button.setText(_translate("PriceKeypadPopup", "2"))
        self.g_three_button.setText(_translate("PriceKeypadPopup", "3"))
        self.g_enter_button.setText(_translate("PriceKeypadPopup", "Enter"))
        self.g_zero_button.setText(_translate("PriceKeypadPopup", "0"))
        self.g_doublezero_button.setText(_translate("PriceKeypadPopup", "00"))
        self.g_decimal_button.setText(_translate("PriceKeypadPopup", "."))
        self.g_title_label.setText(_translate("PriceKeypadPopup", "Unit Price"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    PriceKeypadPopup = QtWidgets.QDialog()
    ui = Ui_PriceKeypadPopup()
    ui.setupUi(PriceKeypadPopup)
    PriceKeypadPopup.show()
    sys.exit(app.exec_())
