# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'customer_pet_select_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_CustomerPetSelectDialog(object):
    def setupUi(self, CustomerPetSelectDialog):
        CustomerPetSelectDialog.setObjectName("CustomerPetSelectDialog")
        CustomerPetSelectDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        CustomerPetSelectDialog.setEnabled(True)
        CustomerPetSelectDialog.resize(788, 458)
        CustomerPetSelectDialog.setMinimumSize(QtCore.QSize(750, 450))
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(False)
        font.setWeight(50)
        CustomerPetSelectDialog.setFont(font)
        CustomerPetSelectDialog.setStyleSheet("#CustomerPetSelectDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    border: 2px solid #287599;\n"
"    color:white\n"
"}\n"
"\n"
"#g_add_customer_pet {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"#g_add_customer_pet:pressed {\n"
"    background: #287599;\n"
"}\n"
"\n"
"#g_add_customer_pet:disabled {\n"
"}\n"
"\n"
"#g_select_button {\n"
"    background: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_select_button:pressed {\n"
"    background: #007f00;\n"
"}\n"
"\n"
"#g_select_button:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QTableWidget QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"        border:2px solid #007f00;\n"
"    color: white;\n"
"    background: #009c00;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}")
        CustomerPetSelectDialog.setModal(True)
        CustomerPetSelectDialog.setProperty("column_widths_list", ['100', '100', '100', '100', '100', '100', '100', '100', '115'])
        self.verticalLayout = QtWidgets.QVBoxLayout(CustomerPetSelectDialog)
        self.verticalLayout.setContentsMargins(18, 18, 18, 18)
        self.verticalLayout.setSpacing(18)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_main_label = QtWidgets.QLabel(CustomerPetSelectDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        self.g_customer_pet_table = QtWidgets.QTableWidget(CustomerPetSelectDialog)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_customer_pet_table.setFont(font)
        self.g_customer_pet_table.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.g_customer_pet_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_customer_pet_table.setSizeAdjustPolicy(QtWidgets.QAbstractScrollArea.AdjustToContentsOnFirstShow)
        self.g_customer_pet_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_customer_pet_table.setAlternatingRowColors(True)
        self.g_customer_pet_table.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.g_customer_pet_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_customer_pet_table.setRowCount(0)
        self.g_customer_pet_table.setObjectName("g_customer_pet_table")
        self.g_customer_pet_table.setColumnCount(8)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_customer_pet_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customer_pet_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customer_pet_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customer_pet_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customer_pet_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customer_pet_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customer_pet_table.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customer_pet_table.setHorizontalHeaderItem(7, item)
        self.g_customer_pet_table.horizontalHeader().setCascadingSectionResizes(False)
        self.g_customer_pet_table.horizontalHeader().setHighlightSections(False)
        self.g_customer_pet_table.horizontalHeader().setMinimumSectionSize(50)
        self.g_customer_pet_table.horizontalHeader().setSortIndicatorShown(True)
        self.g_customer_pet_table.horizontalHeader().setStretchLastSection(True)
        self.g_customer_pet_table.verticalHeader().setVisible(False)
        self.g_customer_pet_table.verticalHeader().setStretchLastSection(False)
        self.verticalLayout.addWidget(self.g_customer_pet_table)
        self.g_bottom_label = QtWidgets.QLabel(CustomerPetSelectDialog)
        self.g_bottom_label.setText("")
        self.g_bottom_label.setWordWrap(True)
        self.g_bottom_label.setObjectName("g_bottom_label")
        self.verticalLayout.addWidget(self.g_bottom_label)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(18)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(CustomerPetSelectDialog)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/customer_pet_select_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_add_customer_pet = QtWidgets.QPushButton(CustomerPetSelectDialog)
        self.g_add_customer_pet.setMinimumSize(QtCore.QSize(120, 40))
        self.g_add_customer_pet.setMaximumSize(QtCore.QSize(120, 40))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/astro_enroll_dialog/add"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_add_customer_pet.setIcon(icon1)
        self.g_add_customer_pet.setIconSize(QtCore.QSize(24, 24))
        self.g_add_customer_pet.setObjectName("g_add_customer_pet")
        self.horizontalLayout.addWidget(self.g_add_customer_pet)
        self.g_select_button = QtWidgets.QPushButton(CustomerPetSelectDialog)
        self.g_select_button.setEnabled(True)
        self.g_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_select_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/customer_pet_select_dialog/save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_select_button.setIcon(icon2)
        self.g_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_select_button.setObjectName("g_select_button")
        self.horizontalLayout.addWidget(self.g_select_button)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(CustomerPetSelectDialog)
        QtCore.QMetaObject.connectSlotsByName(CustomerPetSelectDialog)

    def retranslateUi(self, CustomerPetSelectDialog):
        _translate = QtCore.QCoreApplication.translate
        CustomerPetSelectDialog.setWindowTitle(_translate("CustomerPetSelectDialog", "Choose a Pet"))
        self.g_main_label.setText(_translate("CustomerPetSelectDialog", "Please choose a customer pet to apply the purchased subscription."))
        self.g_customer_pet_table.setSortingEnabled(True)
        item = self.g_customer_pet_table.horizontalHeaderItem(0)
        item.setText(_translate("CustomerPetSelectDialog", "Name"))
        item = self.g_customer_pet_table.horizontalHeaderItem(1)
        item.setText(_translate("CustomerPetSelectDialog", "Breed"))
        item = self.g_customer_pet_table.horizontalHeaderItem(2)
        item.setText(_translate("CustomerPetSelectDialog", "Type"))
        item = self.g_customer_pet_table.horizontalHeaderItem(3)
        item.setText(_translate("CustomerPetSelectDialog", "Color/Markings"))
        item = self.g_customer_pet_table.horizontalHeaderItem(4)
        item.setText(_translate("CustomerPetSelectDialog", "Identifiers"))
        item = self.g_customer_pet_table.horizontalHeaderItem(5)
        item.setText(_translate("CustomerPetSelectDialog", "Sex"))
        item = self.g_customer_pet_table.horizontalHeaderItem(6)
        item.setText(_translate("CustomerPetSelectDialog", "Birthdate"))
        self.g_cancel_button.setText(_translate("CustomerPetSelectDialog", " Cancel"))
        self.g_add_customer_pet.setText(_translate("CustomerPetSelectDialog", "Add Pet"))
        self.g_select_button.setText(_translate("CustomerPetSelectDialog", "Apply"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    CustomerPetSelectDialog = QtWidgets.QDialog()
    ui = Ui_CustomerPetSelectDialog()
    ui.setupUi(CustomerPetSelectDialog)
    CustomerPetSelectDialog.show()
    sys.exit(app.exec_())
