# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'update_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_UpdateDialog(object):
    def setupUi(self, UpdateDialog):
        UpdateDialog.setObjectName("UpdateDialog")
        UpdateDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        UpdateDialog.resize(691, 472)
        UpdateDialog.setMinimumSize(QtCore.QSize(691, 472))
        UpdateDialog.setStyleSheet("\n"
"QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QPushButton {\n"
"    color: white;\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_download_button {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"}\n"
"\n"
"#g_download_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border: 2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_message_label {\n"
"    font-size: 13px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_releasenotes_textedit {\n"
"    font-size: 12px;\n"
"}\n"
"")
        UpdateDialog.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(UpdateDialog)
        self.verticalLayout.setContentsMargins(18, 18, 18, 18)
        self.verticalLayout.setSpacing(18)
        self.verticalLayout.setObjectName("verticalLayout")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout()
        self.verticalLayout_2.setSpacing(6)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_message_label = QtWidgets.QLabel(UpdateDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_message_label.setFont(font)
        self.g_message_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_message_label.setWordWrap(True)
        self.g_message_label.setObjectName("g_message_label")
        self.verticalLayout_2.addWidget(self.g_message_label)
        self.label = QtWidgets.QLabel(UpdateDialog)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.label.setFont(font)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.verticalLayout_2.addWidget(self.label)
        self.verticalLayout.addLayout(self.verticalLayout_2)
        self.g_releasenotes_textedit = QtWidgets.QPlainTextEdit(UpdateDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_releasenotes_textedit.setFont(font)
        self.g_releasenotes_textedit.setReadOnly(True)
        self.g_releasenotes_textedit.setObjectName("g_releasenotes_textedit")
        self.verticalLayout.addWidget(self.g_releasenotes_textedit)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(16)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(UpdateDialog)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_cancel_button.setFont(font)
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/update_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_download_button = QtWidgets.QPushButton(UpdateDialog)
        self.g_download_button.setMinimumSize(QtCore.QSize(140, 40))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_download_button.setFont(font)
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/update_dialog/ok"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_download_button.setIcon(icon1)
        self.g_download_button.setIconSize(QtCore.QSize(24, 24))
        self.g_download_button.setObjectName("g_download_button")
        self.horizontalLayout.addWidget(self.g_download_button)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(UpdateDialog)
        QtCore.QMetaObject.connectSlotsByName(UpdateDialog)

    def retranslateUi(self, UpdateDialog):
        _translate = QtCore.QCoreApplication.translate
        UpdateDialog.setWindowTitle(_translate("UpdateDialog", "Update Available"))
        UpdateDialog.setProperty("new_version_msg_text", _translate("UpdateDialog", "A new version of Pinogy POS (v{version} build {build}) is available."))
        UpdateDialog.setProperty("dialog_title_text", _translate("UpdateDialog", "Update Available"))
        UpdateDialog.setProperty("downloading_update_msg_text", _translate("UpdateDialog", "Downloading update ..."))
        UpdateDialog.setProperty("applying_update_msg_text", _translate("UpdateDialog", "Applying update ..."))
        self.g_message_label.setText(_translate("UpdateDialog", "A new version of Pinogy POS (v1.14 build 2014-01-24) is available."))
        self.label.setText(_translate("UpdateDialog", "Would you like to download and install it?"))
        self.g_releasenotes_textedit.setPlainText(_translate("UpdateDialog", "Release Notes v1.14\n"
"======================\n"
"\n"
"- Change 1\n"
"- Change 2\n"
"- Change 3\n"
"\n"
"- First update of the Pinogy POS software suite. First update of the Pinogy\", \n"
"  Online software suite. First update of the Pinogy POS software suite. First\", \n"
"  update of the Pinogy POS software suite. First update of the Pinogy POS\", \n"
"  software suite.\", "))
        self.g_cancel_button.setText(_translate("UpdateDialog", "  Cancel"))
        self.g_download_button.setText(_translate("UpdateDialog", "  Download"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    UpdateDialog = QtWidgets.QDialog()
    ui = Ui_UpdateDialog()
    ui.setupUi(UpdateDialog)
    UpdateDialog.show()
    sys.exit(app.exec_())
