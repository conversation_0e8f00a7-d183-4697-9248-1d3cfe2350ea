# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'selecttrayrecount_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_SelectTrayRecount(object):
    def setupUi(self, SelectTrayRecount):
        SelectTrayRecount.setObjectName("SelectTrayRecount")
        SelectTrayRecount.resize(943, 571)
        SelectTrayRecount.setMinimumSize(QtCore.QSize(730, 360))
        SelectTrayRecount.setStyleSheet("#SelectTrayRecount {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox { \n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px; \n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/settings_screen/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/settings_screen/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"#g_unifiedsearch_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_searchbox_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_clearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_searchtext_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_search_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QDateTimeEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    height:31px;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"\n"
"#g_dialog_frame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QTabWidget {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"#g_desc_label {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color: #dddddd;\n"
"    color: #333333;\n"
"}\n"
"\n"
"\n"
"#g_select_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_select_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    background-image: url(:/invoices_screen/drop-down-arrow);\n"
"}\n"
"\n"
"#g_search_combobox_01::drop-down{\n"
"    background-image: url(:/invoices_screen/drop-down-arrow);\n"
"}\n"
"\n"
"QTableView {\n"
"\n"
"    /* Color of the table text.\n"
"\n"
"    Must be defined explicitly because the text color of the focused\n"
"    (:focus) item will be set to this.  */\n"
"    color: black;\n"
"\n"
"    /* Colors of the selection text and background respectively.\n"
"\n"
"    These must be defined explicitly because the text/background color\n"
"    of the :focus:selected item will be set to this.  */\n"
"    selection-color: white;\n"
"    selection-background-color: rgb(51, 153, 255);\n"
"\n"
"    /* Removes the dotted border from selected cells in a table widget. */\n"
"    outline: 0;\n"
"\n"
"}\n"
"\n"
"\n"
"QTableWidget::item:focus:selected,\n"
"QTableWidget::item:focus {\n"
"\n"
"    /* Do not display a border around any focused item whether it\'s part\n"
"    of a selection or not.  */\n"
"    border: none;\n"
"    \n"
"}\n"
"\n"
"\n"
"QTableWidget::item:focus:selected {\n"
"\n"
"    /* Set the text and background colors to be the same as the selection\n"
"    colors defined earlier.\n"
"\n"
"    Pseudo-states can be chained, in which case a logical AND is\n"
"    implied. */\n"
"    color: white;\n"
"    background-color: rgb(51, 153, 255);\n"
"}\n"
"\n"
"\n"
"QTableWidget::item:focus {\n"
"\n"
"    /* Set the focused item text to be the same \n"
"\n"
"    No \'unselected\' pseudo-state exists, only \'selected\'. */\n"
"\n"
"    color: black;\n"
"\n"
"    /* Alternating row colors can also be styled, but \'transparent\' allows\n"
"    the default alternating row colors to be used. */\n"
"    background-color: transparent;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_totalresults_label,\n"
"#g_pagination_frame_2 QLabel,\n"
"#g_pagination_frame_2 QLineEdit {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_startdate_label\n"
"#g_enddate_label {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"")
        self.verticalLayout = QtWidgets.QVBoxLayout(SelectTrayRecount)
        self.verticalLayout.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_progress_frame = QtWidgets.QFrame(SelectTrayRecount)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.verticalLayout.addWidget(self.g_progress_frame)
        self.g_main_label = QtWidgets.QLabel(SelectTrayRecount)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        self.g_daterange_frame = QtWidgets.QFrame(SelectTrayRecount)
        self.g_daterange_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_daterange_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_daterange_frame.setObjectName("g_daterange_frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_daterange_frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_startdate_label = QtWidgets.QLabel(self.g_daterange_frame)
        self.g_startdate_label.setMinimumSize(QtCore.QSize(75, 0))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_startdate_label.setFont(font)
        self.g_startdate_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_startdate_label.setObjectName("g_startdate_label")
        self.horizontalLayout_7.addWidget(self.g_startdate_label)
        self.g_start_datetimeedit = QtWidgets.QDateTimeEdit(self.g_daterange_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_start_datetimeedit.sizePolicy().hasHeightForWidth())
        self.g_start_datetimeedit.setSizePolicy(sizePolicy)
        self.g_start_datetimeedit.setMinimumSize(QtCore.QSize(120, 31))
        self.g_start_datetimeedit.setMaximumSize(QtCore.QSize(120, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_start_datetimeedit.setFont(font)
        self.g_start_datetimeedit.setStyleSheet("")
        self.g_start_datetimeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_start_datetimeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 6), QtCore.QTime(0, 0, 0)))
        self.g_start_datetimeedit.setCalendarPopup(True)
        self.g_start_datetimeedit.setTimeSpec(QtCore.Qt.LocalTime)
        self.g_start_datetimeedit.setObjectName("g_start_datetimeedit")
        self.horizontalLayout_7.addWidget(self.g_start_datetimeedit)
        self.g_enddate_label = QtWidgets.QLabel(self.g_daterange_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_enddate_label.sizePolicy().hasHeightForWidth())
        self.g_enddate_label.setSizePolicy(sizePolicy)
        self.g_enddate_label.setMinimumSize(QtCore.QSize(75, 0))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_enddate_label.setFont(font)
        self.g_enddate_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_enddate_label.setObjectName("g_enddate_label")
        self.horizontalLayout_7.addWidget(self.g_enddate_label)
        self.g_end_datetimeedit = QtWidgets.QDateTimeEdit(self.g_daterange_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_end_datetimeedit.sizePolicy().hasHeightForWidth())
        self.g_end_datetimeedit.setSizePolicy(sizePolicy)
        self.g_end_datetimeedit.setMinimumSize(QtCore.QSize(120, 31))
        self.g_end_datetimeedit.setMaximumSize(QtCore.QSize(120, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_end_datetimeedit.setFont(font)
        self.g_end_datetimeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_end_datetimeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 6), QtCore.QTime(0, 0, 0)))
        self.g_end_datetimeedit.setCalendarPopup(True)
        self.g_end_datetimeedit.setObjectName("g_end_datetimeedit")
        self.horizontalLayout_7.addWidget(self.g_end_datetimeedit)
        self.g_status_options_frame = QtWidgets.QFrame(self.g_daterange_frame)
        self.g_status_options_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_status_options_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_status_options_frame.setObjectName("g_status_options_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_status_options_frame)
        self.horizontalLayout_5.setContentsMargins(12, 6, 0, 2)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.horizontalLayout_7.addWidget(self.g_status_options_frame)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem)
        self.__g_searchresults_hlayout = QtWidgets.QHBoxLayout()
        self.__g_searchresults_hlayout.setSpacing(4)
        self.__g_searchresults_hlayout.setObjectName("__g_searchresults_hlayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.__g_searchresults_hlayout.addItem(spacerItem1)
        self.g_pagination_frame_2 = QtWidgets.QFrame(self.g_daterange_frame)
        self.g_pagination_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pagination_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pagination_frame_2.setObjectName("g_pagination_frame_2")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_pagination_frame_2)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(8)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagination_frame_2)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/invoices_screen/icons/previous.png"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.horizontalLayout_6.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagination_frame_2)
        self.g_nextpage_label.setEnabled(False)
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/invoices_screen/icons/next.png"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.horizontalLayout_6.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagination_frame_2)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setReadOnly(False)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.horizontalLayout_6.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label_2 = QtWidgets.QLabel(self.g_pagination_frame_2)
        self.g_slash_label_2.setObjectName("g_slash_label_2")
        self.horizontalLayout_6.addWidget(self.g_slash_label_2)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagination_frame_2)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.horizontalLayout_6.addWidget(self.g_totalpages_label)
        self.__g_searchresults_hlayout.addWidget(self.g_pagination_frame_2)
        self.horizontalLayout_7.addLayout(self.__g_searchresults_hlayout)
        self.verticalLayout.addWidget(self.g_daterange_frame)
        self.g_tray_table = QtWidgets.QTableWidget(SelectTrayRecount)
        self.g_tray_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_tray_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_tray_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_tray_table.setAlternatingRowColors(True)
        self.g_tray_table.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.g_tray_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_tray_table.setShowGrid(False)
        self.g_tray_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_tray_table.setObjectName("g_tray_table")
        self.g_tray_table.setColumnCount(6)
        self.g_tray_table.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_tray_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tray_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tray_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tray_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tray_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tray_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_tray_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_tray_table.setItem(0, 5, item)
        self.g_tray_table.horizontalHeader().setDefaultSectionSize(140)
        self.g_tray_table.horizontalHeader().setStretchLastSection(True)
        self.verticalLayout.addWidget(self.g_tray_table)
        self.g_dialogbuttons_frame = QtWidgets.QFrame(SelectTrayRecount)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dialogbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_dialogbuttons_frame.setSizePolicy(sizePolicy)
        self.g_dialogbuttons_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_dialogbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_dialogbuttons_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_dialogbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_dialogbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_dialogbuttons_frame.setObjectName("g_dialogbuttons_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_dialogbuttons_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setSpacing(5)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_totalresults_label = QtWidgets.QLabel(self.g_dialogbuttons_frame)
        self.g_totalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_totalresults_label.setFont(font)
        self.g_totalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_totalresults_label.setObjectName("g_totalresults_label")
        self.horizontalLayout_2.addWidget(self.g_totalresults_label)
        spacerItem2 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem2)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_cancel_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_cancel_button.sizePolicy().hasHeightForWidth())
        self.g_cancel_button.setSizePolicy(sizePolicy)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/invoices_screen/icons/flat_x_white_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/customers_screen_dialog/customers"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setProperty("screen_toolbutton_icon", icon1)
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_2.addWidget(self.g_cancel_button)
        self.g_select_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_select_button.setEnabled(True)
        self.g_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_select_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_select_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_select_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/confirm_dialog/yes"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_select_button.setIcon(icon2)
        self.g_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_select_button.setObjectName("g_select_button")
        self.horizontalLayout_2.addWidget(self.g_select_button)
        self.verticalLayout.addWidget(self.g_dialogbuttons_frame)

        self.retranslateUi(SelectTrayRecount)
        QtCore.QMetaObject.connectSlotsByName(SelectTrayRecount)

    def retranslateUi(self, SelectTrayRecount):
        _translate = QtCore.QCoreApplication.translate
        SelectTrayRecount.setWindowTitle(_translate("SelectTrayRecount", "Select Tray"))
        self.g_main_label.setText(_translate("SelectTrayRecount", "Tray Recount"))
        self.g_startdate_label.setText(_translate("SelectTrayRecount", "Start Date:"))
        self.g_start_datetimeedit.setDisplayFormat(_translate("SelectTrayRecount", "MM/dd/yyyy"))
        self.g_enddate_label.setText(_translate("SelectTrayRecount", "End Date:"))
        self.g_end_datetimeedit.setDisplayFormat(_translate("SelectTrayRecount", "MM/dd/yyyy"))
        self.g_previouspage_label.setToolTip(_translate("SelectTrayRecount", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("SelectTrayRecount", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("SelectTrayRecount", "1"))
        self.g_slash_label_2.setText(_translate("SelectTrayRecount", "/"))
        self.g_totalpages_label.setToolTip(_translate("SelectTrayRecount", "Total Pages"))
        self.g_totalpages_label.setText(_translate("SelectTrayRecount", "1"))
        self.g_tray_table.setSortingEnabled(True)
        item = self.g_tray_table.verticalHeaderItem(0)
        item.setText(_translate("SelectTrayRecount", "New Row"))
        item = self.g_tray_table.horizontalHeaderItem(0)
        item.setText(_translate("SelectTrayRecount", "Device Name"))
        item = self.g_tray_table.horizontalHeaderItem(1)
        item.setText(_translate("SelectTrayRecount", "Tray Name"))
        item = self.g_tray_table.horizontalHeaderItem(2)
        item.setText(_translate("SelectTrayRecount", "Tray Open"))
        item = self.g_tray_table.horizontalHeaderItem(3)
        item.setText(_translate("SelectTrayRecount", "Tray Closed"))
        item = self.g_tray_table.horizontalHeaderItem(4)
        item.setText(_translate("SelectTrayRecount", "Who Closed"))
        item = self.g_tray_table.horizontalHeaderItem(5)
        item.setText(_translate("SelectTrayRecount", "Over/Under"))
        __sortingEnabled = self.g_tray_table.isSortingEnabled()
        self.g_tray_table.setSortingEnabled(False)
        item = self.g_tray_table.item(0, 5)
        item.setText(_translate("SelectTrayRecount", "0.00"))
        self.g_tray_table.setSortingEnabled(__sortingEnabled)
        self.g_totalresults_label.setText(_translate("SelectTrayRecount", "Loading results."))
        self.g_cancel_button.setText(_translate("SelectTrayRecount", "Cancel"))
        self.g_cancel_button.setProperty("screen_toolbutton_tooltip_text", _translate("SelectTrayRecount", "View or manage customers"))
        self.g_cancel_button.setProperty("screen_toolbutton_title_text", _translate("SelectTrayRecount", "Customers"))
        self.g_cancel_button.setProperty("screen_toolbutton_stylesheet_text", _translate("SelectTrayRecount", "\n"
"QToolButton {\n"
"    background: #bb1d48;\n"
"    border: 2px solid #97173A;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #97173A;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}"))
        self.g_cancel_button.setProperty("screen_indicator_stylesheet_text", _translate("SelectTrayRecount", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"            the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #97173A;\n"
"    border: 0;\n"
"}"))
        self.g_select_button.setText(_translate("SelectTrayRecount", "Select"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    SelectTrayRecount = QtWidgets.QDialog()
    ui = Ui_SelectTrayRecount()
    ui.setupUi(SelectTrayRecount)
    SelectTrayRecount.show()
    sys.exit(app.exec_())
