# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'selectexport_popup.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_SelectExport(object):
    def setupUi(self, SelectExport):
        SelectExport.setObjectName("SelectExport")
        SelectExport.resize(320, 290)
        SelectExport.setMinimumSize(QtCore.QSize(0, 0))
        SelectExport.setMaximumSize(QtCore.QSize(16777215, 16777215))
        SelectExport.setStyleSheet("#SelectExport {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_selectbreed_frame, #g_selectbreed_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border:1px solid #073c83;\n"
"    border-radius:10px;\n"
"    background-color:#DDE4E9;\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
".QScrollArea {\n"
"    background-color: #f4f4f4;\n"
"    border-radius:5px;\n"
"    border:1px solid #073c83;\n"
"    padding:4px;\n"
"}\n"
"\n"
"QScrollBar:horizontal {\n"
"height:0px;\n"
"}\n"
"\n"
"#scrollAreaWidgetContents {\n"
"background-color: #f4f4f4;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"#g_cancel_button, #g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button, #g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"\n"
"#g_select_button, #g_new_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_select_button:pressed, #g_new_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button, #g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed, #g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"\n"
"\n"
"#g_delete_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_delete_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(SelectExport)
        self.verticalLayout_2.setContentsMargins(-1, 9, -1, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_selectbreed_frame = QtWidgets.QFrame(SelectExport)
        self.g_selectbreed_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_selectbreed_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_selectbreed_frame.setObjectName("g_selectbreed_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_selectbreed_frame)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_main_label = QtWidgets.QLabel(self.g_selectbreed_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setStyleSheet("")
        self.g_main_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        self.g_controlbuttons_frame_3 = QtWidgets.QFrame(self.g_selectbreed_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame_3.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame_3.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame_3.setMinimumSize(QtCore.QSize(247, 50))
        self.g_controlbuttons_frame_3.setMaximumSize(QtCore.QSize(16777215, 50))
        self.g_controlbuttons_frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame_3.setObjectName("g_controlbuttons_frame_3")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame_3)
        self.horizontalLayout_13.setContentsMargins(0, 10, 0, 0)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_13.addItem(spacerItem)
        self.g_all_button = QtWidgets.QPushButton(self.g_controlbuttons_frame_3)
        self.g_all_button.setMinimumSize(QtCore.QSize(200, 40))
        self.g_all_button.setMaximumSize(QtCore.QSize(200, 40))
        self.g_all_button.setStyleSheet("")
        self.g_all_button.setIconSize(QtCore.QSize(24, 24))
        self.g_all_button.setObjectName("g_all_button")
        self.horizontalLayout_13.addWidget(self.g_all_button)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_13.addItem(spacerItem1)
        self.verticalLayout.addWidget(self.g_controlbuttons_frame_3)
        self.g_controlbuttons_frame_4 = QtWidgets.QFrame(self.g_selectbreed_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame_4.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame_4.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame_4.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame_4.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame_4.setObjectName("g_controlbuttons_frame_4")
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame_4)
        self.horizontalLayout_14.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem2)
        self.g_screen_button = QtWidgets.QPushButton(self.g_controlbuttons_frame_4)
        self.g_screen_button.setMinimumSize(QtCore.QSize(200, 40))
        self.g_screen_button.setMaximumSize(QtCore.QSize(200, 40))
        self.g_screen_button.setStyleSheet("")
        self.g_screen_button.setIconSize(QtCore.QSize(24, 24))
        self.g_screen_button.setObjectName("g_screen_button")
        self.horizontalLayout_14.addWidget(self.g_screen_button)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem3)
        self.verticalLayout.addWidget(self.g_controlbuttons_frame_4)
        self.g_controlbuttons_frame_2 = QtWidgets.QFrame(self.g_selectbreed_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame_2.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame_2.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame_2.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame_2.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame_2.setObjectName("g_controlbuttons_frame_2")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame_2)
        self.horizontalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_12.addItem(spacerItem4)
        self.g_selected_button = QtWidgets.QPushButton(self.g_controlbuttons_frame_2)
        self.g_selected_button.setMinimumSize(QtCore.QSize(200, 40))
        self.g_selected_button.setMaximumSize(QtCore.QSize(200, 40))
        self.g_selected_button.setStyleSheet("")
        self.g_selected_button.setIconSize(QtCore.QSize(24, 24))
        self.g_selected_button.setObjectName("g_selected_button")
        self.horizontalLayout_12.addWidget(self.g_selected_button)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_12.addItem(spacerItem5)
        self.verticalLayout.addWidget(self.g_controlbuttons_frame_2)
        spacerItem6 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem6)
        self.g_bottombar_frame = QtWidgets.QFrame(self.g_selectbreed_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.g_bottombar_frame.setSizePolicy(sizePolicy)
        self.g_bottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_bottombar_frame.setStyleSheet("")
        self.g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottombar_frame.setObjectName("g_bottombar_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_bottombar_frame)
        self.horizontalLayout.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_controlbuttons_frame = QtWidgets.QFrame(self.g_bottombar_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem7)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_11.addWidget(self.g_cancel_button)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem8)
        self.horizontalLayout.addWidget(self.g_controlbuttons_frame)
        self.verticalLayout.addWidget(self.g_bottombar_frame)
        self.verticalLayout_2.addWidget(self.g_selectbreed_frame)

        self.retranslateUi(SelectExport)
        QtCore.QMetaObject.connectSlotsByName(SelectExport)

    def retranslateUi(self, SelectExport):
        _translate = QtCore.QCoreApplication.translate
        SelectExport.setWindowTitle(_translate("SelectExport", "Select Form"))
        self.g_main_label.setText(_translate("SelectExport", "What do you want to export?"))
        self.g_all_button.setText(_translate("SelectExport", "All Records"))
        self.g_screen_button.setText(_translate("SelectExport", "Records on Screen"))
        self.g_selected_button.setText(_translate("SelectExport", "Selected Records"))
        self.g_cancel_button.setText(_translate("SelectExport", "Cancel"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    SelectExport = QtWidgets.QDialog()
    ui = Ui_SelectExport()
    ui.setupUi(SelectExport)
    SelectExport.show()
    sys.exit(app.exec_())
