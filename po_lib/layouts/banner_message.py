# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'banner_message.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_BannerMessage(object):
    def setupUi(self, BannerMessage):
        BannerMessage.setObjectName("BannerMessage")
        BannerMessage.resize(566, 80)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(BannerMessage.sizePolicy().hasHeightForWidth())
        BannerMessage.setSizePolicy(sizePolicy)
        BannerMessage.setStyleSheet("\n"
"QFrame[severity=\"info\"]{ \n"
"    background-color: rgb(112, 153, 202); \n"
"}\n"
"\n"
"QFrame[severity=\"error\"]{\n"
"    background-color: rgb(255, 83, 86);\n"
"}\n"
"\n"
"QFrame[severity=\"critical\"]{\n"
"    background-color: grey;\n"
"}\n"
"\n"
"QFrame[severity=\"warning\"]{\n"
"    background-color: rgb(179, 162, 199);\n"
"}\n"
"\n"
"/* Syntax for testing for true or false values in boolean dynamic property\n"
"\n"
"       QLabel[is_close_button_bool=false]\n"
"       QLabel[is_close_button_bool=true]\n"
"\n"
"    Syntax for testing for any value ...\n"
"\n"
"       QLabel[is_close_button_bool]\n"
"\n"
"*/\n"
"\n"
"\n"
"/* Label that displays the message text */\n"
"QFrame[severity] > QLabel[is_message_bool=true] {\n"
"    color: white;\n"
"}\n"
"\n"
"/* Button for closing the message frame */\n"
"QFrame[severity] > QPushButton {\n"
"    background-color: none;\n"
"    border: none;\n"
"}\n"
"\n"
"#g_message_label {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}")
        BannerMessage.setFrameShape(QtWidgets.QFrame.NoFrame)
        BannerMessage.setFrameShadow(QtWidgets.QFrame.Raised)
        BannerMessage.setProperty("banner_stylesheet_text", "")
        BannerMessage.setProperty("banner_animation_speed_int", 5)
        BannerMessage.setProperty("banner_auto_close_timeout_int", 4)
        self.horizontalLayout = QtWidgets.QHBoxLayout(BannerMessage)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_message_label = QtWidgets.QLabel(BannerMessage)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_message_label.sizePolicy().hasHeightForWidth())
        self.g_message_label.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_message_label.setFont(font)
        self.g_message_label.setContextMenuPolicy(QtCore.Qt.NoContextMenu)
        self.g_message_label.setStyleSheet("")
        self.g_message_label.setTextFormat(QtCore.Qt.AutoText)
        self.g_message_label.setScaledContents(False)
        self.g_message_label.setWordWrap(True)
        self.g_message_label.setProperty("is_message_bool", True)
        self.g_message_label.setObjectName("g_message_label")
        self.horizontalLayout.addWidget(self.g_message_label)
        self.g_close_button = QtWidgets.QPushButton(BannerMessage)
        self.g_close_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_close_button.setContextMenuPolicy(QtCore.Qt.NoContextMenu)
        self.g_close_button.setStyleSheet("")
        self.g_close_button.setText("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/banner_message/close"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_close_button.setIcon(icon)
        self.g_close_button.setIconSize(QtCore.QSize(32, 32))
        self.g_close_button.setDefault(False)
        self.g_close_button.setFlat(True)
        self.g_close_button.setObjectName("g_close_button")
        self.horizontalLayout.addWidget(self.g_close_button)

        self.retranslateUi(BannerMessage)
        QtCore.QMetaObject.connectSlotsByName(BannerMessage)

    def retranslateUi(self, BannerMessage):
        _translate = QtCore.QCoreApplication.translate
        BannerMessage.setWindowTitle(_translate("BannerMessage", "Frame"))
        BannerMessage.setProperty("severity", _translate("BannerMessage", "info"))
        self.g_message_label.setText(_translate("BannerMessage", "Message displayed to the user ... Click the X button to close this message window."))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    BannerMessage = QtWidgets.QFrame()
    ui = Ui_BannerMessage()
    ui.setupUi(BannerMessage)
    BannerMessage.show()
    sys.exit(app.exec_())
