# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managetaxrates_modaldialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ManageTaxRates(object):
    def setupUi(self, ManageTaxRates):
        ManageTaxRates.setObjectName("ManageTaxRates")
        ManageTaxRates.resize(574, 392)
        ManageTaxRates.setMinimumSize(QtCore.QSize(574, 0))
        ManageTaxRates.setMaximumSize(QtCore.QSize(769, 16777215))
        ManageTaxRates.setStyleSheet("#ManageTaxRates {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"\n"
"#g_unifiedsearch_frame, #__g_unifiedsearch_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_searchbox_frame, #__g_searchbox_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_clearsearch_button, #__g_clearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_searchtext_lineedit, #__g_searchtext_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_search_combobox, #__g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_search_combobox, #g_search_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"\n"
"#g_cancel_button, #__g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed, #__g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_save_button, #__g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed, #__g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_pagenav_frame QLineEdit,\n"
"#g_pagenav_frame QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: normal;\n"
"}\n"
"\n"
"QFrame QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QFrame QLineEdit,\n"
"QFrame QDateEdit {\n"
"    font-size: 11px;\n"
"\n"
"}")
        self.verticalLayout = QtWidgets.QVBoxLayout(ManageTaxRates)
        self.verticalLayout.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_top_frame = QtWidgets.QFrame(ManageTaxRates)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_6.addWidget(self.g_progress_frame)
        self.verticalLayout.addWidget(self.g_top_frame)
        self.g_titlepagenav_frame = QtWidgets.QFrame(ManageTaxRates)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_30 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_30.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_30.setObjectName("horizontalLayout_30")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout_23 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_23.setObjectName("verticalLayout_23")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_23.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_23.addItem(spacerItem)
        self.horizontalLayout_30.addWidget(self.g_title_frame)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout.setSpacing(6)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout.addItem(spacerItem1)
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/managedevice_modaldialog/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/managedevice_modaldialog/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagenav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_slash_label.setObjectName("g_slash_label")
        self.g_pagenav_hlayout.addWidget(self.g_slash_label)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout.addWidget(self.g_totalpages_label)
        self.horizontalLayout_30.addWidget(self.g_pagenav_frame)
        self.verticalLayout.addWidget(self.g_titlepagenav_frame)
        self.g_taxname_frame = QtWidgets.QFrame(ManageTaxRates)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_taxname_frame.sizePolicy().hasHeightForWidth())
        self.g_taxname_frame.setSizePolicy(sizePolicy)
        self.g_taxname_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_taxname_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_taxname_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_taxname_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_taxname_frame.setObjectName("g_taxname_frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_taxname_frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setSpacing(4)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_taxgroup_label = QtWidgets.QLabel(self.g_taxname_frame)
        self.g_taxgroup_label.setMinimumSize(QtCore.QSize(104, 0))
        self.g_taxgroup_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_taxgroup_label.setFont(font)
        self.g_taxgroup_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_taxgroup_label.setObjectName("g_taxgroup_label")
        self.horizontalLayout_7.addWidget(self.g_taxgroup_label)
        self.g_taxname_lineedit = QtWidgets.QLineEdit(self.g_taxname_frame)
        self.g_taxname_lineedit.setEnabled(False)
        self.g_taxname_lineedit.setMinimumSize(QtCore.QSize(426, 0))
        self.g_taxname_lineedit.setMaximumSize(QtCore.QSize(275, 16777215))
        self.g_taxname_lineedit.setObjectName("g_taxname_lineedit")
        self.horizontalLayout_7.addWidget(self.g_taxname_lineedit)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem2)
        self.verticalLayout.addWidget(self.g_taxname_frame)
        self.g_taxpercentage_frame = QtWidgets.QFrame(ManageTaxRates)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_taxpercentage_frame.sizePolicy().hasHeightForWidth())
        self.g_taxpercentage_frame.setSizePolicy(sizePolicy)
        self.g_taxpercentage_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_taxpercentage_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_taxpercentage_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_taxpercentage_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_taxpercentage_frame.setObjectName("g_taxpercentage_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_taxpercentage_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_taxpercentage_label = QtWidgets.QLabel(self.g_taxpercentage_frame)
        self.g_taxpercentage_label.setMinimumSize(QtCore.QSize(104, 0))
        self.g_taxpercentage_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_taxpercentage_label.setFont(font)
        self.g_taxpercentage_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_taxpercentage_label.setObjectName("g_taxpercentage_label")
        self.horizontalLayout.addWidget(self.g_taxpercentage_label)
        self.g_taxpercentage_lineedit = QtWidgets.QLineEdit(self.g_taxpercentage_frame)
        self.g_taxpercentage_lineedit.setMinimumSize(QtCore.QSize(100, 0))
        self.g_taxpercentage_lineedit.setMaximumSize(QtCore.QSize(50, 16777215))
        self.g_taxpercentage_lineedit.setPlaceholderText("")
        self.g_taxpercentage_lineedit.setProperty("qp_taxrt_percent", "")
        self.g_taxpercentage_lineedit.setObjectName("g_taxpercentage_lineedit")
        self.horizontalLayout.addWidget(self.g_taxpercentage_lineedit)
        self.g_taxpercentage_label_2 = QtWidgets.QLabel(self.g_taxpercentage_frame)
        self.g_taxpercentage_label_2.setMinimumSize(QtCore.QSize(10, 0))
        self.g_taxpercentage_label_2.setMaximumSize(QtCore.QSize(29, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_taxpercentage_label_2.setFont(font)
        self.g_taxpercentage_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_taxpercentage_label_2.setObjectName("g_taxpercentage_label_2")
        self.horizontalLayout.addWidget(self.g_taxpercentage_label_2)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem3)
        self.verticalLayout.addWidget(self.g_taxpercentage_frame)
        self.g_limit_frame = QtWidgets.QFrame(ManageTaxRates)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_limit_frame.sizePolicy().hasHeightForWidth())
        self.g_limit_frame.setSizePolicy(sizePolicy)
        self.g_limit_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_limit_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_limit_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_limit_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_limit_frame.setObjectName("g_limit_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_limit_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setSpacing(6)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_limit_label = QtWidgets.QLabel(self.g_limit_frame)
        self.g_limit_label.setMinimumSize(QtCore.QSize(102, 0))
        self.g_limit_label.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_limit_label.setFont(font)
        self.g_limit_label.setText("")
        self.g_limit_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_limit_label.setObjectName("g_limit_label")
        self.horizontalLayout_3.addWidget(self.g_limit_label)
        self.g_limit_bydate_checkbox = QtWidgets.QCheckBox(self.g_limit_frame)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_limit_bydate_checkbox.setFont(font)
        self.g_limit_bydate_checkbox.setChecked(False)
        self.g_limit_bydate_checkbox.setProperty("qp_taxrt_valid_during", "")
        self.g_limit_bydate_checkbox.setObjectName("g_limit_bydate_checkbox")
        self.horizontalLayout_3.addWidget(self.g_limit_bydate_checkbox)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem4)
        self.verticalLayout.addWidget(self.g_limit_frame)
        self.g_startenddate_frame = QtWidgets.QFrame(ManageTaxRates)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_startenddate_frame.sizePolicy().hasHeightForWidth())
        self.g_startenddate_frame.setSizePolicy(sizePolicy)
        self.g_startenddate_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_startenddate_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_startenddate_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_startenddate_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_startenddate_frame.setObjectName("g_startenddate_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_startenddate_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(4)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.g_startdate_label = QtWidgets.QLabel(self.g_startenddate_frame)
        self.g_startdate_label.setMinimumSize(QtCore.QSize(104, 0))
        self.g_startdate_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_startdate_label.setFont(font)
        self.g_startdate_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_startdate_label.setObjectName("g_startdate_label")
        self.horizontalLayout_11.addWidget(self.g_startdate_label)
        self.g_startdate_timeedit = QtWidgets.QDateEdit(self.g_startenddate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_startdate_timeedit.sizePolicy().hasHeightForWidth())
        self.g_startdate_timeedit.setSizePolicy(sizePolicy)
        self.g_startdate_timeedit.setMinimumSize(QtCore.QSize(100, 31))
        self.g_startdate_timeedit.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_startdate_timeedit.setFont(font)
        self.g_startdate_timeedit.setStyleSheet("")
        self.g_startdate_timeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_startdate_timeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2013, 12, 6), QtCore.QTime(16, 0, 0)))
        self.g_startdate_timeedit.setCalendarPopup(True)
        self.g_startdate_timeedit.setTimeSpec(QtCore.Qt.LocalTime)
        self.g_startdate_timeedit.setProperty("qp_taxrt_valid_during", "")
        self.g_startdate_timeedit.setObjectName("g_startdate_timeedit")
        self.horizontalLayout_11.addWidget(self.g_startdate_timeedit)
        self.g_enddate_label = QtWidgets.QLabel(self.g_startenddate_frame)
        self.g_enddate_label.setMinimumSize(QtCore.QSize(75, 0))
        self.g_enddate_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_enddate_label.setFont(font)
        self.g_enddate_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_enddate_label.setObjectName("g_enddate_label")
        self.horizontalLayout_11.addWidget(self.g_enddate_label)
        self.g_enddate_timeedit = QtWidgets.QDateEdit(self.g_startenddate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_enddate_timeedit.sizePolicy().hasHeightForWidth())
        self.g_enddate_timeedit.setSizePolicy(sizePolicy)
        self.g_enddate_timeedit.setMinimumSize(QtCore.QSize(100, 31))
        self.g_enddate_timeedit.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_enddate_timeedit.setFont(font)
        self.g_enddate_timeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_enddate_timeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 6), QtCore.QTime(16, 0, 0)))
        self.g_enddate_timeedit.setCalendarPopup(True)
        self.g_enddate_timeedit.setProperty("qp_taxrt_valid_during", "")
        self.g_enddate_timeedit.setObjectName("g_enddate_timeedit")
        self.horizontalLayout_11.addWidget(self.g_enddate_timeedit)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem5)
        self.verticalLayout.addWidget(self.g_startenddate_frame)
        self.g_limit_frame_2 = QtWidgets.QFrame(ManageTaxRates)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_limit_frame_2.sizePolicy().hasHeightForWidth())
        self.g_limit_frame_2.setSizePolicy(sizePolicy)
        self.g_limit_frame_2.setMinimumSize(QtCore.QSize(0, 31))
        self.g_limit_frame_2.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_limit_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_limit_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_limit_frame_2.setObjectName("g_limit_frame_2")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_limit_frame_2)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setSpacing(6)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_limit_label_2 = QtWidgets.QLabel(self.g_limit_frame_2)
        self.g_limit_label_2.setMinimumSize(QtCore.QSize(102, 0))
        self.g_limit_label_2.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_limit_label_2.setFont(font)
        self.g_limit_label_2.setText("")
        self.g_limit_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_limit_label_2.setObjectName("g_limit_label_2")
        self.horizontalLayout_4.addWidget(self.g_limit_label_2)
        self.g_limit_byvalue_checkbox = QtWidgets.QCheckBox(self.g_limit_frame_2)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_limit_byvalue_checkbox.setFont(font)
        self.g_limit_byvalue_checkbox.setChecked(False)
        self.g_limit_byvalue_checkbox.setProperty("qp_taxrt_valid_values_lower", "")
        self.g_limit_byvalue_checkbox.setProperty("qp_taxrt_valid_values_upper", "")
        self.g_limit_byvalue_checkbox.setObjectName("g_limit_byvalue_checkbox")
        self.horizontalLayout_4.addWidget(self.g_limit_byvalue_checkbox)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem6)
        self.verticalLayout.addWidget(self.g_limit_frame_2)
        self.g_startendvalue_frame = QtWidgets.QFrame(ManageTaxRates)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_startendvalue_frame.sizePolicy().hasHeightForWidth())
        self.g_startendvalue_frame.setSizePolicy(sizePolicy)
        self.g_startendvalue_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_startendvalue_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_startendvalue_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_startendvalue_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_startendvalue_frame.setObjectName("g_startendvalue_frame")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.g_startendvalue_frame)
        self.horizontalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_12.setSpacing(4)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.g_startvalue_label = QtWidgets.QLabel(self.g_startendvalue_frame)
        self.g_startvalue_label.setMinimumSize(QtCore.QSize(104, 0))
        self.g_startvalue_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_startvalue_label.setFont(font)
        self.g_startvalue_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_startvalue_label.setObjectName("g_startvalue_label")
        self.horizontalLayout_12.addWidget(self.g_startvalue_label)
        self.g_startvalue_lineedit = QtWidgets.QLineEdit(self.g_startendvalue_frame)
        self.g_startvalue_lineedit.setMinimumSize(QtCore.QSize(100, 0))
        self.g_startvalue_lineedit.setMaximumSize(QtCore.QSize(50, 16777215))
        self.g_startvalue_lineedit.setPlaceholderText("")
        self.g_startvalue_lineedit.setProperty("qp_taxrt_valid_values_lower", "")
        self.g_startvalue_lineedit.setObjectName("g_startvalue_lineedit")
        self.horizontalLayout_12.addWidget(self.g_startvalue_lineedit)
        self.g_endvalue_label = QtWidgets.QLabel(self.g_startendvalue_frame)
        self.g_endvalue_label.setMinimumSize(QtCore.QSize(75, 0))
        self.g_endvalue_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_endvalue_label.setFont(font)
        self.g_endvalue_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_endvalue_label.setObjectName("g_endvalue_label")
        self.horizontalLayout_12.addWidget(self.g_endvalue_label)
        self.g_endvalue_lineedit = QtWidgets.QLineEdit(self.g_startendvalue_frame)
        self.g_endvalue_lineedit.setMinimumSize(QtCore.QSize(100, 0))
        self.g_endvalue_lineedit.setMaximumSize(QtCore.QSize(50, 16777215))
        self.g_endvalue_lineedit.setPlaceholderText("")
        self.g_endvalue_lineedit.setProperty("qp_taxrt_valid_values_upper", "")
        self.g_endvalue_lineedit.setObjectName("g_endvalue_lineedit")
        self.horizontalLayout_12.addWidget(self.g_endvalue_lineedit)
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_12.addItem(spacerItem7)
        self.verticalLayout.addWidget(self.g_startendvalue_frame)
        spacerItem8 = QtWidgets.QSpacerItem(17, 19, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem8)
        self.__g_bottombar_frame = QtWidgets.QFrame(ManageTaxRates)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_bottombar_frame.setSizePolicy(sizePolicy)
        self.__g_bottombar_frame.setMinimumSize(QtCore.QSize(247, 39))
        self.__g_bottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_bottombar_frame.setObjectName("__g_bottombar_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.__g_bottombar_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        spacerItem9 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem9)
        self.g_cancel_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_2.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_2.addWidget(self.g_save_button)
        self.verticalLayout.addWidget(self.__g_bottombar_frame)

        self.retranslateUi(ManageTaxRates)
        QtCore.QMetaObject.connectSlotsByName(ManageTaxRates)

    def retranslateUi(self, ManageTaxRates):
        _translate = QtCore.QCoreApplication.translate
        ManageTaxRates.setWindowTitle(_translate("ManageTaxRates", "Manage Tax Rates"))
        self.g_main_label.setText(_translate("ManageTaxRates", "Manage Tax"))
        self.g_previouspage_label.setToolTip(_translate("ManageTaxRates", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("ManageTaxRates", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("ManageTaxRates", "999"))
        self.g_slash_label.setText(_translate("ManageTaxRates", "/"))
        self.g_totalpages_label.setToolTip(_translate("ManageTaxRates", "Total Pages"))
        self.g_totalpages_label.setText(_translate("ManageTaxRates", "999"))
        self.g_taxgroup_label.setText(_translate("ManageTaxRates", "Tax Name"))
        self.g_taxname_lineedit.setPlaceholderText(_translate("ManageTaxRates", "Enter a name for this tax..."))
        self.g_taxpercentage_label.setText(_translate("ManageTaxRates", "Tax Percentage"))
        self.g_taxpercentage_label_2.setText(_translate("ManageTaxRates", "%"))
        self.g_limit_bydate_checkbox.setText(_translate("ManageTaxRates", "Limit by date range:"))
        self.g_startdate_label.setText(_translate("ManageTaxRates", "Start Date"))
        self.g_enddate_label.setText(_translate("ManageTaxRates", "End Date"))
        self.g_limit_byvalue_checkbox.setText(_translate("ManageTaxRates", "Limit by value range:"))
        self.g_startvalue_label.setText(_translate("ManageTaxRates", "Start Value"))
        self.g_endvalue_label.setText(_translate("ManageTaxRates", "End Value"))
        self.g_cancel_button.setText(_translate("ManageTaxRates", " Cancel"))
        self.g_save_button.setText(_translate("ManageTaxRates", " Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageTaxRates = QtWidgets.QDialog()
    ui = Ui_ManageTaxRates()
    ui.setupUi(ManageTaxRates)
    ManageTaxRates.show()
    sys.exit(app.exec_())
