# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managerole_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_ManageRole(object):
    def setupUi(self, ManageRole):
        ManageRole.setObjectName("ManageRole")
        ManageRole.resize(566, 611)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(ManageRole.sizePolicy().hasHeightForWidth())
        ManageRole.setSizePolicy(sizePolicy)
        ManageRole.setMinimumSize(QtCore.QSize(500, 0))
        ManageRole.setMaximumSize(QtCore.QSize(16777215, 16777215))
        ManageRole.setStyleSheet("#ManageRole {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QCheckBox {\n"
"    padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px; \n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/entity_dialog/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/entity_dialog/checked\');\n"
"    border:0px;\n"
"} \n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"    /*background-color: #000;*/\n"
"}\n"
"\n"
".QListWidget {\n"
"    border-radius: 5px;\n"
"    padding: 2px;\n"
"    background: transparent;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
".QListWidget::item {\n"
"    padding-bottom:1px;\n"
"    padding-right:1px;\n"
"}\n"
"\n"
".QListWidget::item::selected {\n"
"background-color: rgb(174, 227, 255);\n"
"    color: #000000\n"
"}\n"
"\n"
".QListWidget::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
".QListWidget::indicator:unchecked {\n"
"    image: url(\':/entity_dialog/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
".QListWidget::indicator:checked {\n"
"    image: url(\':/entity_dialog/checked\');\n"
"    border:0px;\n"
"} \n"
"\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"\n"
"#g_unifiedsearch_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_searchbox_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_clearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_searchtext_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_search_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QDateTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"#g_dialog_frame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
" QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QTabWidget {\n"
"    border:0px;\n"
"}\n"
"\n"
"#g_printingoptions_widget,\n"
"#g_verify_widget {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius:0px;\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"#g_new_button,\n"
"#g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_new_button:pressed,\n"
"#g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_delete_button,\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_delete_button:pressed,\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    background-image: url(:/purchasing_and_receiving/drop-down-arrow);\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_pagenav_frame QLabel,\n"
"#g_pagenav_frame QLineEdit {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_brandname_label, #g_apps_label {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}")
        ManageRole.setProperty("screen_toolbutton_tooltip_text", "")
        ManageRole.setProperty("screen_toolbutton_title_text", "")
        ManageRole.setProperty("screen_toolbutton_stylesheet_text", "")
        ManageRole.setProperty("screen_indicator_stylesheet_text", "")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(ManageRole)
        self.verticalLayout_2.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_top_frame = QtWidgets.QFrame(ManageRole)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_6.addWidget(self.g_progress_frame)
        self.verticalLayout_2.addWidget(self.g_top_frame)
        self.g_titlepagenav_frame = QtWidgets.QFrame(ManageRole)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_30 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_30.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_30.setObjectName("horizontalLayout_30")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout_23 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_23.setObjectName("verticalLayout_23")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_23.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_23.addItem(spacerItem)
        self.horizontalLayout_30.addWidget(self.g_title_frame)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout.setSpacing(6)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout.addItem(spacerItem1)
        self.horizontalLayout_30.addWidget(self.g_pagenav_frame)
        self.verticalLayout_2.addWidget(self.g_titlepagenav_frame)
        self.g_brandname_frame = QtWidgets.QFrame(ManageRole)
        self.g_brandname_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_brandname_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_brandname_frame.setObjectName("g_brandname_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_brandname_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 9, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_brandname_label = QtWidgets.QLabel(self.g_brandname_frame)
        self.g_brandname_label.setMinimumSize(QtCore.QSize(72, 0))
        self.g_brandname_label.setMaximumSize(QtCore.QSize(72, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_brandname_label.setFont(font)
        self.g_brandname_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_brandname_label.setObjectName("g_brandname_label")
        self.horizontalLayout_2.addWidget(self.g_brandname_label)
        self.g_name_lineedit = QtWidgets.QLineEdit(self.g_brandname_frame)
        self.g_name_lineedit.setMinimumSize(QtCore.QSize(400, 31))
        self.g_name_lineedit.setMaximumSize(QtCore.QSize(100, 31))
        self.g_name_lineedit.setObjectName("g_name_lineedit")
        self.horizontalLayout_2.addWidget(self.g_name_lineedit)
        self.g_role_combobox = QtWidgets.QComboBox(self.g_brandname_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_role_combobox.sizePolicy().hasHeightForWidth())
        self.g_role_combobox.setSizePolicy(sizePolicy)
        self.g_role_combobox.setMinimumSize(QtCore.QSize(400, 0))
        self.g_role_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_role_combobox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_role_combobox.setStyleSheet("")
        self.g_role_combobox.setCurrentText("")
        self.g_role_combobox.setFrame(False)
        self.g_role_combobox.setObjectName("g_role_combobox")
        self.horizontalLayout_2.addWidget(self.g_role_combobox)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem2)
        self.verticalLayout_2.addWidget(self.g_brandname_frame)
        self.g_showsin_groupBox = QtWidgets.QGroupBox(ManageRole)
        self.g_showsin_groupBox.setObjectName("g_showsin_groupBox")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_showsin_groupBox)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_apps_list = QtWidgets.QListWidget(self.g_showsin_groupBox)
        self.g_apps_list.setMinimumSize(QtCore.QSize(0, 0))
        self.g_apps_list.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_apps_list.setAlternatingRowColors(False)
        self.g_apps_list.setSelectionMode(QtWidgets.QAbstractItemView.MultiSelection)
        self.g_apps_list.setProperty("qp_role_assoc_app_ids", "")
        self.g_apps_list.setObjectName("g_apps_list")
        item = QtWidgets.QListWidgetItem()
        self.g_apps_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_apps_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_apps_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_apps_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_apps_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_apps_list.addItem(item)
        self.verticalLayout_4.addWidget(self.g_apps_list)
        self.verticalLayout_2.addWidget(self.g_showsin_groupBox)
        self.g_subgroups_groupBox = QtWidgets.QGroupBox(ManageRole)
        self.g_subgroups_groupBox.setObjectName("g_subgroups_groupBox")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_subgroups_groupBox)
        self.verticalLayout_3.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout_3.setSpacing(6)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_subgroup_listWidget = QtWidgets.QListWidget(self.g_subgroups_groupBox)
        self.g_subgroup_listWidget.setMinimumSize(QtCore.QSize(0, 0))
        self.g_subgroup_listWidget.setObjectName("g_subgroup_listWidget")
        self.verticalLayout_3.addWidget(self.g_subgroup_listWidget)
        self.verticalLayout_2.addWidget(self.g_subgroups_groupBox)
        self.g_controlbuttons_frame = QtWidgets.QFrame(ManageRole)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem3 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem3)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_11.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_11.addWidget(self.g_save_button)
        self.verticalLayout_2.addWidget(self.g_controlbuttons_frame)

        self.retranslateUi(ManageRole)
        self.g_apps_list.setCurrentRow(-1)
        QtCore.QMetaObject.connectSlotsByName(ManageRole)

    def retranslateUi(self, ManageRole):
        _translate = QtCore.QCoreApplication.translate
        ManageRole.setWindowTitle(_translate("ManageRole", "Manage Group"))
        self.g_main_label.setText(_translate("ManageRole", "Manage Group"))
        self.g_brandname_label.setText(_translate("ManageRole", "Group Name"))
        self.g_showsin_groupBox.setTitle(_translate("ManageRole", "Shows in"))
        __sortingEnabled = self.g_apps_list.isSortingEnabled()
        self.g_apps_list.setSortingEnabled(False)
        item = self.g_apps_list.item(0)
        item.setText(_translate("ManageRole", "Cash Register"))
        item = self.g_apps_list.item(1)
        item.setText(_translate("ManageRole", "Frequent Buyer"))
        item = self.g_apps_list.item(2)
        item.setText(_translate("ManageRole", "Pet Tracker"))
        item = self.g_apps_list.item(3)
        item.setText(_translate("ManageRole", "Products"))
        item = self.g_apps_list.item(4)
        item.setText(_translate("ManageRole", "Purchasing & Receiving"))
        item = self.g_apps_list.item(5)
        item.setText(_translate("ManageRole", "Settings & Configuration"))
        self.g_apps_list.setSortingEnabled(__sortingEnabled)
        self.g_subgroups_groupBox.setTitle(_translate("ManageRole", "Sub Groups"))
        self.g_cancel_button.setText(_translate("ManageRole", " Close"))
        self.g_save_button.setText(_translate("ManageRole", " Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageRole = QtWidgets.QDialog()
    ui = Ui_ManageRole()
    ui.setupUi(ManageRole)
    ManageRole.show()
    sys.exit(app.exec_())
