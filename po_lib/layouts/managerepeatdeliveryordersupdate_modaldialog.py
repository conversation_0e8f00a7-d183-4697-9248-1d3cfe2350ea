# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managerepeatdeliveryordersupdate_modaldialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ManageDeliveryOrders(object):
    def setupUi(self, ManageDeliveryOrders):
        ManageDeliveryOrders.setObjectName("ManageDeliveryOrders")
        ManageDeliveryOrders.resize(725, 326)
        ManageDeliveryOrders.setMinimumSize(QtCore.QSize(725, 326))
        ManageDeliveryOrders.setMaximumSize(QtCore.QSize(725, 326))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        ManageDeliveryOrders.setFont(font)
        ManageDeliveryOrders.setStyleSheet("#ManageDeliveryOrders {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QCheckBox {\n"
"    padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"QDateTimeEdit, QTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
" \n"
"QDateTimeEdit::drop-down, QTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"#g_cancel_button,\n"
"#g_delete_button, #__g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed,\n"
"#g_delete_button:pressed, #__g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_save_button,\n"
"#g_add_button, #__g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed,\n"
"#g_add_button:pressed, #__g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"\n"
"\n"
"")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(ManageDeliveryOrders)
        self.verticalLayout_2.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_top_frame = QtWidgets.QFrame(ManageDeliveryOrders)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_6.addWidget(self.g_progress_frame)
        self.verticalLayout_2.addWidget(self.g_top_frame)
        self.g_titlepagenav_frame = QtWidgets.QFrame(ManageDeliveryOrders)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_31 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_31.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_31.setObjectName("horizontalLayout_31")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout4 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout4.setObjectName("verticalLayout4")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout4.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout4.addItem(spacerItem)
        self.horizontalLayout_31.addWidget(self.g_title_frame)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout.setSpacing(6)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout.addItem(spacerItem1)
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagenav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_slash_label.setObjectName("g_slash_label")
        self.g_pagenav_hlayout.addWidget(self.g_slash_label)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout.addWidget(self.g_totalpages_label)
        self.horizontalLayout_31.addWidget(self.g_pagenav_frame)
        self.verticalLayout_2.addWidget(self.g_titlepagenav_frame)
        self.g_startenddate_repeatdelivery_frame = QtWidgets.QFrame(ManageDeliveryOrders)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_startenddate_repeatdelivery_frame.sizePolicy().hasHeightForWidth())
        self.g_startenddate_repeatdelivery_frame.setSizePolicy(sizePolicy)
        self.g_startenddate_repeatdelivery_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_startenddate_repeatdelivery_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_startenddate_repeatdelivery_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_startenddate_repeatdelivery_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_startenddate_repeatdelivery_frame.setObjectName("g_startenddate_repeatdelivery_frame")
        self.horizontalLayout_42 = QtWidgets.QHBoxLayout(self.g_startenddate_repeatdelivery_frame)
        self.horizontalLayout_42.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_42.setSpacing(6)
        self.horizontalLayout_42.setObjectName("horizontalLayout_42")
        self.g_sales_type_frame_2 = QtWidgets.QFrame(self.g_startenddate_repeatdelivery_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sales_type_frame_2.sizePolicy().hasHeightForWidth())
        self.g_sales_type_frame_2.setSizePolicy(sizePolicy)
        self.g_sales_type_frame_2.setMinimumSize(QtCore.QSize(0, 0))
        self.g_sales_type_frame_2.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_sales_type_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_sales_type_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_sales_type_frame_2.setObjectName("g_sales_type_frame_2")
        self.horizontalLayout_68 = QtWidgets.QHBoxLayout(self.g_sales_type_frame_2)
        self.horizontalLayout_68.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_68.setObjectName("horizontalLayout_68")
        self.horizontalLayout_42.addWidget(self.g_sales_type_frame_2)
        self.g_sales_sumbyperiod_frame_2 = QtWidgets.QFrame(self.g_startenddate_repeatdelivery_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sales_sumbyperiod_frame_2.sizePolicy().hasHeightForWidth())
        self.g_sales_sumbyperiod_frame_2.setSizePolicy(sizePolicy)
        self.g_sales_sumbyperiod_frame_2.setMinimumSize(QtCore.QSize(0, 0))
        self.g_sales_sumbyperiod_frame_2.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_sales_sumbyperiod_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_sales_sumbyperiod_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_sales_sumbyperiod_frame_2.setObjectName("g_sales_sumbyperiod_frame_2")
        self.horizontalLayout_69 = QtWidgets.QHBoxLayout(self.g_sales_sumbyperiod_frame_2)
        self.horizontalLayout_69.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_69.setObjectName("horizontalLayout_69")
        self.horizontalLayout_42.addWidget(self.g_sales_sumbyperiod_frame_2)
        self.verticalLayout_2.addWidget(self.g_startenddate_repeatdelivery_frame)
        self.frame_4 = QtWidgets.QFrame(ManageDeliveryOrders)
        self.frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_4.setObjectName("frame_4")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.frame_4)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.frame = QtWidgets.QFrame(self.frame_4)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_location_frame = QtWidgets.QFrame(self.frame)
        self.g_location_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_location_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_location_frame.setObjectName("g_location_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_location_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_location_label = QtWidgets.QLabel(self.g_location_frame)
        self.g_location_label.setMinimumSize(QtCore.QSize(70, 0))
        self.g_location_label.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_location_label.setFont(font)
        self.g_location_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_location_label.setObjectName("g_location_label")
        self.horizontalLayout_3.addWidget(self.g_location_label)
        self.g_location_combobox = QtWidgets.QComboBox(self.g_location_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_location_combobox.sizePolicy().hasHeightForWidth())
        self.g_location_combobox.setSizePolicy(sizePolicy)
        self.g_location_combobox.setMinimumSize(QtCore.QSize(175, 31))
        self.g_location_combobox.setMaximumSize(QtCore.QSize(175, 31))
        self.g_location_combobox.setEditable(True)
        self.g_location_combobox.setCurrentText("")
        self.g_location_combobox.setObjectName("g_location_combobox")
        self.horizontalLayout_3.addWidget(self.g_location_combobox)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem2)
        self.verticalLayout.addWidget(self.g_location_frame)
        self.frame_5 = QtWidgets.QFrame(self.frame)
        self.frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_5.setObjectName("frame_5")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.frame_5)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_startdate_label = QtWidgets.QLabel(self.frame_5)
        self.g_startdate_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_startdate_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_startdate_label.setFont(font)
        self.g_startdate_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_startdate_label.setObjectName("g_startdate_label")
        self.horizontalLayout_8.addWidget(self.g_startdate_label)
        self.g_start_datetimeedit = QtWidgets.QDateTimeEdit(self.frame_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_start_datetimeedit.sizePolicy().hasHeightForWidth())
        self.g_start_datetimeedit.setSizePolicy(sizePolicy)
        self.g_start_datetimeedit.setMinimumSize(QtCore.QSize(120, 31))
        self.g_start_datetimeedit.setMaximumSize(QtCore.QSize(120, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_start_datetimeedit.setFont(font)
        self.g_start_datetimeedit.setStyleSheet("")
        self.g_start_datetimeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_start_datetimeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2013, 12, 6), QtCore.QTime(0, 0, 0)))
        self.g_start_datetimeedit.setCalendarPopup(True)
        self.g_start_datetimeedit.setTimeSpec(QtCore.Qt.LocalTime)
        self.g_start_datetimeedit.setObjectName("g_start_datetimeedit")
        self.horizontalLayout_8.addWidget(self.g_start_datetimeedit)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_8.addItem(spacerItem3)
        self.verticalLayout.addWidget(self.frame_5)
        self.g_startenddate_repeatdelivery_frame_2 = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_startenddate_repeatdelivery_frame_2.sizePolicy().hasHeightForWidth())
        self.g_startenddate_repeatdelivery_frame_2.setSizePolicy(sizePolicy)
        self.g_startenddate_repeatdelivery_frame_2.setMinimumSize(QtCore.QSize(0, 0))
        self.g_startenddate_repeatdelivery_frame_2.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_startenddate_repeatdelivery_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_startenddate_repeatdelivery_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_startenddate_repeatdelivery_frame_2.setObjectName("g_startenddate_repeatdelivery_frame_2")
        self.horizontalLayout_43 = QtWidgets.QHBoxLayout(self.g_startenddate_repeatdelivery_frame_2)
        self.horizontalLayout_43.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_43.setSpacing(6)
        self.horizontalLayout_43.setObjectName("horizontalLayout_43")
        self.g_nextorder_label = QtWidgets.QLabel(self.g_startenddate_repeatdelivery_frame_2)
        self.g_nextorder_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_nextorder_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_nextorder_label.setFont(font)
        self.g_nextorder_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_nextorder_label.setObjectName("g_nextorder_label")
        self.horizontalLayout_43.addWidget(self.g_nextorder_label)
        self.g_nextorder_datetimeedit = QtWidgets.QDateTimeEdit(self.g_startenddate_repeatdelivery_frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_nextorder_datetimeedit.sizePolicy().hasHeightForWidth())
        self.g_nextorder_datetimeedit.setSizePolicy(sizePolicy)
        self.g_nextorder_datetimeedit.setMinimumSize(QtCore.QSize(120, 31))
        self.g_nextorder_datetimeedit.setMaximumSize(QtCore.QSize(120, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_nextorder_datetimeedit.setFont(font)
        self.g_nextorder_datetimeedit.setStyleSheet("")
        self.g_nextorder_datetimeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_nextorder_datetimeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2013, 12, 6), QtCore.QTime(0, 0, 0)))
        self.g_nextorder_datetimeedit.setCalendarPopup(True)
        self.g_nextorder_datetimeedit.setTimeSpec(QtCore.Qt.LocalTime)
        self.g_nextorder_datetimeedit.setObjectName("g_nextorder_datetimeedit")
        self.horizontalLayout_43.addWidget(self.g_nextorder_datetimeedit)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_43.addItem(spacerItem4)
        self.verticalLayout.addWidget(self.g_startenddate_repeatdelivery_frame_2)
        self.g_frequency_frame = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_frequency_frame.sizePolicy().hasHeightForWidth())
        self.g_frequency_frame.setSizePolicy(sizePolicy)
        self.g_frequency_frame.setMinimumSize(QtCore.QSize(31, 0))
        self.g_frequency_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_frequency_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_frequency_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_frequency_frame.setObjectName("g_frequency_frame")
        self.horizontalLayout_44 = QtWidgets.QHBoxLayout(self.g_frequency_frame)
        self.horizontalLayout_44.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_44.setSpacing(6)
        self.horizontalLayout_44.setObjectName("horizontalLayout_44")
        self.g_frequency_label = QtWidgets.QLabel(self.g_frequency_frame)
        self.g_frequency_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_frequency_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_frequency_label.setFont(font)
        self.g_frequency_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_frequency_label.setObjectName("g_frequency_label")
        self.horizontalLayout_44.addWidget(self.g_frequency_label)
        self.g_frequency_combbox = QtWidgets.QComboBox(self.g_frequency_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_frequency_combbox.sizePolicy().hasHeightForWidth())
        self.g_frequency_combbox.setSizePolicy(sizePolicy)
        self.g_frequency_combbox.setMinimumSize(QtCore.QSize(175, 31))
        self.g_frequency_combbox.setMaximumSize(QtCore.QSize(175, 31))
        self.g_frequency_combbox.setEditable(True)
        self.g_frequency_combbox.setCurrentText("")
        self.g_frequency_combbox.setObjectName("g_frequency_combbox")
        self.horizontalLayout_44.addWidget(self.g_frequency_combbox)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_44.addItem(spacerItem5)
        self.verticalLayout.addWidget(self.g_frequency_frame)
        self.g_qty_frame = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_qty_frame.sizePolicy().hasHeightForWidth())
        self.g_qty_frame.setSizePolicy(sizePolicy)
        self.g_qty_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_qty_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_qty_frame.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.g_qty_frame.setObjectName("g_qty_frame")
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout(self.g_qty_frame)
        self.horizontalLayout_17.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_17.setSpacing(6)
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        self.g_qty_label = QtWidgets.QLabel(self.g_qty_frame)
        self.g_qty_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_qty_label.setMaximumSize(QtCore.QSize(70, 31))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_qty_label.setFont(font)
        self.g_qty_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_qty_label.setObjectName("g_qty_label")
        self.horizontalLayout_17.addWidget(self.g_qty_label)
        self.g_qty_lineedit = QtWidgets.QLineEdit(self.g_qty_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_qty_lineedit.sizePolicy().hasHeightForWidth())
        self.g_qty_lineedit.setSizePolicy(sizePolicy)
        self.g_qty_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_qty_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        font = QtGui.QFont()
        self.g_qty_lineedit.setFont(font)
        self.g_qty_lineedit.setReadOnly(False)
        self.g_qty_lineedit.setPlaceholderText("")
        self.g_qty_lineedit.setProperty("qp_epn_names_before_key_names", "")
        self.g_qty_lineedit.setObjectName("g_qty_lineedit")
        self.horizontalLayout_17.addWidget(self.g_qty_lineedit)
        spacerItem6 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_17.addItem(spacerItem6)
        self.verticalLayout.addWidget(self.g_qty_frame)
        spacerItem7 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem7)
        self.horizontalLayout_4.addWidget(self.frame)
        self.frame_2 = QtWidgets.QFrame(self.frame_4)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_entity_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_entity_frame.sizePolicy().hasHeightForWidth())
        self.g_entity_frame.setSizePolicy(sizePolicy)
        self.g_entity_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_entity_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_entity_frame.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.g_entity_frame.setObjectName("g_entity_frame")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout(self.g_entity_frame)
        self.horizontalLayout_16.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_16.setSpacing(6)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.g_sku_label = QtWidgets.QLabel(self.g_entity_frame)
        self.g_sku_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_sku_label.setMaximumSize(QtCore.QSize(70, 31))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_sku_label.setFont(font)
        self.g_sku_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_sku_label.setObjectName("g_sku_label")
        self.horizontalLayout_16.addWidget(self.g_sku_label)
        self.g_sku_lineedit = QtWidgets.QLineEdit(self.g_entity_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sku_lineedit.sizePolicy().hasHeightForWidth())
        self.g_sku_lineedit.setSizePolicy(sizePolicy)
        self.g_sku_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_sku_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        font = QtGui.QFont()
        self.g_sku_lineedit.setFont(font)
        self.g_sku_lineedit.setReadOnly(True)
        self.g_sku_lineedit.setPlaceholderText("")
        self.g_sku_lineedit.setProperty("qp_epn_names_before_key_names", "")
        self.g_sku_lineedit.setObjectName("g_sku_lineedit")
        self.horizontalLayout_16.addWidget(self.g_sku_lineedit)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_16.addItem(spacerItem8)
        spacerItem9 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_16.addItem(spacerItem9)
        self.verticalLayout_3.addWidget(self.g_entity_frame)
        self.frame_3 = QtWidgets.QFrame(self.frame_2)
        self.frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.frame_3)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_barcode_label = QtWidgets.QLabel(self.frame_3)
        self.g_barcode_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_barcode_label.setMaximumSize(QtCore.QSize(70, 31))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_barcode_label.setFont(font)
        self.g_barcode_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_barcode_label.setObjectName("g_barcode_label")
        self.horizontalLayout.addWidget(self.g_barcode_label)
        self.g_barcode_lineedit = QtWidgets.QLineEdit(self.frame_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_barcode_lineedit.sizePolicy().hasHeightForWidth())
        self.g_barcode_lineedit.setSizePolicy(sizePolicy)
        self.g_barcode_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_barcode_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        font = QtGui.QFont()
        self.g_barcode_lineedit.setFont(font)
        self.g_barcode_lineedit.setReadOnly(True)
        self.g_barcode_lineedit.setPlaceholderText("")
        self.g_barcode_lineedit.setProperty("qp_epn_names_before_key_names", "")
        self.g_barcode_lineedit.setObjectName("g_barcode_lineedit")
        self.horizontalLayout.addWidget(self.g_barcode_lineedit)
        spacerItem10 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem10)
        self.verticalLayout_3.addWidget(self.frame_3)
        self.g_description_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_description_frame.sizePolicy().hasHeightForWidth())
        self.g_description_frame.setSizePolicy(sizePolicy)
        self.g_description_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_description_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_description_frame.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.g_description_frame.setObjectName("g_description_frame")
        self.horizontalLayout_18 = QtWidgets.QHBoxLayout(self.g_description_frame)
        self.horizontalLayout_18.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_18.setSpacing(6)
        self.horizontalLayout_18.setObjectName("horizontalLayout_18")
        self.g_productdescription_label = QtWidgets.QLabel(self.g_description_frame)
        self.g_productdescription_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_productdescription_label.setMaximumSize(QtCore.QSize(70, 31))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_productdescription_label.setFont(font)
        self.g_productdescription_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_productdescription_label.setObjectName("g_productdescription_label")
        self.horizontalLayout_18.addWidget(self.g_productdescription_label)
        self.g_description_lineedit = QtWidgets.QLineEdit(self.g_description_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_description_lineedit.sizePolicy().hasHeightForWidth())
        self.g_description_lineedit.setSizePolicy(sizePolicy)
        self.g_description_lineedit.setMinimumSize(QtCore.QSize(300, 31))
        self.g_description_lineedit.setMaximumSize(QtCore.QSize(300, 31))
        font = QtGui.QFont()
        self.g_description_lineedit.setFont(font)
        self.g_description_lineedit.setReadOnly(True)
        self.g_description_lineedit.setPlaceholderText("")
        self.g_description_lineedit.setProperty("qp_epn_names_before_key_names", "")
        self.g_description_lineedit.setObjectName("g_description_lineedit")
        self.horizontalLayout_18.addWidget(self.g_description_lineedit)
        spacerItem11 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_18.addItem(spacerItem11)
        self.verticalLayout_3.addWidget(self.g_description_frame)
        self.g_price_frame = QtWidgets.QFrame(self.frame_2)
        self.g_price_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_price_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_price_frame.setObjectName("g_price_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_price_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_Price_label = QtWidgets.QLabel(self.g_price_frame)
        self.g_Price_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_Price_label.setMaximumSize(QtCore.QSize(70, 31))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_Price_label.setFont(font)
        self.g_Price_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_Price_label.setObjectName("g_Price_label")
        self.horizontalLayout_2.addWidget(self.g_Price_label)
        self.g_price_lineedit = QtWidgets.QLineEdit(self.g_price_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_price_lineedit.sizePolicy().hasHeightForWidth())
        self.g_price_lineedit.setSizePolicy(sizePolicy)
        self.g_price_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_price_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        font = QtGui.QFont()
        self.g_price_lineedit.setFont(font)
        self.g_price_lineedit.setText("")
        self.g_price_lineedit.setReadOnly(True)
        self.g_price_lineedit.setPlaceholderText("")
        self.g_price_lineedit.setProperty("qp_epn_names_before_key_names", "")
        self.g_price_lineedit.setObjectName("g_price_lineedit")
        self.horizontalLayout_2.addWidget(self.g_price_lineedit)
        spacerItem12 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem12)
        self.verticalLayout_3.addWidget(self.g_price_frame)
        spacerItem13 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_3.addItem(spacerItem13)
        self.horizontalLayout_4.addWidget(self.frame_2)
        self.verticalLayout_2.addWidget(self.frame_4)
        self.__g_bottombar_frame_2 = QtWidgets.QFrame(ManageDeliveryOrders)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_bottombar_frame_2.sizePolicy().hasHeightForWidth())
        self.__g_bottombar_frame_2.setSizePolicy(sizePolicy)
        self.__g_bottombar_frame_2.setMinimumSize(QtCore.QSize(247, 39))
        self.__g_bottombar_frame_2.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_bottombar_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_bottombar_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_bottombar_frame_2.setObjectName("__g_bottombar_frame_2")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.__g_bottombar_frame_2)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        spacerItem14 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem14)
        self.g_cancel_button = QtWidgets.QPushButton(self.__g_bottombar_frame_2)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/fpe/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_5.addWidget(self.g_cancel_button)
        self.g_selectproduct_button = QtWidgets.QPushButton(self.__g_bottombar_frame_2)
        self.g_selectproduct_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_selectproduct_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_selectproduct_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/edit"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_selectproduct_button.setIcon(icon1)
        self.g_selectproduct_button.setIconSize(QtCore.QSize(24, 24))
        self.g_selectproduct_button.setObjectName("g_selectproduct_button")
        self.horizontalLayout_5.addWidget(self.g_selectproduct_button)
        self.g_save_button = QtWidgets.QPushButton(self.__g_bottombar_frame_2)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/fpe/check"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon2)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_5.addWidget(self.g_save_button)
        self.verticalLayout_2.addWidget(self.__g_bottombar_frame_2)
        self.g_qty_label.setBuddy(self.g_sku_lineedit)
        self.g_sku_label.setBuddy(self.g_sku_lineedit)

        self.retranslateUi(ManageDeliveryOrders)
        QtCore.QMetaObject.connectSlotsByName(ManageDeliveryOrders)

    def retranslateUi(self, ManageDeliveryOrders):
        _translate = QtCore.QCoreApplication.translate
        ManageDeliveryOrders.setWindowTitle(_translate("ManageDeliveryOrders", "Manage Delivery Orders"))
        self.g_main_label.setText(_translate("ManageDeliveryOrders", "Manage Repeat Delivery"))
        self.g_previouspage_label.setToolTip(_translate("ManageDeliveryOrders", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("ManageDeliveryOrders", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("ManageDeliveryOrders", "999"))
        self.g_slash_label.setText(_translate("ManageDeliveryOrders", "/"))
        self.g_totalpages_label.setToolTip(_translate("ManageDeliveryOrders", "Total Pages"))
        self.g_totalpages_label.setText(_translate("ManageDeliveryOrders", "999"))
        self.g_location_label.setText(_translate("ManageDeliveryOrders", "Location"))
        self.g_startdate_label.setText(_translate("ManageDeliveryOrders", "Start Date"))
        self.g_start_datetimeedit.setDisplayFormat(_translate("ManageDeliveryOrders", "MM/dd/yyyy"))
        self.g_nextorder_label.setText(_translate("ManageDeliveryOrders", "Next Order"))
        self.g_nextorder_datetimeedit.setDisplayFormat(_translate("ManageDeliveryOrders", "MM/dd/yyyy"))
        self.g_frequency_label.setText(_translate("ManageDeliveryOrders", "Frequency"))
        self.g_qty_label.setText(_translate("ManageDeliveryOrders", "Qty"))
        self.g_sku_label.setText(_translate("ManageDeliveryOrders", "Sku"))
        self.g_barcode_label.setText(_translate("ManageDeliveryOrders", "Barcode"))
        self.g_productdescription_label.setText(_translate("ManageDeliveryOrders", "Description"))
        self.g_Price_label.setText(_translate("ManageDeliveryOrders", "Price"))
        self.g_cancel_button.setText(_translate("ManageDeliveryOrders", "Cancel"))
        self.g_selectproduct_button.setText(_translate("ManageDeliveryOrders", "Select\n"
"Product"))
        self.g_save_button.setText(_translate("ManageDeliveryOrders", "Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageDeliveryOrders = QtWidgets.QDialog()
    ui = Ui_ManageDeliveryOrders()
    ui.setupUi(ManageDeliveryOrders)
    ManageDeliveryOrders.show()
    sys.exit(app.exec_())
