# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'itemnotes_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ItemNotesDialog(object):
    def setupUi(self, ItemNotesDialog):
        ItemNotesDialog.setObjectName("ItemNotesDialog")
        ItemNotesDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        ItemNotesDialog.resize(537, 261)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.MinimumExpanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(ItemNotesDialog.sizePolicy().hasHeightForWidth())
        ItemNotesDialog.setSizePolicy(sizePolicy)
        ItemNotesDialog.setMinimumSize(QtCore.QSize(415, 0))
        ItemNotesDialog.setMaximumSize(QtCore.QSize(16777215, 16777215))
        ItemNotesDialog.setStyleSheet("\n"
"#ItemNotesDialog,\n"
"#__ItemNotesDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#ItemNotesDialog QPushButton,\n"
"#__ItemNotesDialog QPushButton {\n"
"    color: white;\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    background-color: #fff;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_save_button, \n"
"#__g_save_button {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed, \n"
"#__g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_save_button:disabled,\n"
"#__g_save_button:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}\n"
"\n"
"#g_cancel_button, \n"
"#__g_cancel_button {\n"
"    background-color: #ee1111;\n"
"    border: 2px solid #ca0e0e;\n"
"}\n"
"\n"
"#g_cancel_button:pressed, \n"
"#__g_cancel_button:pressed {\n"
"    background-color: #ca0e0e;\n"
"}\n"
"\n"
"")
        ItemNotesDialog.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(ItemNotesDialog)
        self.verticalLayout.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout.setSpacing(9)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_notes_textedit = QtWidgets.QPlainTextEdit(ItemNotesDialog)
        self.g_notes_textedit.setObjectName("g_notes_textedit")
        self.verticalLayout.addWidget(self.g_notes_textedit)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(6)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(ItemNotesDialog)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/itemnote_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(ItemNotesDialog)
        self.g_save_button.setMinimumSize(QtCore.QSize(110, 40))
        self.g_save_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/itemnote_dialog/save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout.addWidget(self.g_save_button)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(ItemNotesDialog)
        QtCore.QMetaObject.connectSlotsByName(ItemNotesDialog)
        ItemNotesDialog.setTabOrder(self.g_save_button, self.g_cancel_button)

    def retranslateUi(self, ItemNotesDialog):
        _translate = QtCore.QCoreApplication.translate
        ItemNotesDialog.setWindowTitle(_translate("ItemNotesDialog", "Add or Change Note"))
        self.g_notes_textedit.setPlaceholderText(_translate("ItemNotesDialog", "Enter note for the selected items ..."))
        self.g_cancel_button.setText(_translate("ItemNotesDialog", "  Cancel"))
        self.g_save_button.setText(_translate("ItemNotesDialog", "  Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ItemNotesDialog = QtWidgets.QDialog()
    ui = Ui_ItemNotesDialog()
    ui.setupUi(ItemNotesDialog)
    ItemNotesDialog.show()
    sys.exit(app.exec_())
