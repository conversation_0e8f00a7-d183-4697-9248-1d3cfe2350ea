# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'line_select_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_LineSelectDialog(object):
    def setupUi(self, LineSelectDialog):
        LineSelectDialog.setObjectName("LineSelectDialog")
        LineSelectDialog.resize(657, 497)
        LineSelectDialog.setStyleSheet("#LineSelectDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QL<PERSON>arGrad<PERSON>(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_redeem_button, #g_reset_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_redeem_button:pressed, #g_reset_button:pressed {\n"
"    background-color: #007f00;\n"
"} \n"
"\n"
"#g_main_label,  #g_bottom_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"")
        self.verticalLayout = QtWidgets.QVBoxLayout(LineSelectDialog)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_main_label = QtWidgets.QLabel(LineSelectDialog)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        self.g_main_frame = QtWidgets.QFrame(LineSelectDialog)
        self.g_main_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_main_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_main_frame.setObjectName("g_main_frame")
        self.verticalLayout.addWidget(self.g_main_frame)
        self.g_bottom_label = QtWidgets.QLabel(LineSelectDialog)
        self.g_bottom_label.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_bottom_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_bottom_label.setWordWrap(True)
        self.g_bottom_label.setObjectName("g_bottom_label")
        self.verticalLayout.addWidget(self.g_bottom_label)
        self.buttons_frame = QtWidgets.QFrame(LineSelectDialog)
        self.buttons_frame.setMaximumSize(QtCore.QSize(16777215, 60))
        self.buttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.buttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.buttons_frame.setObjectName("buttons_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.buttons_frame)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(self.buttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/line_select_dialog/no"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_reset_button = QtWidgets.QPushButton(self.buttons_frame)
        self.g_reset_button.setMinimumSize(QtCore.QSize(160, 40))
        self.g_reset_button.setMaximumSize(QtCore.QSize(160, 40))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/line_select_dialog/undo"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_reset_button.setIcon(icon1)
        self.g_reset_button.setIconSize(QtCore.QSize(24, 24))
        self.g_reset_button.setObjectName("g_reset_button")
        self.horizontalLayout.addWidget(self.g_reset_button)
        self.g_redeem_button = QtWidgets.QPushButton(self.buttons_frame)
        self.g_redeem_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_redeem_button.setMaximumSize(QtCore.QSize(120, 40))
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/line_select_dialog/yes"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_redeem_button.setIcon(icon2)
        self.g_redeem_button.setIconSize(QtCore.QSize(24, 24))
        self.g_redeem_button.setObjectName("g_redeem_button")
        self.horizontalLayout.addWidget(self.g_redeem_button)
        self.verticalLayout.addWidget(self.buttons_frame)

        self.retranslateUi(LineSelectDialog)
        QtCore.QMetaObject.connectSlotsByName(LineSelectDialog)

    def retranslateUi(self, LineSelectDialog):
        _translate = QtCore.QCoreApplication.translate
        LineSelectDialog.setWindowTitle(_translate("LineSelectDialog", "Dialog"))
        self.g_main_label.setText(_translate("LineSelectDialog", "Select an Item below for the {Coupon Name}"))
        self.g_bottom_label.setText(_translate("LineSelectDialog", "Value of Coupon to Apply: $3 "))
        self.g_cancel_button.setText(_translate("LineSelectDialog", "Cancel"))
        self.g_reset_button.setText(_translate("LineSelectDialog", "Reset Selection and Save "))
        self.g_redeem_button.setText(_translate("LineSelectDialog", "Save \n"
"selection"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    LineSelectDialog = QtWidgets.QDialog()
    ui = Ui_LineSelectDialog()
    ui.setupUi(LineSelectDialog)
    LineSelectDialog.show()
    sys.exit(app.exec_())
