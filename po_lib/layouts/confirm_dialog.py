# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'confirm_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ConfirmDialog(object):
    def setupUi(self, ConfirmDialog):
        ConfirmDialog.setObjectName("ConfirmDialog")
        ConfirmDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        ConfirmDialog.resize(418, 136)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.MinimumExpanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(ConfirmDialog.sizePolicy().hasHeightForWidth())
        ConfirmDialog.setSizePolicy(sizePolicy)
        ConfirmDialog.setMinimumSize(QtCore.QSize(0, 0))
        ConfirmDialog.setMaximumSize(QtCore.QSize(16777215, 16777215))
        ConfirmDialog.setStyleSheet("#g_yes_button, #__g_yes_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_yes_button:pressed, #__g_yes_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_no_button, #__g_no_button {\n"
"    background-color: #ee1111;\n"
"    border:2px solid #CA0E0E;\n"
"}\n"
"\n"
"#g_no_button:pressed, #__g_no_button:pressed {\n"
"    background-color:#CA0E0E;\n"
"}\n"
"\n"
"#ConfirmDialog QPushButton, #__ConfirmDialog QPushButton {\n"
"    color:white;\n"
"    font-weight:bold;\n"
"    font-size:15px;\n"
"}\n"
"\n"
"#ConfirmDialog, #__ConfirmDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"\n"
"QLabel {\n"
"    font-weight:bold;\n"
"    font-size:16px;\n"
"}")
        ConfirmDialog.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(ConfirmDialog)
        self.verticalLayout.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_message_label = QtWidgets.QLabel(ConfirmDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.MinimumExpanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_message_label.sizePolicy().hasHeightForWidth())
        self.g_message_label.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_message_label.setFont(font)
        self.g_message_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_message_label.setWordWrap(True)
        self.g_message_label.setObjectName("g_message_label")
        self.verticalLayout.addWidget(self.g_message_label)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(6)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_no_button = QtWidgets.QPushButton(ConfirmDialog)
        self.g_no_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_no_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/confirm_dialog/no"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_no_button.setIcon(icon)
        self.g_no_button.setIconSize(QtCore.QSize(24, 24))
        self.g_no_button.setObjectName("g_no_button")
        self.horizontalLayout.addWidget(self.g_no_button)
        spacerItem1 = QtWidgets.QSpacerItem(0, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.g_yes_button = QtWidgets.QPushButton(ConfirmDialog)
        self.g_yes_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_yes_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/confirm_dialog/yes"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_yes_button.setIcon(icon1)
        self.g_yes_button.setIconSize(QtCore.QSize(24, 24))
        self.g_yes_button.setObjectName("g_yes_button")
        self.horizontalLayout.addWidget(self.g_yes_button)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem2)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(ConfirmDialog)
        QtCore.QMetaObject.connectSlotsByName(ConfirmDialog)
        ConfirmDialog.setTabOrder(self.g_yes_button, self.g_no_button)

    def retranslateUi(self, ConfirmDialog):
        _translate = QtCore.QCoreApplication.translate
        ConfirmDialog.setWindowTitle(_translate("ConfirmDialog", "Confirm"))
        ConfirmDialog.setProperty("default_window_title_text", _translate("ConfirmDialog", "Confirm Action"))
        ConfirmDialog.setProperty("replace_config_dialog_yes_text", _translate("ConfirmDialog", "Overwrite"))
        ConfirmDialog.setProperty("replace_config_dialog_no_text", _translate("ConfirmDialog", "Exit"))
        self.g_message_label.setText(_translate("ConfirmDialog", "Are you sure you want to cancel the sale?"))
        self.g_no_button.setText(_translate("ConfirmDialog", "  No"))
        self.g_no_button.setShortcut(_translate("ConfirmDialog", "Enter"))
        self.g_yes_button.setText(_translate("ConfirmDialog", "  Yes"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ConfirmDialog = QtWidgets.QDialog()
    ui = Ui_ConfirmDialog()
    ui.setupUi(ConfirmDialog)
    ConfirmDialog.show()
    sys.exit(app.exec_())
