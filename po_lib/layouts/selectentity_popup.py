# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'selectentity_popup.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_SelectEntity(object):
    def setupUi(self, SelectEntity):
        SelectEntity.setObjectName("SelectEntity")
        SelectEntity.resize(644, 480)
        SelectEntity.setMinimumSize(QtCore.QSize(0, 480))
        SelectEntity.setMaximumSize(QtCore.QSize(********, ********))
        SelectEntity.setStyleSheet("#SelectEntity {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QLabel {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font: 17px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_selectbreeder_frame, #g_selectbreeder_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border:1px solid #073c83;\n"
"    border-radius:10px;\n"
"    background-color:#DDE4E9;\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
".QScrollArea {\n"
"    background-color: #f4f4f4;\n"
"    border-radius:5px;\n"
"    border:1px solid #073c83;\n"
"    padding:4px;\n"
"}\n"
"\n"
"QScrollBar:horizontal {\n"
"height:0px;\n"
"}\n"
"\n"
"#scrollAreaWidgetContents {\n"
"background-color: #f4f4f4;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"#g_cancel_button, #g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button, #g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_select_button, #g_new_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_select_button:pressed, #g_new_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_delete_button, #g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_delete_button:pressed, #g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_search_frame, #__g_search_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_search_box_frame, #__g_search_box_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_clearsearch_button, #__g_clearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_search_lineedit, #__g_search_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_search_combobox, #__g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_search_combobox::drop-down, #__g_search_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"}\n"
"\n"
"#g_searchtext_lineedit, #__g_searchtext_lineedit {\n"
"    border: 0px;\n"
"}")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(SelectEntity)
        self.verticalLayout_2.setContentsMargins(9, 0, 9, 9)
        self.verticalLayout_2.setSpacing(6)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_top_frame = QtWidgets.QFrame(SelectEntity)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_8.addWidget(self.g_progress_frame)
        self.verticalLayout_2.addWidget(self.g_top_frame)
        self.g_selectbreeder_frame = QtWidgets.QFrame(SelectEntity)
        self.g_selectbreeder_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_selectbreeder_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_selectbreeder_frame.setObjectName("g_selectbreeder_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_selectbreeder_frame)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_main_label = QtWidgets.QLabel(self.g_selectbreeder_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(********, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setStyleSheet("")
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        self.g_search_frame = QtWidgets.QFrame(self.g_selectbreeder_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_search_frame.sizePolicy().hasHeightForWidth())
        self.g_search_frame.setSizePolicy(sizePolicy)
        self.g_search_frame.setMinimumSize(QtCore.QSize(200, 41))
        self.g_search_frame.setMaximumSize(QtCore.QSize(166546, 59))
        self.g_search_frame.setStyleSheet("")
        self.g_search_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_search_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_search_frame.setObjectName("g_search_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_search_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(14)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_search_combobox = QtWidgets.QComboBox(self.g_search_frame)
        self.g_search_combobox.setMinimumSize(QtCore.QSize(125, 0))
        self.g_search_combobox.setMaximumSize(QtCore.QSize(125, 31))
        self.g_search_combobox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_search_combobox.setStyleSheet("")
        self.g_search_combobox.setFrame(False)
        self.g_search_combobox.setObjectName("g_search_combobox")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.horizontalLayout_6.addWidget(self.g_search_combobox)
        self.__g_search_box_frame = QtWidgets.QFrame(self.g_search_frame)
        self.__g_search_box_frame.setMaximumSize(QtCore.QSize(********, 31))
        self.__g_search_box_frame.setStyleSheet("")
        self.__g_search_box_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_search_box_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_search_box_frame.setObjectName("__g_search_box_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.__g_search_box_frame)
        self.horizontalLayout_5.setContentsMargins(2, 0, 5, 0)
        self.horizontalLayout_5.setSpacing(6)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_searchtext_lineedit = QtWidgets.QLineEdit(self.__g_search_box_frame)
        self.g_searchtext_lineedit.setStyleSheet("")
        self.g_searchtext_lineedit.setText("")
        self.g_searchtext_lineedit.setObjectName("g_searchtext_lineedit")
        self.horizontalLayout_5.addWidget(self.g_searchtext_lineedit)
        self.g_clearsearch_button = QtWidgets.QPushButton(self.__g_search_box_frame)
        self.g_clearsearch_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_clearsearch_button.setStyleSheet("")
        self.g_clearsearch_button.setText("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/accounts/small_x"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_clearsearch_button.setIcon(icon)
        self.g_clearsearch_button.setIconSize(QtCore.QSize(18, 18))
        self.g_clearsearch_button.setObjectName("g_clearsearch_button")
        self.horizontalLayout_5.addWidget(self.g_clearsearch_button)
        self.horizontalLayout_6.addWidget(self.__g_search_box_frame)
        self.g_nav_frame = QtWidgets.QFrame(self.g_search_frame)
        self.g_nav_frame.setMinimumSize(QtCore.QSize(150, 0))
        self.g_nav_frame.setMaximumSize(QtCore.QSize(150, ********))
        self.g_nav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_nav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_nav_frame.setObjectName("g_nav_frame")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_nav_frame)
        self.verticalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_4.setSpacing(0)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout()
        self.g_pagenav_hlayout.setSpacing(6)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        self.g_previouspage_label = QtWidgets.QLabel(self.g_nav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_previouspage_label.setMaximumSize(QtCore.QSize(30, ********))
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_nav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_nextpage_label.setMaximumSize(QtCore.QSize(30, ********))
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_nav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(30, ********))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label_5 = QtWidgets.QLabel(self.g_nav_frame)
        self.g_slash_label_5.setMinimumSize(QtCore.QSize(7, 0))
        self.g_slash_label_5.setMaximumSize(QtCore.QSize(7, ********))
        self.g_slash_label_5.setObjectName("g_slash_label_5")
        self.g_pagenav_hlayout.addWidget(self.g_slash_label_5)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_nav_frame)
        self.g_totalpages_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_totalpages_label.setMaximumSize(QtCore.QSize(********, ********))
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout.addWidget(self.g_totalpages_label)
        self.verticalLayout_4.addLayout(self.g_pagenav_hlayout)
        self.horizontalLayout_6.addWidget(self.g_nav_frame)
        self.verticalLayout.addWidget(self.g_search_frame)
        self.g_selectcustomer_table = QtWidgets.QTableWidget(self.g_selectbreeder_frame)
        self.g_selectcustomer_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_selectcustomer_table.setAlternatingRowColors(True)
        self.g_selectcustomer_table.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.g_selectcustomer_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_selectcustomer_table.setShowGrid(False)
        self.g_selectcustomer_table.setObjectName("g_selectcustomer_table")
        self.g_selectcustomer_table.setColumnCount(4)
        self.g_selectcustomer_table.setRowCount(14)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setVerticalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_selectcustomer_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_selectcustomer_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_selectcustomer_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_selectcustomer_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_selectcustomer_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_selectcustomer_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setItem(1, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setItem(2, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setItem(3, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_selectcustomer_table.setItem(4, 3, item)
        self.g_selectcustomer_table.horizontalHeader().setVisible(True)
        self.g_selectcustomer_table.horizontalHeader().setDefaultSectionSize(129)
        self.g_selectcustomer_table.horizontalHeader().setStretchLastSection(True)
        self.g_selectcustomer_table.verticalHeader().setVisible(False)
        self.verticalLayout.addWidget(self.g_selectcustomer_table)
        self.g_bottombar_frame = QtWidgets.QFrame(self.g_selectbreeder_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.g_bottombar_frame.setSizePolicy(sizePolicy)
        self.g_bottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_bottombar_frame.setStyleSheet("")
        self.g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottombar_frame.setObjectName("g_bottombar_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_bottombar_frame)
        self.horizontalLayout.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_controlbuttons_frame = QtWidgets.QFrame(self.g_bottombar_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(6)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.g_totalresults_label = QtWidgets.QLabel(self.g_controlbuttons_frame)
        self.g_totalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_totalresults_label.setFont(font)
        self.g_totalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_totalresults_label.setObjectName("g_totalresults_label")
        self.horizontalLayout_11.addWidget(self.g_totalresults_label)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem1)
        self.g_delete_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_delete_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_delete_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_delete_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_delete_button.setIcon(icon1)
        self.g_delete_button.setIconSize(QtCore.QSize(24, 24))
        self.g_delete_button.setObjectName("g_delete_button")
        self.horizontalLayout_11.addWidget(self.g_delete_button)
        self.g_manage_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_manage_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/entity_dialog/edit"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_manage_button.setIcon(icon2)
        self.g_manage_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manage_button.setObjectName("g_manage_button")
        self.horizontalLayout_11.addWidget(self.g_manage_button)
        self.g_new_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_new_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_new_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_new_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/entity_dialog/icons/flat_add_white_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_new_button.setIcon(icon3)
        self.g_new_button.setIconSize(QtCore.QSize(24, 24))
        self.g_new_button.setObjectName("g_new_button")
        self.horizontalLayout_11.addWidget(self.g_new_button)
        self.g_select_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_select_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_select_button.setStyleSheet("")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_select_button.setIcon(icon4)
        self.g_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_select_button.setObjectName("g_select_button")
        self.horizontalLayout_11.addWidget(self.g_select_button)
        self.horizontalLayout.addWidget(self.g_controlbuttons_frame)
        self.verticalLayout.addWidget(self.g_bottombar_frame)
        self.verticalLayout_2.addWidget(self.g_selectbreeder_frame)

        self.retranslateUi(SelectEntity)
        QtCore.QMetaObject.connectSlotsByName(SelectEntity)

    def retranslateUi(self, SelectEntity):
        _translate = QtCore.QCoreApplication.translate
        SelectEntity.setWindowTitle(_translate("SelectEntity", "Select Existing"))
        self.g_main_label.setText(_translate("SelectEntity", "Select Existing"))
        self.g_search_combobox.setItemText(0, _translate("SelectEntity", "Smart Search"))
        self.g_search_combobox.setItemText(1, _translate("SelectEntity", "Search Name"))
        self.g_search_combobox.setItemText(2, _translate("SelectEntity", "Search Phone"))
        self.g_search_combobox.setItemText(3, _translate("SelectEntity", "Search Email"))
        self.g_search_combobox.setItemText(4, _translate("SelectEntity", "Search Loyalty Card"))
        self.g_search_combobox.setItemText(5, _translate("SelectEntity", "Search All Roles"))
        self.g_searchtext_lineedit.setPlaceholderText(_translate("SelectEntity", "Enter your search terms then press Enter..."))
        self.g_previouspage_label.setToolTip(_translate("SelectEntity", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("SelectEntity", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("SelectEntity", "999"))
        self.g_slash_label_5.setText(_translate("SelectEntity", "/"))
        self.g_totalpages_label.setToolTip(_translate("SelectEntity", "Total Pages"))
        self.g_totalpages_label.setText(_translate("SelectEntity", "999"))
        self.g_selectcustomer_table.setSortingEnabled(False)
        item = self.g_selectcustomer_table.verticalHeaderItem(0)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(1)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(2)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(3)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(4)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(5)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(6)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(7)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(8)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(9)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(10)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(11)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(12)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.verticalHeaderItem(13)
        item.setText(_translate("SelectEntity", "New Row"))
        item = self.g_selectcustomer_table.horizontalHeaderItem(0)
        item.setText(_translate("SelectEntity", "Name"))
        item = self.g_selectcustomer_table.horizontalHeaderItem(1)
        item.setText(_translate("SelectEntity", "Phone"))
        item = self.g_selectcustomer_table.horizontalHeaderItem(2)
        item.setText(_translate("SelectEntity", "Email"))
        item = self.g_selectcustomer_table.horizontalHeaderItem(3)
        item.setText(_translate("SelectEntity", "City, State"))
        __sortingEnabled = self.g_selectcustomer_table.isSortingEnabled()
        self.g_selectcustomer_table.setSortingEnabled(False)
        item = self.g_selectcustomer_table.item(0, 0)
        item.setText(_translate("SelectEntity", "Business 1"))
        item = self.g_selectcustomer_table.item(0, 3)
        item.setText(_translate("SelectEntity", "Orlando, FL"))
        item = self.g_selectcustomer_table.item(1, 0)
        item.setText(_translate("SelectEntity", "Business 2"))
        item = self.g_selectcustomer_table.item(1, 3)
        item.setText(_translate("SelectEntity", "Herndon, VA"))
        item = self.g_selectcustomer_table.item(2, 0)
        item.setText(_translate("SelectEntity", "Business 3"))
        item = self.g_selectcustomer_table.item(2, 3)
        item.setText(_translate("SelectEntity", "Beach, FL"))
        item = self.g_selectcustomer_table.item(3, 0)
        item.setText(_translate("SelectEntity", "Business 4"))
        item = self.g_selectcustomer_table.item(3, 3)
        item.setText(_translate("SelectEntity", "North, WA"))
        item = self.g_selectcustomer_table.item(4, 0)
        item.setText(_translate("SelectEntity", "Business 5"))
        item = self.g_selectcustomer_table.item(4, 3)
        item.setText(_translate("SelectEntity", "B-Town, MA"))
        self.g_selectcustomer_table.setSortingEnabled(__sortingEnabled)
        self.g_totalresults_label.setText(_translate("SelectEntity", "Loading results."))
        self.g_delete_button.setText(_translate("SelectEntity", " Delete"))
        self.g_manage_button.setText(_translate("SelectEntity", " Manage"))
        self.g_new_button.setText(_translate("SelectEntity", " New"))
        self.g_select_button.setText(_translate("SelectEntity", " Select"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    SelectEntity = QtWidgets.QDialog()
    ui = Ui_SelectEntity()
    ui.setupUi(SelectEntity)
    SelectEntity.show()
    sys.exit(app.exec_())
