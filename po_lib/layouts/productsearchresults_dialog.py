# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'productsearchresults_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ProductSearchResultsDialog(object):
    def setupUi(self, ProductSearchResultsDialog):
        ProductSearchResultsDialog.setObjectName("ProductSearchResultsDialog")
        ProductSearchResultsDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        ProductSearchResultsDialog.setEnabled(True)
        ProductSearchResultsDialog.resize(682, 456)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(False)
        font.setWeight(50)
        ProductSearchResultsDialog.setFont(font)
        ProductSearchResultsDialog.setStyleSheet("#ProductSearchResultsDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    border: 2px solid #287599;\n"
"    color: white;\n"
"}\n"
"\n"
"#g_select_button, \n"
"#__g_select_button {\n"
"    background: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_select_button:pressed,\n"
"#__g_select_button:pressed {\n"
"    background: #007f00;\n"
"}\n"
"\n"
"#g_select_button:disabled,\n"
"#__g_select_button:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}\n"
"\n"
"#g_cancel_button,\n"
"#__g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed,\n"
"#__g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"#label {\n"
"    font-size: 15px;\n"
"}")
        ProductSearchResultsDialog.setModal(True)
        ProductSearchResultsDialog.setProperty("column_widths_list", ['545', '86'])
        self.verticalLayout = QtWidgets.QVBoxLayout(ProductSearchResultsDialog)
        self.verticalLayout.setContentsMargins(18, 18, 18, 18)
        self.verticalLayout.setSpacing(18)
        self.verticalLayout.setObjectName("verticalLayout")
        self.label = QtWidgets.QLabel(ProductSearchResultsDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.label.setFont(font)
        self.label.setObjectName("label")
        self.verticalLayout.addWidget(self.label)
        self.g_productresults_table = QtWidgets.QTableWidget(ProductSearchResultsDialog)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_productresults_table.setFont(font)
        self.g_productresults_table.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.g_productresults_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_productresults_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_productresults_table.setAlternatingRowColors(True)
        self.g_productresults_table.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.g_productresults_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_productresults_table.setRowCount(0)
        self.g_productresults_table.setObjectName("g_productresults_table")
        self.g_productresults_table.setColumnCount(2)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_productresults_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_productresults_table.setHorizontalHeaderItem(1, item)
        self.g_productresults_table.horizontalHeader().setHighlightSections(False)
        self.g_productresults_table.horizontalHeader().setSortIndicatorShown(True)
        self.g_productresults_table.horizontalHeader().setStretchLastSection(True)
        self.g_productresults_table.verticalHeader().setVisible(False)
        self.g_productresults_table.verticalHeader().setStretchLastSection(False)
        self.verticalLayout.addWidget(self.g_productresults_table)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(18)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(ProductSearchResultsDialog)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/sales_screen/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_select_button = QtWidgets.QPushButton(ProductSearchResultsDialog)
        self.g_select_button.setEnabled(True)
        self.g_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_select_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/sales_screen/save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_select_button.setIcon(icon1)
        self.g_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_select_button.setObjectName("g_select_button")
        self.horizontalLayout.addWidget(self.g_select_button)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(ProductSearchResultsDialog)
        QtCore.QMetaObject.connectSlotsByName(ProductSearchResultsDialog)

    def retranslateUi(self, ProductSearchResultsDialog):
        _translate = QtCore.QCoreApplication.translate
        ProductSearchResultsDialog.setWindowTitle(_translate("ProductSearchResultsDialog", "Choose a Product"))
        self.label.setText(_translate("ProductSearchResultsDialog", "The following search results were found. Please choose one."))
        self.g_productresults_table.setSortingEnabled(True)
        item = self.g_productresults_table.horizontalHeaderItem(0)
        item.setText(_translate("ProductSearchResultsDialog", "Description"))
        item = self.g_productresults_table.horizontalHeaderItem(1)
        item.setText(_translate("ProductSearchResultsDialog", "Price"))
        self.g_cancel_button.setText(_translate("ProductSearchResultsDialog", " Cancel"))
        self.g_select_button.setText(_translate("ProductSearchResultsDialog", " Select"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ProductSearchResultsDialog = QtWidgets.QDialog()
    ui = Ui_ProductSearchResultsDialog()
    ui.setupUi(ProductSearchResultsDialog)
    ProductSearchResultsDialog.show()
    sys.exit(app.exec_())
