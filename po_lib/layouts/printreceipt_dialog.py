# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'printreceipt_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_PrintReceiptDialog(object):
    def setupUi(self, PrintReceiptDialog):
        PrintReceiptDialog.setObjectName("PrintReceiptDialog")
        PrintReceiptDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        PrintReceiptDialog.resize(590, 610)
        PrintReceiptDialog.setMinimumSize(QtCore.QSize(590, 610))
        PrintReceiptDialog.setMaximumSize(QtCore.QSize(590, 610))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/printreceipt_modaldialog/window_icon"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        PrintReceiptDialog.setWindowIcon(icon)
        PrintReceiptDialog.setStyleSheet("\n"
"#g_invoice_table {\n"
"    border: 1px solid #073c83;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_invoice_table::item {\n"
"    padding-left: 5px;\n"
"    padding-right: 5px;\n"
"}\n"
"\n"
"#g_invoice_table QHeaderView::section {\n"
"    font-size: 13px;\n"
"    font-weight: bold;    \n"
"    spacing: 20px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    border-bottom: 1px solid #073c83;\n"
"    font-size:12px;\n"
"    height: 29px;\n"
"    padding-left: 5px;\n"
"    padding-right: 5px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"#g_close_button, \n"
"#__g_close_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_close_button:pressed, \n"
"#__g_close_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"\n"
"\n"
"QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-size: 13px;\n"
"\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#PrintReceiptDialog, \n"
"#__PrintReceiptDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_email_lineedit, \n"
"#__g_email_lineedit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#__g_dialog_buttons_frame .QPushButton,\n"
"#__g_dialog_buttons_frame .QPushButton {\n"
"    color:white;\n"
"    font-weight:bold;\n"
"    font-size:11pt;\n"
"}\n"
"\n"
"#g_reason_combobox, \n"
"#__g_reason_combobox {\n"
"    display: inline-block;\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_reason_combobox::drop-down, \n"
"#__g_reason_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"#__g_title_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#__g_invoice_number_title_label,\n"
"#g_invoicenumber_label,\n"
"#__g_invoice_total_title_label,\n"
"#g_invoicetotal_label,\n"
"#g_change_frame QLabel {\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}")
        PrintReceiptDialog.setModal(True)
        PrintReceiptDialog.setProperty("invoice_table_column_widths", ['275', '50'])
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(PrintReceiptDialog)
        self.verticalLayout_2.setSpacing(16)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.__g_title_label = QtWidgets.QLabel(PrintReceiptDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.__g_title_label.setFont(font)
        self.__g_title_label.setObjectName("__g_title_label")
        self.verticalLayout_2.addWidget(self.__g_title_label)
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setContentsMargins(-1, 9, -1, -1)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem)
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setHorizontalSpacing(6)
        self.gridLayout.setVerticalSpacing(11)
        self.gridLayout.setObjectName("gridLayout")
        self.__g_invoice_number_title_label = QtWidgets.QLabel(PrintReceiptDialog)
        self.__g_invoice_number_title_label.setMinimumSize(QtCore.QSize(150, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.__g_invoice_number_title_label.setFont(font)
        self.__g_invoice_number_title_label.setObjectName("__g_invoice_number_title_label")
        self.gridLayout.addWidget(self.__g_invoice_number_title_label, 0, 0, 1, 1)
        self.g_invoicenumber_label = QtWidgets.QLabel(PrintReceiptDialog)
        self.g_invoicenumber_label.setMinimumSize(QtCore.QSize(150, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_invoicenumber_label.setFont(font)
        self.g_invoicenumber_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_invoicenumber_label.setObjectName("g_invoicenumber_label")
        self.gridLayout.addWidget(self.g_invoicenumber_label, 0, 1, 1, 1)
        self.__g_invoice_total_title_label = QtWidgets.QLabel(PrintReceiptDialog)
        self.__g_invoice_total_title_label.setMinimumSize(QtCore.QSize(150, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.__g_invoice_total_title_label.setFont(font)
        self.__g_invoice_total_title_label.setObjectName("__g_invoice_total_title_label")
        self.gridLayout.addWidget(self.__g_invoice_total_title_label, 1, 0, 1, 1)
        self.g_invoicetotal_label = QtWidgets.QLabel(PrintReceiptDialog)
        self.g_invoicetotal_label.setMinimumSize(QtCore.QSize(150, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_invoicetotal_label.setFont(font)
        self.g_invoicetotal_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_invoicetotal_label.setObjectName("g_invoicetotal_label")
        self.gridLayout.addWidget(self.g_invoicetotal_label, 1, 1, 1, 1)
        self.horizontalLayout_5.addLayout(self.gridLayout)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem1)
        self.verticalLayout_2.addLayout(self.horizontalLayout_5)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setContentsMargins(-1, 10, -1, -1)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem2)
        self.g_invoice_table = QtWidgets.QTableWidget(PrintReceiptDialog)
        self.g_invoice_table.setMinimumSize(QtCore.QSize(370, 225))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_invoice_table.setFont(font)
        self.g_invoice_table.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_invoice_table.setStyleSheet("")
        self.g_invoice_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_invoice_table.setAlternatingRowColors(True)
        self.g_invoice_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_invoice_table.setShowGrid(False)
        self.g_invoice_table.setColumnCount(2)
        self.g_invoice_table.setObjectName("g_invoice_table")
        self.g_invoice_table.setRowCount(4)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoice_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoice_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoice_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoice_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_invoice_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_invoice_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoice_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_invoice_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoice_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_invoice_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_invoice_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_invoice_table.setItem(2, 1, item)
        self.g_invoice_table.horizontalHeader().setVisible(True)
        self.g_invoice_table.horizontalHeader().setDefaultSectionSize(166)
        self.g_invoice_table.horizontalHeader().setStretchLastSection(True)
        self.g_invoice_table.verticalHeader().setVisible(False)
        self.horizontalLayout_2.addWidget(self.g_invoice_table)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem3)
        self.verticalLayout_2.addLayout(self.horizontalLayout_2)
        self.g_change_frame = QtWidgets.QFrame(PrintReceiptDialog)
        self.g_change_frame.setObjectName("g_change_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_change_frame)
        self.horizontalLayout_4.setSpacing(105)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem4)
        self.__g_change_title_label = QtWidgets.QLabel(self.g_change_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.__g_change_title_label.setFont(font)
        self.__g_change_title_label.setObjectName("__g_change_title_label")
        self.horizontalLayout_4.addWidget(self.__g_change_title_label)
        self.g_change_label = QtWidgets.QLabel(self.g_change_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_change_label.setFont(font)
        self.g_change_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_change_label.setObjectName("g_change_label")
        self.horizontalLayout_4.addWidget(self.g_change_label)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem5)
        self.verticalLayout_2.addWidget(self.g_change_frame)
        spacerItem6 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_2.addItem(spacerItem6)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_emailreceipt_groupbox = QtWidgets.QGroupBox(PrintReceiptDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_emailreceipt_groupbox.setFont(font)
        self.g_emailreceipt_groupbox.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_emailreceipt_groupbox.setObjectName("g_emailreceipt_groupbox")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_emailreceipt_groupbox)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_emailaddress_lineedit = QtWidgets.QLineEdit(self.g_emailreceipt_groupbox)
        self.g_emailaddress_lineedit.setMinimumSize(QtCore.QSize(99, 40))
        self.g_emailaddress_lineedit.setMaximumSize(QtCore.QSize(300, 40))
        font = QtGui.QFont()
        font.setPointSize(9)
        self.g_emailaddress_lineedit.setFont(font)
        self.g_emailaddress_lineedit.setStyleSheet("")
        self.g_emailaddress_lineedit.setText("")
        self.g_emailaddress_lineedit.setObjectName("g_emailaddress_lineedit")
        self.horizontalLayout_6.addWidget(self.g_emailaddress_lineedit)
        self.g_send_button = QtWidgets.QPushButton(self.g_emailreceipt_groupbox)
        self.g_send_button.setMinimumSize(QtCore.QSize(140, 40))
        self.g_send_button.setMaximumSize(QtCore.QSize(140, 40))
        self.g_send_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/sales_screen/white_envelope"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_send_button.setIcon(icon1)
        self.g_send_button.setIconSize(QtCore.QSize(24, 24))
        self.g_send_button.setObjectName("g_send_button")
        self.horizontalLayout_6.addWidget(self.g_send_button)
        self.horizontalLayout_3.addWidget(self.g_emailreceipt_groupbox)
        self.verticalLayout_2.addLayout(self.horizontalLayout_3)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setContentsMargins(-1, -1, 6, 6)
        self.horizontalLayout.setSpacing(18)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem7)
        self.g_storereceipt_button = QtWidgets.QPushButton(PrintReceiptDialog)
        self.g_storereceipt_button.setMinimumSize(QtCore.QSize(140, 40))
        self.g_storereceipt_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/sales_screen/print"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_storereceipt_button.setIcon(icon2)
        self.g_storereceipt_button.setIconSize(QtCore.QSize(24, 24))
        self.g_storereceipt_button.setObjectName("g_storereceipt_button")
        self.horizontalLayout.addWidget(self.g_storereceipt_button)
        self.g_print_button = QtWidgets.QPushButton(PrintReceiptDialog)
        self.g_print_button.setMinimumSize(QtCore.QSize(140, 40))
        self.g_print_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_print_button.setIcon(icon2)
        self.g_print_button.setIconSize(QtCore.QSize(24, 24))
        self.g_print_button.setObjectName("g_print_button")
        self.horizontalLayout.addWidget(self.g_print_button)
        self.g_close_button = QtWidgets.QPushButton(PrintReceiptDialog)
        self.g_close_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_close_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_close_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/printreceipt_modaldialog/close"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_close_button.setIcon(icon3)
        self.g_close_button.setIconSize(QtCore.QSize(24, 24))
        self.g_close_button.setObjectName("g_close_button")
        self.horizontalLayout.addWidget(self.g_close_button)
        self.verticalLayout_2.addLayout(self.horizontalLayout)

        self.retranslateUi(PrintReceiptDialog)
        QtCore.QMetaObject.connectSlotsByName(PrintReceiptDialog)

    def retranslateUi(self, PrintReceiptDialog):
        _translate = QtCore.QCoreApplication.translate
        PrintReceiptDialog.setWindowTitle(_translate("PrintReceiptDialog", "Print Receipt"))
        PrintReceiptDialog.setProperty("print_receipt_title_text", _translate("PrintReceiptDialog", "Print Receipt"))
        PrintReceiptDialog.setProperty("printing_now_msg_text", _translate("PrintReceiptDialog", "Printing your receipt. Please wait ..."))
        PrintReceiptDialog.setProperty("email_address_invalid_msg_text", _translate("PrintReceiptDialog", "Please enter a valid email address."))
        PrintReceiptDialog.setProperty("emailing_now_msg_text", _translate("PrintReceiptDialog", "Emailing your receipt. Please wait ..."))
        PrintReceiptDialog.setProperty("current_session_url_text", _translate("PrintReceiptDialog", "/apps/any/sessions/mine"))
        PrintReceiptDialog.setProperty("error_title_text", _translate("PrintReceiptDialog", "Error"))
        self.__g_title_label.setText(_translate("PrintReceiptDialog", "Print Receipt"))
        self.__g_invoice_number_title_label.setText(_translate("PrintReceiptDialog", "Invoice #"))
        self.g_invoicenumber_label.setText(_translate("PrintReceiptDialog", "9942234"))
        self.__g_invoice_total_title_label.setText(_translate("PrintReceiptDialog", "Invoice Total"))
        self.g_invoicetotal_label.setText(_translate("PrintReceiptDialog", "80.45"))
        item = self.g_invoice_table.verticalHeaderItem(0)
        item.setText(_translate("PrintReceiptDialog", "New Row"))
        item = self.g_invoice_table.verticalHeaderItem(1)
        item.setText(_translate("PrintReceiptDialog", "New Row"))
        item = self.g_invoice_table.verticalHeaderItem(2)
        item.setText(_translate("PrintReceiptDialog", "New Row"))
        item = self.g_invoice_table.verticalHeaderItem(3)
        item.setText(_translate("PrintReceiptDialog", "New Row"))
        item = self.g_invoice_table.horizontalHeaderItem(0)
        item.setText(_translate("PrintReceiptDialog", "Payment Type"))
        item = self.g_invoice_table.horizontalHeaderItem(1)
        item.setText(_translate("PrintReceiptDialog", "Amount"))
        __sortingEnabled = self.g_invoice_table.isSortingEnabled()
        self.g_invoice_table.setSortingEnabled(False)
        item = self.g_invoice_table.item(0, 0)
        item.setText(_translate("PrintReceiptDialog", "American Express Card (5614)"))
        item = self.g_invoice_table.item(0, 1)
        item.setText(_translate("PrintReceiptDialog", "50.00"))
        item = self.g_invoice_table.item(1, 0)
        item.setText(_translate("PrintReceiptDialog", "Personal Check"))
        item = self.g_invoice_table.item(1, 1)
        item.setText(_translate("PrintReceiptDialog", "25.00"))
        item = self.g_invoice_table.item(2, 0)
        item.setText(_translate("PrintReceiptDialog", "Cash"))
        item = self.g_invoice_table.item(2, 1)
        item.setText(_translate("PrintReceiptDialog", "10.45"))
        self.g_invoice_table.setSortingEnabled(__sortingEnabled)
        self.__g_change_title_label.setText(_translate("PrintReceiptDialog", "Change"))
        self.g_change_label.setText(_translate("PrintReceiptDialog", "0.00"))
        self.g_emailreceipt_groupbox.setTitle(_translate("PrintReceiptDialog", "Email Receipt"))
        self.g_emailaddress_lineedit.setPlaceholderText(_translate("PrintReceiptDialog", "Enter email address."))
        self.g_send_button.setText(_translate("PrintReceiptDialog", "Send Now"))
        self.g_storereceipt_button.setText(_translate("PrintReceiptDialog", "  Store Receipt"))
        self.g_print_button.setText(_translate("PrintReceiptDialog", "  Print Now"))
        self.g_close_button.setText(_translate("PrintReceiptDialog", "  Close"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    PrintReceiptDialog = QtWidgets.QDialog()
    ui = Ui_PrintReceiptDialog()
    ui.setupUi(PrintReceiptDialog)
    PrintReceiptDialog.show()
    sys.exit(app.exec_())
