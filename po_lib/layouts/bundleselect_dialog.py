# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'bundleselect_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_BundleSelectDialog(object):
    def setupUi(self, BundleSelectDialog):
        BundleSelectDialog.setObjectName("BundleSelectDialog")
        BundleSelectDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        BundleSelectDialog.setEnabled(True)
        BundleSelectDialog.resize(682, 456)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(False)
        font.setWeight(50)
        BundleSelectDialog.setFont(font)
        BundleSelectDialog.setStyleSheet("#BundleSelectDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    border: 2px solid #287599;\n"
"    color: white;\n"
"}\n"
"\n"
"#g_select_button, \n"
"#__g_select_button {\n"
"    background: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_select_button:pressed,\n"
"#__g_select_button:pressed {\n"
"    background: #007f00;\n"
"}\n"
"\n"
"#g_select_button:disabled,\n"
"#__g_select_button:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}\n"
"\n"
"#g_cancel_button,\n"
"#__g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed,\n"
"#__g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"\n"
"QTreeView {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"#label {\n"
"    font-size: 15px;\n"
"}")
        BundleSelectDialog.setModal(True)
        BundleSelectDialog.setProperty("column_widths_list", ['545', '86'])
        self.verticalLayout = QtWidgets.QVBoxLayout(BundleSelectDialog)
        self.verticalLayout.setContentsMargins(18, 18, 18, 18)
        self.verticalLayout.setSpacing(18)
        self.verticalLayout.setObjectName("verticalLayout")
        self.label = QtWidgets.QLabel(BundleSelectDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.label.setFont(font)
        self.label.setObjectName("label")
        self.verticalLayout.addWidget(self.label)
        self.g_frame = QtWidgets.QFrame(BundleSelectDialog)
        self.g_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_frame.setObjectName("g_frame")
        self.verticalLayout.addWidget(self.g_frame)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(18)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(BundleSelectDialog)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/sales_screen/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_select_button = QtWidgets.QPushButton(BundleSelectDialog)
        self.g_select_button.setEnabled(True)
        self.g_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_select_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/sales_screen/save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_select_button.setIcon(icon1)
        self.g_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_select_button.setObjectName("g_select_button")
        self.horizontalLayout.addWidget(self.g_select_button)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(BundleSelectDialog)
        QtCore.QMetaObject.connectSlotsByName(BundleSelectDialog)

    def retranslateUi(self, BundleSelectDialog):
        _translate = QtCore.QCoreApplication.translate
        BundleSelectDialog.setWindowTitle(_translate("BundleSelectDialog", "Choose a Bundle"))
        self.label.setText(_translate("BundleSelectDialog", "The following bundles were found. Please choose one."))
        self.g_cancel_button.setText(_translate("BundleSelectDialog", " Cancel"))
        self.g_select_button.setText(_translate("BundleSelectDialog", " Select"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    BundleSelectDialog = QtWidgets.QDialog()
    ui = Ui_BundleSelectDialog()
    ui.setupUi(BundleSelectDialog)
    BundleSelectDialog.show()
    sys.exit(app.exec_())
