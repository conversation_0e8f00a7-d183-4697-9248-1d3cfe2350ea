# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'reports_screen.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ReportsScreen(object):
    def setupUi(self, ReportsScreen):
        ReportsScreen.setObjectName("ReportsScreen")
        ReportsScreen.resize(1063, 667)
        ReportsScreen.setMinimumSize(QtCore.QSize(0, 360))
        ReportsScreen.setToolTipDuration(-9)
        ReportsScreen.setStyleSheet("#ReportsScreen {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"QCheckBox {\n"
"    padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/pet_tracker/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/pet_tracker/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QDateTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTreeWidget {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    padding-top:5px;\n"
"}\n"
"\n"
"QListWidget {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    padding: 2px;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"QTabWidget::tab-bar {\n"
"   left: 0;\n"
"}\n"
"\n"
"#g_dialog_frame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-size: 12px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"QTabWidget::tab-bar {\n"
"   left: 0;\n"
"}\n"
"\n"
"QTabWidget {\n"
"    border:0px;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"#g_settings_tab,\n"
"#g_generate_tab,\n"
"#g_saved_tab,\n"
"#g_schedule_tab {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius:0px;\n"
"    background-color: #e7eff4;\n"
"}\n"
"\n"
"#g_description_label {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color: #dddddd;\n"
"    color: #333333;\n"
"}\n"
"\n"
"#g_save_settings_button,\n"
"#g_scheduling_button,\n"
"#g_update_schedule_button,\n"
"#g_manage_button,\n"
"#g_addschedule_button,\n"
"#g_generate_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_settings_button:pressed,\n"
"#g_scheduling_button:pressed,\n"
"#g_update_schedule_button:pressed,\n"
"#g_manage_button:pressed,\n"
"#g_addschedule_button:pressed,\n"
"#g_generate_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_settings_button,\n"
"#g_delete_button{\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_settings_button:pressed,\n"
"#g_delete_button:pressed{\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_settings_frame QLabel,\n"
"#g_settings_frame QComboBox,\n"
"#g_settings_frame PyCheckCombobox,\n"
"#g_settings_frame QListWidget {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_quicksearchfilters_frame QLabel,\n"
"#g_quicksearchfilters_frame QCheckBox,\n"
"#g_totalresults_label,\n"
"#g_currentpage_lineedit,\n"
"#g_totalpages_label {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QDateTimeEdit::drop-down,\n"
"QComboBox::drop-down  {        \n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"}\n"
"\n"
"#::drop-down {    \n"
"    background-image: url(:/entity_dialog/drop_down_3dot);\n"
"}\n"
"")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/sales_screen/print"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        ReportsScreen.setProperty("screen_toolbutton_icon", icon)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(ReportsScreen)
        self.verticalLayout_2.setContentsMargins(9, 0, 9, 9)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_top_frame = QtWidgets.QFrame(ReportsScreen)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_8.addWidget(self.g_progress_frame)
        self.verticalLayout_2.addWidget(self.g_top_frame)
        self.g_main_label = QtWidgets.QLabel(ReportsScreen)
        self.g_main_label.setMinimumSize(QtCore.QSize(0, 30))
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 30))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setStyleSheet("")
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_2.addWidget(self.g_main_label)
        self.g_reportscontainer_frame = QtWidgets.QFrame(ReportsScreen)
        self.g_reportscontainer_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_reportscontainer_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_reportscontainer_frame.setObjectName("g_reportscontainer_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_reportscontainer_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setSpacing(6)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_reportcategories_groupbox = QtWidgets.QGroupBox(self.g_reportscontainer_frame)
        self.g_reportcategories_groupbox.setMinimumSize(QtCore.QSize(300, 0))
        self.g_reportcategories_groupbox.setMaximumSize(QtCore.QSize(300, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_reportcategories_groupbox.setFont(font)
        self.g_reportcategories_groupbox.setObjectName("g_reportcategories_groupbox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_reportcategories_groupbox)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_reports_tree = QtWidgets.QTreeWidget(self.g_reportcategories_groupbox)
        self.g_reports_tree.setStyleSheet("")
        self.g_reports_tree.setAlternatingRowColors(False)
        self.g_reports_tree.setIndentation(20)
        self.g_reports_tree.setRootIsDecorated(True)
        self.g_reports_tree.setUniformRowHeights(False)
        self.g_reports_tree.setAnimated(True)
        self.g_reports_tree.setAllColumnsShowFocus(False)
        self.g_reports_tree.setHeaderHidden(True)
        self.g_reports_tree.setColumnCount(1)
        self.g_reports_tree.setProperty("qp_file_global_file_id", "")
        self.g_reports_tree.setObjectName("g_reports_tree")
        item_0 = QtWidgets.QTreeWidgetItem(self.g_reports_tree)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item_0.setFont(0, font)
        item_1 = QtWidgets.QTreeWidgetItem(item_0)
        item_1 = QtWidgets.QTreeWidgetItem(item_0)
        item_1 = QtWidgets.QTreeWidgetItem(item_0)
        item_0 = QtWidgets.QTreeWidgetItem(self.g_reports_tree)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item_0.setFont(0, font)
        item_1 = QtWidgets.QTreeWidgetItem(item_0)
        item_1 = QtWidgets.QTreeWidgetItem(item_0)
        item_1 = QtWidgets.QTreeWidgetItem(item_0)
        item_0 = QtWidgets.QTreeWidgetItem(self.g_reports_tree)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item_0.setFont(0, font)
        item_1 = QtWidgets.QTreeWidgetItem(item_0)
        item_1 = QtWidgets.QTreeWidgetItem(item_0)
        item_1 = QtWidgets.QTreeWidgetItem(item_0)
        self.g_reports_tree.header().setVisible(False)
        self.g_reports_tree.header().setCascadingSectionResizes(False)
        self.g_reports_tree.header().setDefaultSectionSize(100)
        self.g_reports_tree.header().setSortIndicatorShown(False)
        self.g_reports_tree.header().setStretchLastSection(True)
        self.verticalLayout.addWidget(self.g_reports_tree)
        self.horizontalLayout.addWidget(self.g_reportcategories_groupbox)
        self.g_main_tab = QtWidgets.QTabWidget(self.g_reportscontainer_frame)
        self.g_main_tab.setObjectName("g_main_tab")
        self.g_generate_tab = QtWidgets.QWidget()
        self.g_generate_tab.setObjectName("g_generate_tab")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_generate_tab)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_reportoptions_stack = QtWidgets.QStackedWidget(self.g_generate_tab)
        self.g_reportoptions_stack.setObjectName("g_reportoptions_stack")
        self.page_5 = QtWidgets.QWidget()
        self.page_5.setObjectName("page_5")
        self.g_reportoptions_stack.addWidget(self.page_5)
        self.page_6 = QtWidgets.QWidget()
        self.page_6.setObjectName("page_6")
        self.g_reportoptions_stack.addWidget(self.page_6)
        self.verticalLayout_3.addWidget(self.g_reportoptions_stack)
        self.g_reportscontrols_frame = QtWidgets.QFrame(self.g_generate_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_reportscontrols_frame.sizePolicy().hasHeightForWidth())
        self.g_reportscontrols_frame.setSizePolicy(sizePolicy)
        self.g_reportscontrols_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_reportscontrols_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_reportscontrols_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_reportscontrols_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_reportscontrols_frame.setObjectName("g_reportscontrols_frame")
        self.horizontalLayout_20 = QtWidgets.QHBoxLayout(self.g_reportscontrols_frame)
        self.horizontalLayout_20.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_20.setSpacing(6)
        self.horizontalLayout_20.setObjectName("horizontalLayout_20")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_20.addItem(spacerItem)
        self.g_generate_button = QtWidgets.QPushButton(self.g_reportscontrols_frame)
        self.g_generate_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_generate_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_generate_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/report_modaldialog/check"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_generate_button.setIcon(icon1)
        self.g_generate_button.setIconSize(QtCore.QSize(24, 24))
        self.g_generate_button.setObjectName("g_generate_button")
        self.horizontalLayout_20.addWidget(self.g_generate_button)
        self.verticalLayout_3.addWidget(self.g_reportscontrols_frame)
        self.g_main_tab.addTab(self.g_generate_tab, "")
        self.g_saved_tab = QtWidgets.QWidget()
        self.g_saved_tab.setObjectName("g_saved_tab")
        self.verticalLayout_11 = QtWidgets.QVBoxLayout(self.g_saved_tab)
        self.verticalLayout_11.setObjectName("verticalLayout_11")
        self.g_quicksearchfilters_frame = QtWidgets.QFrame(self.g_saved_tab)
        self.g_quicksearchfilters_frame.setMaximumSize(QtCore.QSize(16777215, 34))
        self.g_quicksearchfilters_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_quicksearchfilters_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_quicksearchfilters_frame.setObjectName("g_quicksearchfilters_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_quicksearchfilters_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(6)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_quickfilters_label = QtWidgets.QLabel(self.g_quicksearchfilters_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_quickfilters_label.setFont(font)
        self.g_quickfilters_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_quickfilters_label.setObjectName("g_quickfilters_label")
        self.horizontalLayout_6.addWidget(self.g_quickfilters_label)
        self.g_status_options_frame = QtWidgets.QFrame(self.g_quicksearchfilters_frame)
        self.g_status_options_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_status_options_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_status_options_frame.setObjectName("g_status_options_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_status_options_frame)
        self.horizontalLayout_5.setContentsMargins(0, 6, 0, 0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.horizontalLayout_6.addWidget(self.g_status_options_frame)
        self.frame_3 = QtWidgets.QFrame(self.g_quicksearchfilters_frame)
        self.frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.frame_3)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 6)
        self.horizontalLayout_9.setSpacing(0)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.g_onlyatthisreport_checkbox = QtWidgets.QCheckBox(self.frame_3)
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_onlyatthisreport_checkbox.setFont(font)
        self.g_onlyatthisreport_checkbox.setText("")
        self.g_onlyatthisreport_checkbox.setIconSize(QtCore.QSize(16, 16))
        self.g_onlyatthisreport_checkbox.setChecked(True)
        self.g_onlyatthisreport_checkbox.setObjectName("g_onlyatthisreport_checkbox")
        self.horizontalLayout_9.addWidget(self.g_onlyatthisreport_checkbox)
        self.horizontalLayout_6.addWidget(self.frame_3)
        self.g_quickfiltertext_label = QtWidgets.QLabel(self.g_quicksearchfilters_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_quickfiltertext_label.setFont(font)
        self.g_quickfiltertext_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_quickfiltertext_label.setObjectName("g_quickfiltertext_label")
        self.horizontalLayout_6.addWidget(self.g_quickfiltertext_label)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem1)
        self.g_navscheduled_frame = QtWidgets.QFrame(self.g_quicksearchfilters_frame)
        self.g_navscheduled_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_navscheduled_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_navscheduled_frame.setObjectName("g_navscheduled_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_navscheduled_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout()
        self.g_pagenav_hlayout.setSpacing(8)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout.addItem(spacerItem2)
        self.g_previouspage_label = QtWidgets.QLabel(self.g_navscheduled_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/report_modaldialog/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_navscheduled_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/report_modaldialog/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_navscheduled_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label = QtWidgets.QLabel(self.g_navscheduled_frame)
        self.g_slash_label.setObjectName("g_slash_label")
        self.g_pagenav_hlayout.addWidget(self.g_slash_label)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_navscheduled_frame)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout.addWidget(self.g_totalpages_label)
        self.horizontalLayout_3.addLayout(self.g_pagenav_hlayout)
        self.horizontalLayout_6.addWidget(self.g_navscheduled_frame)
        self.verticalLayout_11.addWidget(self.g_quicksearchfilters_frame)
        self.g_schedule_table = QtWidgets.QTableWidget(self.g_saved_tab)
        self.g_schedule_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_schedule_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_schedule_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_schedule_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_schedule_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_schedule_table.setAlternatingRowColors(True)
        self.g_schedule_table.setSelectionMode(QtWidgets.QAbstractItemView.MultiSelection)
        self.g_schedule_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_schedule_table.setShowGrid(False)
        self.g_schedule_table.setObjectName("g_schedule_table")
        self.g_schedule_table.setColumnCount(6)
        self.g_schedule_table.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_schedule_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_schedule_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_schedule_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_schedule_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_schedule_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_schedule_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_schedule_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_schedule_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_schedule_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_schedule_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_schedule_table.setItem(0, 4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_schedule_table.setItem(0, 5, item)
        self.g_schedule_table.horizontalHeader().setDefaultSectionSize(150)
        self.g_schedule_table.horizontalHeader().setStretchLastSection(True)
        self.g_schedule_table.verticalHeader().setVisible(False)
        self.verticalLayout_11.addWidget(self.g_schedule_table)
        self.g_controlbuttons_frame_5 = QtWidgets.QFrame(self.g_saved_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame_5.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame_5.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame_5.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame_5.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame_5.setObjectName("g_controlbuttons_frame_5")
        self.horizontalLayout_19 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame_5)
        self.horizontalLayout_19.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_19.setSpacing(6)
        self.horizontalLayout_19.setObjectName("horizontalLayout_19")
        self.g_totalresults_label = QtWidgets.QLabel(self.g_controlbuttons_frame_5)
        self.g_totalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_totalresults_label.setFont(font)
        self.g_totalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_totalresults_label.setObjectName("g_totalresults_label")
        self.horizontalLayout_19.addWidget(self.g_totalresults_label)
        spacerItem3 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_19.addItem(spacerItem3)
        self.g_delete_button = QtWidgets.QPushButton(self.g_controlbuttons_frame_5)
        self.g_delete_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_delete_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_delete_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/report_modaldialog/delete"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_delete_button.setIcon(icon2)
        self.g_delete_button.setIconSize(QtCore.QSize(24, 24))
        self.g_delete_button.setObjectName("g_delete_button")
        self.horizontalLayout_19.addWidget(self.g_delete_button)
        self.g_disable_button = QtWidgets.QPushButton(self.g_controlbuttons_frame_5)
        self.g_disable_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_disable_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_disable_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/report_modaldialog/disable"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_disable_button.setIcon(icon3)
        self.g_disable_button.setIconSize(QtCore.QSize(24, 24))
        self.g_disable_button.setObjectName("g_disable_button")
        self.horizontalLayout_19.addWidget(self.g_disable_button)
        self.g_enable_button = QtWidgets.QPushButton(self.g_controlbuttons_frame_5)
        self.g_enable_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_enable_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_enable_button.setStyleSheet("")
        self.g_enable_button.setIcon(icon3)
        self.g_enable_button.setIconSize(QtCore.QSize(24, 24))
        self.g_enable_button.setObjectName("g_enable_button")
        self.horizontalLayout_19.addWidget(self.g_enable_button)
        self.g_scheduling_button = QtWidgets.QPushButton(self.g_controlbuttons_frame_5)
        self.g_scheduling_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_scheduling_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_scheduling_button.setStyleSheet("")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/report_modaldialog/schedule"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_scheduling_button.setIcon(icon4)
        self.g_scheduling_button.setIconSize(QtCore.QSize(24, 24))
        self.g_scheduling_button.setObjectName("g_scheduling_button")
        self.horizontalLayout_19.addWidget(self.g_scheduling_button)
        self.g_manage_button = QtWidgets.QPushButton(self.g_controlbuttons_frame_5)
        self.g_manage_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setStyleSheet("")
        icon5 = QtGui.QIcon()
        icon5.addPixmap(QtGui.QPixmap(":/report_modaldialog/edit"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_manage_button.setIcon(icon5)
        self.g_manage_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manage_button.setObjectName("g_manage_button")
        self.horizontalLayout_19.addWidget(self.g_manage_button)
        self.verticalLayout_11.addWidget(self.g_controlbuttons_frame_5)
        self.g_main_tab.addTab(self.g_saved_tab, "")
        self.g_settings_tab = QtWidgets.QWidget()
        self.g_settings_tab.setObjectName("g_settings_tab")
        self.verticalLayout_13 = QtWidgets.QVBoxLayout(self.g_settings_tab)
        self.verticalLayout_13.setObjectName("verticalLayout_13")
        self.g_settings_frame = QtWidgets.QFrame(self.g_settings_tab)
        self.g_settings_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_settings_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_settings_frame.setObjectName("g_settings_frame")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_settings_frame)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_application_groupbox = QtWidgets.QGroupBox(self.g_settings_frame)
        self.g_application_groupbox.setMinimumSize(QtCore.QSize(0, 80))
        self.g_application_groupbox.setMaximumSize(QtCore.QSize(16777215, 80))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_application_groupbox.setFont(font)
        self.g_application_groupbox.setObjectName("g_application_groupbox")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.g_application_groupbox)
        self.verticalLayout_6.setSpacing(6)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.g_apphelp_label = QtWidgets.QLabel(self.g_application_groupbox)
        self.g_apphelp_label.setObjectName("g_apphelp_label")
        self.verticalLayout_6.addWidget(self.g_apphelp_label)
        self.g_application_checkcombobox = PyCheckCombobox(self.g_application_groupbox)
        self.g_application_checkcombobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_application_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_application_checkcombobox.setToolTip("")
        self.g_application_checkcombobox.setWhatsThis("")
        self.g_application_checkcombobox.setObjectName("g_application_checkcombobox")
        self.verticalLayout_6.addWidget(self.g_application_checkcombobox)
        self.verticalLayout_4.addWidget(self.g_application_groupbox)
        self.g_permission_groupbox = QtWidgets.QGroupBox(self.g_settings_frame)
        self.g_permission_groupbox.setMinimumSize(QtCore.QSize(0, 80))
        self.g_permission_groupbox.setMaximumSize(QtCore.QSize(16777215, 80))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_permission_groupbox.setFont(font)
        self.g_permission_groupbox.setObjectName("g_permission_groupbox")
        self.verticalLayout_12 = QtWidgets.QVBoxLayout(self.g_permission_groupbox)
        self.verticalLayout_12.setSpacing(6)
        self.verticalLayout_12.setObjectName("verticalLayout_12")
        self.g_permhelp_label = QtWidgets.QLabel(self.g_permission_groupbox)
        self.g_permhelp_label.setObjectName("g_permhelp_label")
        self.verticalLayout_12.addWidget(self.g_permhelp_label)
        self.g_permission_checkcombobox = PyCheckCombobox(self.g_permission_groupbox)
        self.g_permission_checkcombobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_permission_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_permission_checkcombobox.setToolTip("")
        self.g_permission_checkcombobox.setWhatsThis("")
        self.g_permission_checkcombobox.setObjectName("g_permission_checkcombobox")
        self.verticalLayout_12.addWidget(self.g_permission_checkcombobox)
        self.verticalLayout_4.addWidget(self.g_permission_groupbox)
        self.g_sortby_groupbox = QtWidgets.QGroupBox(self.g_settings_frame)
        self.g_sortby_groupbox.setMinimumSize(QtCore.QSize(0, 80))
        self.g_sortby_groupbox.setMaximumSize(QtCore.QSize(16777215, 80))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_sortby_groupbox.setFont(font)
        self.g_sortby_groupbox.setObjectName("g_sortby_groupbox")
        self.verticalLayout_14 = QtWidgets.QVBoxLayout(self.g_sortby_groupbox)
        self.verticalLayout_14.setSpacing(6)
        self.verticalLayout_14.setObjectName("verticalLayout_14")
        self.g_sorthelp_label = QtWidgets.QLabel(self.g_sortby_groupbox)
        self.g_sorthelp_label.setObjectName("g_sorthelp_label")
        self.verticalLayout_14.addWidget(self.g_sorthelp_label)
        self.g_sortby_comboBox = QtWidgets.QComboBox(self.g_sortby_groupbox)
        self.g_sortby_comboBox.setObjectName("g_sortby_comboBox")
        self.verticalLayout_14.addWidget(self.g_sortby_comboBox)
        self.verticalLayout_4.addWidget(self.g_sortby_groupbox)
        self.g_columns_groupbox = QtWidgets.QGroupBox(self.g_settings_frame)
        self.g_columns_groupbox.setMinimumSize(QtCore.QSize(0, 150))
        self.g_columns_groupbox.setMaximumSize(QtCore.QSize(16777215, 150))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_columns_groupbox.setFont(font)
        self.g_columns_groupbox.setObjectName("g_columns_groupbox")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_columns_groupbox)
        self.horizontalLayout_2.setContentsMargins(9, 9, 9, 9)
        self.horizontalLayout_2.setSpacing(6)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.frame = QtWidgets.QFrame(self.g_columns_groupbox)
        self.frame.setMaximumSize(QtCore.QSize(350, 16777215))
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_7.setSpacing(6)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.label_4 = QtWidgets.QLabel(self.frame)
        self.label_4.setObjectName("label_4")
        self.verticalLayout_7.addWidget(self.label_4)
        self.g_columns_list = QtWidgets.QListWidget(self.frame)
        self.g_columns_list.setMaximumSize(QtCore.QSize(350, 16777215))
        self.g_columns_list.setObjectName("g_columns_list")
        self.verticalLayout_7.addWidget(self.g_columns_list)
        self.horizontalLayout_2.addWidget(self.frame)
        self.frame_2 = QtWidgets.QFrame(self.g_columns_groupbox)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_8.setSpacing(6)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.label_5 = QtWidgets.QLabel(self.frame_2)
        self.label_5.setObjectName("label_5")
        self.verticalLayout_8.addWidget(self.label_5)
        self.g_field_combobox = QtWidgets.QComboBox(self.frame_2)
        self.g_field_combobox.setMinimumSize(QtCore.QSize(175, 0))
        self.g_field_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_field_combobox.setCursor(QtGui.QCursor(QtCore.Qt.ArrowCursor))
        self.g_field_combobox.setStyleSheet("")
        self.g_field_combobox.setEditable(True)
        self.g_field_combobox.setCurrentText("")
        self.g_field_combobox.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
        self.g_field_combobox.setFrame(True)
        self.g_field_combobox.setObjectName("g_field_combobox")
        self.verticalLayout_8.addWidget(self.g_field_combobox)
        spacerItem4 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_8.addItem(spacerItem4)
        self.horizontalLayout_2.addWidget(self.frame_2)
        self.verticalLayout_4.addWidget(self.g_columns_groupbox)
        spacerItem5 = QtWidgets.QSpacerItem(599, 478, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem5)
        self.verticalLayout_13.addWidget(self.g_settings_frame)
        self.g_main_tab.addTab(self.g_settings_tab, "")
        self.horizontalLayout.addWidget(self.g_main_tab)
        self.verticalLayout_2.addWidget(self.g_reportscontainer_frame)

        self.retranslateUi(ReportsScreen)
        self.g_main_tab.setCurrentIndex(0)
        self.g_field_combobox.setCurrentIndex(-1)
        QtCore.QMetaObject.connectSlotsByName(ReportsScreen)

    def retranslateUi(self, ReportsScreen):
        _translate = QtCore.QCoreApplication.translate
        ReportsScreen.setWindowTitle(_translate("ReportsScreen", "Reports"))
        ReportsScreen.setProperty("screen_toolbutton_tooltip_text", _translate("ReportsScreen", "View Reports"))
        ReportsScreen.setProperty("screen_toolbutton_title_text", _translate("ReportsScreen", "Reports"))
        ReportsScreen.setProperty("screen_toolbutton_stylesheet_text", _translate("ReportsScreen", "\n"
"QToolButton {\n"
"    background: #bb1d48;\n"
"    border: 2px solid #97173A;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #97173A;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: grey;\n"
"    border: 2px solid grey;\n"
"}"))
        ReportsScreen.setProperty("screen_indicator_stylesheet_text", _translate("ReportsScreen", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"            the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #97173A;\n"
"    border: 0;\n"
"}"))
        self.g_main_label.setText(_translate("ReportsScreen", "Reports"))
        self.g_reportcategories_groupbox.setTitle(_translate("ReportsScreen", "Report Categories"))
        self.g_reports_tree.headerItem().setText(0, _translate("ReportsScreen", "Select Report"))
        __sortingEnabled = self.g_reports_tree.isSortingEnabled()
        self.g_reports_tree.setSortingEnabled(False)
        self.g_reports_tree.topLevelItem(0).setText(0, _translate("ReportsScreen", "Category 1"))
        self.g_reports_tree.topLevelItem(0).child(0).setText(0, _translate("ReportsScreen", "Item 1"))
        self.g_reports_tree.topLevelItem(0).child(1).setText(0, _translate("ReportsScreen", "Item 2"))
        self.g_reports_tree.topLevelItem(0).child(2).setText(0, _translate("ReportsScreen", "Item 3"))
        self.g_reports_tree.topLevelItem(1).setText(0, _translate("ReportsScreen", "Category 2"))
        self.g_reports_tree.topLevelItem(1).child(0).setText(0, _translate("ReportsScreen", "Item 1"))
        self.g_reports_tree.topLevelItem(1).child(1).setText(0, _translate("ReportsScreen", "Item 2"))
        self.g_reports_tree.topLevelItem(1).child(2).setText(0, _translate("ReportsScreen", "Item 3"))
        self.g_reports_tree.topLevelItem(2).setText(0, _translate("ReportsScreen", "Category 3"))
        self.g_reports_tree.topLevelItem(2).child(0).setText(0, _translate("ReportsScreen", "Item 1"))
        self.g_reports_tree.topLevelItem(2).child(1).setText(0, _translate("ReportsScreen", "Item 2"))
        self.g_reports_tree.topLevelItem(2).child(2).setText(0, _translate("ReportsScreen", "Item 3"))
        self.g_reports_tree.setSortingEnabled(__sortingEnabled)
        self.g_generate_button.setText(_translate("ReportsScreen", " Generate\n"
"   Report"))
        self.g_main_tab.setTabText(self.g_main_tab.indexOf(self.g_generate_tab), _translate("ReportsScreen", "Generate"))
        self.g_quickfilters_label.setText(_translate("ReportsScreen", "Quick Filters"))
        self.g_quickfiltertext_label.setText(_translate("ReportsScreen", "Current Selected Report"))
        self.g_previouspage_label.setToolTip(_translate("ReportsScreen", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("ReportsScreen", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("ReportsScreen", "999"))
        self.g_slash_label.setText(_translate("ReportsScreen", "/"))
        self.g_totalpages_label.setToolTip(_translate("ReportsScreen", "Total Pages"))
        self.g_totalpages_label.setText(_translate("ReportsScreen", "999"))
        self.g_schedule_table.setSortingEnabled(True)
        item = self.g_schedule_table.verticalHeaderItem(0)
        item.setText(_translate("ReportsScreen", "New Row"))
        item = self.g_schedule_table.horizontalHeaderItem(0)
        item.setText(_translate("ReportsScreen", "Report Name"))
        item = self.g_schedule_table.horizontalHeaderItem(1)
        item.setText(_translate("ReportsScreen", "Status"))
        item = self.g_schedule_table.horizontalHeaderItem(2)
        item.setText(_translate("ReportsScreen", "Repeat"))
        item = self.g_schedule_table.horizontalHeaderItem(3)
        item.setText(_translate("ReportsScreen", "Days"))
        item = self.g_schedule_table.horizontalHeaderItem(4)
        item.setText(_translate("ReportsScreen", "Time"))
        item = self.g_schedule_table.horizontalHeaderItem(5)
        item.setText(_translate("ReportsScreen", "Report Options"))
        __sortingEnabled = self.g_schedule_table.isSortingEnabled()
        self.g_schedule_table.setSortingEnabled(False)
        item = self.g_schedule_table.item(0, 0)
        item.setText(_translate("ReportsScreen", "Report Name"))
        item = self.g_schedule_table.item(0, 2)
        item.setText(_translate("ReportsScreen", "Weekly"))
        item = self.g_schedule_table.item(0, 3)
        item.setText(_translate("ReportsScreen", "Monday, Wednesday"))
        item = self.g_schedule_table.item(0, 4)
        item.setText(_translate("ReportsScreen", "1:00 pm"))
        item = self.g_schedule_table.item(0, 5)
        item.setText(_translate("ReportsScreen", "Type: Cat, Dog"))
        self.g_schedule_table.setSortingEnabled(__sortingEnabled)
        self.g_totalresults_label.setText(_translate("ReportsScreen", "Loading results."))
        self.g_delete_button.setText(_translate("ReportsScreen", " Delete"))
        self.g_disable_button.setText(_translate("ReportsScreen", "Disable"))
        self.g_enable_button.setText(_translate("ReportsScreen", "Enable"))
        self.g_scheduling_button.setText(_translate("ReportsScreen", "Schedule\n"
"Report"))
        self.g_manage_button.setText(_translate("ReportsScreen", "Manage"))
        self.g_main_tab.setTabText(self.g_main_tab.indexOf(self.g_saved_tab), _translate("ReportsScreen", "Scheduled"))
        self.g_application_groupbox.setTitle(_translate("ReportsScreen", "Application"))
        self.g_apphelp_label.setText(_translate("ReportsScreen", "Select the Applications that this Report is visible in."))
        self.g_permission_groupbox.setTitle(_translate("ReportsScreen", "Permissions"))
        self.g_permhelp_label.setText(_translate("ReportsScreen", "Select the Roles that this Report is visible to."))
        self.g_sortby_groupbox.setTitle(_translate("ReportsScreen", "Sort By"))
        self.g_sorthelp_label.setText(_translate("ReportsScreen", "Select Column to sort by."))
        self.g_columns_groupbox.setTitle(_translate("ReportsScreen", "Columns"))
        self.label_4.setText(_translate("ReportsScreen", "Columns"))
        self.label_5.setText(_translate("ReportsScreen", "Fields"))
        self.g_main_tab.setTabText(self.g_main_tab.indexOf(self.g_settings_tab), _translate("ReportsScreen", "Settings"))
from po_lib.checkcombobox import PyCheckCombobox
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ReportsScreen = QtWidgets.QDialog()
    ui = Ui_ReportsScreen()
    ui.setupUi(ReportsScreen)
    ReportsScreen.show()
    sys.exit(app.exec_())
