# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managefrequentbuyer_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ManageFrequentBuyer(object):
    def setupUi(self, ManageFrequentBuyer):
        ManageFrequentBuyer.setObjectName("ManageFrequentBuyer")
        ManageFrequentBuyer.resize(800, 508)
        ManageFrequentBuyer.setMinimumSize(QtCore.QSize(0, 0))
        ManageFrequentBuyer.setMaximumSize(QtCore.QSize(16777215, 16777215))
        ManageFrequentBuyer.setStyleSheet("#ManageFrequentBuyer {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QLabel {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font: 17px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/products/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/products/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/products/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"QGroupBox {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"\n"
"\n"
"#g_remove_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_remove_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_close_button, #__g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_close_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"")
        self.verticalLayout = QtWidgets.QVBoxLayout(ManageFrequentBuyer)
        self.verticalLayout.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_top_frame = QtWidgets.QFrame(ManageFrequentBuyer)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setMaximumSize(QtCore.QSize(16777215, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_13.setSpacing(0)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_13.addWidget(self.g_progress_frame)
        self.verticalLayout.addWidget(self.g_top_frame)
        self.g_main_label = QtWidgets.QLabel(ManageFrequentBuyer)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setStyleSheet("")
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 5, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout.addItem(spacerItem)
        self.frame = QtWidgets.QFrame(ManageFrequentBuyer)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_products_table = QtWidgets.QTableWidget(self.frame)
        self.g_products_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_products_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_products_table.setAlternatingRowColors(True)
        self.g_products_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_products_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_products_table.setShowGrid(False)
        self.g_products_table.setObjectName("g_products_table")
        self.g_products_table.setColumnCount(6)
        self.g_products_table.setRowCount(6)
        item = QtWidgets.QTableWidgetItem()
        self.g_products_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_products_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_products_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_products_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_products_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_products_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_products_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_products_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_products_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_products_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_products_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_products_table.setHorizontalHeaderItem(5, item)
        self.g_products_table.horizontalHeader().setStretchLastSection(True)
        self.horizontalLayout.addWidget(self.g_products_table)
        self.verticalLayout.addWidget(self.frame)
        self.__g_controlbuttons_frame = QtWidgets.QFrame(ManageFrequentBuyer)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.__g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.__g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.__g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_controlbuttons_frame.setObjectName("__g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.__g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(6)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem1 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem1)
        self.g_remove_button = QtWidgets.QPushButton(self.__g_controlbuttons_frame)
        self.g_remove_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_remove_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_remove_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/pet_tracker/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_remove_button.setIcon(icon)
        self.g_remove_button.setIconSize(QtCore.QSize(24, 24))
        self.g_remove_button.setObjectName("g_remove_button")
        self.horizontalLayout_11.addWidget(self.g_remove_button)
        self.g_add_button = QtWidgets.QPushButton(self.__g_controlbuttons_frame)
        self.g_add_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_add_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_add_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/pet_tracker/add"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_add_button.setIcon(icon1)
        self.g_add_button.setIconSize(QtCore.QSize(24, 24))
        self.g_add_button.setObjectName("g_add_button")
        self.horizontalLayout_11.addWidget(self.g_add_button)
        self.g_manage_button = QtWidgets.QPushButton(self.__g_controlbuttons_frame)
        self.g_manage_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_manage_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/entity_dialog/edit"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_manage_button.setIcon(icon2)
        self.g_manage_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manage_button.setObjectName("g_manage_button")
        self.horizontalLayout_11.addWidget(self.g_manage_button)
        self.g_close_button = QtWidgets.QPushButton(self.__g_controlbuttons_frame)
        self.g_close_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_close_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_close_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_close_button.setIcon(icon3)
        self.g_close_button.setIconSize(QtCore.QSize(24, 24))
        self.g_close_button.setObjectName("g_close_button")
        self.horizontalLayout_11.addWidget(self.g_close_button)
        self.verticalLayout.addWidget(self.__g_controlbuttons_frame)

        self.retranslateUi(ManageFrequentBuyer)
        QtCore.QMetaObject.connectSlotsByName(ManageFrequentBuyer)

    def retranslateUi(self, ManageFrequentBuyer):
        _translate = QtCore.QCoreApplication.translate
        ManageFrequentBuyer.setWindowTitle(_translate("ManageFrequentBuyer", "Manage Frequent Buyer"))
        self.g_main_label.setText(_translate("ManageFrequentBuyer", "Manage Frequent Buyer"))
        self.g_products_table.setSortingEnabled(True)
        item = self.g_products_table.verticalHeaderItem(0)
        item.setText(_translate("ManageFrequentBuyer", "New Row"))
        item = self.g_products_table.verticalHeaderItem(1)
        item.setText(_translate("ManageFrequentBuyer", "New Row"))
        item = self.g_products_table.verticalHeaderItem(2)
        item.setText(_translate("ManageFrequentBuyer", "New Row"))
        item = self.g_products_table.verticalHeaderItem(3)
        item.setText(_translate("ManageFrequentBuyer", "New Row"))
        item = self.g_products_table.verticalHeaderItem(4)
        item.setText(_translate("ManageFrequentBuyer", "New Row"))
        item = self.g_products_table.verticalHeaderItem(5)
        item.setText(_translate("ManageFrequentBuyer", "New Row"))
        item = self.g_products_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageFrequentBuyer", "Invoice"))
        item = self.g_products_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageFrequentBuyer", "Invoice Type"))
        item = self.g_products_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageFrequentBuyer", "SKU"))
        item = self.g_products_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageFrequentBuyer", "Description"))
        item = self.g_products_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageFrequentBuyer", "Net"))
        item = self.g_products_table.horizontalHeaderItem(5)
        item.setText(_translate("ManageFrequentBuyer", "Note"))
        self.g_remove_button.setText(_translate("ManageFrequentBuyer", " Delete"))
        self.g_add_button.setText(_translate("ManageFrequentBuyer", " Add"))
        self.g_manage_button.setText(_translate("ManageFrequentBuyer", "Manage"))
        self.g_close_button.setText(_translate("ManageFrequentBuyer", "Close"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageFrequentBuyer = QtWidgets.QDialog()
    ui = Ui_ManageFrequentBuyer()
    ui.setupUi(ManageFrequentBuyer)
    ManageFrequentBuyer.show()
    sys.exit(app.exec_())
