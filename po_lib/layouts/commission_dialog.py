# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'commission_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_CommissionDialog(object):
    def setupUi(self, CommissionDialog):
        CommissionDialog.setObjectName("CommissionDialog")
        CommissionDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        CommissionDialog.resize(767, 542)
        CommissionDialog.setMinimumSize(QtCore.QSize(0, 450))
        CommissionDialog.setMaximumSize(QtCore.QSize(16777215, 16777215))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/rewards_screen/employee_dashboard"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        CommissionDialog.setWindowIcon(icon)
        CommissionDialog.setStyleSheet("\n"
"#CommissionDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#SelectTray {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_selectform_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border:1px solid #073c83;\n"
"    border-radius:10px;\n"
"    background-color:#DDE4E9;\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QScrollArea {\n"
"    background-color: #f4f4f4;\n"
"    border-radius:5px;\n"
"    border:1px solid #073c83;\n"
"    padding:4px;\n"
"}\n"
"\n"
"QScrollBar:horizontal {\n"
"    height: 0px;\n"
"}\n"
"\n"
"#scrollAreaWidgetContents {\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"#g_employee_combobox_1::drop-down,\n"
"#g_employee_combobox_2::drop-down,\n"
"#g_employee_combobox_3::drop-down,\n"
"#g_employee_combobox_4::drop-down,\n"
"#g_employee_combobox_5::drop-down {    \n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"}\n"
"\n"
"#g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button,\n"
"#g_deletecomm_button,\n"
"#g_deletetip_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed,\n"
"#g_deletecomm_button:pressed,\n"
"#g_deletetip_button:pressed {\n"
"    background: #c80319;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_commission_scrollarea QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_tipslabel_frame2 QLabel {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"#g_comtipslabel_frame QLabel {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"#g_currentpage_lineedit, #g_totalpages_label, #g_slash_label {\n"
"    font-size: 11px;\n"
"}")
        CommissionDialog.setModal(True)
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(CommissionDialog)
        self.verticalLayout_4.setContentsMargins(0, 0, 0, 9)
        self.verticalLayout_4.setSpacing(0)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_9.setSpacing(0)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.g_progress_frame = QtWidgets.QFrame(CommissionDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_9.addWidget(self.g_progress_frame)
        self.g_anchoring_frame = QtWidgets.QFrame(CommissionDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_anchoring_frame.sizePolicy().hasHeightForWidth())
        self.g_anchoring_frame.setSizePolicy(sizePolicy)
        self.g_anchoring_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_anchoring_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_anchoring_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_anchoring_frame.setObjectName("g_anchoring_frame")
        self.horizontalLayout_9.addWidget(self.g_anchoring_frame)
        self.verticalLayout_4.addLayout(self.horizontalLayout_9)
        self.g_main_frame = QtWidgets.QFrame(CommissionDialog)
        self.g_main_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_main_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_main_frame.setObjectName("g_main_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_main_frame)
        self.verticalLayout_2.setContentsMargins(-1, 8, -1, 0)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_selectform_frame = QtWidgets.QFrame(self.g_main_frame)
        self.g_selectform_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_selectform_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_selectform_frame.setObjectName("g_selectform_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_selectform_frame)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_titlepagenav_frame = QtWidgets.QFrame(self.g_selectform_frame)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_main_label = QtWidgets.QLabel(self.g_titlepagenav_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setStyleSheet("")
        self.g_main_label.setObjectName("g_main_label")
        self.horizontalLayout_3.addWidget(self.g_main_label)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout_2 = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout_2.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout_2.setSpacing(6)
        self.g_pagenav_hlayout_2.setObjectName("g_pagenav_hlayout_2")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout_2.addItem(spacerItem)
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/rewards_screen/previous_arrow"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout_2.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/rewards_screen/next_arrow"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout_2.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagenav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(31, 0))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(31, 16777215))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout_2.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_slash_label.setObjectName("g_slash_label")
        self.g_pagenav_hlayout_2.addWidget(self.g_slash_label)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout_2.addWidget(self.g_totalpages_label)
        self.horizontalLayout_3.addWidget(self.g_pagenav_frame)
        self.verticalLayout.addWidget(self.g_titlepagenav_frame)
        self.g_tipslabel_frame = QtWidgets.QFrame(self.g_selectform_frame)
        self.g_tipslabel_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_tipslabel_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_tipslabel_frame.setObjectName("g_tipslabel_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_tipslabel_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(6)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_8.addItem(spacerItem1)
        self.g_tipslabel_frame2 = QtWidgets.QFrame(self.g_tipslabel_frame)
        self.g_tipslabel_frame2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_tipslabel_frame2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_tipslabel_frame2.setObjectName("g_tipslabel_frame2")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.g_tipslabel_frame2)
        self.horizontalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_12.addItem(spacerItem2)
        self.g_entertip_label = QtWidgets.QLabel(self.g_tipslabel_frame2)
        self.g_entertip_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_entertip_label.setFont(font)
        self.g_entertip_label.setStyleSheet("")
        self.g_entertip_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entertip_label.setObjectName("g_entertip_label")
        self.horizontalLayout_12.addWidget(self.g_entertip_label)
        self.g_entertip_lineedit = QtWidgets.QLineEdit(self.g_tipslabel_frame2)
        self.g_entertip_lineedit.setMinimumSize(QtCore.QSize(100, 0))
        self.g_entertip_lineedit.setMaximumSize(QtCore.QSize(100, 16777215))
        self.g_entertip_lineedit.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entertip_lineedit.setReadOnly(False)
        self.g_entertip_lineedit.setObjectName("g_entertip_lineedit")
        self.horizontalLayout_12.addWidget(self.g_entertip_lineedit)
        self.g_entertip_button = QtWidgets.QPushButton(self.g_tipslabel_frame2)
        self.g_entertip_button.setEnabled(True)
        self.g_entertip_button.setMinimumSize(QtCore.QSize(50, 0))
        self.g_entertip_button.setMaximumSize(QtCore.QSize(50, 16777215))
        self.g_entertip_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_entertip_button.setStyleSheet("border:0px;\n"
"background:none;")
        self.g_entertip_button.setText("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/commission_modaldialog/6_dot_grid"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_entertip_button.setIcon(icon1)
        self.g_entertip_button.setIconSize(QtCore.QSize(18, 18))
        self.g_entertip_button.setObjectName("g_entertip_button")
        self.horizontalLayout_12.addWidget(self.g_entertip_button)
        self.horizontalLayout_8.addWidget(self.g_tipslabel_frame2)
        self.verticalLayout.addWidget(self.g_tipslabel_frame)
        self.g_commission_scrollarea = QtWidgets.QScrollArea(self.g_selectform_frame)
        self.g_commission_scrollarea.setMinimumSize(QtCore.QSize(0, 0))
        self.g_commission_scrollarea.setStyleSheet("")
        self.g_commission_scrollarea.setWidgetResizable(True)
        self.g_commission_scrollarea.setObjectName("g_commission_scrollarea")
        self.g_newpet_widget = QtWidgets.QWidget()
        self.g_newpet_widget.setGeometry(QtCore.QRect(0, 0, 719, 325))
        self.g_newpet_widget.setStyleSheet("")
        self.g_newpet_widget.setObjectName("g_newpet_widget")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_newpet_widget)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_commission_frame_1 = QtWidgets.QFrame(self.g_newpet_widget)
        self.g_commission_frame_1.setMinimumSize(QtCore.QSize(0, 50))
        self.g_commission_frame_1.setMaximumSize(QtCore.QSize(16777215, 200))
        self.g_commission_frame_1.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_commission_frame_1.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_commission_frame_1.setObjectName("g_commission_frame_1")
        self.horizontalLayout_22 = QtWidgets.QHBoxLayout(self.g_commission_frame_1)
        self.horizontalLayout_22.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_22.setObjectName("horizontalLayout_22")
        self.g_employee_label_1 = QtWidgets.QLabel(self.g_commission_frame_1)
        self.g_employee_label_1.setMinimumSize(QtCore.QSize(70, 0))
        self.g_employee_label_1.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_employee_label_1.setFont(font)
        self.g_employee_label_1.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_employee_label_1.setObjectName("g_employee_label_1")
        self.horizontalLayout_22.addWidget(self.g_employee_label_1)
        self.g_employee_combobox_1 = QtWidgets.QComboBox(self.g_commission_frame_1)
        self.g_employee_combobox_1.setMinimumSize(QtCore.QSize(0, 31))
        self.g_employee_combobox_1.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_employee_combobox_1.setObjectName("g_employee_combobox_1")
        self.horizontalLayout_22.addWidget(self.g_employee_combobox_1)
        self.g_comm_label_1 = QtWidgets.QLabel(self.g_commission_frame_1)
        self.g_comm_label_1.setMinimumSize(QtCore.QSize(0, 0))
        self.g_comm_label_1.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_comm_label_1.setFont(font)
        self.g_comm_label_1.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_comm_label_1.setObjectName("g_comm_label_1")
        self.horizontalLayout_22.addWidget(self.g_comm_label_1)
        self.g_cost_lineedit_1 = QtWidgets.QLineEdit(self.g_commission_frame_1)
        self.g_cost_lineedit_1.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_cost_lineedit_1.sizePolicy().hasHeightForWidth())
        self.g_cost_lineedit_1.setSizePolicy(sizePolicy)
        self.g_cost_lineedit_1.setMinimumSize(QtCore.QSize(75, 31))
        self.g_cost_lineedit_1.setMaximumSize(QtCore.QSize(75, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_cost_lineedit_1.setFont(font)
        self.g_cost_lineedit_1.setInputMask("")
        self.g_cost_lineedit_1.setText("")
        self.g_cost_lineedit_1.setMaxLength(8)
        self.g_cost_lineedit_1.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cost_lineedit_1.setReadOnly(False)
        self.g_cost_lineedit_1.setPlaceholderText("")
        self.g_cost_lineedit_1.setObjectName("g_cost_lineedit_1")
        self.horizontalLayout_22.addWidget(self.g_cost_lineedit_1)
        self.g_perc_label_1 = QtWidgets.QLabel(self.g_commission_frame_1)
        self.g_perc_label_1.setMinimumSize(QtCore.QSize(20, 0))
        self.g_perc_label_1.setMaximumSize(QtCore.QSize(20, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_perc_label_1.setFont(font)
        self.g_perc_label_1.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_perc_label_1.setObjectName("g_perc_label_1")
        self.horizontalLayout_22.addWidget(self.g_perc_label_1)
        self.g_popup_button_1 = QtWidgets.QPushButton(self.g_commission_frame_1)
        self.g_popup_button_1.setEnabled(True)
        self.g_popup_button_1.setMinimumSize(QtCore.QSize(25, 0))
        self.g_popup_button_1.setMaximumSize(QtCore.QSize(25, 16777215))
        self.g_popup_button_1.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_popup_button_1.setStyleSheet("border:0px;\n"
"background:none;")
        self.g_popup_button_1.setText("")
        self.g_popup_button_1.setIcon(icon1)
        self.g_popup_button_1.setIconSize(QtCore.QSize(18, 18))
        self.g_popup_button_1.setObjectName("g_popup_button_1")
        self.horizontalLayout_22.addWidget(self.g_popup_button_1)
        spacerItem3 = QtWidgets.QSpacerItem(30, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_22.addItem(spacerItem3)
        self.g_adddelcomm_frame_1 = QtWidgets.QFrame(self.g_commission_frame_1)
        self.g_adddelcomm_frame_1.setMinimumSize(QtCore.QSize(80, 0))
        self.g_adddelcomm_frame_1.setMaximumSize(QtCore.QSize(80, 16777215))
        self.g_adddelcomm_frame_1.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_adddelcomm_frame_1.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_adddelcomm_frame_1.setObjectName("g_adddelcomm_frame_1")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.g_adddelcomm_frame_1)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.g_delcom_button_1 = QtWidgets.QPushButton(self.g_adddelcomm_frame_1)
        self.g_delcom_button_1.setMinimumSize(QtCore.QSize(22, 0))
        self.g_delcom_button_1.setMaximumSize(QtCore.QSize(22, 16777215))
        self.g_delcom_button_1.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_delcom_button_1.setStyleSheet("border:none;\n"
"background-color:none;")
        self.g_delcom_button_1.setText("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/commission_modaldialog/xred"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_delcom_button_1.setIcon(icon2)
        self.g_delcom_button_1.setIconSize(QtCore.QSize(24, 24))
        self.g_delcom_button_1.setObjectName("g_delcom_button_1")
        self.horizontalLayout_10.addWidget(self.g_delcom_button_1)
        spacerItem4 = QtWidgets.QSpacerItem(47, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_10.addItem(spacerItem4)
        self.g_addcom_button_1 = QtWidgets.QPushButton(self.g_adddelcomm_frame_1)
        self.g_addcom_button_1.setMinimumSize(QtCore.QSize(22, 0))
        self.g_addcom_button_1.setMaximumSize(QtCore.QSize(22, 16777215))
        self.g_addcom_button_1.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_addcom_button_1.setStyleSheet("border:none;\n"
"background-color:none;")
        self.g_addcom_button_1.setText("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/commission_modaldialog/add"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_addcom_button_1.setIcon(icon3)
        self.g_addcom_button_1.setIconSize(QtCore.QSize(24, 24))
        self.g_addcom_button_1.setObjectName("g_addcom_button_1")
        self.horizontalLayout_10.addWidget(self.g_addcom_button_1)
        self.horizontalLayout_22.addWidget(self.g_adddelcomm_frame_1)
        self.verticalLayout_3.addWidget(self.g_commission_frame_1)
        self.g_commission_frame_2 = QtWidgets.QFrame(self.g_newpet_widget)
        self.g_commission_frame_2.setMinimumSize(QtCore.QSize(0, 50))
        self.g_commission_frame_2.setMaximumSize(QtCore.QSize(16777215, 200))
        self.g_commission_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_commission_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_commission_frame_2.setObjectName("g_commission_frame_2")
        self.horizontalLayout_23 = QtWidgets.QHBoxLayout(self.g_commission_frame_2)
        self.horizontalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_23.setObjectName("horizontalLayout_23")
        self.g_employee_label_2 = QtWidgets.QLabel(self.g_commission_frame_2)
        self.g_employee_label_2.setMinimumSize(QtCore.QSize(70, 0))
        self.g_employee_label_2.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_employee_label_2.setFont(font)
        self.g_employee_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_employee_label_2.setObjectName("g_employee_label_2")
        self.horizontalLayout_23.addWidget(self.g_employee_label_2)
        self.g_employee_combobox_2 = QtWidgets.QComboBox(self.g_commission_frame_2)
        self.g_employee_combobox_2.setMinimumSize(QtCore.QSize(0, 31))
        self.g_employee_combobox_2.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_employee_combobox_2.setObjectName("g_employee_combobox_2")
        self.horizontalLayout_23.addWidget(self.g_employee_combobox_2)
        self.g_comm_label_2 = QtWidgets.QLabel(self.g_commission_frame_2)
        self.g_comm_label_2.setMinimumSize(QtCore.QSize(0, 0))
        self.g_comm_label_2.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_comm_label_2.setFont(font)
        self.g_comm_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_comm_label_2.setObjectName("g_comm_label_2")
        self.horizontalLayout_23.addWidget(self.g_comm_label_2)
        self.g_cost_lineedit_2 = QtWidgets.QLineEdit(self.g_commission_frame_2)
        self.g_cost_lineedit_2.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_cost_lineedit_2.sizePolicy().hasHeightForWidth())
        self.g_cost_lineedit_2.setSizePolicy(sizePolicy)
        self.g_cost_lineedit_2.setMinimumSize(QtCore.QSize(75, 31))
        self.g_cost_lineedit_2.setMaximumSize(QtCore.QSize(75, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_cost_lineedit_2.setFont(font)
        self.g_cost_lineedit_2.setText("")
        self.g_cost_lineedit_2.setMaxLength(8)
        self.g_cost_lineedit_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cost_lineedit_2.setReadOnly(False)
        self.g_cost_lineedit_2.setPlaceholderText("")
        self.g_cost_lineedit_2.setObjectName("g_cost_lineedit_2")
        self.horizontalLayout_23.addWidget(self.g_cost_lineedit_2)
        self.g_perc_label_2 = QtWidgets.QLabel(self.g_commission_frame_2)
        self.g_perc_label_2.setMinimumSize(QtCore.QSize(20, 0))
        self.g_perc_label_2.setMaximumSize(QtCore.QSize(20, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_perc_label_2.setFont(font)
        self.g_perc_label_2.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_perc_label_2.setObjectName("g_perc_label_2")
        self.horizontalLayout_23.addWidget(self.g_perc_label_2)
        self.g_popup_button_2 = QtWidgets.QPushButton(self.g_commission_frame_2)
        self.g_popup_button_2.setEnabled(True)
        self.g_popup_button_2.setMinimumSize(QtCore.QSize(25, 0))
        self.g_popup_button_2.setMaximumSize(QtCore.QSize(25, 16777215))
        self.g_popup_button_2.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_popup_button_2.setStyleSheet("border:0px;\n"
"background:none;")
        self.g_popup_button_2.setText("")
        self.g_popup_button_2.setIcon(icon1)
        self.g_popup_button_2.setIconSize(QtCore.QSize(18, 18))
        self.g_popup_button_2.setObjectName("g_popup_button_2")
        self.horizontalLayout_23.addWidget(self.g_popup_button_2)
        spacerItem5 = QtWidgets.QSpacerItem(30, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_23.addItem(spacerItem5)
        self.g_adddelcomm_frame_2 = QtWidgets.QFrame(self.g_commission_frame_2)
        self.g_adddelcomm_frame_2.setMinimumSize(QtCore.QSize(80, 0))
        self.g_adddelcomm_frame_2.setMaximumSize(QtCore.QSize(80, 16777215))
        self.g_adddelcomm_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_adddelcomm_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_adddelcomm_frame_2.setObjectName("g_adddelcomm_frame_2")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_adddelcomm_frame_2)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_delcom_button_2 = QtWidgets.QPushButton(self.g_adddelcomm_frame_2)
        self.g_delcom_button_2.setMinimumSize(QtCore.QSize(22, 0))
        self.g_delcom_button_2.setMaximumSize(QtCore.QSize(22, 16777215))
        self.g_delcom_button_2.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_delcom_button_2.setStyleSheet("border:none;\n"
"background-color:none;")
        self.g_delcom_button_2.setText("")
        self.g_delcom_button_2.setIcon(icon2)
        self.g_delcom_button_2.setIconSize(QtCore.QSize(24, 24))
        self.g_delcom_button_2.setObjectName("g_delcom_button_2")
        self.horizontalLayout_4.addWidget(self.g_delcom_button_2)
        spacerItem6 = QtWidgets.QSpacerItem(47, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem6)
        self.g_addcom_button_2 = QtWidgets.QPushButton(self.g_adddelcomm_frame_2)
        self.g_addcom_button_2.setMinimumSize(QtCore.QSize(22, 0))
        self.g_addcom_button_2.setMaximumSize(QtCore.QSize(22, 16777215))
        self.g_addcom_button_2.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_addcom_button_2.setStyleSheet("border:none;\n"
"background-color:none;")
        self.g_addcom_button_2.setText("")
        self.g_addcom_button_2.setIcon(icon3)
        self.g_addcom_button_2.setIconSize(QtCore.QSize(24, 24))
        self.g_addcom_button_2.setObjectName("g_addcom_button_2")
        self.horizontalLayout_4.addWidget(self.g_addcom_button_2)
        self.horizontalLayout_23.addWidget(self.g_adddelcomm_frame_2)
        self.verticalLayout_3.addWidget(self.g_commission_frame_2)
        self.g_commission_frame_3 = QtWidgets.QFrame(self.g_newpet_widget)
        self.g_commission_frame_3.setMinimumSize(QtCore.QSize(0, 50))
        self.g_commission_frame_3.setMaximumSize(QtCore.QSize(16777215, 200))
        self.g_commission_frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_commission_frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_commission_frame_3.setObjectName("g_commission_frame_3")
        self.horizontalLayout_24 = QtWidgets.QHBoxLayout(self.g_commission_frame_3)
        self.horizontalLayout_24.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_24.setObjectName("horizontalLayout_24")
        self.g_employee_label_3 = QtWidgets.QLabel(self.g_commission_frame_3)
        self.g_employee_label_3.setMinimumSize(QtCore.QSize(70, 0))
        self.g_employee_label_3.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_employee_label_3.setFont(font)
        self.g_employee_label_3.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_employee_label_3.setObjectName("g_employee_label_3")
        self.horizontalLayout_24.addWidget(self.g_employee_label_3)
        self.g_employee_combobox_3 = QtWidgets.QComboBox(self.g_commission_frame_3)
        self.g_employee_combobox_3.setMinimumSize(QtCore.QSize(0, 31))
        self.g_employee_combobox_3.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_employee_combobox_3.setObjectName("g_employee_combobox_3")
        self.horizontalLayout_24.addWidget(self.g_employee_combobox_3)
        self.g_comm_label_3 = QtWidgets.QLabel(self.g_commission_frame_3)
        self.g_comm_label_3.setMinimumSize(QtCore.QSize(0, 0))
        self.g_comm_label_3.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_comm_label_3.setFont(font)
        self.g_comm_label_3.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_comm_label_3.setObjectName("g_comm_label_3")
        self.horizontalLayout_24.addWidget(self.g_comm_label_3)
        self.g_cost_lineedit_3 = QtWidgets.QLineEdit(self.g_commission_frame_3)
        self.g_cost_lineedit_3.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_cost_lineedit_3.sizePolicy().hasHeightForWidth())
        self.g_cost_lineedit_3.setSizePolicy(sizePolicy)
        self.g_cost_lineedit_3.setMinimumSize(QtCore.QSize(75, 31))
        self.g_cost_lineedit_3.setMaximumSize(QtCore.QSize(75, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_cost_lineedit_3.setFont(font)
        self.g_cost_lineedit_3.setText("")
        self.g_cost_lineedit_3.setMaxLength(8)
        self.g_cost_lineedit_3.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cost_lineedit_3.setReadOnly(False)
        self.g_cost_lineedit_3.setPlaceholderText("")
        self.g_cost_lineedit_3.setObjectName("g_cost_lineedit_3")
        self.horizontalLayout_24.addWidget(self.g_cost_lineedit_3)
        self.g_perc_label_3 = QtWidgets.QLabel(self.g_commission_frame_3)
        self.g_perc_label_3.setMinimumSize(QtCore.QSize(20, 0))
        self.g_perc_label_3.setMaximumSize(QtCore.QSize(20, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_perc_label_3.setFont(font)
        self.g_perc_label_3.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_perc_label_3.setObjectName("g_perc_label_3")
        self.horizontalLayout_24.addWidget(self.g_perc_label_3)
        self.g_popup_button_3 = QtWidgets.QPushButton(self.g_commission_frame_3)
        self.g_popup_button_3.setEnabled(True)
        self.g_popup_button_3.setMinimumSize(QtCore.QSize(25, 0))
        self.g_popup_button_3.setMaximumSize(QtCore.QSize(25, 16777215))
        self.g_popup_button_3.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_popup_button_3.setStyleSheet("border:0px;\n"
"background:none;")
        self.g_popup_button_3.setText("")
        self.g_popup_button_3.setIcon(icon1)
        self.g_popup_button_3.setIconSize(QtCore.QSize(18, 18))
        self.g_popup_button_3.setObjectName("g_popup_button_3")
        self.horizontalLayout_24.addWidget(self.g_popup_button_3)
        spacerItem7 = QtWidgets.QSpacerItem(30, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_24.addItem(spacerItem7)
        self.g_adddelcomm_frame_3 = QtWidgets.QFrame(self.g_commission_frame_3)
        self.g_adddelcomm_frame_3.setMinimumSize(QtCore.QSize(80, 0))
        self.g_adddelcomm_frame_3.setMaximumSize(QtCore.QSize(80, 16777215))
        self.g_adddelcomm_frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_adddelcomm_frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_adddelcomm_frame_3.setObjectName("g_adddelcomm_frame_3")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_adddelcomm_frame_3)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_delcom_button_3 = QtWidgets.QPushButton(self.g_adddelcomm_frame_3)
        self.g_delcom_button_3.setMinimumSize(QtCore.QSize(22, 0))
        self.g_delcom_button_3.setMaximumSize(QtCore.QSize(22, 16777215))
        self.g_delcom_button_3.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_delcom_button_3.setStyleSheet("border:none;\n"
"background-color:none;")
        self.g_delcom_button_3.setText("")
        self.g_delcom_button_3.setIcon(icon2)
        self.g_delcom_button_3.setIconSize(QtCore.QSize(24, 24))
        self.g_delcom_button_3.setObjectName("g_delcom_button_3")
        self.horizontalLayout_5.addWidget(self.g_delcom_button_3)
        spacerItem8 = QtWidgets.QSpacerItem(47, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem8)
        self.g_addcom_button_3 = QtWidgets.QPushButton(self.g_adddelcomm_frame_3)
        self.g_addcom_button_3.setMinimumSize(QtCore.QSize(22, 0))
        self.g_addcom_button_3.setMaximumSize(QtCore.QSize(22, 16777215))
        self.g_addcom_button_3.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_addcom_button_3.setStyleSheet("border:none;\n"
"background-color:none;")
        self.g_addcom_button_3.setText("")
        self.g_addcom_button_3.setIcon(icon3)
        self.g_addcom_button_3.setIconSize(QtCore.QSize(24, 24))
        self.g_addcom_button_3.setObjectName("g_addcom_button_3")
        self.horizontalLayout_5.addWidget(self.g_addcom_button_3)
        self.horizontalLayout_24.addWidget(self.g_adddelcomm_frame_3)
        self.verticalLayout_3.addWidget(self.g_commission_frame_3)
        self.g_commission_frame_4 = QtWidgets.QFrame(self.g_newpet_widget)
        self.g_commission_frame_4.setMinimumSize(QtCore.QSize(0, 50))
        self.g_commission_frame_4.setMaximumSize(QtCore.QSize(16777215, 200))
        self.g_commission_frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_commission_frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_commission_frame_4.setObjectName("g_commission_frame_4")
        self.horizontalLayout_25 = QtWidgets.QHBoxLayout(self.g_commission_frame_4)
        self.horizontalLayout_25.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_25.setObjectName("horizontalLayout_25")
        self.g_employee_label_4 = QtWidgets.QLabel(self.g_commission_frame_4)
        self.g_employee_label_4.setMinimumSize(QtCore.QSize(70, 0))
        self.g_employee_label_4.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_employee_label_4.setFont(font)
        self.g_employee_label_4.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_employee_label_4.setObjectName("g_employee_label_4")
        self.horizontalLayout_25.addWidget(self.g_employee_label_4)
        self.g_employee_combobox_4 = QtWidgets.QComboBox(self.g_commission_frame_4)
        self.g_employee_combobox_4.setMinimumSize(QtCore.QSize(0, 31))
        self.g_employee_combobox_4.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_employee_combobox_4.setObjectName("g_employee_combobox_4")
        self.horizontalLayout_25.addWidget(self.g_employee_combobox_4)
        self.g_comm_label_4 = QtWidgets.QLabel(self.g_commission_frame_4)
        self.g_comm_label_4.setMinimumSize(QtCore.QSize(0, 0))
        self.g_comm_label_4.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_comm_label_4.setFont(font)
        self.g_comm_label_4.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_comm_label_4.setObjectName("g_comm_label_4")
        self.horizontalLayout_25.addWidget(self.g_comm_label_4)
        self.g_cost_lineedit_4 = QtWidgets.QLineEdit(self.g_commission_frame_4)
        self.g_cost_lineedit_4.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_cost_lineedit_4.sizePolicy().hasHeightForWidth())
        self.g_cost_lineedit_4.setSizePolicy(sizePolicy)
        self.g_cost_lineedit_4.setMinimumSize(QtCore.QSize(75, 31))
        self.g_cost_lineedit_4.setMaximumSize(QtCore.QSize(75, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_cost_lineedit_4.setFont(font)
        self.g_cost_lineedit_4.setText("")
        self.g_cost_lineedit_4.setMaxLength(8)
        self.g_cost_lineedit_4.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cost_lineedit_4.setReadOnly(False)
        self.g_cost_lineedit_4.setPlaceholderText("")
        self.g_cost_lineedit_4.setObjectName("g_cost_lineedit_4")
        self.horizontalLayout_25.addWidget(self.g_cost_lineedit_4)
        self.g_perc_label_4 = QtWidgets.QLabel(self.g_commission_frame_4)
        self.g_perc_label_4.setMinimumSize(QtCore.QSize(20, 0))
        self.g_perc_label_4.setMaximumSize(QtCore.QSize(20, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_perc_label_4.setFont(font)
        self.g_perc_label_4.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_perc_label_4.setObjectName("g_perc_label_4")
        self.horizontalLayout_25.addWidget(self.g_perc_label_4)
        self.g_popup_button_4 = QtWidgets.QPushButton(self.g_commission_frame_4)
        self.g_popup_button_4.setEnabled(True)
        self.g_popup_button_4.setMinimumSize(QtCore.QSize(25, 0))
        self.g_popup_button_4.setMaximumSize(QtCore.QSize(25, 16777215))
        self.g_popup_button_4.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_popup_button_4.setStyleSheet("border:0px;\n"
"background:none;")
        self.g_popup_button_4.setText("")
        self.g_popup_button_4.setIcon(icon1)
        self.g_popup_button_4.setIconSize(QtCore.QSize(18, 18))
        self.g_popup_button_4.setObjectName("g_popup_button_4")
        self.horizontalLayout_25.addWidget(self.g_popup_button_4)
        spacerItem9 = QtWidgets.QSpacerItem(30, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_25.addItem(spacerItem9)
        self.g_adddelcomm_frame_4 = QtWidgets.QFrame(self.g_commission_frame_4)
        self.g_adddelcomm_frame_4.setMinimumSize(QtCore.QSize(80, 0))
        self.g_adddelcomm_frame_4.setMaximumSize(QtCore.QSize(80, 16777215))
        self.g_adddelcomm_frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_adddelcomm_frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_adddelcomm_frame_4.setObjectName("g_adddelcomm_frame_4")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_adddelcomm_frame_4)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_delcom_button_4 = QtWidgets.QPushButton(self.g_adddelcomm_frame_4)
        self.g_delcom_button_4.setMinimumSize(QtCore.QSize(22, 0))
        self.g_delcom_button_4.setMaximumSize(QtCore.QSize(22, 16777215))
        self.g_delcom_button_4.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_delcom_button_4.setStyleSheet("border:none;\n"
"background-color:none;")
        self.g_delcom_button_4.setText("")
        self.g_delcom_button_4.setIcon(icon2)
        self.g_delcom_button_4.setIconSize(QtCore.QSize(24, 24))
        self.g_delcom_button_4.setObjectName("g_delcom_button_4")
        self.horizontalLayout_6.addWidget(self.g_delcom_button_4)
        spacerItem10 = QtWidgets.QSpacerItem(47, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem10)
        self.g_addcom_button_4 = QtWidgets.QPushButton(self.g_adddelcomm_frame_4)
        self.g_addcom_button_4.setMinimumSize(QtCore.QSize(22, 0))
        self.g_addcom_button_4.setMaximumSize(QtCore.QSize(22, 16777215))
        self.g_addcom_button_4.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_addcom_button_4.setStyleSheet("border:none;\n"
"background-color:none;")
        self.g_addcom_button_4.setText("")
        self.g_addcom_button_4.setIcon(icon3)
        self.g_addcom_button_4.setIconSize(QtCore.QSize(24, 24))
        self.g_addcom_button_4.setObjectName("g_addcom_button_4")
        self.horizontalLayout_6.addWidget(self.g_addcom_button_4)
        self.horizontalLayout_25.addWidget(self.g_adddelcomm_frame_4)
        self.verticalLayout_3.addWidget(self.g_commission_frame_4)
        self.g_commission_frame_5 = QtWidgets.QFrame(self.g_newpet_widget)
        self.g_commission_frame_5.setMinimumSize(QtCore.QSize(0, 50))
        self.g_commission_frame_5.setMaximumSize(QtCore.QSize(16777215, 200))
        self.g_commission_frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_commission_frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_commission_frame_5.setObjectName("g_commission_frame_5")
        self.horizontalLayout_26 = QtWidgets.QHBoxLayout(self.g_commission_frame_5)
        self.horizontalLayout_26.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_26.setObjectName("horizontalLayout_26")
        self.g_employee_label_5 = QtWidgets.QLabel(self.g_commission_frame_5)
        self.g_employee_label_5.setMinimumSize(QtCore.QSize(70, 0))
        self.g_employee_label_5.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_employee_label_5.setFont(font)
        self.g_employee_label_5.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_employee_label_5.setObjectName("g_employee_label_5")
        self.horizontalLayout_26.addWidget(self.g_employee_label_5)
        self.g_employee_combobox_5 = QtWidgets.QComboBox(self.g_commission_frame_5)
        self.g_employee_combobox_5.setMinimumSize(QtCore.QSize(0, 31))
        self.g_employee_combobox_5.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_employee_combobox_5.setObjectName("g_employee_combobox_5")
        self.horizontalLayout_26.addWidget(self.g_employee_combobox_5)
        self.g_comm_label_5 = QtWidgets.QLabel(self.g_commission_frame_5)
        self.g_comm_label_5.setMinimumSize(QtCore.QSize(0, 0))
        self.g_comm_label_5.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_comm_label_5.setFont(font)
        self.g_comm_label_5.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_comm_label_5.setObjectName("g_comm_label_5")
        self.horizontalLayout_26.addWidget(self.g_comm_label_5)
        self.g_cost_lineedit_5 = QtWidgets.QLineEdit(self.g_commission_frame_5)
        self.g_cost_lineedit_5.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_cost_lineedit_5.sizePolicy().hasHeightForWidth())
        self.g_cost_lineedit_5.setSizePolicy(sizePolicy)
        self.g_cost_lineedit_5.setMinimumSize(QtCore.QSize(75, 31))
        self.g_cost_lineedit_5.setMaximumSize(QtCore.QSize(75, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_cost_lineedit_5.setFont(font)
        self.g_cost_lineedit_5.setText("")
        self.g_cost_lineedit_5.setMaxLength(8)
        self.g_cost_lineedit_5.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cost_lineedit_5.setReadOnly(False)
        self.g_cost_lineedit_5.setPlaceholderText("")
        self.g_cost_lineedit_5.setObjectName("g_cost_lineedit_5")
        self.horizontalLayout_26.addWidget(self.g_cost_lineedit_5)
        self.g_perc_label_5 = QtWidgets.QLabel(self.g_commission_frame_5)
        self.g_perc_label_5.setMinimumSize(QtCore.QSize(20, 0))
        self.g_perc_label_5.setMaximumSize(QtCore.QSize(20, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_perc_label_5.setFont(font)
        self.g_perc_label_5.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_perc_label_5.setObjectName("g_perc_label_5")
        self.horizontalLayout_26.addWidget(self.g_perc_label_5)
        self.g_popup_button_5 = QtWidgets.QPushButton(self.g_commission_frame_5)
        self.g_popup_button_5.setEnabled(True)
        self.g_popup_button_5.setMinimumSize(QtCore.QSize(25, 0))
        self.g_popup_button_5.setMaximumSize(QtCore.QSize(25, 16777215))
        self.g_popup_button_5.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_popup_button_5.setStyleSheet("border:0px;\n"
"background:none;")
        self.g_popup_button_5.setText("")
        self.g_popup_button_5.setIcon(icon1)
        self.g_popup_button_5.setIconSize(QtCore.QSize(18, 18))
        self.g_popup_button_5.setObjectName("g_popup_button_5")
        self.horizontalLayout_26.addWidget(self.g_popup_button_5)
        spacerItem11 = QtWidgets.QSpacerItem(30, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_26.addItem(spacerItem11)
        self.g_adddelcomm_frame_5 = QtWidgets.QFrame(self.g_commission_frame_5)
        self.g_adddelcomm_frame_5.setMinimumSize(QtCore.QSize(80, 0))
        self.g_adddelcomm_frame_5.setMaximumSize(QtCore.QSize(80, 16777215))
        self.g_adddelcomm_frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_adddelcomm_frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_adddelcomm_frame_5.setObjectName("g_adddelcomm_frame_5")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_adddelcomm_frame_5)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_delcom_button_5 = QtWidgets.QPushButton(self.g_adddelcomm_frame_5)
        self.g_delcom_button_5.setMinimumSize(QtCore.QSize(22, 0))
        self.g_delcom_button_5.setMaximumSize(QtCore.QSize(22, 16777215))
        self.g_delcom_button_5.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_delcom_button_5.setStyleSheet("border:none;\n"
"background-color:none;")
        self.g_delcom_button_5.setText("")
        self.g_delcom_button_5.setIcon(icon2)
        self.g_delcom_button_5.setIconSize(QtCore.QSize(24, 24))
        self.g_delcom_button_5.setObjectName("g_delcom_button_5")
        self.horizontalLayout_7.addWidget(self.g_delcom_button_5)
        spacerItem12 = QtWidgets.QSpacerItem(57, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem12)
        self.horizontalLayout_26.addWidget(self.g_adddelcomm_frame_5)
        self.verticalLayout_3.addWidget(self.g_commission_frame_5)
        spacerItem13 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_3.addItem(spacerItem13)
        self.g_commission_scrollarea.setWidget(self.g_newpet_widget)
        self.verticalLayout.addWidget(self.g_commission_scrollarea)
        self.g_comtipslabel_frame = QtWidgets.QFrame(self.g_selectform_frame)
        self.g_comtipslabel_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_comtipslabel_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_comtipslabel_frame.setObjectName("g_comtipslabel_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_comtipslabel_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_commissionred_label = QtWidgets.QLabel(self.g_comtipslabel_frame)
        self.g_commissionred_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_commissionred_label.setFont(font)
        self.g_commissionred_label.setStyleSheet("")
        self.g_commissionred_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_commissionred_label.setObjectName("g_commissionred_label")
        self.horizontalLayout_2.addWidget(self.g_commissionred_label)
        self.g_commission_label = QtWidgets.QLabel(self.g_comtipslabel_frame)
        self.g_commission_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_commission_label.setFont(font)
        self.g_commission_label.setStyleSheet("")
        self.g_commission_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_commission_label.setObjectName("g_commission_label")
        self.horizontalLayout_2.addWidget(self.g_commission_label)
        self.g_comtotal_lineedit = QtWidgets.QLineEdit(self.g_comtipslabel_frame)
        self.g_comtotal_lineedit.setMinimumSize(QtCore.QSize(100, 0))
        self.g_comtotal_lineedit.setMaximumSize(QtCore.QSize(100, 16777215))
        self.g_comtotal_lineedit.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_comtotal_lineedit.setReadOnly(True)
        self.g_comtotal_lineedit.setObjectName("g_comtotal_lineedit")
        self.horizontalLayout_2.addWidget(self.g_comtotal_lineedit)
        self.g_comtotalpercent_label = QtWidgets.QLabel(self.g_comtipslabel_frame)
        self.g_comtotalpercent_label.setMinimumSize(QtCore.QSize(20, 0))
        self.g_comtotalpercent_label.setMaximumSize(QtCore.QSize(20, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_comtotalpercent_label.setFont(font)
        self.g_comtotalpercent_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_comtotalpercent_label.setObjectName("g_comtotalpercent_label")
        self.horizontalLayout_2.addWidget(self.g_comtotalpercent_label)
        self.verticalLayout.addWidget(self.g_comtipslabel_frame)
        self.g_bottombar_frame = QtWidgets.QFrame(self.g_selectform_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.g_bottombar_frame.setSizePolicy(sizePolicy)
        self.g_bottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_bottombar_frame.setStyleSheet("")
        self.g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottombar_frame.setObjectName("g_bottombar_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_bottombar_frame)
        self.horizontalLayout.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_controlbuttons_frame = QtWidgets.QFrame(self.g_bottombar_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem14 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem14)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setStyleSheet("")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/pet_tracker/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon4)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_11.addWidget(self.g_cancel_button)
        self.g_deletecomm_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_deletecomm_button.setMinimumSize(QtCore.QSize(180, 40))
        self.g_deletecomm_button.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_deletecomm_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon5 = QtGui.QIcon()
        icon5.addPixmap(QtGui.QPixmap(":/commission_modaldialog/xwhite"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_deletecomm_button.setIcon(icon5)
        self.g_deletecomm_button.setIconSize(QtCore.QSize(24, 24))
        self.g_deletecomm_button.setObjectName("g_deletecomm_button")
        self.horizontalLayout_11.addWidget(self.g_deletecomm_button)
        self.g_deletetip_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_deletetip_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_deletetip_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_deletetip_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_deletetip_button.setStyleSheet("")
        icon6 = QtGui.QIcon()
        icon6.addPixmap(QtGui.QPixmap(":/settings_screen/icons/flat_x_white_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_deletetip_button.setIcon(icon6)
        self.g_deletetip_button.setIconSize(QtCore.QSize(24, 24))
        self.g_deletetip_button.setObjectName("g_deletetip_button")
        self.horizontalLayout_11.addWidget(self.g_deletetip_button)
        self.g_split_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_split_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_split_button.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_split_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_split_button.setStyleSheet("")
        icon7 = QtGui.QIcon()
        icon7.addPixmap(QtGui.QPixmap(":/commission_modaldialog/split"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_split_button.setIcon(icon7)
        self.g_split_button.setIconSize(QtCore.QSize(24, 24))
        self.g_split_button.setObjectName("g_split_button")
        self.horizontalLayout_11.addWidget(self.g_split_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_save_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_save_button.setStyleSheet("")
        icon8 = QtGui.QIcon()
        icon8.addPixmap(QtGui.QPixmap(":/commission_modaldialog/check"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon8)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_11.addWidget(self.g_save_button)
        self.horizontalLayout.addWidget(self.g_controlbuttons_frame)
        self.verticalLayout.addWidget(self.g_bottombar_frame)
        self.verticalLayout_2.addWidget(self.g_selectform_frame)
        self.verticalLayout_4.addWidget(self.g_main_frame)

        self.retranslateUi(CommissionDialog)
        QtCore.QMetaObject.connectSlotsByName(CommissionDialog)

    def retranslateUi(self, CommissionDialog):
        _translate = QtCore.QCoreApplication.translate
        CommissionDialog.setWindowTitle(_translate("CommissionDialog", "Commissions / Tips"))
        self.g_main_label.setText(_translate("CommissionDialog", "Default Commission on Invoice"))
        self.g_previouspage_label.setToolTip(_translate("CommissionDialog", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("CommissionDialog", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("CommissionDialog", "999"))
        self.g_slash_label.setText(_translate("CommissionDialog", "/"))
        self.g_totalpages_label.setToolTip(_translate("CommissionDialog", "Total Pages"))
        self.g_totalpages_label.setText(_translate("CommissionDialog", "999"))
        self.g_entertip_label.setText(_translate("CommissionDialog", "Enter Tip"))
        self.g_employee_label_1.setText(_translate("CommissionDialog", "Employee"))
        self.g_comm_label_1.setText(_translate("CommissionDialog", "Commission %"))
        self.g_perc_label_1.setText(_translate("CommissionDialog", "%"))
        self.g_employee_label_2.setText(_translate("CommissionDialog", "Employee"))
        self.g_comm_label_2.setText(_translate("CommissionDialog", "Commission %"))
        self.g_perc_label_2.setText(_translate("CommissionDialog", "%"))
        self.g_employee_label_3.setText(_translate("CommissionDialog", "Employee"))
        self.g_comm_label_3.setText(_translate("CommissionDialog", "Commission %"))
        self.g_perc_label_3.setText(_translate("CommissionDialog", "%"))
        self.g_employee_label_4.setText(_translate("CommissionDialog", "Employee"))
        self.g_comm_label_4.setText(_translate("CommissionDialog", "Commission %"))
        self.g_perc_label_4.setText(_translate("CommissionDialog", "%"))
        self.g_employee_label_5.setText(_translate("CommissionDialog", "Employee"))
        self.g_comm_label_5.setText(_translate("CommissionDialog", "Commission %"))
        self.g_perc_label_5.setText(_translate("CommissionDialog", "%"))
        self.g_commissionred_label.setText(_translate("CommissionDialog", "<html><head/><body><p><span style=\" color:#ff0000;\">Percentage Totals Must Equal 100%:</span></p></body></html>"))
        self.g_commission_label.setText(_translate("CommissionDialog", "Commission Percentage Total"))
        self.g_comtotalpercent_label.setText(_translate("CommissionDialog", "%"))
        self.g_cancel_button.setText(_translate("CommissionDialog", "  Cancel"))
        self.g_deletecomm_button.setText(_translate("CommissionDialog", "  No Commission"))
        self.g_deletetip_button.setText(_translate("CommissionDialog", "  No Tip"))
        self.g_split_button.setText(_translate("CommissionDialog", "  Split"))
        self.g_save_button.setText(_translate("CommissionDialog", "  Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    CommissionDialog = QtWidgets.QDialog()
    ui = Ui_CommissionDialog()
    ui.setupUi(CommissionDialog)
    CommissionDialog.show()
    sys.exit(app.exec_())
