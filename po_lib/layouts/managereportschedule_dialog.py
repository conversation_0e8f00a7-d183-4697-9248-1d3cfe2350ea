# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managereportschedule_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ManageReportSchedule(object):
    def setupUi(self, ManageReportSchedule):
        ManageReportSchedule.setObjectName("ManageReportSchedule")
        ManageReportSchedule.resize(876, 600)
        ManageReportSchedule.setMinimumSize(QtCore.QSize(550, 425))
        ManageReportSchedule.setMaximumSize(QtCore.QSize(16777215, 16777215))
        ManageReportSchedule.setStyleSheet("#ManageReportSchedule{\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_schedule_groupBox,\n"
"#g_reportoptions_groupbox,\n"
"#g_settings_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border:1px solid #073c83;\n"
"    border-radius:10px;\n"
"    background-color:#DDE4E9;\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"    color: #000;\n"
"}\n"
"\n"
"QLabel {\n"
"    color: #000;\n"
"}\n"
"\n"
"QTreeWidget {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    padding-top:5px;\n"
"}\n"
"\n"
"QListWidget {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    /*background-color: #000;*/\n"
"}\n"
"\n"
"QDateTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
" }\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"} \n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"#g_tabs QTabWidget::tab-bar {\n"
"     left: 10px; \n"
" }\n"
"\n"
"QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected, \n"
"QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"\n"
"#g_create_tab,\n"
"#g_scheduled_tab {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius:0px;\n"
"    background-color: #e7eff4;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-size: 12px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"#g_tabs QTabWidget::tab-bar {\n"
"     left: 5px; \n"
"}\n"
"\n"
"#g_update_schedule_button,\n"
"#g_add_schedule_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_update_schedule_button:pressed,\n"
"#g_add_schedule_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_delete_button,\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_delete_button:pressed,\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
" \n"
"#g_date_dateedit::drop-down,\n"
"#g_custom_combobox::drop-down,\n"
"#g_repeat_combobox::drop-down,\n"
"#g_time_combobox::drop-down,\n"
"#g_day_checkcombobox::drop-down {\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"}\n"
"\n"
"#g_pagenav_frame QLabel,\n"
"#g_pagenav_frame QLineEdit,\n"
"#g_date_dateedit,\n"
"#g_day_checkcombobox {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_notifyemail_label, \n"
"#g_notifyemail_label_2,\n"
"#g_schedule_groupBox QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"")
        self.verticalLayout = QtWidgets.QVBoxLayout(ManageReportSchedule)
        self.verticalLayout.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout.setSpacing(4)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_progress_frame = QtWidgets.QFrame(ManageReportSchedule)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.verticalLayout.addWidget(self.g_progress_frame)
        self.g_titlepagenav_frame = QtWidgets.QFrame(ManageReportSchedule)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 36))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_30 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_30.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_30.setObjectName("horizontalLayout_30")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout_23 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_23.setObjectName("verticalLayout_23")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_23.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_23.addItem(spacerItem)
        self.horizontalLayout_30.addWidget(self.g_title_frame)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout.setSpacing(6)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout.addItem(spacerItem1)
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagenav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_slash_label.setObjectName("g_slash_label")
        self.g_pagenav_hlayout.addWidget(self.g_slash_label)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout.addWidget(self.g_totalpages_label)
        self.horizontalLayout_30.addWidget(self.g_pagenav_frame)
        self.verticalLayout.addWidget(self.g_titlepagenav_frame)
        self.g_notifyemail_label = QtWidgets.QLabel(ManageReportSchedule)
        self.g_notifyemail_label.setMinimumSize(QtCore.QSize(0, 20))
        self.g_notifyemail_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_notifyemail_label.setFont(font)
        self.g_notifyemail_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_notifyemail_label.setObjectName("g_notifyemail_label")
        self.verticalLayout.addWidget(self.g_notifyemail_label)
        self.g_notifyemail_label_2 = QtWidgets.QLabel(ManageReportSchedule)
        self.g_notifyemail_label_2.setMinimumSize(QtCore.QSize(0, 20))
        self.g_notifyemail_label_2.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_notifyemail_label_2.setFont(font)
        self.g_notifyemail_label_2.setAlignment(QtCore.Qt.AlignCenter)
        self.g_notifyemail_label_2.setObjectName("g_notifyemail_label_2")
        self.verticalLayout.addWidget(self.g_notifyemail_label_2)
        self.g_reportoptions_groupbox = QtWidgets.QGroupBox(ManageReportSchedule)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_reportoptions_groupbox.setFont(font)
        self.g_reportoptions_groupbox.setObjectName("g_reportoptions_groupbox")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_reportoptions_groupbox)
        self.verticalLayout_4.setSpacing(0)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_reportoptions_stack = QtWidgets.QStackedWidget(self.g_reportoptions_groupbox)
        self.g_reportoptions_stack.setObjectName("g_reportoptions_stack")
        self.page = QtWidgets.QWidget()
        self.page.setObjectName("page")
        self.g_reportoptions_stack.addWidget(self.page)
        self.page_2 = QtWidgets.QWidget()
        self.page_2.setObjectName("page_2")
        self.g_reportoptions_stack.addWidget(self.page_2)
        self.verticalLayout_4.addWidget(self.g_reportoptions_stack)
        self.verticalLayout.addWidget(self.g_reportoptions_groupbox)
        self.g_schedule_groupBox = QtWidgets.QGroupBox(ManageReportSchedule)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_schedule_groupBox.setFont(font)
        self.g_schedule_groupBox.setObjectName("g_schedule_groupBox")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_schedule_groupBox)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.frame = QtWidgets.QFrame(self.g_schedule_groupBox)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setSpacing(6)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_startdate_label = QtWidgets.QLabel(self.frame)
        self.g_startdate_label.setMinimumSize(QtCore.QSize(100, 0))
        self.g_startdate_label.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_startdate_label.setFont(font)
        self.g_startdate_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_startdate_label.setObjectName("g_startdate_label")
        self.horizontalLayout_7.addWidget(self.g_startdate_label)
        self.g_date_dateedit = QtWidgets.QDateEdit(self.frame)
        self.g_date_dateedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_date_dateedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_date_dateedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 1), QtCore.QTime(0, 0, 0)))
        self.g_date_dateedit.setCalendarPopup(True)
        self.g_date_dateedit.setObjectName("g_date_dateedit")
        self.horizontalLayout_7.addWidget(self.g_date_dateedit)
        self.g_repeat_label = QtWidgets.QLabel(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_repeat_label.sizePolicy().hasHeightForWidth())
        self.g_repeat_label.setSizePolicy(sizePolicy)
        self.g_repeat_label.setMinimumSize(QtCore.QSize(90, 0))
        self.g_repeat_label.setMaximumSize(QtCore.QSize(90, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_repeat_label.setFont(font)
        self.g_repeat_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_repeat_label.setObjectName("g_repeat_label")
        self.horizontalLayout_7.addWidget(self.g_repeat_label)
        self.g_repeat_combobox = QtWidgets.QComboBox(self.frame)
        self.g_repeat_combobox.setMinimumSize(QtCore.QSize(175, 31))
        self.g_repeat_combobox.setMaximumSize(QtCore.QSize(175, 30))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_repeat_combobox.setFont(font)
        self.g_repeat_combobox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_repeat_combobox.setStyleSheet("")
        self.g_repeat_combobox.setFrame(False)
        self.g_repeat_combobox.setObjectName("g_repeat_combobox")
        self.g_repeat_combobox.addItem("")
        self.g_repeat_combobox.addItem("")
        self.g_repeat_combobox.addItem("")
        self.g_repeat_combobox.addItem("")
        self.g_repeat_combobox.addItem("")
        self.g_repeat_combobox.addItem("")
        self.g_repeat_combobox.addItem("")
        self.g_repeat_combobox.addItem("")
        self.horizontalLayout_7.addWidget(self.g_repeat_combobox)
        self.g_time_label = QtWidgets.QLabel(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_time_label.sizePolicy().hasHeightForWidth())
        self.g_time_label.setSizePolicy(sizePolicy)
        self.g_time_label.setMinimumSize(QtCore.QSize(90, 0))
        self.g_time_label.setMaximumSize(QtCore.QSize(90, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_time_label.setFont(font)
        self.g_time_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_time_label.setObjectName("g_time_label")
        self.horizontalLayout_7.addWidget(self.g_time_label)
        self.g_time_combobox = QtWidgets.QComboBox(self.frame)
        self.g_time_combobox.setMinimumSize(QtCore.QSize(175, 31))
        self.g_time_combobox.setMaximumSize(QtCore.QSize(175, 30))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_time_combobox.setFont(font)
        self.g_time_combobox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_time_combobox.setStyleSheet("")
        self.g_time_combobox.setFrame(False)
        self.g_time_combobox.setObjectName("g_time_combobox")
        self.g_time_combobox.addItem("")
        self.g_time_combobox.addItem("")
        self.g_time_combobox.addItem("")
        self.g_time_combobox.addItem("")
        self.g_time_combobox.addItem("")
        self.g_time_combobox.addItem("")
        self.g_time_combobox.addItem("")
        self.g_time_combobox.addItem("")
        self.horizontalLayout_7.addWidget(self.g_time_combobox)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem2)
        self.verticalLayout_3.addWidget(self.frame)
        self.g_custom_frame = QtWidgets.QFrame(self.g_schedule_groupBox)
        self.g_custom_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_custom_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_custom_frame.setObjectName("g_custom_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_custom_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        spacerItem3 = QtWidgets.QSpacerItem(261, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem3)
        self.g_months_label = QtWidgets.QLabel(self.g_custom_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_months_label.sizePolicy().hasHeightForWidth())
        self.g_months_label.setSizePolicy(sizePolicy)
        self.g_months_label.setMinimumSize(QtCore.QSize(90, 0))
        self.g_months_label.setMaximumSize(QtCore.QSize(90, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_months_label.setFont(font)
        self.g_months_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_months_label.setObjectName("g_months_label")
        self.horizontalLayout_2.addWidget(self.g_months_label)
        self.g_month_checkcombobox = PyCheckCombobox(self.g_custom_frame)
        self.g_month_checkcombobox.setMinimumSize(QtCore.QSize(175, 31))
        self.g_month_checkcombobox.setMaximumSize(QtCore.QSize(175, 31))
        self.g_month_checkcombobox.setToolTip("")
        self.g_month_checkcombobox.setWhatsThis("")
        self.g_month_checkcombobox.setObjectName("g_month_checkcombobox")
        self.horizontalLayout_2.addWidget(self.g_month_checkcombobox)
        self.g_days_label = QtWidgets.QLabel(self.g_custom_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_days_label.sizePolicy().hasHeightForWidth())
        self.g_days_label.setSizePolicy(sizePolicy)
        self.g_days_label.setMinimumSize(QtCore.QSize(90, 0))
        self.g_days_label.setMaximumSize(QtCore.QSize(90, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_days_label.setFont(font)
        self.g_days_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_days_label.setObjectName("g_days_label")
        self.horizontalLayout_2.addWidget(self.g_days_label)
        self.g_day_checkcombobox = PyCheckCombobox(self.g_custom_frame)
        self.g_day_checkcombobox.setMinimumSize(QtCore.QSize(175, 31))
        self.g_day_checkcombobox.setMaximumSize(QtCore.QSize(175, 31))
        self.g_day_checkcombobox.setToolTip("")
        self.g_day_checkcombobox.setWhatsThis("")
        self.g_day_checkcombobox.setObjectName("g_day_checkcombobox")
        self.horizontalLayout_2.addWidget(self.g_day_checkcombobox)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem4)
        self.verticalLayout_3.addWidget(self.g_custom_frame)
        self.verticalLayout.addWidget(self.g_schedule_groupBox)
        self.frame_2 = QtWidgets.QFrame(ManageReportSchedule)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.frame_2)
        self.horizontalLayout_3.setContentsMargins(0, 6, 0, 0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem5)
        self.g_cancel_button = QtWidgets.QPushButton(self.frame_2)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/report_modaldialog/delete"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_3.addWidget(self.g_cancel_button)
        self.g_update_schedule_button = QtWidgets.QPushButton(self.frame_2)
        self.g_update_schedule_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_update_schedule_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_update_schedule_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/report_modaldialog/check"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_update_schedule_button.setIcon(icon1)
        self.g_update_schedule_button.setIconSize(QtCore.QSize(24, 24))
        self.g_update_schedule_button.setObjectName("g_update_schedule_button")
        self.horizontalLayout_3.addWidget(self.g_update_schedule_button)
        self.g_add_schedule_button = QtWidgets.QPushButton(self.frame_2)
        self.g_add_schedule_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_add_schedule_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_add_schedule_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/report_modaldialog/add"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_add_schedule_button.setIcon(icon2)
        self.g_add_schedule_button.setIconSize(QtCore.QSize(24, 24))
        self.g_add_schedule_button.setObjectName("g_add_schedule_button")
        self.horizontalLayout_3.addWidget(self.g_add_schedule_button)
        self.verticalLayout.addWidget(self.frame_2)
        self.g_bottombar_frame = QtWidgets.QFrame(ManageReportSchedule)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.g_bottombar_frame.setSizePolicy(sizePolicy)
        self.g_bottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_bottombar_frame.setStyleSheet("")
        self.g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottombar_frame.setObjectName("g_bottombar_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_bottombar_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.verticalLayout.addWidget(self.g_bottombar_frame)

        self.retranslateUi(ManageReportSchedule)
        QtCore.QMetaObject.connectSlotsByName(ManageReportSchedule)

    def retranslateUi(self, ManageReportSchedule):
        _translate = QtCore.QCoreApplication.translate
        ManageReportSchedule.setWindowTitle(_translate("ManageReportSchedule", "Manage Report Schedule"))
        self.g_main_label.setText(_translate("ManageReportSchedule", "Update Report: "))
        self.g_previouspage_label.setToolTip(_translate("ManageReportSchedule", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("ManageReportSchedule", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("ManageReportSchedule", "999"))
        self.g_slash_label.setText(_translate("ManageReportSchedule", "/"))
        self.g_totalpages_label.setToolTip(_translate("ManageReportSchedule", "Total Pages"))
        self.g_totalpages_label.setText(_translate("ManageReportSchedule", "999"))
        self.g_notifyemail_label.setText(_translate("ManageReportSchedule", "Emails will be <NAME_EMAIL> related to when pets are sold."))
        self.g_notifyemail_label_2.setText(_translate("ManageReportSchedule", "Want to ensure that you get every email? <NAME_EMAIL> to your address book!"))
        self.g_reportoptions_groupbox.setTitle(_translate("ManageReportSchedule", "Report Options"))
        self.g_schedule_groupBox.setTitle(_translate("ManageReportSchedule", "Schedule Options"))
        self.g_startdate_label.setText(_translate("ManageReportSchedule", "Start Date:"))
        self.g_repeat_label.setText(_translate("ManageReportSchedule", "Repeat:"))
        self.g_repeat_combobox.setCurrentText(_translate("ManageReportSchedule", "Smart Search"))
        self.g_repeat_combobox.setItemText(0, _translate("ManageReportSchedule", "Smart Search"))
        self.g_repeat_combobox.setItemText(1, _translate("ManageReportSchedule", "Search Breed Name"))
        self.g_repeat_combobox.setItemText(2, _translate("ManageReportSchedule", "Search Breeder"))
        self.g_repeat_combobox.setItemText(3, _translate("ManageReportSchedule", "Search Pet #"))
        self.g_repeat_combobox.setItemText(4, _translate("ManageReportSchedule", "Search Microchip"))
        self.g_repeat_combobox.setItemText(5, _translate("ManageReportSchedule", "Search Sex"))
        self.g_repeat_combobox.setItemText(6, _translate("ManageReportSchedule", "Search Status"))
        self.g_repeat_combobox.setItemText(7, _translate("ManageReportSchedule", "Search Identifiers"))
        self.g_time_label.setText(_translate("ManageReportSchedule", "Time:"))
        self.g_time_combobox.setCurrentText(_translate("ManageReportSchedule", "Smart Search"))
        self.g_time_combobox.setItemText(0, _translate("ManageReportSchedule", "Smart Search"))
        self.g_time_combobox.setItemText(1, _translate("ManageReportSchedule", "Search Breed Name"))
        self.g_time_combobox.setItemText(2, _translate("ManageReportSchedule", "Search Breeder"))
        self.g_time_combobox.setItemText(3, _translate("ManageReportSchedule", "Search Pet #"))
        self.g_time_combobox.setItemText(4, _translate("ManageReportSchedule", "Search Microchip"))
        self.g_time_combobox.setItemText(5, _translate("ManageReportSchedule", "Search Sex"))
        self.g_time_combobox.setItemText(6, _translate("ManageReportSchedule", "Search Status"))
        self.g_time_combobox.setItemText(7, _translate("ManageReportSchedule", "Search Identifiers"))
        self.g_months_label.setText(_translate("ManageReportSchedule", "Select Months:"))
        self.g_days_label.setText(_translate("ManageReportSchedule", "Select Days:"))
        self.g_cancel_button.setText(_translate("ManageReportSchedule", "Cancel"))
        self.g_update_schedule_button.setText(_translate("ManageReportSchedule", "Update"))
        self.g_add_schedule_button.setText(_translate("ManageReportSchedule", " Add"))
from po_lib.checkcombobox import PyCheckCombobox
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageReportSchedule = QtWidgets.QDialog()
    ui = Ui_ManageReportSchedule()
    ui.setupUi(ManageReportSchedule)
    ManageReportSchedule.show()
    sys.exit(app.exec_())
