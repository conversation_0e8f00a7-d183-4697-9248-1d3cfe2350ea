# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'esign_ppw_qrcode_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ESignPpwQrCodeDialog(object):
    def setupUi(self, ESignPpwQrCodeDialog):
        ESignPpwQrCodeDialog.setObjectName("ESignPpwQrCodeDialog")
        ESignPpwQrCodeDialog.resize(540, 502)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(ESignPpwQrCodeDialog.sizePolicy().hasHeightForWidth())
        ESignPpwQrCodeDialog.setSizePolicy(sizePolicy)
        ESignPpwQrCodeDialog.setStyleSheet("#ESignPpwQrCodeDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"\n"
"#g_controlbuttons_frame, \n"
"#g_qr_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_controlbuttons_frame, \n"
"#g_qr_frame {\n"
"   /* border: 1px solid #aaa;*/\n"
"    border-radius:5px;\n"
"    /*background-color:#FFF;*/\n"
"}\n"
"\n"
"#g_cancel_button, \n"
"#g_completed_button,\n"
"#g_hold_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_completed_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_completed_button:pressed {\n"
"    background-color: #007f00;\n"
"} \n"
"\n"
"#g_cancel_button {\n"
"    background-color: #ee1111;\n"
"    border:2px solid #CA0E0E;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background-color:#CA0E0E;\n"
"}\n"
"\n"
"#g_hold_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"#g_hold_button:pressed {\n"
"    background: #287599;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}")
        self.verticalLayout = QtWidgets.QVBoxLayout(ESignPpwQrCodeDialog)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_main_label = QtWidgets.QLabel(ESignPpwQrCodeDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_main_label.sizePolicy().hasHeightForWidth())
        self.g_main_label.setSizePolicy(sizePolicy)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        self.g_qr_frame = QtWidgets.QFrame(ESignPpwQrCodeDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_qr_frame.sizePolicy().hasHeightForWidth())
        self.g_qr_frame.setSizePolicy(sizePolicy)
        self.g_qr_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_qr_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_qr_frame.setObjectName("g_qr_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_qr_frame)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_qr_label = QtWidgets.QLabel(self.g_qr_frame)
        self.g_qr_label.setMinimumSize(QtCore.QSize(340, 340))
        self.g_qr_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_qr_label.setObjectName("g_qr_label")
        self.horizontalLayout_2.addWidget(self.g_qr_label)
        self.verticalLayout.addWidget(self.g_qr_frame)
        self.g_controlbuttons_frame = QtWidgets.QFrame(ESignPpwQrCodeDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_cancel_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(140, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(140, 40))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_hold_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_hold_button.setMinimumSize(QtCore.QSize(140, 40))
        self.g_hold_button.setMaximumSize(QtCore.QSize(140, 40))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_hold_button.setIcon(icon1)
        self.g_hold_button.setIconSize(QtCore.QSize(24, 24))
        self.g_hold_button.setObjectName("g_hold_button")
        self.horizontalLayout.addWidget(self.g_hold_button)
        self.g_completed_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_completed_button.setMinimumSize(QtCore.QSize(140, 40))
        self.g_completed_button.setMaximumSize(QtCore.QSize(140, 40))
        self.g_completed_button.setIcon(icon1)
        self.g_completed_button.setIconSize(QtCore.QSize(24, 24))
        self.g_completed_button.setObjectName("g_completed_button")
        self.horizontalLayout.addWidget(self.g_completed_button)
        self.verticalLayout.addWidget(self.g_controlbuttons_frame)

        self.retranslateUi(ESignPpwQrCodeDialog)
        QtCore.QMetaObject.connectSlotsByName(ESignPpwQrCodeDialog)

    def retranslateUi(self, ESignPpwQrCodeDialog):
        _translate = QtCore.QCoreApplication.translate
        ESignPpwQrCodeDialog.setWindowTitle(_translate("ESignPpwQrCodeDialog", "Presale paperwork"))
        self.g_main_label.setText(_translate("ESignPpwQrCodeDialog", "Enter invoice #{INVOICE} or scan this QR"))
        self.g_qr_label.setText(_translate("ESignPpwQrCodeDialog", "QR"))
        self.g_cancel_button.setText(_translate("ESignPpwQrCodeDialog", "Cancel"))
        self.g_hold_button.setText(_translate("ESignPpwQrCodeDialog", "Hold"))
        self.g_completed_button.setText(_translate("ESignPpwQrCodeDialog", "Completed"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ESignPpwQrCodeDialog = QtWidgets.QDialog()
    ui = Ui_ESignPpwQrCodeDialog()
    ui.setupUi(ESignPpwQrCodeDialog)
    ESignPpwQrCodeDialog.show()
    sys.exit(app.exec_())
