# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managefrequentbuyerproduct_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_FrequentBuyerProduct(object):
    def setupUi(self, FrequentBuyerProduct):
        FrequentBuyerProduct.setObjectName("FrequentBuyerProduct")
        FrequentBuyerProduct.resize(494, 354)
        FrequentBuyerProduct.setMinimumSize(QtCore.QSize(0, 0))
        FrequentBuyerProduct.setMaximumSize(QtCore.QSize(16777215, 16777215))
        FrequentBuyerProduct.setStyleSheet("#FrequentBuyerProduct {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QLabel {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font: 17px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/products/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/products/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"QGroupBox {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"#g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_save_button, #__g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"")
        self.verticalLayout = QtWidgets.QVBoxLayout(FrequentBuyerProduct)
        self.verticalLayout.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_top_frame = QtWidgets.QFrame(FrequentBuyerProduct)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setMaximumSize(QtCore.QSize(16777215, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_13.setSpacing(0)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_13.addWidget(self.g_progress_frame)
        self.verticalLayout.addWidget(self.g_top_frame)
        self.g_main_label = QtWidgets.QLabel(FrequentBuyerProduct)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setStyleSheet("")
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 5, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout.addItem(spacerItem)
        self.g_promotion_frame = QtWidgets.QFrame(FrequentBuyerProduct)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_promotion_frame.sizePolicy().hasHeightForWidth())
        self.g_promotion_frame.setSizePolicy(sizePolicy)
        self.g_promotion_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_promotion_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_promotion_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_promotion_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_promotion_frame.setObjectName("g_promotion_frame")
        self.horizontalLayout_29 = QtWidgets.QHBoxLayout(self.g_promotion_frame)
        self.horizontalLayout_29.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_29.setSpacing(6)
        self.horizontalLayout_29.setObjectName("horizontalLayout_29")
        self.g_promotion_label = QtWidgets.QLabel(self.g_promotion_frame)
        self.g_promotion_label.setMinimumSize(QtCore.QSize(70, 0))
        self.g_promotion_label.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_promotion_label.setFont(font)
        self.g_promotion_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_promotion_label.setObjectName("g_promotion_label")
        self.horizontalLayout_29.addWidget(self.g_promotion_label)
        self.g_promotion_combobox = QtWidgets.QComboBox(self.g_promotion_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_promotion_combobox.sizePolicy().hasHeightForWidth())
        self.g_promotion_combobox.setSizePolicy(sizePolicy)
        self.g_promotion_combobox.setMinimumSize(QtCore.QSize(175, 31))
        self.g_promotion_combobox.setEditable(True)
        self.g_promotion_combobox.setCurrentText("")
        self.g_promotion_combobox.setProperty("qp_prod_sub_category", "")
        self.g_promotion_combobox.setObjectName("g_promotion_combobox")
        self.horizontalLayout_29.addWidget(self.g_promotion_combobox)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_29.addItem(spacerItem1)
        self.verticalLayout.addWidget(self.g_promotion_frame)
        self.frame = QtWidgets.QFrame(FrequentBuyerProduct)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.frame_2 = QtWidgets.QFrame(self.frame)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_purchasedqty_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_purchasedqty_frame.sizePolicy().hasHeightForWidth())
        self.g_purchasedqty_frame.setSizePolicy(sizePolicy)
        self.g_purchasedqty_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_purchasedqty_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_purchasedqty_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_purchasedqty_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_purchasedqty_frame.setObjectName("g_purchasedqty_frame")
        self.horizontalLayout_28 = QtWidgets.QHBoxLayout(self.g_purchasedqty_frame)
        self.horizontalLayout_28.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_28.setSpacing(6)
        self.horizontalLayout_28.setObjectName("horizontalLayout_28")
        self.g_purchasedqty_label = QtWidgets.QLabel(self.g_purchasedqty_frame)
        self.g_purchasedqty_label.setMinimumSize(QtCore.QSize(70, 0))
        self.g_purchasedqty_label.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_purchasedqty_label.setFont(font)
        self.g_purchasedqty_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_purchasedqty_label.setObjectName("g_purchasedqty_label")
        self.horizontalLayout_28.addWidget(self.g_purchasedqty_label)
        self.g_sku_edit = QtWidgets.QLineEdit(self.g_purchasedqty_frame)
        self.g_sku_edit.setMinimumSize(QtCore.QSize(100, 31))
        self.g_sku_edit.setMaximumSize(QtCore.QSize(89, 16777215))
        self.g_sku_edit.setReadOnly(False)
        self.g_sku_edit.setObjectName("g_sku_edit")
        self.horizontalLayout_28.addWidget(self.g_sku_edit)
        self.g_selectproduct_button = QtWidgets.QPushButton(self.g_purchasedqty_frame)
        self.g_selectproduct_button.setMinimumSize(QtCore.QSize(25, 0))
        self.g_selectproduct_button.setMaximumSize(QtCore.QSize(25, 31))
        self.g_selectproduct_button.setStyleSheet("border:none;\n"
"background-color:none;")
        self.g_selectproduct_button.setText("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/rewards_dialog/magnifier_clear"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_selectproduct_button.setIcon(icon)
        self.g_selectproduct_button.setIconSize(QtCore.QSize(22, 31))
        self.g_selectproduct_button.setObjectName("g_selectproduct_button")
        self.horizontalLayout_28.addWidget(self.g_selectproduct_button)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_28.addItem(spacerItem2)
        self.verticalLayout_2.addWidget(self.g_purchasedqty_frame)
        self.g_neededqty_frame_2 = QtWidgets.QFrame(self.frame_2)
        self.g_neededqty_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_neededqty_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_neededqty_frame_2.setObjectName("g_neededqty_frame_2")
        self.horizontalLayout_35 = QtWidgets.QHBoxLayout(self.g_neededqty_frame_2)
        self.horizontalLayout_35.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_35.setSpacing(6)
        self.horizontalLayout_35.setObjectName("horizontalLayout_35")
        self.g_neededqty_label_2 = QtWidgets.QLabel(self.g_neededqty_frame_2)
        self.g_neededqty_label_2.setMinimumSize(QtCore.QSize(70, 0))
        self.g_neededqty_label_2.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_neededqty_label_2.setFont(font)
        self.g_neededqty_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_neededqty_label_2.setObjectName("g_neededqty_label_2")
        self.horizontalLayout_35.addWidget(self.g_neededqty_label_2)
        self.g_description_lineedit = QtWidgets.QLineEdit(self.g_neededqty_frame_2)
        self.g_description_lineedit.setMinimumSize(QtCore.QSize(0, 0))
        self.g_description_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_description_lineedit.setStyleSheet("")
        self.g_description_lineedit.setText("")
        self.g_description_lineedit.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_description_lineedit.setReadOnly(False)
        self.g_description_lineedit.setPlaceholderText("")
        self.g_description_lineedit.setProperty("qp_prlocs_min_safety_stock_days", "")
        self.g_description_lineedit.setObjectName("g_description_lineedit")
        self.horizontalLayout_35.addWidget(self.g_description_lineedit)
        self.verticalLayout_2.addWidget(self.g_neededqty_frame_2)
        self.g_neededqty_frame = QtWidgets.QFrame(self.frame_2)
        self.g_neededqty_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_neededqty_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_neededqty_frame.setObjectName("g_neededqty_frame")
        self.horizontalLayout_34 = QtWidgets.QHBoxLayout(self.g_neededqty_frame)
        self.horizontalLayout_34.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_34.setSpacing(6)
        self.horizontalLayout_34.setObjectName("horizontalLayout_34")
        self.g_neededqty_label = QtWidgets.QLabel(self.g_neededqty_frame)
        self.g_neededqty_label.setMinimumSize(QtCore.QSize(70, 0))
        self.g_neededqty_label.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_neededqty_label.setFont(font)
        self.g_neededqty_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_neededqty_label.setObjectName("g_neededqty_label")
        self.horizontalLayout_34.addWidget(self.g_neededqty_label)
        self.g_price_lineedit = QtWidgets.QLineEdit(self.g_neededqty_frame)
        self.g_price_lineedit.setMinimumSize(QtCore.QSize(100, 0))
        self.g_price_lineedit.setMaximumSize(QtCore.QSize(89, 31))
        self.g_price_lineedit.setStyleSheet("")
        self.g_price_lineedit.setText("")
        self.g_price_lineedit.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_price_lineedit.setReadOnly(False)
        self.g_price_lineedit.setPlaceholderText("")
        self.g_price_lineedit.setProperty("qp_prlocs_min_safety_stock_days", "")
        self.g_price_lineedit.setObjectName("g_price_lineedit")
        self.horizontalLayout_34.addWidget(self.g_price_lineedit)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_34.addItem(spacerItem3)
        self.verticalLayout_2.addWidget(self.g_neededqty_frame)
        self.horizontalLayout.addWidget(self.frame_2)
        self.verticalLayout.addWidget(self.frame)
        self.g_recorddate_frame_2 = QtWidgets.QFrame(FrequentBuyerProduct)
        self.g_recorddate_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_recorddate_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_recorddate_frame_2.setObjectName("g_recorddate_frame_2")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_recorddate_frame_2)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setSpacing(6)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.frame_4 = QtWidgets.QFrame(self.g_recorddate_frame_2)
        self.frame_4.setMinimumSize(QtCore.QSize(70, 0))
        self.frame_4.setMaximumSize(QtCore.QSize(70, 16777215))
        self.frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_4.setObjectName("frame_4")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.frame_4)
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_3.setSpacing(6)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_notes_label = QtWidgets.QLabel(self.frame_4)
        self.g_notes_label.setMinimumSize(QtCore.QSize(70, 25))
        self.g_notes_label.setMaximumSize(QtCore.QSize(70, 25))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_notes_label.setFont(font)
        self.g_notes_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_notes_label.setObjectName("g_notes_label")
        self.verticalLayout_3.addWidget(self.g_notes_label)
        spacerItem4 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_3.addItem(spacerItem4)
        self.horizontalLayout_5.addWidget(self.frame_4)
        self.g_notes_plaintextedit = QtWidgets.QPlainTextEdit(self.g_recorddate_frame_2)
        self.g_notes_plaintextedit.setMinimumSize(QtCore.QSize(400, 100))
        self.g_notes_plaintextedit.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_notes_plaintextedit.setProperty("qp_txn_notes", "")
        self.g_notes_plaintextedit.setObjectName("g_notes_plaintextedit")
        self.horizontalLayout_5.addWidget(self.g_notes_plaintextedit)
        self.verticalLayout.addWidget(self.g_recorddate_frame_2)
        spacerItem5 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem5)
        self.__g_controlbuttons_frame = QtWidgets.QFrame(FrequentBuyerProduct)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.__g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.__g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.__g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_controlbuttons_frame.setObjectName("__g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.__g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(6)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem6 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem6)
        self.g_cancel_button = QtWidgets.QPushButton(self.__g_controlbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon1)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_11.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.__g_controlbuttons_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon2)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_11.addWidget(self.g_save_button)
        self.verticalLayout.addWidget(self.__g_controlbuttons_frame)

        self.retranslateUi(FrequentBuyerProduct)
        QtCore.QMetaObject.connectSlotsByName(FrequentBuyerProduct)

    def retranslateUi(self, FrequentBuyerProduct):
        _translate = QtCore.QCoreApplication.translate
        FrequentBuyerProduct.setWindowTitle(_translate("FrequentBuyerProduct", "Manage Frequent Buyer Product"))
        self.g_main_label.setText(_translate("FrequentBuyerProduct", "Manage Frequent Buyer Product"))
        self.g_promotion_label.setText(_translate("FrequentBuyerProduct", "Promotion"))
        self.g_purchasedqty_label.setText(_translate("FrequentBuyerProduct", "SKU"))
        self.g_neededqty_label_2.setText(_translate("FrequentBuyerProduct", "Description"))
        self.g_neededqty_label.setText(_translate("FrequentBuyerProduct", "Price"))
        self.g_notes_label.setText(_translate("FrequentBuyerProduct", "Notes"))
        self.g_cancel_button.setText(_translate("FrequentBuyerProduct", " Cancel"))
        self.g_save_button.setText(_translate("FrequentBuyerProduct", " Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    FrequentBuyerProduct = QtWidgets.QDialog()
    ui = Ui_FrequentBuyerProduct()
    ui.setupUi(FrequentBuyerProduct)
    FrequentBuyerProduct.show()
    sys.exit(app.exec_())
