# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'selectmassaction_popup.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_SelectMassAction(object):
    def setupUi(self, SelectMassAction):
        SelectMassAction.setObjectName("SelectMassAction")
        SelectMassAction.resize(421, 450)
        SelectMassAction.setMinimumSize(QtCore.QSize(0, 450))
        SelectMassAction.setMaximumSize(QtCore.QSize(16777215, 16777215))
        SelectMassAction.setFocusPolicy(QtCore.Qt.NoFocus)
        SelectMassAction.setStyleSheet("#SelectMassAction {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"/*************************************************************\n"
"    General styling for table widgets\n"
"*************************************************************/\n"
"\n"
"QTableWidget {\n"
"    border: 0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight: bold;\n"
"    border: 0px;\n"
"    height: 15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size: 12px;\n"
"    padding-top: 5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"\n"
"/*************************************************************\n"
"    Styling directives for removing dotted outlines around table cells\n"
"*************************************************************/\n"
"\n"
"\n"
"QTableView {\n"
"\n"
"    /* Color of the table text.\n"
"\n"
"    Must be defined explicitly because the text color of the focused\n"
"    (:focus) item will be set to this.  */\n"
"    color: black;\n"
"\n"
"    /* Colors of the selection text and background respectively.\n"
"\n"
"    These must be defined explicitly because the text/background color\n"
"    of the :focus:selected item will be set to this.  */\n"
"    selection-color: white;\n"
"    selection-background-color: rgb(51, 153, 255);\n"
"\n"
"    /* Contrary to certain StackOverflow posts, this by itself will NOT remove the \n"
"    dotted outlines from table widgets. \n"
"\n"
"    It must appear with the other declarations in this style rule.\n"
"\n"
"    The style rules that follow apply additional declarations that are needed for a consistent\n"
"    appearance. */\n"
"    outline: 0;\n"
"\n"
"}\n"
"\n"
"\n"
"QTableWidget::item:focus:selected,\n"
"QTableWidget::item:focus {\n"
"\n"
"    /* Do not display a border around any focused item whether it\'s part\n"
"    of a selection or not.  */\n"
"    border: none;\n"
"    \n"
"}\n"
"\n"
"\n"
"QTableWidget::item:focus:selected {\n"
"\n"
"    /* Set the text and background colors to be the same as the selection\n"
"    colors defined earlier.\n"
"\n"
"    Pseudo-states can be chained, in which case a logical AND is\n"
"    implied. */\n"
"    color: white;\n"
"    background-color: rgb(51, 153, 255);\n"
"}\n"
"\n"
"\n"
"QTableWidget::item:focus {\n"
"\n"
"    /* Set the focused item text to be the same \n"
"\n"
"    No \'unselected\' pseudo-state exists, only \'selected\'. */\n"
"\n"
"    color: black;\n"
"\n"
"    /* Alternating row colors can also be styled, but \'transparent\' allows\n"
"    the default alternating row colors to be used. */\n"
"    background-color: transparent;\n"
"}\n"
"\n"
"\n"
"/*************************************************************\n"
"    Default blue color and styling for buttons\n"
"*************************************************************/\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border: 2px solid #287599;\n"
"    color: white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border: 2px solid #287599;\n"
"}\n"
"\n"
"\n"
"/*************************************************************\n"
"    Styling for specific buttons\n"
"*************************************************************/\n"
"\n"
"#g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border: 2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed  {\n"
"    background: #c80319;\n"
"}\n"
"\n"
"#g_select_button {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"}\n"
"\n"
"#g_select_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"\n"
"/*************************************************************\n"
"    Styling for containers and other structural elements\n"
"*************************************************************/\n"
"\n"
"\n"
"#g_container_frame {\n"
"    \n"
"    /* Outer container enclosing the widgets of the popup itself; usually a rounded \n"
"    rectangular frame.\n"
"\n"
"    Must be named \'g_container_frame\' in order to work with popup-handling code in the POS. */\n"
"\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border: 1px solid #073c83;\n"
"    border-radius: 10px;\n"
"    background-color: #DDE4E9;\n"
"}\n"
"\n"
"\n"
"#g_cover_frame {\n"
"\n"
"    /* Darkened semi-transparent backdrop that appears with the popup when the popup is activated.\n"
"\n"
"    Covers the entire main window or dialog the popup appears in.\n"
"\n"
"    Must be named \'g_cover_frame\' in order to work with popup-handling code in the POS. */\n"
"\n"
"    background-color: rgb(0,0,0, 25%);\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(SelectMassAction)
        self.verticalLayout_3.setContentsMargins(6, 6, 6, 6)
        self.verticalLayout_3.setSpacing(6)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_container_frame = QtWidgets.QFrame(SelectMassAction)
        self.g_container_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_container_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_container_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_container_frame.setObjectName("g_container_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_container_frame)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_main_label = QtWidgets.QLabel(self.g_container_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_main_label.setStyleSheet("")
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 5, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout.addItem(spacerItem)
        self.g_select_table = QtWidgets.QTableWidget(self.g_container_frame)
        self.g_select_table.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.g_select_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_select_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_select_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_select_table.setAlternatingRowColors(True)
        self.g_select_table.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.g_select_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_select_table.setShowGrid(False)
        self.g_select_table.setCornerButtonEnabled(False)
        self.g_select_table.setObjectName("g_select_table")
        self.g_select_table.setColumnCount(1)
        self.g_select_table.setRowCount(3)
        item = QtWidgets.QTableWidgetItem()
        self.g_select_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_select_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_select_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_select_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_select_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_select_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_select_table.setItem(2, 0, item)
        self.g_select_table.horizontalHeader().setVisible(False)
        self.g_select_table.horizontalHeader().setDefaultSectionSize(200)
        self.g_select_table.horizontalHeader().setStretchLastSection(True)
        self.g_select_table.verticalHeader().setVisible(False)
        self.verticalLayout.addWidget(self.g_select_table)
        self.g_bottombar_frame = QtWidgets.QFrame(self.g_container_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.g_bottombar_frame.setSizePolicy(sizePolicy)
        self.g_bottombar_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_bottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_bottombar_frame.setStyleSheet("")
        self.g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottombar_frame.setObjectName("g_bottombar_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_bottombar_frame)
        self.horizontalLayout.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_controlbuttons_frame = QtWidgets.QFrame(self.g_bottombar_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem1)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/confirm_dialog/no"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_11.addWidget(self.g_cancel_button)
        self.g_select_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_select_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_select_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_select_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_select_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/confirm_dialog/yes"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_select_button.setIcon(icon1)
        self.g_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_select_button.setObjectName("g_select_button")
        self.horizontalLayout_11.addWidget(self.g_select_button)
        self.horizontalLayout.addWidget(self.g_controlbuttons_frame)
        self.verticalLayout.addWidget(self.g_bottombar_frame)
        self.verticalLayout_3.addWidget(self.g_container_frame)

        self.retranslateUi(SelectMassAction)
        QtCore.QMetaObject.connectSlotsByName(SelectMassAction)

    def retranslateUi(self, SelectMassAction):
        _translate = QtCore.QCoreApplication.translate
        SelectMassAction.setWindowTitle(_translate("SelectMassAction", "Select Mass Action"))
        self.g_main_label.setText(_translate("SelectMassAction", "Select Mass Action"))
        self.g_select_table.setSortingEnabled(False)
        item = self.g_select_table.verticalHeaderItem(0)
        item.setText(_translate("SelectMassAction", "New Row"))
        item = self.g_select_table.verticalHeaderItem(1)
        item.setText(_translate("SelectMassAction", "New Row"))
        item = self.g_select_table.verticalHeaderItem(2)
        item.setText(_translate("SelectMassAction", "New Row"))
        item = self.g_select_table.horizontalHeaderItem(0)
        item.setText(_translate("SelectMassAction", "Actions"))
        __sortingEnabled = self.g_select_table.isSortingEnabled()
        self.g_select_table.setSortingEnabled(False)
        item = self.g_select_table.item(0, 0)
        item.setText(_translate("SelectMassAction", "Change Note"))
        item = self.g_select_table.item(1, 0)
        item.setText(_translate("SelectMassAction", "Change Commission"))
        item = self.g_select_table.item(2, 0)
        item.setText(_translate("SelectMassAction", "Change Tax"))
        self.g_select_table.setSortingEnabled(__sortingEnabled)
        self.g_cancel_button.setText(_translate("SelectMassAction", "  Cancel"))
        self.g_select_button.setText(_translate("SelectMassAction", "  Select"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    SelectMassAction = QtWidgets.QDialog()
    ui = Ui_SelectMassAction()
    ui.setupUi(SelectMassAction)
    SelectMassAction.show()
    sys.exit(app.exec_())
