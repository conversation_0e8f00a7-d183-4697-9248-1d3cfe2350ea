# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'payment_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_PaymentDialog(object):
    def setupUi(self, PaymentDialog):
        PaymentDialog.setObjectName("PaymentDialog")
        PaymentDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        PaymentDialog.resize(1003, 691)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.MinimumExpanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(PaymentDialog.sizePolicy().hasHeightForWidth())
        PaymentDialog.setSizePolicy(sizePolicy)
        PaymentDialog.setMinimumSize(QtCore.QSize(1003, 691))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/payment_modaldialog/window_icon"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        PaymentDialog.setWindowIcon(icon)
        PaymentDialog.setStyleSheet("\n"
"\n"
"/********************************************\n"
"    Style directives for widget classes\n"
"********************************************/\n"
"\n"
"\n"
"#PaymentDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border: 2px solid #287599;\n"
"    color: white;\n"
"}\n"
"\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"\n"
"QPushButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: None;\n"
"}\n"
"\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    background-color: #FFF;\n"
"    height: 31px;\n"
"}\n"
"\n"
"       QGroupBox {\n"
"       border:1px solid #073c83;\n"
"       border-radius: 5px;\n"
"       margin-top: 0.5em;\n"
"       font-size: 12px;\n"
"       font-weight: bold;\n"
"\n"
"       }\n"
"\n"
"       QGroupBox::title {\n"
"       subcontrol-origin: margin;\n"
"       left: 16px;\n"
"       padding: 0 3px 0 3px;\n"
"       }\n"
"\n"
"\n"
"/********************************************\n"
"    Styling for tables and table-related widgets\n"
"********************************************/\n"
"\n"
"\n"
"QHeaderView::section {\n"
"    text-align: left;\n"
"    spacing: 15px;\n"
"    background-color: #ffffff;\n"
"    color: #000000;\n"
"    font-weight: bold;\n"
"    border: 0px;\n"
"    height: 25px;\n"
"    border-bottom: 1px solid #073c83;\n"
"    font-size: 12px;\n"
"    padding-left: 3px;\n"
"}\n"
"\n"
"\n"
"QTableWidget {\n"
"       /*border: none;\n"
"    border-left: 1px solid #073c83;\n"
"       border-right: 1px solid #073c83;*/\n"
"}\n"
"\n"
"\n"
"QTableView {\n"
"\n"
"    /* Color of the table text.\n"
"\n"
"    Must be defined explicitly because the text color of the focused\n"
"    (:focus) item will be set to this. */\n"
"\n"
"    color: black;\n"
"\n"
"    /* Colors of the selection text and background respectively.\n"
"\n"
"    These must be defined explicitly because the text/background color\n"
"    of the :focus:selected item will be set to this. */\n"
"\n"
"    selection-color: white;\n"
"    selection-background-color: rgb(51, 153, 255);\n"
"\n"
"    /* Removes the dotted border from selected cells in a table widget. */\n"
"    outline: 0;\n"
"}\n"
"\n"
"\n"
"QTableWidget::item:focus:selected,\n"
"QTableWidget::item:focus {\n"
"\n"
"    /* Do not display a border around any focused item whether it\'s part\n"
"    of a selection or not. */\n"
"\n"
"    border: none;\n"
"}\n"
"\n"
"\n"
"QTableWidget::item:focus:selected {\n"
"\n"
"    /* Set the text and background colors to be the same as the selection\n"
"    colors defined earlier.\n"
"\n"
"    Pseudo-states can be chained, in which case a logical AND is\n"
"    implied. */\n"
"\n"
"    color: white;\n"
"    background-color: rgb(51, 153, 255);\n"
"}\n"
"\n"
"\n"
"QTableWidget::item:focus {\n"
"\n"
"    /* Set the focused item text to be the same as the unfocused\n"
"    text color.\n"
"\n"
"    No \'unselected\' pseudo-state exists, only \'selected\'. */\n"
"\n"
"    color: black;\n"
"\n"
"    /* Alternating row colors can also be styled, but \'transparent\'\n"
"    allows the default alternating row colors to be used without\n"
"    having to explicitly define new ones. */\n"
"\n"
"    background-color: transparent;\n"
"}\n"
"\n"
"\n"
"/*******************************************************\n"
"    Styling for container frames and other structural elements\n"
"*******************************************************/\n"
"\n"
"       /*\n"
"#g_left_frame {\n"
"    min-width: 321px;\n"
"    max-width: 321px;\n"
"}\n"
"       */\n"
"\n"
"/********************************************\n"
"    Styling for the prior payments and refunds tables\n"
"********************************************/\n"
"\n"
"\n"
"       #g_priorpayments_groupbox *,\n"
"       #g_priorpayments_groupbox QTableWidget::item:focus {\n"
"    color: darkgreen;\n"
"}\n"
"\n"
"\n"
"       #g_priorrefunds_groupbox *,\n"
"       #g_priorrefunds_groupbox QTableWidget::item:focus {\n"
"    color: red;\n"
"}\n"
"\n"
"\n"
"       #g_priorpayments_groupbox QTableWidget::item:focus:selected,\n"
"       #g_priorrefunds_groupbox QTableWidget::item:focus:selected {\n"
"\n"
"    /* Ensures focused cells in a selection appear using the same text\n"
"    color as their neighboring cells. */\n"
"\n"
"    color: white;\n"
"}\n"
"\n"
"\n"
"       #g_priorpayments_groupbox QLabel,\n"
"       #g_priorrefunds_groupbox QLabel {\n"
"    background: white;\n"
"    padding-left: 1px;\n"
"    border: 1px solid #073c83;\n"
"    min-height: 25px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"\n"
"/********************************************\n"
"    Styling for the payments table\n"
"********************************************/\n"
"\n"
"#g_payments_table::item:focus {\n"
"\n"
"    /* Make this the same as the \"Highlight\" color in the palette. */\n"
"    background-color: rgb(13, 95, 208);\n"
"}\n"
"\n"
"\n"
"/********************************************\n"
"    Styling for the \"Quick Cash\" and \"Finish\" buttons\n"
"********************************************/\n"
"\n"
"\n"
"#g_finish_button,\n"
"#__g_finish_button,\n"
"#g_savelayaway_button,\n"
"#__g_savelayaway_button,\n"
"#g_quickcashnext1_button,\n"
"#__g_quickcashnext1_button,\n"
"#g_quickcashnext5_button,\n"
"#__g_quickcashnext5_button,\n"
"#g_quickcashnext10_button,\n"
"#__g_quickcashnext10_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"\n"
"#g_finish_button:pressed,\n"
"#__g_finish_button:pressed,\n"
"#g_savelayaway_button:pressed,\n"
"#__g_savelayaway_button:pressed,\n"
"#g_quickcashnext1_button:pressed,\n"
"#__g_quickcashnext1_button:pressed,\n"
"#g_quickcashnext5_button:pressed,\n"
"#__g_quickcashnext5_button:pressed,\n"
"#g_quickcashnext10_button:pressed,\n"
"#__g_quickcashnext10_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"\n"
"#PaymentDialog[transaction_type_text=\"Return from Customer\"] #g_quickcashnext1_button,\n"
"#PaymentDialog[transaction_type_text=\"Return from Customer\"] #g_quickcashnext5_button,\n"
"#PaymentDialog[transaction_type_text=\"Return from Customer\"] #g_quickcashnext10_button {\n"
"\n"
"    border: rgb(170, 170, 170);\n"
"    background-color: rgb(187, 187, 187);\n"
"\n"
"    /* Make font color the same as the background color, so that there is no need to clear the button\n"
"        text in application code. */\n"
"    color: rgb(187, 187, 187);\n"
"}\n"
"\n"
"\n"
"/********************************************\n"
"    Styling for the customer card\n"
"********************************************/\n"
"\n"
"\n"
"#g_customer_frame {\n"
"    background: #FFF;\n"
"    border: 1px solid #073c83;\n"
"    min-height: 181px;\n"
"    max-height: 181px;\n"
"}\n"
"\n"
"\n"
"#g_customer_frame QPushButton {\n"
"    background: none;\n"
"    border: 0px;\n"
"}\n"
"\n"
"\n"
"#g_customername_label,\n"
"#__g_customername_label,\n"
"#g_customerphone_label,\n"
"#__g_customerphone_label {\n"
"    color: #337791;\n"
"}\n"
"\n"
"\n"
"#g_nocustomer_label {\n"
"\n"
"    /* When no customer is selected in the sales screen customer section, this causes\n"
"    the \"No Customer Selected\" label to shift upward such that it appears centered\n"
"    vertically within the customer section. */\n"
"\n"
"    margin-top: -30px;\n"
"}\n"
"\n"
"#g_main_label {\n"
"       font-size: 18px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_tenderamount_lineedit {\n"
"    font-size: 16px;\n"
"\n"
"}\n"
"#g_tenderamount_label {\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#label_2, #label_6 {\n"
"    font-size: 13px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"       #g_totals_table,\n"
"#g_payments_table,\n"
"#g_priorpayments_table,\n"
"#g_priorrefunds_table {\n"
"    font-size: 11px;\n"
"    font-weight: normal;\n"
"       background-color: white;\n"
"       border-radius: 5px;\n"
"       border: 1px solid #073c83;\n"
"}\n"
"\n"
"#g_customerdetails_frame QLabel {\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"       #g_payment_totals_groupbox QLabel {\n"
"       font-size: 14px;\n"
"       font-weight: normal;\n"
"       }\n"
"\n"
"       #g_mid_frame,\n"
"       #g_right_frame {\n"
"       border-radius: 10px;\n"
"       border: 1px solid #073c83;\n"
"       background: rgb(242, 242, 242);\n"
"       }\n"
"\n"
"       #PaymentDialog[transaction_type_text=\"Return from Customer\"] #g_totaldue_label,\n"
"       #PaymentDialog[transaction_type_text=\"Return from Customer\"] #g_totaldue_amount_label {\n"
"       color: red;\n"
"       }\n"
"   ")
        PaymentDialog.setModal(True)
        PaymentDialog.setProperty("payments_table_column_widths_list", ['224', '70', '0'])
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/payment_modaldialog/x_normal"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        PaymentDialog.setProperty("g_xnormal_icon", icon1)
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/payment_modaldialog/x_grey"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        PaymentDialog.setProperty("g_xgrey_icon", icon2)
        PaymentDialog.setProperty("loyalty_port_int", 5000)
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(PaymentDialog)
        self.verticalLayout_3.setContentsMargins(2, 2, 2, 2)
        self.verticalLayout_3.setSpacing(2)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setContentsMargins(6, 6, 8, 6)
        self.horizontalLayout_3.setSpacing(6)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_left_frame = QtWidgets.QFrame(PaymentDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_left_frame.sizePolicy().hasHeightForWidth())
        self.g_left_frame.setSizePolicy(sizePolicy)
        self.g_left_frame.setMinimumSize(QtCore.QSize(360, 0))
        self.g_left_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_left_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_left_frame.setObjectName("g_left_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_left_frame)
        self.verticalLayout_2.setContentsMargins(2, 2, 2, 2)
        self.verticalLayout_2.setSpacing(1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_receipt_frame = QtWidgets.QFrame(self.g_left_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_receipt_frame.sizePolicy().hasHeightForWidth())
        self.g_receipt_frame.setSizePolicy(sizePolicy)
        self.g_receipt_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_receipt_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_receipt_frame.setObjectName("g_receipt_frame")
        self.verticalLayout_2.addWidget(self.g_receipt_frame)
        self.horizontalLayout_3.addWidget(self.g_left_frame)
        self.g_mid_frame = QtWidgets.QFrame(PaymentDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_mid_frame.sizePolicy().hasHeightForWidth())
        self.g_mid_frame.setSizePolicy(sizePolicy)
        self.g_mid_frame.setMinimumSize(QtCore.QSize(345, 0))
        self.g_mid_frame.setMaximumSize(QtCore.QSize(400, 16777215))
        self.g_mid_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_mid_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_mid_frame.setObjectName("g_mid_frame")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_mid_frame)
        self.verticalLayout_4.setContentsMargins(6, 6, 6, 6)
        self.verticalLayout_4.setSpacing(2)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_payment_frame = QtWidgets.QFrame(self.g_mid_frame)
        self.g_payment_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_payment_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_payment_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_payment_frame.setObjectName("g_payment_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_payment_frame)
        self.horizontalLayout_2.setContentsMargins(6, 6, 6, 6)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_tenderamount_label = QtWidgets.QLabel(self.g_payment_frame)
        self.g_tenderamount_label.setObjectName("g_tenderamount_label")
        self.horizontalLayout_2.addWidget(self.g_tenderamount_label)
        self.g_tenderamount_lineedit = QtWidgets.QLineEdit(self.g_payment_frame)
        self.g_tenderamount_lineedit.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_tenderamount_lineedit.setObjectName("g_tenderamount_lineedit")
        self.horizontalLayout_2.addWidget(self.g_tenderamount_lineedit)
        self.verticalLayout_4.addWidget(self.g_payment_frame)
        self.g_paymentbuttons_frame = QtWidgets.QFrame(self.g_mid_frame)
        self.g_paymentbuttons_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_paymentbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_paymentbuttons_frame.setStyleSheet("")
        self.g_paymentbuttons_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_paymentbuttons_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_paymentbuttons_frame.setObjectName("g_paymentbuttons_frame")
        self.gridLayout = QtWidgets.QGridLayout(self.g_paymentbuttons_frame)
        self.gridLayout.setContentsMargins(1, 5, 1, 60)
        self.gridLayout.setSpacing(7)
        self.gridLayout.setObjectName("gridLayout")
        self.g_dynamicpayment7_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dynamicpayment7_button.sizePolicy().hasHeightForWidth())
        self.g_dynamicpayment7_button.setSizePolicy(sizePolicy)
        self.g_dynamicpayment7_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_dynamicpayment7_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_dynamicpayment7_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_dynamicpayment7_button.setObjectName("g_dynamicpayment7_button")
        self.gridLayout.addWidget(self.g_dynamicpayment7_button, 3, 0, 1, 1)
        self.g_dynamicpayment8_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dynamicpayment8_button.sizePolicy().hasHeightForWidth())
        self.g_dynamicpayment8_button.setSizePolicy(sizePolicy)
        self.g_dynamicpayment8_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_dynamicpayment8_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_dynamicpayment8_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_dynamicpayment8_button.setObjectName("g_dynamicpayment8_button")
        self.gridLayout.addWidget(self.g_dynamicpayment8_button, 3, 1, 1, 1)
        self.g_dynamicpayment9_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dynamicpayment9_button.sizePolicy().hasHeightForWidth())
        self.g_dynamicpayment9_button.setSizePolicy(sizePolicy)
        self.g_dynamicpayment9_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_dynamicpayment9_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_dynamicpayment9_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_dynamicpayment9_button.setObjectName("g_dynamicpayment9_button")
        self.gridLayout.addWidget(self.g_dynamicpayment9_button, 3, 3, 1, 1)
        self.g_dynamicpayment12_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dynamicpayment12_button.sizePolicy().hasHeightForWidth())
        self.g_dynamicpayment12_button.setSizePolicy(sizePolicy)
        self.g_dynamicpayment12_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_dynamicpayment12_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_dynamicpayment12_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_dynamicpayment12_button.setObjectName("g_dynamicpayment12_button")
        self.gridLayout.addWidget(self.g_dynamicpayment12_button, 4, 3, 1, 1)
        self.g_dynamicpayment5_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dynamicpayment5_button.sizePolicy().hasHeightForWidth())
        self.g_dynamicpayment5_button.setSizePolicy(sizePolicy)
        self.g_dynamicpayment5_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_dynamicpayment5_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_dynamicpayment5_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_dynamicpayment5_button.setObjectName("g_dynamicpayment5_button")
        self.gridLayout.addWidget(self.g_dynamicpayment5_button, 2, 1, 1, 1)
        self.g_dynamicpayment4_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        self.g_dynamicpayment4_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dynamicpayment4_button.sizePolicy().hasHeightForWidth())
        self.g_dynamicpayment4_button.setSizePolicy(sizePolicy)
        self.g_dynamicpayment4_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_dynamicpayment4_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_dynamicpayment4_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_dynamicpayment4_button.setObjectName("g_dynamicpayment4_button")
        self.gridLayout.addWidget(self.g_dynamicpayment4_button, 2, 0, 1, 1)
        self.g_dynamicpayment10_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dynamicpayment10_button.sizePolicy().hasHeightForWidth())
        self.g_dynamicpayment10_button.setSizePolicy(sizePolicy)
        self.g_dynamicpayment10_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_dynamicpayment10_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_dynamicpayment10_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_dynamicpayment10_button.setObjectName("g_dynamicpayment10_button")
        self.gridLayout.addWidget(self.g_dynamicpayment10_button, 4, 0, 1, 1)
        self.g_quickcashnext5_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_quickcashnext5_button.sizePolicy().hasHeightForWidth())
        self.g_quickcashnext5_button.setSizePolicy(sizePolicy)
        self.g_quickcashnext5_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_quickcashnext5_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_quickcashnext5_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_quickcashnext5_button.setStyleSheet("")
        self.g_quickcashnext5_button.setObjectName("g_quickcashnext5_button")
        self.gridLayout.addWidget(self.g_quickcashnext5_button, 0, 1, 1, 1)
        self.g_quickcashnext10_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        self.g_quickcashnext10_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_quickcashnext10_button.sizePolicy().hasHeightForWidth())
        self.g_quickcashnext10_button.setSizePolicy(sizePolicy)
        self.g_quickcashnext10_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_quickcashnext10_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_quickcashnext10_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_quickcashnext10_button.setStyleSheet("")
        self.g_quickcashnext10_button.setObjectName("g_quickcashnext10_button")
        self.gridLayout.addWidget(self.g_quickcashnext10_button, 0, 0, 1, 1)
        self.g_dynamicpayment2_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        self.g_dynamicpayment2_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dynamicpayment2_button.sizePolicy().hasHeightForWidth())
        self.g_dynamicpayment2_button.setSizePolicy(sizePolicy)
        self.g_dynamicpayment2_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_dynamicpayment2_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_dynamicpayment2_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_dynamicpayment2_button.setObjectName("g_dynamicpayment2_button")
        self.gridLayout.addWidget(self.g_dynamicpayment2_button, 1, 1, 1, 1)
        self.g_dynamicpayment1_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dynamicpayment1_button.sizePolicy().hasHeightForWidth())
        self.g_dynamicpayment1_button.setSizePolicy(sizePolicy)
        self.g_dynamicpayment1_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_dynamicpayment1_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_dynamicpayment1_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_dynamicpayment1_button.setAutoDefault(False)
        self.g_dynamicpayment1_button.setDefault(False)
        self.g_dynamicpayment1_button.setFlat(False)
        self.g_dynamicpayment1_button.setObjectName("g_dynamicpayment1_button")
        self.gridLayout.addWidget(self.g_dynamicpayment1_button, 1, 0, 1, 1)
        self.g_dynamicpayment11_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dynamicpayment11_button.sizePolicy().hasHeightForWidth())
        self.g_dynamicpayment11_button.setSizePolicy(sizePolicy)
        self.g_dynamicpayment11_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_dynamicpayment11_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_dynamicpayment11_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_dynamicpayment11_button.setObjectName("g_dynamicpayment11_button")
        self.gridLayout.addWidget(self.g_dynamicpayment11_button, 4, 1, 1, 1)
        self.g_quickcashnext1_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_quickcashnext1_button.sizePolicy().hasHeightForWidth())
        self.g_quickcashnext1_button.setSizePolicy(sizePolicy)
        self.g_quickcashnext1_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_quickcashnext1_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_quickcashnext1_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_quickcashnext1_button.setStyleSheet("")
        self.g_quickcashnext1_button.setObjectName("g_quickcashnext1_button")
        self.gridLayout.addWidget(self.g_quickcashnext1_button, 0, 3, 1, 1)
        self.g_dynamicpayment3_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dynamicpayment3_button.sizePolicy().hasHeightForWidth())
        self.g_dynamicpayment3_button.setSizePolicy(sizePolicy)
        self.g_dynamicpayment3_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_dynamicpayment3_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_dynamicpayment3_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_dynamicpayment3_button.setObjectName("g_dynamicpayment3_button")
        self.gridLayout.addWidget(self.g_dynamicpayment3_button, 1, 3, 1, 1)
        self.g_dynamicpayment6_button = QtWidgets.QPushButton(self.g_paymentbuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dynamicpayment6_button.sizePolicy().hasHeightForWidth())
        self.g_dynamicpayment6_button.setSizePolicy(sizePolicy)
        self.g_dynamicpayment6_button.setMinimumSize(QtCore.QSize(105, 93))
        self.g_dynamicpayment6_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_dynamicpayment6_button.setFont(font)
        self.g_dynamicpayment6_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_dynamicpayment6_button.setObjectName("g_dynamicpayment6_button")
        self.gridLayout.addWidget(self.g_dynamicpayment6_button, 2, 3, 1, 1)
        self.verticalLayout_4.addWidget(self.g_paymentbuttons_frame)
        self.horizontalLayout_3.addWidget(self.g_mid_frame)
        self.g_right_frame = QtWidgets.QFrame(PaymentDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_right_frame.sizePolicy().hasHeightForWidth())
        self.g_right_frame.setSizePolicy(sizePolicy)
        self.g_right_frame.setMinimumSize(QtCore.QSize(270, 0))
        self.g_right_frame.setMaximumSize(QtCore.QSize(270, 16777215))
        self.g_right_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_right_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_right_frame.setObjectName("g_right_frame")
        self.verticalLayout_12 = QtWidgets.QVBoxLayout(self.g_right_frame)
        self.verticalLayout_12.setContentsMargins(6, 6, 6, 6)
        self.verticalLayout_12.setSpacing(8)
        self.verticalLayout_12.setObjectName("verticalLayout_12")
        self.g_invoice_totals_groupbox = QtWidgets.QGroupBox(self.g_right_frame)
        self.g_invoice_totals_groupbox.setMinimumSize(QtCore.QSize(0, 180))
        self.g_invoice_totals_groupbox.setObjectName("g_invoice_totals_groupbox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_invoice_totals_groupbox)
        self.verticalLayout.setContentsMargins(10, 10, 10, 10)
        self.verticalLayout.setSpacing(2)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_totals_table = QtWidgets.QTableWidget(self.g_invoice_totals_groupbox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_totals_table.sizePolicy().hasHeightForWidth())
        self.g_totals_table.setSizePolicy(sizePolicy)
        self.g_totals_table.setMinimumSize(QtCore.QSize(0, 140))
        self.g_totals_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_totals_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_totals_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_totals_table.setAutoScroll(False)
        self.g_totals_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_totals_table.setAlternatingRowColors(False)
        self.g_totals_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_totals_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_totals_table.setShowGrid(False)
        self.g_totals_table.setCornerButtonEnabled(False)
        self.g_totals_table.setColumnCount(3)
        self.g_totals_table.setObjectName("g_totals_table")
        self.g_totals_table.setRowCount(8)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_totals_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_totals_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_totals_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_totals_table.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_totals_table.setItem(4, 1, item)
        item = QtWidgets.QTableWidgetItem()
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/sales_screen/edit"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        item.setIcon(icon3)
        self.g_totals_table.setItem(4, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(5, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_totals_table.setItem(5, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(6, 0, item)
        item = QtWidgets.QTableWidgetItem()
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setBackground(brush)
        self.g_totals_table.setItem(6, 1, item)
        item = QtWidgets.QTableWidgetItem()
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setBackground(brush)
        self.g_totals_table.setItem(6, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(7, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_totals_table.setItem(7, 1, item)
        self.g_totals_table.horizontalHeader().setVisible(False)
        self.g_totals_table.horizontalHeader().setDefaultSectionSize(130)
        self.g_totals_table.horizontalHeader().setStretchLastSection(True)
        self.g_totals_table.verticalHeader().setVisible(False)
        self.g_totals_table.verticalHeader().setDefaultSectionSize(21)
        self.g_totals_table.verticalHeader().setMinimumSectionSize(1)
        self.verticalLayout.addWidget(self.g_totals_table)
        self.verticalLayout_12.addWidget(self.g_invoice_totals_groupbox)
        self.g_payments_groupbox = QtWidgets.QGroupBox(self.g_right_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_payments_groupbox.sizePolicy().hasHeightForWidth())
        self.g_payments_groupbox.setSizePolicy(sizePolicy)
        self.g_payments_groupbox.setObjectName("g_payments_groupbox")
        self.verticalLayout_13 = QtWidgets.QVBoxLayout(self.g_payments_groupbox)
        self.verticalLayout_13.setContentsMargins(10, 10, 10, 10)
        self.verticalLayout_13.setSpacing(1)
        self.verticalLayout_13.setObjectName("verticalLayout_13")
        self.g_payments_table = QtWidgets.QTableWidget(self.g_payments_groupbox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_payments_table.sizePolicy().hasHeightForWidth())
        self.g_payments_table.setSizePolicy(sizePolicy)
        self.g_payments_table.setMaximumSize(QtCore.QSize(16777215, 16777215))
        palette = QtGui.QPalette()
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.WindowText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ButtonText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Base, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(51, 153, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Highlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.HighlightedText, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.WindowText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ButtonText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Base, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(51, 153, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Highlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.HighlightedText, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.WindowText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ButtonText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Base, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(51, 153, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Highlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.HighlightedText, brush)
        self.g_payments_table.setPalette(palette)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_payments_table.setFont(font)
        self.g_payments_table.setStyleSheet("")
        self.g_payments_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_payments_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_payments_table.setDragEnabled(False)
        self.g_payments_table.setAlternatingRowColors(True)
        self.g_payments_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_payments_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_payments_table.setTextElideMode(QtCore.Qt.ElideRight)
        self.g_payments_table.setShowGrid(False)
        self.g_payments_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_payments_table.setRowCount(3)
        self.g_payments_table.setObjectName("g_payments_table")
        self.g_payments_table.setColumnCount(3)
        item = QtWidgets.QTableWidgetItem()
        self.g_payments_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_payments_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_payments_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_payments_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_payments_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_payments_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_payments_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_payments_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/sales_screen/x_grey"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        item.setIcon(icon4)
        self.g_payments_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_payments_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_payments_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon4)
        self.g_payments_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_payments_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_payments_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon4)
        self.g_payments_table.setItem(2, 2, item)
        self.g_payments_table.horizontalHeader().setVisible(False)
        self.g_payments_table.horizontalHeader().setDefaultSectionSize(120)
        self.g_payments_table.horizontalHeader().setStretchLastSection(True)
        self.g_payments_table.verticalHeader().setVisible(False)
        self.g_payments_table.verticalHeader().setDefaultSectionSize(30)
        self.g_payments_table.verticalHeader().setMinimumSectionSize(21)
        self.verticalLayout_13.addWidget(self.g_payments_table)
        self.verticalLayout_12.addWidget(self.g_payments_groupbox)
        self.g_priorpayments_groupbox = QtWidgets.QGroupBox(self.g_right_frame)
        self.g_priorpayments_groupbox.setObjectName("g_priorpayments_groupbox")
        self.verticalLayout_16 = QtWidgets.QVBoxLayout(self.g_priorpayments_groupbox)
        self.verticalLayout_16.setContentsMargins(10, 10, 10, 10)
        self.verticalLayout_16.setSpacing(2)
        self.verticalLayout_16.setObjectName("verticalLayout_16")
        self.g_priorpayments_table = QtWidgets.QTableWidget(self.g_priorpayments_groupbox)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_priorpayments_table.setFont(font)
        self.g_priorpayments_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_priorpayments_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_priorpayments_table.setAlternatingRowColors(True)
        self.g_priorpayments_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_priorpayments_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_priorpayments_table.setTextElideMode(QtCore.Qt.ElideRight)
        self.g_priorpayments_table.setShowGrid(True)
        self.g_priorpayments_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_priorpayments_table.setObjectName("g_priorpayments_table")
        self.g_priorpayments_table.setColumnCount(4)
        self.g_priorpayments_table.setRowCount(5)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_priorpayments_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon2)
        self.g_priorpayments_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_priorpayments_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon2)
        self.g_priorpayments_table.setItem(1, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_priorpayments_table.setItem(2, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon2)
        self.g_priorpayments_table.setItem(2, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_priorpayments_table.setItem(3, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon2)
        self.g_priorpayments_table.setItem(3, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorpayments_table.setItem(4, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_priorpayments_table.setItem(4, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon2)
        self.g_priorpayments_table.setItem(4, 3, item)
        self.g_priorpayments_table.horizontalHeader().setVisible(False)
        self.g_priorpayments_table.horizontalHeader().setDefaultSectionSize(97)
        self.g_priorpayments_table.horizontalHeader().setStretchLastSection(True)
        self.g_priorpayments_table.verticalHeader().setVisible(False)
        self.g_priorpayments_table.verticalHeader().setDefaultSectionSize(30)
        self.verticalLayout_16.addWidget(self.g_priorpayments_table)
        self.verticalLayout_12.addWidget(self.g_priorpayments_groupbox)
        self.g_priorrefunds_groupbox = QtWidgets.QGroupBox(self.g_right_frame)
        self.g_priorrefunds_groupbox.setObjectName("g_priorrefunds_groupbox")
        self.verticalLayout_17 = QtWidgets.QVBoxLayout(self.g_priorrefunds_groupbox)
        self.verticalLayout_17.setContentsMargins(10, 10, 10, 10)
        self.verticalLayout_17.setSpacing(2)
        self.verticalLayout_17.setObjectName("verticalLayout_17")
        self.g_priorrefunds_table = QtWidgets.QTableWidget(self.g_priorrefunds_groupbox)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_priorrefunds_table.setFont(font)
        self.g_priorrefunds_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_priorrefunds_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_priorrefunds_table.setAlternatingRowColors(True)
        self.g_priorrefunds_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_priorrefunds_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_priorrefunds_table.setTextElideMode(QtCore.Qt.ElideRight)
        self.g_priorrefunds_table.setShowGrid(False)
        self.g_priorrefunds_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_priorrefunds_table.setObjectName("g_priorrefunds_table")
        self.g_priorrefunds_table.setColumnCount(4)
        self.g_priorrefunds_table.setRowCount(5)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_priorrefunds_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon2)
        self.g_priorrefunds_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_priorrefunds_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon2)
        self.g_priorrefunds_table.setItem(1, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_priorrefunds_table.setItem(2, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon2)
        self.g_priorrefunds_table.setItem(2, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_priorrefunds_table.setItem(3, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon2)
        self.g_priorrefunds_table.setItem(3, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_priorrefunds_table.setItem(4, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_priorrefunds_table.setItem(4, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon2)
        self.g_priorrefunds_table.setItem(4, 3, item)
        self.g_priorrefunds_table.horizontalHeader().setVisible(False)
        self.g_priorrefunds_table.horizontalHeader().setDefaultSectionSize(97)
        self.g_priorrefunds_table.horizontalHeader().setStretchLastSection(True)
        self.g_priorrefunds_table.verticalHeader().setVisible(False)
        self.g_priorrefunds_table.verticalHeader().setDefaultSectionSize(30)
        self.verticalLayout_17.addWidget(self.g_priorrefunds_table)
        self.verticalLayout_12.addWidget(self.g_priorrefunds_groupbox)
        self.g_payment_totals_groupbox = QtWidgets.QGroupBox(self.g_right_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_payment_totals_groupbox.sizePolicy().hasHeightForWidth())
        self.g_payment_totals_groupbox.setSizePolicy(sizePolicy)
        self.g_payment_totals_groupbox.setObjectName("g_payment_totals_groupbox")
        self.verticalLayout_14 = QtWidgets.QVBoxLayout(self.g_payment_totals_groupbox)
        self.verticalLayout_14.setContentsMargins(10, 10, 10, 10)
        self.verticalLayout_14.setSpacing(2)
        self.verticalLayout_14.setObjectName("verticalLayout_14")
        self.g_totaldue_frame = QtWidgets.QFrame(self.g_payment_totals_groupbox)
        self.g_totaldue_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_totaldue_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_totaldue_frame.setObjectName("g_totaldue_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_totaldue_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_totaldue_label = QtWidgets.QLabel(self.g_totaldue_frame)
        self.g_totaldue_label.setObjectName("g_totaldue_label")
        self.horizontalLayout_8.addWidget(self.g_totaldue_label)
        spacerItem = QtWidgets.QSpacerItem(167, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_8.addItem(spacerItem)
        self.g_totaldue_amount_label = QtWidgets.QLabel(self.g_totaldue_frame)
        self.g_totaldue_amount_label.setObjectName("g_totaldue_amount_label")
        self.horizontalLayout_8.addWidget(self.g_totaldue_amount_label)
        self.verticalLayout_14.addWidget(self.g_totaldue_frame)
        self.g_payment_total_frame = QtWidgets.QFrame(self.g_payment_totals_groupbox)
        self.g_payment_total_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_payment_total_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_payment_total_frame.setObjectName("g_payment_total_frame")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.g_payment_total_frame)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.g_paymenttotal_label = QtWidgets.QLabel(self.g_payment_total_frame)
        self.g_paymenttotal_label.setObjectName("g_paymenttotal_label")
        self.horizontalLayout_9.addWidget(self.g_paymenttotal_label)
        spacerItem1 = QtWidgets.QSpacerItem(167, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem1)
        self.g_payment_total_amount_label = QtWidgets.QLabel(self.g_payment_total_frame)
        self.g_payment_total_amount_label.setObjectName("g_payment_total_amount_label")
        self.horizontalLayout_9.addWidget(self.g_payment_total_amount_label)
        self.verticalLayout_14.addWidget(self.g_payment_total_frame)
        self.g_line_frame = QtWidgets.QFrame(self.g_payment_totals_groupbox)
        self.g_line_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_line_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_line_frame.setObjectName("g_line_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_line_frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setSpacing(0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        spacerItem2 = QtWidgets.QSpacerItem(150, 1, QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem2)
        self.line = QtWidgets.QFrame(self.g_line_frame)
        self.line.setMinimumSize(QtCore.QSize(100, 0))
        self.line.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.line.setFrameShadow(QtWidgets.QFrame.Plain)
        self.line.setLineWidth(1)
        self.line.setFrameShape(QtWidgets.QFrame.HLine)
        self.line.setObjectName("line")
        self.horizontalLayout_4.addWidget(self.line)
        self.verticalLayout_14.addWidget(self.g_line_frame)
        self.g_balance_frame = QtWidgets.QFrame(self.g_payment_totals_groupbox)
        self.g_balance_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_balance_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_balance_frame.setObjectName("g_balance_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_balance_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_balancedue_label = QtWidgets.QLabel(self.g_balance_frame)
        self.g_balancedue_label.setObjectName("g_balancedue_label")
        self.horizontalLayout.addWidget(self.g_balancedue_label)
        spacerItem3 = QtWidgets.QSpacerItem(135, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem3)
        self.g_balance_label = QtWidgets.QLabel(self.g_balance_frame)
        self.g_balance_label.setObjectName("g_balance_label")
        self.horizontalLayout.addWidget(self.g_balance_label)
        self.verticalLayout_14.addWidget(self.g_balance_frame)
        self.verticalLayout_12.addWidget(self.g_payment_totals_groupbox)
        self.g_restaurant_save_button = QtWidgets.QPushButton(self.g_right_frame)
        self.g_restaurant_save_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_restaurant_save_button.setObjectName("g_restaurant_save_button")
        self.verticalLayout_12.addWidget(self.g_restaurant_save_button)
        self.g_finish_button = QtWidgets.QPushButton(self.g_right_frame)
        self.g_finish_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_finish_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon5 = QtGui.QIcon()
        icon5.addPixmap(QtGui.QPixmap(":/sales_screen/save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_finish_button.setIcon(icon5)
        self.g_finish_button.setIconSize(QtCore.QSize(24, 24))
        self.g_finish_button.setObjectName("g_finish_button")
        self.verticalLayout_12.addWidget(self.g_finish_button)
        self.horizontalLayout_3.addWidget(self.g_right_frame)
        self.verticalLayout_3.addLayout(self.horizontalLayout_3)

        self.retranslateUi(PaymentDialog)
        QtCore.QMetaObject.connectSlotsByName(PaymentDialog)

    def retranslateUi(self, PaymentDialog):
        _translate = QtCore.QCoreApplication.translate
        PaymentDialog.setWindowTitle(_translate("PaymentDialog", "Make Payment"))
        PaymentDialog.setProperty("balance_remaining_title_text", _translate("PaymentDialog", "Finish Payment"))
        PaymentDialog.setProperty("balance_remaining_msg_text", _translate("PaymentDialog", "Please provide payments\n"
"for the full balance due."))
        PaymentDialog.setProperty("preparing_credit_card_form_msg_text", _translate("PaymentDialog", "Preparing credit card form ..."))
        PaymentDialog.setProperty("balance_paid_msg_text", _translate("PaymentDialog", "The balance has already been paid."))
        PaymentDialog.setProperty("balance_paid_title_text", _translate("PaymentDialog", "Add Payment"))
        PaymentDialog.setProperty("voiding_payment_msg_text", _translate("PaymentDialog", "Voiding payment ..."))
        PaymentDialog.setProperty("error_title_text", _translate("PaymentDialog", "Error"))
        PaymentDialog.setProperty("refund_failed_msg_text", _translate("PaymentDialog", "Failed to issue refund.\n"
"\n"
"Please contact Pinogy support\n"
"at 877-360-7381."))
        PaymentDialog.setProperty("refunding_payment_msg_text", _translate("PaymentDialog", "Refunding payment ..."))
        PaymentDialog.setProperty("excessive_refund_msg_text", _translate("PaymentDialog", "Payment amounts cannot exceed the total refund."))
        PaymentDialog.setProperty("read_payment_methods_url_text", _translate("PaymentDialog", "/apps/cash_register/queries/read__tbl__payment_methods"))
        PaymentDialog.setProperty("read_payment_display_buttons_url_text", _translate("PaymentDialog", "/apps/cash_register/queries/read__tbl__payment_display_buttons"))
        PaymentDialog.setProperty("preparing_payment_dialog_msg_text", _translate("PaymentDialog", "Preparing payment dialog ..."))
        PaymentDialog.setProperty("read_payment_gateways_url_text", _translate("PaymentDialog", "/apps/cash_register/queries/read__tbl__payment_gateways"))
        PaymentDialog.setProperty("read_payment_processors_url_text", _translate("PaymentDialog", "/apps/cash_register/queries/read__tbl__payment_processors"))
        PaymentDialog.setProperty("read_payment_types_url_text", _translate("PaymentDialog", "/apps/cash_register/queries/read__tbl__payment_types"))
        PaymentDialog.setProperty("swipe_card_msg_text", _translate("PaymentDialog", "Swipe card now ..."))
        PaymentDialog.setProperty("swipe_card_title_text", _translate("PaymentDialog", "Swipe Card"))
        PaymentDialog.setProperty("manual_entry_msg_text", _translate("PaymentDialog", "Enter card number using\n"
"your PIN pad now ..."))
        PaymentDialog.setProperty("manual_entry_title_text", _translate("PaymentDialog", "Manual Entry"))
        PaymentDialog.setProperty("gateway_payment_failed_msg_text", _translate("PaymentDialog", "Payment gateway failed\n"
"to process this payment.\n"
"\n"
"Please contact Pinogy support\n"
"at 877-360-7381."))
        PaymentDialog.setProperty("missing_gateway_credentials_msg_text", _translate("PaymentDialog", "One or more payment gateways for this\n"
"location contain missing or incomplete\n"
"merchant credentials.\n"
"\n"
"Please provide all gateway credentials\n"
"using the Settings and Configuration tool."))
        PaymentDialog.setProperty("warning_title_text", _translate("PaymentDialog", "Warning"))
        PaymentDialog.setProperty("no_gateways_msg_text", _translate("PaymentDialog", "No payment gateways are set up for this location.\n"
"\n"
"If this is incorrect, please contact Pinogy support\n"
"at 877-360-7381.\n"
"\n"
"Otherwise, you may set up payment gateways\n"
"using the Settings and Configuration tool."))
        PaymentDialog.setProperty("gateway_payment_msg_text", _translate("PaymentDialog", "Processing gateway payment ..."))
        PaymentDialog.setProperty("partial_approval_msg_text", _translate("PaymentDialog", "The last payment amount was not\n"
"approved in its entirety.\n"
"\n"
"Only the approved amount has\n"
"been added as a payment."))
        PaymentDialog.setProperty("button_mapping_error_msg_text", _translate("PaymentDialog", "One or more payment methods could not be\n"
"assigned to a payment button.\n"
"\n"
"Please check your configured payment methods\n"
"using the Settings and Configuration app and\n"
"re-assign the missing methods to new buttons."))
        PaymentDialog.setProperty("trancloud_device_unknown_msg_text", _translate("PaymentDialog", "There is a Trancloud device configured with your POS to charge Credit and Debit cards but there are no configured pinpads which are required to do this.\n"
"\n"
"Contact Pinogy support at 877-360-7381 for assistance."))
        PaymentDialog.setProperty("customer_required_msg_text", _translate("PaymentDialog", "You must select a customer in order to\n"
"use this payment method."))
        PaymentDialog.setProperty("no_bonus_bucks_msg_text", _translate("PaymentDialog", "The selected customer does not have any Bonus Bucks."))
        PaymentDialog.setProperty("multiple_trancloud_devices_msg_text", _translate("PaymentDialog", "Multiple PIN-pads have been assigned to this POS device.\n"
"\n"
"Only a single PIN-pad can be used per device.\n"
"\n"
"Please recheck your Vantiv/Mercury configuration using\n"
"the Settings and Configuration app."))
        PaymentDialog.setProperty("processing_payment_msg_text", _translate("PaymentDialog", "Processing payment ..."))
        PaymentDialog.setProperty("void_payment_confirmation_msg_text", _translate("PaymentDialog", "You are about to void a payment.\n"
"This can not be undone.\n"
"\n"
"Are you sure you want to continue?"))
        PaymentDialog.setProperty("void_payments_confirmation_msg_text", _translate("PaymentDialog", "You are about to void these payments.\n"
"This can not be undone.\n"
"\n"
"Are you sure you want to continue?"))
        PaymentDialog.setProperty("back_to_sale_confirmation_msg_text", _translate("PaymentDialog", "A payment has already been applied.\n"
"\n"
"Are you sure you want to edit the invoice?"))
        PaymentDialog.setProperty("loyalty_integration_id_int", _translate("PaymentDialog", "3"))
        PaymentDialog.setProperty("read_integration_settings_url_text", _translate("PaymentDialog", "/apps/cash_register/queries/read__tbl__integration_settings"))
        PaymentDialog.setProperty("retrieving_settings_msg_text", _translate("PaymentDialog", "Retrieving settings ..."))
        PaymentDialog.setProperty("setting_retrieval_error_msg_text", _translate("PaymentDialog", "A required integration setting failed to load.\n"
"\n"
"Please contact Pinogy support at 877-360-7381."))
        PaymentDialog.setProperty("no_gift_card_balance_msg_text", _translate("PaymentDialog", "No cash amount is present on this gift card."))
        PaymentDialog.setProperty("gift_card_not_found_msg_text", _translate("PaymentDialog", "The gift card you entered could\n"
"not be found."))
        PaymentDialog.setProperty("create_transaction_payments_url_text", _translate("PaymentDialog", "/apps/cash_register/queries/create__tbl__transaction_payments"))
        PaymentDialog.setProperty("update_transaction_payments_url_text", _translate("PaymentDialog", "/apps/cash_register/queries/update__tbl__transaction_payments"))
        PaymentDialog.setProperty("refund_single_payment_msg_text", _translate("PaymentDialog", "Return %(currency_symbol)s%(amount).2f on original payment method of\n"
"          \"%(payment_method_name)s\"?\n"
"      "))
        PaymentDialog.setProperty("back_to_transaction_confirmation_msg_text", _translate("PaymentDialog", "A payment has already been applied.\n"
"\n"
"Are you sure you want to edit the invoice?"))
        PaymentDialog.setProperty("trancloud_no_pinpad_msg_text", _translate("PaymentDialog", "There is no pinpad associated with this device ({}).\n"
"\n"
"If you need to charge or refund a Credit or Debit card, the following devices have a pinpad configured.\n"
"\n"
""))
        PaymentDialog.setProperty("transaction_type_text", _translate("PaymentDialog", "Sale"))
        PaymentDialog.setProperty("feature_unavailable_title_text", _translate("PaymentDialog", "Feature Unavailable"))
        PaymentDialog.setProperty("returns_feature_unavailable_msg_text", _translate("PaymentDialog", "This feature is not available\n"
"for return transactions."))
        PaymentDialog.setProperty("invoice_tip_amount_msg_text", _translate("PaymentDialog", "$%(amount).2f of payment was from a tip on the original invoice."))
        PaymentDialog.setProperty("ha_authorized_customer_required_msg_text", _translate("PaymentDialog", "A customer with a valid house account\n"
"must be selected to use this payment method."))
        PaymentDialog.setProperty("ha_must_be_open_msg_text", _translate("PaymentDialog", "House account linked to a customer must be open."))
        PaymentDialog.setProperty("ha_amount_exceeds_limit_msg_text", _translate("PaymentDialog", "Payment amount exceeds the allowed limit on this house account."))
        PaymentDialog.setProperty("info_title_text", _translate("PaymentDialog", "Information"))
        PaymentDialog.setProperty("not_implemented_msg_text", _translate("PaymentDialog", "This functionality is under development"))
        PaymentDialog.setProperty("non_voidable_payment_msg_text", _translate("PaymentDialog", "This type of payments cannot be voided"))
        PaymentDialog.setProperty("pps_location_without_key_msg_text", _translate("PaymentDialog", "This location has no user and password specified to use Pinogy Payment Services."))
        PaymentDialog.setProperty("pps_no_pinpad_msg_text", _translate("PaymentDialog", "There is no Pinogy Payment Services pinpad associated with this device."))
        PaymentDialog.setProperty("proceed_with_inquire_msg_text", _translate("PaymentDialog", "We need to double check prior payment on this invoice."))
        PaymentDialog.setProperty("inquire_msg_text", _translate("PaymentDialog", "Checking prior payments..."))
        PaymentDialog.setProperty("list_house_account_customers_url_text", _translate("PaymentDialog", "/apps/any/queries/read__qpt__list_house_account_customers"))
        self.g_tenderamount_label.setText(_translate("PaymentDialog", "Payment Amount"))
        self.g_tenderamount_lineedit.setText(_translate("PaymentDialog", "42.31"))
        self.g_dynamicpayment7_button.setText(_translate("PaymentDialog", "Store\n"
"                                                   Account\n"
"                                               "))
        self.g_dynamicpayment8_button.setText(_translate("PaymentDialog", "Bonus\n"
"                                                   Bucks\n"
"                                               "))
        self.g_dynamicpayment9_button.setText(_translate("PaymentDialog", "Store\n"
"                                                   Credit\n"
"                                               "))
        self.g_dynamicpayment12_button.setText(_translate("PaymentDialog", "Google\n"
"                                                   Checkout\n"
"                                               "))
        self.g_dynamicpayment5_button.setText(_translate("PaymentDialog", "Credit\n"
"                                                   Card\n"
"                                               "))
        self.g_dynamicpayment4_button.setText(_translate("PaymentDialog", "Nextep\n"
"                                                   Funding\n"
"                                               "))
        self.g_dynamicpayment10_button.setText(_translate("PaymentDialog", "WAGS\n"
"                                                   Financing\n"
"                                               "))
        self.g_quickcashnext5_button.setText(_translate("PaymentDialog", "$5"))
        self.g_quickcashnext10_button.setText(_translate("PaymentDialog", "$10"))
        self.g_dynamicpayment2_button.setText(_translate("PaymentDialog", "Check"))
        self.g_dynamicpayment1_button.setText(_translate("PaymentDialog", "Cash"))
        self.g_dynamicpayment11_button.setText(_translate("PaymentDialog", "PayPal"))
        self.g_quickcashnext1_button.setText(_translate("PaymentDialog", "$1"))
        self.g_dynamicpayment3_button.setText(_translate("PaymentDialog", "Gift\n"
"                                                   Card\n"
"                                               "))
        self.g_dynamicpayment6_button.setText(_translate("PaymentDialog", "Other"))
        self.g_invoice_totals_groupbox.setTitle(_translate("PaymentDialog", "Invoice Totals"))
        item = self.g_totals_table.verticalHeaderItem(0)
        item.setText(_translate("PaymentDialog", "Subtotal"))
        item = self.g_totals_table.verticalHeaderItem(1)
        item.setText(_translate("PaymentDialog", "Discounts"))
        item = self.g_totals_table.verticalHeaderItem(2)
        item.setText(_translate("PaymentDialog", "Coupons"))
        item = self.g_totals_table.verticalHeaderItem(3)
        item.setText(_translate("PaymentDialog", "Tax"))
        item = self.g_totals_table.verticalHeaderItem(4)
        item.setText(_translate("PaymentDialog", "Tip"))
        item = self.g_totals_table.verticalHeaderItem(5)
        item.setText(_translate("PaymentDialog", "Fee"))
        item = self.g_totals_table.verticalHeaderItem(6)
        item.setText(_translate("PaymentDialog", "Border line"))
        item = self.g_totals_table.verticalHeaderItem(7)
        item.setText(_translate("PaymentDialog", "Total"))
        item = self.g_totals_table.horizontalHeaderItem(0)
        item.setText(_translate("PaymentDialog", "Description"))
        item = self.g_totals_table.horizontalHeaderItem(1)
        item.setText(_translate("PaymentDialog", "Amount"))
        item = self.g_totals_table.horizontalHeaderItem(2)
        item.setText(_translate("PaymentDialog", "Button"))
        __sortingEnabled = self.g_totals_table.isSortingEnabled()
        self.g_totals_table.setSortingEnabled(False)
        item = self.g_totals_table.item(0, 0)
        item.setText(_translate("PaymentDialog", "Subtotal"))
        item = self.g_totals_table.item(0, 1)
        item.setText(_translate("PaymentDialog", "212500.73"))
        item = self.g_totals_table.item(1, 0)
        item.setText(_translate("PaymentDialog", "Discounts"))
        item = self.g_totals_table.item(1, 1)
        item.setText(_translate("PaymentDialog", "300.00"))
        item = self.g_totals_table.item(2, 0)
        item.setText(_translate("PaymentDialog", "Coupons"))
        item = self.g_totals_table.item(2, 1)
        item.setText(_translate("PaymentDialog", "-3.00"))
        item = self.g_totals_table.item(3, 0)
        item.setText(_translate("PaymentDialog", "Tax"))
        item = self.g_totals_table.item(3, 1)
        item.setText(_translate("PaymentDialog", "100.00"))
        item = self.g_totals_table.item(4, 0)
        item.setText(_translate("PaymentDialog", "Tip"))
        item = self.g_totals_table.item(4, 1)
        item.setText(_translate("PaymentDialog", "5.00"))
        item = self.g_totals_table.item(5, 0)
        item.setText(_translate("PaymentDialog", "Fee"))
        item = self.g_totals_table.item(5, 1)
        item.setText(_translate("PaymentDialog", "0.00"))
        item = self.g_totals_table.item(7, 0)
        item.setText(_translate("PaymentDialog", "Total"))
        item = self.g_totals_table.item(7, 1)
        item.setText(_translate("PaymentDialog", "2500.00"))
        self.g_totals_table.setSortingEnabled(__sortingEnabled)
        self.g_payments_groupbox.setTitle(_translate("PaymentDialog", "Payments"))
        item = self.g_payments_table.verticalHeaderItem(0)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_payments_table.verticalHeaderItem(1)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_payments_table.verticalHeaderItem(2)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_payments_table.horizontalHeaderItem(0)
        item.setText(_translate("PaymentDialog", "Payment Type"))
        item = self.g_payments_table.horizontalHeaderItem(1)
        item.setText(_translate("PaymentDialog", "Amount"))
        item = self.g_payments_table.horizontalHeaderItem(2)
        item.setText(_translate("PaymentDialog", "Button"))
        __sortingEnabled = self.g_payments_table.isSortingEnabled()
        self.g_payments_table.setSortingEnabled(False)
        item = self.g_payments_table.item(0, 0)
        item.setText(_translate("PaymentDialog", "American Express Card (5614)"))
        item = self.g_payments_table.item(0, 1)
        item.setText(_translate("PaymentDialog", "50.00"))
        item = self.g_payments_table.item(1, 0)
        item.setText(_translate("PaymentDialog", "Personal Check"))
        item = self.g_payments_table.item(1, 1)
        item.setText(_translate("PaymentDialog", "25.00"))
        item = self.g_payments_table.item(2, 0)
        item.setText(_translate("PaymentDialog", "Cash"))
        item = self.g_payments_table.item(2, 1)
        item.setText(_translate("PaymentDialog", "10.45"))
        self.g_payments_table.setSortingEnabled(__sortingEnabled)
        self.g_priorpayments_groupbox.setTitle(_translate("PaymentDialog", "Prior Payments From Original Invoices"))
        item = self.g_priorpayments_table.verticalHeaderItem(0)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_priorpayments_table.verticalHeaderItem(1)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_priorpayments_table.verticalHeaderItem(2)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_priorpayments_table.verticalHeaderItem(3)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_priorpayments_table.verticalHeaderItem(4)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_priorpayments_table.horizontalHeaderItem(0)
        item.setText(_translate("PaymentDialog", "Invoice"))
        item = self.g_priorpayments_table.horizontalHeaderItem(1)
        item.setText(_translate("PaymentDialog", "Name"))
        item = self.g_priorpayments_table.horizontalHeaderItem(2)
        item.setText(_translate("PaymentDialog", "Amount"))
        item = self.g_priorpayments_table.horizontalHeaderItem(3)
        item.setText(_translate("PaymentDialog", "Delete"))
        __sortingEnabled = self.g_priorpayments_table.isSortingEnabled()
        self.g_priorpayments_table.setSortingEnabled(False)
        item = self.g_priorpayments_table.item(0, 0)
        item.setText(_translate("PaymentDialog", "1111"))
        item = self.g_priorpayments_table.item(0, 1)
        item.setText(_translate("PaymentDialog", "Cash"))
        item = self.g_priorpayments_table.item(0, 2)
        item.setText(_translate("PaymentDialog", "10.50"))
        item = self.g_priorpayments_table.item(1, 0)
        item.setText(_translate("PaymentDialog", "1111"))
        item = self.g_priorpayments_table.item(1, 1)
        item.setText(_translate("PaymentDialog", "Check"))
        item = self.g_priorpayments_table.item(1, 2)
        item.setText(_translate("PaymentDialog", "2.00"))
        item = self.g_priorpayments_table.item(2, 0)
        item.setText(_translate("PaymentDialog", "1111"))
        item = self.g_priorpayments_table.item(2, 1)
        item.setText(_translate("PaymentDialog", "Visa (**7100)"))
        item = self.g_priorpayments_table.item(2, 2)
        item.setText(_translate("PaymentDialog", "1.00"))
        item = self.g_priorpayments_table.item(3, 0)
        item.setText(_translate("PaymentDialog", "1112"))
        item = self.g_priorpayments_table.item(3, 1)
        item.setText(_translate("PaymentDialog", "Cash"))
        item = self.g_priorpayments_table.item(3, 2)
        item.setText(_translate("PaymentDialog", "100.00"))
        item = self.g_priorpayments_table.item(4, 0)
        item.setText(_translate("PaymentDialog", "1112"))
        item = self.g_priorpayments_table.item(4, 1)
        item.setText(_translate("PaymentDialog", "Cash"))
        item = self.g_priorpayments_table.item(4, 2)
        item.setText(_translate("PaymentDialog", "10.00"))
        self.g_priorpayments_table.setSortingEnabled(__sortingEnabled)
        self.g_priorrefunds_groupbox.setTitle(_translate("PaymentDialog", "Prior Refunds Against Original Invoices"))
        item = self.g_priorrefunds_table.verticalHeaderItem(0)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_priorrefunds_table.verticalHeaderItem(1)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_priorrefunds_table.verticalHeaderItem(2)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_priorrefunds_table.verticalHeaderItem(3)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_priorrefunds_table.verticalHeaderItem(4)
        item.setText(_translate("PaymentDialog", "New Row"))
        item = self.g_priorrefunds_table.horizontalHeaderItem(0)
        item.setText(_translate("PaymentDialog", "Invoice"))
        item = self.g_priorrefunds_table.horizontalHeaderItem(1)
        item.setText(_translate("PaymentDialog", "Name"))
        item = self.g_priorrefunds_table.horizontalHeaderItem(2)
        item.setText(_translate("PaymentDialog", "Amount"))
        item = self.g_priorrefunds_table.horizontalHeaderItem(3)
        item.setText(_translate("PaymentDialog", "Delete"))
        __sortingEnabled = self.g_priorrefunds_table.isSortingEnabled()
        self.g_priorrefunds_table.setSortingEnabled(False)
        item = self.g_priorrefunds_table.item(0, 0)
        item.setText(_translate("PaymentDialog", "99999999"))
        item = self.g_priorrefunds_table.item(0, 1)
        item.setText(_translate("PaymentDialog", "Cash"))
        item = self.g_priorrefunds_table.item(0, 2)
        item.setText(_translate("PaymentDialog", "10.50"))
        item = self.g_priorrefunds_table.item(1, 0)
        item.setText(_translate("PaymentDialog", "1111"))
        item = self.g_priorrefunds_table.item(1, 1)
        item.setText(_translate("PaymentDialog", "Check"))
        item = self.g_priorrefunds_table.item(1, 2)
        item.setText(_translate("PaymentDialog", "2.00"))
        item = self.g_priorrefunds_table.item(2, 0)
        item.setText(_translate("PaymentDialog", "1111"))
        item = self.g_priorrefunds_table.item(2, 1)
        item.setText(_translate("PaymentDialog", "Visa (**7100)"))
        item = self.g_priorrefunds_table.item(2, 2)
        item.setText(_translate("PaymentDialog", "330019.57"))
        item = self.g_priorrefunds_table.item(3, 0)
        item.setText(_translate("PaymentDialog", "1112"))
        item = self.g_priorrefunds_table.item(3, 1)
        item.setText(_translate("PaymentDialog", "Cash"))
        item = self.g_priorrefunds_table.item(3, 2)
        item.setText(_translate("PaymentDialog", "759100.33"))
        item = self.g_priorrefunds_table.item(4, 0)
        item.setText(_translate("PaymentDialog", "1112"))
        item = self.g_priorrefunds_table.item(4, 1)
        item.setText(_translate("PaymentDialog", "Cash"))
        item = self.g_priorrefunds_table.item(4, 2)
        item.setText(_translate("PaymentDialog", "10.00"))
        self.g_priorrefunds_table.setSortingEnabled(__sortingEnabled)
        self.g_payment_totals_groupbox.setTitle(_translate("PaymentDialog", "Payment Totals"))
        self.g_totaldue_label.setText(_translate("PaymentDialog", "Total Due"))
        self.g_totaldue_amount_label.setText(_translate("PaymentDialog", "25.00"))
        self.g_paymenttotal_label.setText(_translate("PaymentDialog", "Payment Total"))
        self.g_payment_total_amount_label.setText(_translate("PaymentDialog", "35.00"))
        self.g_balancedue_label.setText(_translate("PaymentDialog", "Balance/Change Due"))
        self.g_balance_label.setText(_translate("PaymentDialog", "10.00"))
        self.g_restaurant_save_button.setText(_translate("PaymentDialog", "Save Order"))
        self.g_finish_button.setText(_translate("PaymentDialog", "Finish/Save Layaway"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    PaymentDialog = QtWidgets.QDialog()
    ui = Ui_PaymentDialog()
    ui.setupUi(PaymentDialog)
    PaymentDialog.show()
    sys.exit(app.exec_())
