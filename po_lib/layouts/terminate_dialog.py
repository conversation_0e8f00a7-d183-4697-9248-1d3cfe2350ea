# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'terminate_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_TerminateDialog(object):
    def setupUi(self, TerminateDialog):
        TerminateDialog.setObjectName("TerminateDialog")
        TerminateDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        TerminateDialog.resize(693, 397)
        TerminateDialog.setStyleSheet("\n"
"QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QPushButton {\n"
"    color: white;\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_closeprograms_button {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"}\n"
"\n"
"#g_closeprograms_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border: 2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_retry_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border: 2px solid #287599;\n"
"}\n"
"\n"
"#g_retry_button:pressed {\n"
"    background: #287599;\n"
"    border: 2px solid #287599;\n"
"}\n"
"\n"
"#g_programs_textedit {\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"#label {\n"
"    font-size: 13px;\n"
"    font-weight: bold;\n"
"}\n"
"")
        TerminateDialog.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(TerminateDialog)
        self.verticalLayout.setContentsMargins(18, 18, 18, 18)
        self.verticalLayout.setSpacing(18)
        self.verticalLayout.setObjectName("verticalLayout")
        self.label = QtWidgets.QLabel(TerminateDialog)
        self.label.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.label.setFont(font)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setWordWrap(True)
        self.label.setObjectName("label")
        self.verticalLayout.addWidget(self.label)
        self.g_programs_textedit = QtWidgets.QPlainTextEdit(TerminateDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_programs_textedit.setFont(font)
        self.g_programs_textedit.setTabChangesFocus(True)
        self.g_programs_textedit.setReadOnly(True)
        self.g_programs_textedit.setObjectName("g_programs_textedit")
        self.verticalLayout.addWidget(self.g_programs_textedit)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(18)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(TerminateDialog)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(116, 40))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_cancel_button.setFont(font)
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/terminate_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_retry_button = QtWidgets.QPushButton(TerminateDialog)
        self.g_retry_button.setMinimumSize(QtCore.QSize(122, 40))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_retry_button.setFont(font)
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/terminate_dialog/retry"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_retry_button.setIcon(icon1)
        self.g_retry_button.setIconSize(QtCore.QSize(32, 32))
        self.g_retry_button.setObjectName("g_retry_button")
        self.horizontalLayout.addWidget(self.g_retry_button)
        self.g_closeprograms_button = QtWidgets.QPushButton(TerminateDialog)
        self.g_closeprograms_button.setMinimumSize(QtCore.QSize(236, 40))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_closeprograms_button.setFont(font)
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/terminate_dialog/close_programs"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_closeprograms_button.setIcon(icon2)
        self.g_closeprograms_button.setIconSize(QtCore.QSize(24, 24))
        self.g_closeprograms_button.setObjectName("g_closeprograms_button")
        self.horizontalLayout.addWidget(self.g_closeprograms_button)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(TerminateDialog)
        QtCore.QMetaObject.connectSlotsByName(TerminateDialog)

    def retranslateUi(self, TerminateDialog):
        _translate = QtCore.QCoreApplication.translate
        TerminateDialog.setWindowTitle(_translate("TerminateDialog", "Close Programs"))
        TerminateDialog.setProperty("confirm_close_title_text", _translate("TerminateDialog", "Warning"))
        TerminateDialog.setProperty("confirm_close_msg_text", _translate("TerminateDialog", "Any unsaved data will be lost.\n"
"\n"
"Are you sure you wish to close these programs and run the update?"))
        self.label.setText(_translate("TerminateDialog", "The following Pinogy applications are running on your system and must be closed before the update can continue."))
        self.g_programs_textedit.setPlainText(_translate("TerminateDialog", "Frequent Buyer (fb.exe)\n"
"Setup Wizard (sw.exe)\n"
"Settings and Configuration (sac.exe)\n"
"Cash Register (cr.exe)"))
        self.g_cancel_button.setText(_translate("TerminateDialog", "  Cancel"))
        self.g_retry_button.setText(_translate("TerminateDialog", "  Retry"))
        self.g_closeprograms_button.setText(_translate("TerminateDialog", "  Close Programs For Me"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    TerminateDialog = QtWidgets.QDialog()
    ui = Ui_TerminateDialog()
    ui.setupUi(TerminateDialog)
    TerminateDialog.show()
    sys.exit(app.exec_())
