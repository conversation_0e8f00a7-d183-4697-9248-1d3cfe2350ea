# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'sales_screen.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_SalesScreen(object):
    def setupUi(self, SalesScreen):
        SalesScreen.setObjectName("SalesScreen")
        SalesScreen.resize(1498, 773)
        SalesScreen.setFocusPolicy(QtCore.Qt.NoFocus)
        SalesScreen.setAutoFillBackground(False)
        SalesScreen.setStyleSheet("\n"
"QTableView {\n"
"    /* Removes the dotted border from selected cells in a table widget. */\n"
"    outline: 0;\n"
"}\n"
"\n"
"QTableWidget::item:focus {\n"
"    /* Use the same color as the selection background, otherwise the focused item in a row will appear in a different color\n"
"    than in the rest of the row when it is selected. */\n"
"    background-color: rgb(13, 95, 208);\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QLabel {\n"
"    color: #000;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QDateTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
" }\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    font-size: 13px;\n"
"    font-weight: bold;\n"
"    \n"
"}\n"
"\n"
"QTabWidget::tab-bar {\n"
"   left: 0;\n"
"}\n"
"\n"
"#SalesScreen {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_nocustomer_label {\n"
"    /* When no customer is selected in the sales screen customer section, this causes the \"No Customer Selected\" label to shift upward such that it\n"
"        appears centered vertically within the customer section. */\n"
"    margin-top: -30px;\n"
"}\n"
"\n"
"#g_searchreturn_label,\n"
"#__g_searchreturn_label {\n"
"    color: #ed3940;\n"
"}\n"
"\n"
"#g_requiredaddons_table,\n"
"#__g_requiredaddons_table,\n"
"#g_requiredaddons_table::item:focus,\n"
"#__g_requiredaddons_table::item:focus,\n"
"#g_optionaladdons_table,\n"
"#__g_optionaladdons_table,\n"
"#g_optionaladdons_table::item:focus,\n"
"#__g_optionaladdons_table::item:focus {\n"
"    background: transparent;\n"
"       border: 0;\n"
"       font-weight: bold;\n"
"       }\n"
"\n"
"       #__g_customerreward_label,\n"
"       #g_customerreward_label {\n"
"       color: blue;\n"
"       }\n"
"\n"
"       #g_layawaydue_label,\n"
"       #__g_layawaydue_label,\n"
"       #g_deliverydate_label {\n"
"       /* Vertically positions the \"Layaway Due in N Days\" message between the \"Total\" table item\n"
"       and the \"Pay\" button. */\n"
"       margin-bottom: 3px;\n"
"       }\n"
"\n"
"       #g_register_widget,\n"
"       #__g_register_widget {\n"
"       background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"       }\n"
"\n"
"       #g_unified_search_frame,\n"
"       #__g_unified_search_frame {\n"
"       border:none;\n"
"}\n"
"\n"
"#g_osk_button,\n"
"#__g_osk_button {\n"
"    border:0px;\n"
"    background: none;\n"
"}\n"
"\n"
"#g_search_box_frame,\n"
"#__g_search_box_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_clearsearch_button,\n"
"#__g_clearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_productsearch_lineedit,\n"
"#__g_productsearch_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_search_combobox,\n"
"#__g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_search_combobox::drop-down,\n"
"#__g_search_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width: 20px;\n"
"}\n"
"\n"
"#g_register_items_frame,\n"
"#__g_register_items_frame {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_register_table,\n"
"#__g_register_table {\n"
"    border: 0px;\n"
"    border-bottom: 1px solid #073c83;\n"
"}\n"
"\n"
"\n"
"#g_register_table QHeaderView::section,\n"
"#__g_register_table QHeaderView::section {\n"
"    spacing: 15px;\n"
"    color: #000000;\n"
"    font-weight: bold;\n"
"    border: 0px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size: 12px;\n"
"    padding-top: 5px;\n"
"    padding-bottom: 5px;\n"
"    height: 15px;\n"
"}\n"
"\n"
"/*#g_register_table QHeaderView::section:first {\n"
"    border-top-left-radius: 10px;\n"
"}\n"
"\n"
"#g_register_table QHeaderView::section:last {\n"
"    border-top-right-radius: 10px;\n"
"}*/\n"
"\n"
"#g_posfunctions_frame {\n"
"    border: 0px;\n"
"    background: none;\n"
"}\n"
"\n"
"#g_posfunctions_frame QPushButton {\n"
"\n"
"    /* Default blue color for all buttons in the POS functions frame.\n"
"\n"
"    Buttons that need special colors or styling should be appear after this section.\n"
"    */\n"
"\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #0B54BB, stop: 1 #0e69e4);\n"
"    border: 2px solid #073c83;\n"
"    color: white;\n"
"    font-weight: bold;\n"
"    font-size: 11pt;\n"
"}\n"
"\n"
"\n"
"#g_posfunctions_frame #g_cancelsale_button,\n"
"#g_posfunctions_frame #__g_cancelsale_button,\n"
"#g_posfunctions_frame #g_cancelreturn_button,\n"
"#g_posfunctions_frame #__g_cancelreturn_button,\n"
"#g_posfunctions_frame #g_cancellayaway_button,\n"
"#g_posfunctions_frame #__g_cancellayaway_button,\n"
"#g_posfunctions_frame #g_abandonlayaway_button,\n"
"#g_posfunctions_frame #__g_abandonlayaway_button {\n"
"    /* Cancel buttons have a red color. */\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border: 2px solid #c80319;\n"
"    spacing: 10;\n"
"}\n"
"\n"
"#g_posfunctions_frame #g_abandonlayaway_button:disabled,\n"
"#g_posfunctions_frame #__g_abandonlayaway_button:disabled,\n"
"       #g_posfunctions_frame #g_cancellayaway_button:disabled,\n"
"       #g_posfunctions_frame #__g_cancellayaway_button:disabled,\n"
"       #g_posfunctions_frame #g_cancelsale_button:disabled,\n"
"       #g_posfunctions_frame #__g_cancelsale_button:disabled,\n"
"       #g_posfunctions_frame #g_cancelreturn_button:disabled,\n"
"       #g_posfunctions_frame #__g_cancelreturn_button:disabled,\n"
"       #g_posfunctions_frame #g_return_button:disabled,\n"
"       #g_posfunctions_frame #__g_return_button:disabled,\n"
"       #g_posfunctions_frame #g_openregister_button:disabled,\n"
"       #g_posfunctions_frame #__g_openregister_button:disabled,\n"
"       #g_posfunctions_frame #g_reschedule_button:disabled {\n"
"       background-color: rgb(164, 164, 164);\n"
"       border: None;\n"
"       }\n"
"\n"
"       #g_pay_button,\n"
"       #__g_pay_button,\n"
"       #g_refund_button,\n"
"       #__g_refund_button {\n"
"       /* Pay and Refund buttons have a green color. */\n"
"       background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #278938, stop: 1 #3e9e27);\n"
"    border: 2px solid #368A24;\n"
"}\n"
"\n"
"       /* Back button appears on Payment dlg */\n"
"       #g_posfunctions_frame #g_back_button {\n"
"       font-size: 15px;\n"
"       font-weight: bold;\n"
"       background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"       border: 2px solid #287599;\n"
"       }\n"
"       #g_posfunctions_frame #g_back_button:pressed {\n"
"       background: #287599;\n"
"       border:2px solid #287599;\n"
"       }\n"
"\n"
"#g_customer_frame,\n"
"#__g_customer_frame {\n"
"    background: #FFF;\n"
"    border: 0px;\n"
"    border-bottom: 1px solid #073c83;\n"
"}\n"
"\n"
"#g_customer_frame QPushButton,\n"
"#__g_customer_frame QPushButton {\n"
"    border: 0px;\n"
"    background: none;\n"
"}\n"
"\n"
"#g_customername_label,\n"
"#__g_customername_label {\n"
"    color: #337791;\n"
"}\n"
"\n"
"#g_customerphone_label,\n"
"#__g_customerphone_label {\n"
"    color: #337791;\n"
"}\n"
"\n"
"#g_totals_table,\n"
"#__g_totals_table {\n"
"    border: 0px;\n"
"    border-bottom: 1px solid #073c83;\n"
"    border-left: 1px solid #ddd;\n"
"}\n"
"\n"
"#g_totals_table::item:focus,\n"
"#__g_totals_table::item:focus {\n"
"    /* The totals table will not have any row selection capability but the item \"focus\" will still appear.\n"
"    The following directive causes the focus not to appear for any item in the totals table. */\n"
"    background-color: transparent;\n"
"}\n"
"\n"
"\n"
"#g_totals_table .QTableView::item,\n"
"#__g_totals_table .QTableView::item {\n"
"    border-bottom: 1px solid black;\n"
"}\n"
"\n"
"\n"
"#g_left_frame,\n"
"#__g_left_frame {\n"
"    border: 1px solid #073c83;\n"
"    border-radius: 10px;\n"
"    background-color:#DDE4E9;\n"
"}\n"
"\n"
"#g_right_frame,\n"
"#__g_right_frame  {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 10px;\n"
"    background-color: #DDE4E9;\n"
"    padding-top: 9px;\n"
"    padding-left: 5px;\n"
"    /* padding-bottom: 5px; */\n"
"}\n"
"\n"
"\n"
"#g_quicklinks_tabwidget QTabWidget::tab-bar,\n"
"#__g_quicklinks_tabwidget QTabWidget::tab-bar {\n"
"     left: 5px;\n"
" }\n"
"\n"
"#g_quicklinks_tabwidget QTabBar::tab,\n"
"#__g_quicklinks_tabwidget QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB;\n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height: 34px;\n"
"}\n"
"\n"
"#g_quicklinks_tabwidget QTabBar::tab:selected, QTabBar::tab:hover,\n"
"#__g_quicklinks_tabwidget QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"#g_quicklinks_tabwidget QTabBar::tab:selected,\n"
"#__g_quicklinks_tabwidget QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"#g_quicklinks_tabwidget,\n"
"#__g_quicklinks_tabwidget {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_products_widget,\n"
"#__g_products_widget,\n"
"#g_shortcuts_widget,\n"
"#__g_shortcuts_widget,\n"
"#g_addons_widget,\n"
"#g_keypad_widget,\n"
"#g_bundle_widget,\n"
"#g_invoicenotes_widget {\n"
"    border: 1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius: 0px;\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"\n"
"#g_products_frame,\n"
"#__g_products_frame {\n"
"    border: 1px solid #DDE4E9;\n"
"    border-radius: 10px;\n"
"    background-color: #ddd;\n"
"}\n"
"\n"
"#g_product_buttons_frame QPushButton,\n"
"#__g_product_buttons_frame QPushButton {\n"
"    background: none;\n"
"    border: 0px;\n"
"    color: white;\n"
"    font-weight: bold;\n"
"    font-size: 11pt;\n"
"}\n"
"\n"
"#g_product_buttons_frame QPushButton:hover,\n"
"#__g_product_buttons_frame QPushButton:hover {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #2980a9, stop: 1 #2980a9);\n"
"    border: 2px solid #215f7c;\n"
"}\n"
"\n"
"#g_product_buttons_frame QPushButton:pressed,\n"
"#__g_product_buttons_frame QPushButton:pressed {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #87CEEB, stop: 1 #fff);\n"
"    border: 1px solid #333;\n"
"}\n"
"\n"
"#g_product_buttons_frame,\n"
"#__g_product_buttons_frame {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_productsearchmode_combobox:disabled,\n"
"#__g_productsearchmode_combobox:disabled {\n"
"    color: rgb(187, 187, 187);\n"
"}\n"
"\n"
"#g_productsearchmode_combobox::drop-down,\n"
"#__g_productsearchmode_combobox::drop-down {\n"
"    background-image: url(:/sales_screen/combobox_down_arrow);\n"
"    height: 31px;\n"
"}\n"
"\n"
"#g_productsearchmode_combobox::drop-down:disabled,\n"
"#__g_productsearchmode_combobox::drop-down:disabled {\n"
"    background-image: url(:/sales_screen/disabled_combobox_down_arrow);\n"
"    height: 31px;\n"
"}\n"
"\n"
"#g_product_churro_button,\n"
"#__g_product_churro_button {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"    color: white;\n"
"    font-weight: bold;\n"
"    font-size: 9pt;\n"
"}\n"
"\n"
"#g_product_cinnamon_button,\n"
"#__g_product_cinnamon_button {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"}\n"
"\n"
"#g_product_ipad4_toolbutton,\n"
"#__g_product_ipad4_toolbutton {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"    color: white;\n"
"    font-weight: bold;\n"
"    font-size: 9pt;\n"
"}\n"
"\n"
"#g_shortcuts_frame,\n"
"#__g_shortcuts_frame {\n"
"    border: 1px solid #333;\n"
"    border-radius: 10px;\n"
"    background-color: #ddd;\n"
"}\n"
"\n"
"#g_shortcuts_button_frame,\n"
"#__g_shortcuts_button_frame {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_shortcuts_button_frame QToolButton,\n"
"#__g_shortcuts_button_frame QToolButton {\n"
"    border: 0px;\n"
"    color: white;\n"
"    font-weight: bold;\n"
"    font-size: 9pt;\n"
"}\n"
"\n"
"#g_reports_toolbutton,\n"
"#__g_reports_toolbutton {\n"
"    background: #9f00a7;\n"
"    border: 2px solid #7D0083;\n"
"    color: white;\n"
"}\n"
"#g_reports_toolbutton:pressed,\n"
"#__g_reports_toolbutton:pressed {\n"
"    background: #7D0083;\n"
"}\n"
"\n"
"#g_reports_toolbutton QToolButton:checked,\n"
"#__g_reports_toolbutton QToolButton:checked {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #9f00a7, stop:1 #7D0083);\n"
"}\n"
"\n"
"#g_timeclock_toolbutton,\n"
"#__g_timeclock_toolbutton {\n"
"    background: #bb1d48;\n"
"    border: 2px solid #97173A;\n"
"}\n"
"\n"
"#g_timeclock_toolbutton:pressed,\n"
"#__g_timeclock_toolbutton:pressed {\n"
"    background: #97173A;\n"
"}\n"
"\n"
"#g_delete_item_button,\n"
"#__g_delete_item_button {\n"
"    background: none;\n"
"    border: 0px;\n"
"}\n"
"\n"
"\n"
"#g_products_widget QToolButton,\n"
"#__g_products_widget QToolButton {\n"
"    border: 2px solid #073C83;\n"
"    background-color: #e6eff4;\n"
"    color: #000;\n"
"    font-size: 8pt;\n"
"    border-radius: 3px;\n"
"}\n"
"\n"
"#g_shortcuts_widget,\n"
"#__g_shortcuts_widget {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_shortcut_buttons_frame,\n"
"#__g_shortcut_buttons_frame {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_rewards_tablewidget,\n"
"#__g_rewards_tablewidget {\n"
"    selection-background-color: rgb(13, 95, 208);\n"
"    border: 0px;\n"
"    padding-top: 5px;\n"
"}\n"
"\n"
"#g_rewards_tablewidget::item:focus,\n"
"#__g_rewards_tablewidget::item:focus {\n"
"    background-color: rgb(13, 95, 208);;\n"
"    border: 1px solid #000000;;\n"
"    color: #ffffff;\n"
"}\n"
"\n"
"#g_rewards_tablewidget QHeaderView::section,\n"
"#__g_rewards_tablewidget QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color: #ffffff;;\n"
"    color: #000000;\n"
"    font-weight: bold;\n"
"    border: 0px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size: 12px;\n"
"    padding-top: 5px;\n"
"    padding-bottom: 5px;\n"
"    height: 15px;\n"
"}\n"
"\n"
"#g_shortcuts_scrollarea_widget,\n"
"#__g_shortcuts_scrollarea_widget {\n"
"    border: 0px;\n"
"    background: rgba(0,0,0,0.0);\n"
"}\n"
"\n"
"#g_links_scrollarea_widget,\n"
"#__g_links_scrollarea_widget {\n"
"    background-color: #DDE4E9;\n"
"}\n"
"\n"
"#g_promotions_scrollarea,\n"
"#__g_promotions_scrollarea {\n"
"    border:0px;\n"
"    background-color: #DDE4E9;\n"
"}\n"
"\n"
"\n"
"/* Start Keypad Styling */\n"
"\n"
"#g_itemkeypad_frame,\n"
"#__g_itemkeypad_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border: 1px solid #073c83;\n"
"    border-radius: 10px;\n"
"    background-color: #DDE4E9;\n"
"}\n"
"\n"
".QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"\n"
"#g_keypad_item_buttons_frame,\n"
"#__g_keypad_item_buttons_frame {\n"
"    border: 0px;\n"
"}\n"
"\n"
"\n"
"#g_keypad_item_buttons_frame .QPushButton,\n"
"#__g_keypad_item_buttons_frame .QPushButton {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border: 2px solid #287599;\n"
"    color: white;\n"
"    border-radius: 5px;\n"
"    margin: 1px;\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"#g_keypad_item_buttons_frame .QPushButton:hover,\n"
"#__g_keypad_item_buttons_frame .QPushButton:hover {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #2980a9, stop: 1 #2980a9);\n"
"    border: 2px solid #215f7c;\n"
"}\n"
"\n"
"#g_keypad_item_buttons_frame .QPushButton:pressed,\n"
"#__g_keypad_item_buttons_frame .QPushButton:pressed {\n"
"    background: #287599;\n"
"    border: 1px solid #287599;\n"
"}\n"
"\n"
"/* End Keypad Styling */\n"
"\n"
"#g_salediscount_button:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}\n"
"\n"
"/* POS Functions buttons */\n"
"#g_posfunctions_frame QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"/* Custom Details frame */\n"
"#g_customerdetails_frame QLabel {\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"#g_register_table {\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"#g_search_label, \n"
"#g_searchreturn_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"       #g_registerclosed_label {\n"
"       font-size: 24px;\n"
"       font-weight: bold;\n"
"       }\n"
"\n"
"       #g_layawaydue_label {\n"
"       font-size: 15px;\n"
"       font-weight: normal;\n"
"       }\n"
"       #g_deliverydate_label {\n"
"       font-size: 15px;\n"
"       font-weight: normal;\n"
"       }\n"
"\n"
"       #g_totals_table {\n"
"       font-size: 12px;\n"
"       }\n"
"\n"
"#g_warning_frame {\n"
"    background: yellow; \n"
"    border-top-left-radius: 10px;\n"
"    border-top-right-radius: 10px;\n"
"}\n"
"#g_warning_message_label {\n"
"    color: red;\n"
"}\n"
"#g_close_warning_message_button {\n"
"    background: yellow;\n"
"    border: 0px;\n"
"}")
        SalesScreen.setProperty("rewards_table_column_widths_list", ['260', '75', '86'])
        SalesScreen.setProperty("register_table_column_widths_list", ['46', '310', '35', '60', '27', '28'])
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/sales_screen/sales"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        SalesScreen.setProperty("sales_toolbutton_icon", icon)
        SalesScreen.setProperty("visible_or_hidden_controls_list", ['g_posbuttons_frame', 'g_back_button', 'g_drawerfunctions_button', 'g_openhold_button', 'g_return_button', 'g_cancelsale_button', 'g_cancelreturn_button', 'g_holdreturn_button', 'g_holdsale_button', 'g_returndiscount_button', 'g_salediscount_button', 'g_quote_button', 'g_layaway_button', 'g_layawaydue_label', 'g_itemkeypad_frame', 'g_registerclosed_label', 'g_pay_button', 'g_refund_button', 'g_search_label', 'g_searchreturn_label', 'g_cancellayaway_button', 'g_closelayaway_button', 'g_tipedit_button', 'g_layawaydiscount_button', 'g_abandonlayaway_button', 'g_massaction_button', 'g_start_delivery_button', 'g_picklist_button', 'g_reschedule_button', 'g_deliverydate_label', 'g_reorder_button', 'g_payatpickup_button'])
        SalesScreen.setProperty("enabled_or_disabled_controls_list", ['g_right_frame', 'g_cancellayaway_button', 'g_cancelsale_button', 'g_cancelreturn_button', 'g_tipedit_button', 'g_abandonlayaway_button'])
        SalesScreen.setProperty("register_closed.visible_controls_list", ['g_posbuttons_frame', 'g_registerclosed_label', 'g_drawerfunctions_button', 'g_search_label', 'g_tipedit_button'])
        SalesScreen.setProperty("register_closed.enabled_controls_list", [])
        SalesScreen.setProperty("register_closed.dependencies_list", [])
        SalesScreen.setProperty("register_open.visible_controls_list", ['g_posbuttons_frame', 'g_itemkeypad_frame', 'g_tipedit_button'])
        SalesScreen.setProperty("register_open.enabled_controls_list", ['g_right_frame', 'g_itemkeypad_frame'])
        SalesScreen.setProperty("register_open.dependencies_list", [])
        SalesScreen.setProperty("sale.visible_controls_list", ['g_pay_button', 'g_cancelsale_button', 'g_tipedit_button'])
        SalesScreen.setProperty("sale.enabled_controls_list", ['g_tipedit_button'])
        SalesScreen.setProperty("sale.dependencies_list", ['non_return'])
        SalesScreen.setProperty("sale_item_selected.visible_controls_list", ['g_salediscount_button', 'g_massaction_button', 'g_reorder_button'])
        SalesScreen.setProperty("sale_item_selected.enabled_controls_list", [])
        SalesScreen.setProperty("sale_item_selected.dependencies_list", ['sale'])
        SalesScreen.setProperty("sale_item_unselected.visible_controls_list", ['g_holdsale_button', 'g_quote_button', 'g_layaway_button', 'g_picklist_button', 'g_reschedule_button', 'g_deliverydate_label', 'g_payatpickup_button'])
        SalesScreen.setProperty("sale_item_unselected.enabled_controls_list", [])
        SalesScreen.setProperty("sale_item_unselected.dependencies_list", ['sale'])
        SalesScreen.setProperty("return.visible_controls_list", ['g_refund_button', 'g_searchreturn_label', 'g_cancelreturn_button', 'g_tipedit_button'])
        SalesScreen.setProperty("return.enabled_controls_list", [])
        SalesScreen.setProperty("return.dependencies_list", ['register_open'])
        SalesScreen.setProperty("no_transaction.visible_controls_list", ['g_drawerfunctions_button', 'g_openhold_button', 'g_return_button', 'g_start_delivery_button'])
        SalesScreen.setProperty("no_transaction.enabled_controls_list", [])
        SalesScreen.setProperty("no_transaction.dependencies_list", ['non_return'])
        SalesScreen.setProperty("return_item_selected.visible_controls_list", ['g_returndiscount_button', 'g_massaction_button'])
        SalesScreen.setProperty("return_item_selected.enabled_controls_list", [])
        SalesScreen.setProperty("return_item_selected.dependencies_list", ['return'])
        SalesScreen.setProperty("return_item_unselected.visible_controls_list", ['g_holdreturn_button'])
        SalesScreen.setProperty("return_item_unselected.enabled_controls_list", [])
        SalesScreen.setProperty("return_item_unselected.dependencies_list", ['return'])
        SalesScreen.setProperty("return_item_selected_with_cancel.visible_controls_list", [])
        SalesScreen.setProperty("return_item_selected_with_cancel.enabled_controls_list", ['g_cancelreturn_button'])
        SalesScreen.setProperty("return_item_selected_with_cancel.dependencies_list", ['return_item_selected'])
        SalesScreen.setProperty("return_item_unselected_with_cancel.visible_controls_list", [])
        SalesScreen.setProperty("return_item_unselected_with_cancel.dependencies_list", ['return_item_unselected'])
        SalesScreen.setProperty("return_item_unselected_with_cancel.enabled_controls_list", ['g_cancelreturn_button'])
        SalesScreen.setProperty("sale_item_unselected_with_cancel.visible_controls_list", [])
        SalesScreen.setProperty("sale_item_unselected_with_cancel.enabled_controls_list", ['g_cancelsale_button'])
        SalesScreen.setProperty("sale_item_unselected_with_cancel.dependencies_list", ['sale_item_unselected'])
        SalesScreen.setProperty("sale_item_selected_with_cancel.visible_controls_list", [])
        SalesScreen.setProperty("sale_item_selected_with_cancel.enabled_controls_list", ['g_cancelsale_button'])
        SalesScreen.setProperty("sale_item_selected_with_cancel.dependencies_list", ['sale_item_selected'])
        SalesScreen.setProperty("layaway.visible_controls_list", ['g_layawaydue_label', 'g_cancellayaway_button', 'g_pay_button', 'g_tipedit_button', 'g_abandonlayaway_button'])
        SalesScreen.setProperty("layaway.enabled_controls_list", ['g_tipedit_button'])
        SalesScreen.setProperty("layaway.dependencies_list", ['non_return'])
        SalesScreen.setProperty("non_return.visible_controls_list", ['g_search_label'])
        SalesScreen.setProperty("non_return.enabled_controls_list", [])
        SalesScreen.setProperty("non_return.dependencies_list", ['register_open'])
        SalesScreen.setProperty("invoice_display.visible_controls_list", [])
        SalesScreen.setProperty("invoice_display.enabled_controls_list", [])
        SalesScreen.setProperty("invoice_display.dependencies_list", [])
        SalesScreen.setProperty("layaway_item_selected.visible_controls_list", ['g_layawaydiscount_button', 'g_massaction_button'])
        SalesScreen.setProperty("layaway_item_selected.enabled_controls_list", [])
        SalesScreen.setProperty("layaway_item_selected.dependencies_list", ['layaway'])
        SalesScreen.setProperty("layaway_item_unselected.visible_controls_list", ['g_closelayaway_button', 'g_abandonlayaway_button'])
        SalesScreen.setProperty("layaway_item_unselected.enabled_controls_list", [])
        SalesScreen.setProperty("layaway_item_unselected.dependencies_list", ['layaway'])
        SalesScreen.setProperty("layaway_item_selected_with_abandon.visible_controls_list", [])
        SalesScreen.setProperty("layaway_item_selected_with_abandon.enabled_controls_list", ['g_cancellayaway_button'])
        SalesScreen.setProperty("layaway_item_selected_with_abandon.dependencies_list", ['layaway_item_selected'])
        SalesScreen.setProperty("layaway_item_unselected_with_abandon.visible_controls_list", [])
        SalesScreen.setProperty("layaway_item_unselected_with_abandon.enabled_controls_list", ['g_cancellayaway_button'])
        SalesScreen.setProperty("layaway_item_unselected_with_abandon.dependencies_list", ['layaway_item_unselected'])
        SalesScreen.setProperty("total_row_display_mode_color", QtGui.QColor(181, 217, 169))
        font = QtGui.QFont()
        font.setPointSize(20)
        SalesScreen.setProperty("total_bottom_row_display_mode_font", font)
        font = QtGui.QFont()
        font.setFamily("Gill Sans MT Condensed")
        font.setPointSize(15)
        SalesScreen.setProperty("display_mode_table_font", font)
        SalesScreen.setProperty("display_mode_visible_columns_list", ['0', '1', '2', '3', '5'])
        SalesScreen.setProperty("display_mode_hidden_columns_list", ['4'])
        SalesScreen.setProperty("display_mode_price_column_int", 3)
        SalesScreen.setProperty("display_mode_qty_column_int", 0)
        SalesScreen.setProperty("display_mode_visible_column_widths_list", ['69', '350', '40', '76', '28'])
        SalesScreen.setProperty("display_mode_totals_column_widths_list", ['285', '250'])
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/sales_screen/x_grey"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        SalesScreen.setProperty("g_xgrey_icon", icon1)
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/sales_screen/x_normal"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        SalesScreen.setProperty("g_xnormal_icon", icon2)
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/sales_screen/add"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        SalesScreen.setProperty("g_plus_icon", icon3)
        SalesScreen.setProperty("product_search_options_list", ['Smart Search::qp_smart_search::Scan barcode or enter search term and press Enter', '*Barcode::qp_prodbar_barcode::Scan/enter barcode or qty*barcode and press Enter', 'SKU::qp_product_id::Enter the product SKU and press Enter', 'Description::qp_prod_name::Enter the product description and press Enter'])
        SalesScreen.setProperty("display_mode_row_height_int", 30)
        SalesScreen.setProperty("required_settings_list", ['customer_display_enabled', 'questionnaire_enable', 'transaction_auto_logout', 'req_mgr_prc_override', 'req_mgr_disc_override_percent', 'tip_enabled', 'commission_enabled', 'commission_ask_for_on_txn', 'tray_journal_receipt', 'tray_closing_receipt', 'tray_opening_receipt', 'return_requires_customer', 'sale_requires_customer'])
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/sales_screen/edit"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        SalesScreen.setProperty("g_edit_icon", icon4)
        SalesScreen.setProperty("tip_product_id_int", -10004)
        self.g_leftright_glayout = QtWidgets.QGridLayout(SalesScreen)
        self.g_leftright_glayout.setContentsMargins(-1, 0, -1, -1)
        self.g_leftright_glayout.setObjectName("g_leftright_glayout")
        self.g_left_frame = QtWidgets.QFrame(SalesScreen)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_left_frame.sizePolicy().hasHeightForWidth())
        self.g_left_frame.setSizePolicy(sizePolicy)
        self.g_left_frame.setMinimumSize(QtCore.QSize(554, 0))
        self.g_left_frame.setMaximumSize(QtCore.QSize(554, 16777215))
        self.g_left_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_left_frame.setAutoFillBackground(False)
        self.g_left_frame.setStyleSheet("")
        self.g_left_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_left_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_left_frame.setLineWidth(0)
        self.g_left_frame.setObjectName("g_left_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_left_frame)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_register_items_frame = QtWidgets.QFrame(self.g_left_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_register_items_frame.sizePolicy().hasHeightForWidth())
        self.g_register_items_frame.setSizePolicy(sizePolicy)
        self.g_register_items_frame.setMinimumSize(QtCore.QSize(552, 0))
        self.g_register_items_frame.setMaximumSize(QtCore.QSize(552, 16777215))
        self.g_register_items_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_register_items_frame.setStyleSheet("")
        self.g_register_items_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_register_items_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_register_items_frame.setObjectName("g_register_items_frame")
        self.g_registeritems_vlayout = QtWidgets.QVBoxLayout(self.g_register_items_frame)
        self.g_registeritems_vlayout.setContentsMargins(0, 8, 0, 0)
        self.g_registeritems_vlayout.setSpacing(0)
        self.g_registeritems_vlayout.setObjectName("g_registeritems_vlayout")
        self.g_warning_frame = QtWidgets.QFrame(self.g_register_items_frame)
        self.g_warning_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_warning_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_warning_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_warning_frame.setObjectName("g_warning_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_warning_frame)
        self.horizontalLayout_11.setContentsMargins(12, 0, -1, 0)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.g_warning_message_label = QtWidgets.QLabel(self.g_warning_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_warning_message_label.sizePolicy().hasHeightForWidth())
        self.g_warning_message_label.setSizePolicy(sizePolicy)
        self.g_warning_message_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_warning_message_label.setWordWrap(True)
        self.g_warning_message_label.setObjectName("g_warning_message_label")
        self.horizontalLayout_11.addWidget(self.g_warning_message_label)
        self.g_close_warning_message_button = QtWidgets.QPushButton(self.g_warning_frame)
        self.g_close_warning_message_button.setText("")
        self.g_close_warning_message_button.setIcon(icon2)
        self.g_close_warning_message_button.setObjectName("g_close_warning_message_button")
        self.horizontalLayout_11.addWidget(self.g_close_warning_message_button)
        self.g_registeritems_vlayout.addWidget(self.g_warning_frame)
        self.g_register_table = QtWidgets.QTableWidget(self.g_register_items_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_register_table.sizePolicy().hasHeightForWidth())
        self.g_register_table.setSizePolicy(sizePolicy)
        self.g_register_table.setMinimumSize(QtCore.QSize(552, 0))
        self.g_register_table.setMaximumSize(QtCore.QSize(552, 16777215))
        self.g_register_table.setMouseTracking(True)
        self.g_register_table.setFocusPolicy(QtCore.Qt.WheelFocus)
        self.g_register_table.setStyleSheet("")
        self.g_register_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_register_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_register_table.setLineWidth(0)
        self.g_register_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOn)
        self.g_register_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_register_table.setAutoScroll(True)
        self.g_register_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_register_table.setDragDropOverwriteMode(False)
        self.g_register_table.setAlternatingRowColors(True)
        self.g_register_table.setSelectionMode(QtWidgets.QAbstractItemView.MultiSelection)
        self.g_register_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_register_table.setIconSize(QtCore.QSize(16, 16))
        self.g_register_table.setVerticalScrollMode(QtWidgets.QAbstractItemView.ScrollPerPixel)
        self.g_register_table.setShowGrid(False)
        self.g_register_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_register_table.setObjectName("g_register_table")
        self.g_register_table.setColumnCount(6)
        self.g_register_table.setRowCount(19)
        item = QtWidgets.QTableWidgetItem()
        item.setBackground(QtGui.QColor(255, 255, 255, 203))
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(14, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(15, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(16, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(17, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setVerticalHeaderItem(18, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignHCenter|QtCore.Qt.AlignTop)
        self.g_register_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_register_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_register_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_register_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(0, 5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(2, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(2, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(2, 5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(3, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(3, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(3, 5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setBackground(brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setForeground(brush)
        self.g_register_table.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(4, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(4, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setForeground(brush)
        self.g_register_table.setItem(4, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(4, 5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setForeground(brush)
        self.g_register_table.setItem(5, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(5, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(5, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setBackground(brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setForeground(brush)
        self.g_register_table.setItem(5, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(5, 5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setForeground(brush)
        self.g_register_table.setItem(6, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(6, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(6, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setForeground(brush)
        self.g_register_table.setItem(6, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(6, 5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(7, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setForeground(brush)
        self.g_register_table.setItem(8, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(8, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(8, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setForeground(brush)
        self.g_register_table.setItem(8, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(8, 5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setForeground(brush)
        self.g_register_table.setItem(9, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(9, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(9, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(9, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(9, 5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setForeground(brush)
        self.g_register_table.setItem(10, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(10, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(10, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(10, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(10, 5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(11, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(11, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setBackground(brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(11, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(12, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(12, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(12, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(13, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(13, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(13, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(13, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(14, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(14, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(14, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(14, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(15, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(15, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(15, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(15, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(16, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(16, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(16, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(16, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(17, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(17, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(17, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(17, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(18, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_register_table.setItem(18, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_register_table.setItem(18, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_register_table.setItem(18, 3, item)
        self.g_register_table.horizontalHeader().setVisible(True)
        self.g_register_table.horizontalHeader().setCascadingSectionResizes(False)
        self.g_register_table.horizontalHeader().setDefaultSectionSize(80)
        self.g_register_table.horizontalHeader().setStretchLastSection(True)
        self.g_register_table.verticalHeader().setVisible(False)
        self.g_register_table.verticalHeader().setHighlightSections(True)
        self.g_register_table.verticalHeader().setMinimumSectionSize(24)
        self.g_registeritems_vlayout.addWidget(self.g_register_table)
        self.verticalLayout.addWidget(self.g_register_items_frame)
        self.g_customertotals_frame = QtWidgets.QFrame(self.g_left_frame)
        self.g_customertotals_frame.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_customertotals_frame.sizePolicy().hasHeightForWidth())
        self.g_customertotals_frame.setSizePolicy(sizePolicy)
        self.g_customertotals_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_customertotals_frame.setMaximumSize(QtCore.QSize(551, 200))
        self.g_customertotals_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customertotals_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_customertotals_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_customertotals_frame.setLineWidth(0)
        self.g_customertotals_frame.setObjectName("g_customertotals_frame")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout(self.g_customertotals_frame)
        self.horizontalLayout_16.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_16.setSpacing(0)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.g_customer_frame = QtWidgets.QFrame(self.g_customertotals_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_customer_frame.sizePolicy().hasHeightForWidth())
        self.g_customer_frame.setSizePolicy(sizePolicy)
        self.g_customer_frame.setMinimumSize(QtCore.QSize(321, 200))
        self.g_customer_frame.setMaximumSize(QtCore.QSize(16777215, 200))
        self.g_customer_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customer_frame.setStyleSheet("")
        self.g_customer_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_customer_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_customer_frame.setLineWidth(0)
        self.g_customer_frame.setObjectName("g_customer_frame")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.g_customer_frame)
        self.verticalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_8.setSpacing(0)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.g_customercontrols_frame = QtWidgets.QFrame(self.g_customer_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_customercontrols_frame.sizePolicy().hasHeightForWidth())
        self.g_customercontrols_frame.setSizePolicy(sizePolicy)
        self.g_customercontrols_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_customercontrols_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customercontrols_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_customercontrols_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_customercontrols_frame.setObjectName("g_customercontrols_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_customercontrols_frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 10, 0)
        self.horizontalLayout_4.setSpacing(20)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem)
        self.g_searchcustomer_button = QtWidgets.QPushButton(self.g_customercontrols_frame)
        self.g_searchcustomer_button.setEnabled(True)
        self.g_searchcustomer_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_searchcustomer_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_searchcustomer_button.setStyleSheet("")
        self.g_searchcustomer_button.setText("")
        icon5 = QtGui.QIcon()
        icon5.addPixmap(QtGui.QPixmap(":/sales_screen/magnifier"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_searchcustomer_button.setIcon(icon5)
        self.g_searchcustomer_button.setIconSize(QtCore.QSize(18, 18))
        self.g_searchcustomer_button.setObjectName("g_searchcustomer_button")
        self.horizontalLayout_4.addWidget(self.g_searchcustomer_button)
        self.g_addcustomer_button = QtWidgets.QPushButton(self.g_customercontrols_frame)
        self.g_addcustomer_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_addcustomer_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_addcustomer_button.setStyleSheet("")
        self.g_addcustomer_button.setText("")
        self.g_addcustomer_button.setIcon(icon3)
        self.g_addcustomer_button.setIconSize(QtCore.QSize(18, 18))
        self.g_addcustomer_button.setObjectName("g_addcustomer_button")
        self.horizontalLayout_4.addWidget(self.g_addcustomer_button)
        self.g_editcustomer_button = QtWidgets.QPushButton(self.g_customercontrols_frame)
        self.g_editcustomer_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_editcustomer_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_editcustomer_button.setStyleSheet("")
        self.g_editcustomer_button.setText("")
        self.g_editcustomer_button.setIcon(icon4)
        self.g_editcustomer_button.setIconSize(QtCore.QSize(18, 18))
        self.g_editcustomer_button.setObjectName("g_editcustomer_button")
        self.horizontalLayout_4.addWidget(self.g_editcustomer_button)
        self.g_deletecustomer_button = QtWidgets.QPushButton(self.g_customercontrols_frame)
        self.g_deletecustomer_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_deletecustomer_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_deletecustomer_button.setStyleSheet("")
        self.g_deletecustomer_button.setText("")
        self.g_deletecustomer_button.setIcon(icon2)
        self.g_deletecustomer_button.setIconSize(QtCore.QSize(18, 18))
        self.g_deletecustomer_button.setObjectName("g_deletecustomer_button")
        self.horizontalLayout_4.addWidget(self.g_deletecustomer_button)
        self.verticalLayout_8.addWidget(self.g_customercontrols_frame)
        self.g_nocustomer_label = QtWidgets.QLabel(self.g_customer_frame)
        self.g_nocustomer_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(20)
        self.g_nocustomer_label.setFont(font)
        self.g_nocustomer_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_nocustomer_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_nocustomer_label.setObjectName("g_nocustomer_label")
        self.verticalLayout_8.addWidget(self.g_nocustomer_label)
        self.g_customerdetails_frame = QtWidgets.QFrame(self.g_customer_frame)
        self.g_customerdetails_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_customerdetails_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customerdetails_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_customerdetails_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_customerdetails_frame.setObjectName("g_customerdetails_frame")
        self.verticalLayout_9 = QtWidgets.QVBoxLayout(self.g_customerdetails_frame)
        self.verticalLayout_9.setContentsMargins(8, 0, 0, 2)
        self.verticalLayout_9.setSpacing(4)
        self.verticalLayout_9.setObjectName("verticalLayout_9")
        self.g_customername_frame = QtWidgets.QFrame(self.g_customerdetails_frame)
        self.g_customername_frame.setObjectName("g_customername_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_customername_frame)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setSpacing(8)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_customernameicon_label = QtWidgets.QLabel(self.g_customername_frame)
        self.g_customernameicon_label.setEnabled(True)
        self.g_customernameicon_label.setMinimumSize(QtCore.QSize(21, 21))
        self.g_customernameicon_label.setMaximumSize(QtCore.QSize(21, 21))
        self.g_customernameicon_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customernameicon_label.setText("")
        self.g_customernameicon_label.setPixmap(QtGui.QPixmap(":/sales_screen/customer_grey"))
        self.g_customernameicon_label.setScaledContents(True)
        self.g_customernameicon_label.setObjectName("g_customernameicon_label")
        self.horizontalLayout_5.addWidget(self.g_customernameicon_label)
        self.g_customername_label = QtWidgets.QLabel(self.g_customername_frame)
        self.g_customername_label.setMinimumSize(QtCore.QSize(0, 0))
        self.g_customername_label.setMaximumSize(QtCore.QSize(16777215, 21))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_customername_label.setFont(font)
        self.g_customername_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customername_label.setStyleSheet("")
        self.g_customername_label.setObjectName("g_customername_label")
        self.horizontalLayout_5.addWidget(self.g_customername_label)
        spacerItem1 = QtWidgets.QSpacerItem(68, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem1)
        self.verticalLayout_9.addWidget(self.g_customername_frame)
        self.g_customerphone_frame = QtWidgets.QFrame(self.g_customerdetails_frame)
        self.g_customerphone_frame.setObjectName("g_customerphone_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_customerphone_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(8)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_customerphoneicon_label = QtWidgets.QLabel(self.g_customerphone_frame)
        self.g_customerphoneicon_label.setMinimumSize(QtCore.QSize(21, 21))
        self.g_customerphoneicon_label.setMaximumSize(QtCore.QSize(21, 21))
        self.g_customerphoneicon_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customerphoneicon_label.setText("")
        self.g_customerphoneicon_label.setPixmap(QtGui.QPixmap(":/sales_screen/phone"))
        self.g_customerphoneicon_label.setScaledContents(True)
        self.g_customerphoneicon_label.setObjectName("g_customerphoneicon_label")
        self.horizontalLayout_6.addWidget(self.g_customerphoneicon_label)
        self.g_customerphone_label = QtWidgets.QLabel(self.g_customerphone_frame)
        self.g_customerphone_label.setMaximumSize(QtCore.QSize(16777215, 21))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_customerphone_label.setFont(font)
        self.g_customerphone_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customerphone_label.setStyleSheet("")
        self.g_customerphone_label.setObjectName("g_customerphone_label")
        self.horizontalLayout_6.addWidget(self.g_customerphone_label)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem2)
        self.g_petz_button = QtWidgets.QPushButton(self.g_customerphone_frame)
        self.g_petz_button.setMinimumSize(QtCore.QSize(80, 21))
        self.g_petz_button.setMaximumSize(QtCore.QSize(80, 21))
        self.g_petz_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_petz_button.setText("")
        icon6 = QtGui.QIcon()
        icon6.addPixmap(QtGui.QPixmap(":/logos/petz"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_petz_button.setIcon(icon6)
        self.g_petz_button.setIconSize(QtCore.QSize(60, 21))
        self.g_petz_button.setObjectName("g_petz_button")
        self.horizontalLayout_6.addWidget(self.g_petz_button)
        self.verticalLayout_9.addWidget(self.g_customerphone_frame)
        self.g_customeremail_frame = QtWidgets.QFrame(self.g_customerdetails_frame)
        self.g_customeremail_frame.setObjectName("g_customeremail_frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_customeremail_frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setSpacing(8)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_customeremailicon_label = QtWidgets.QLabel(self.g_customeremail_frame)
        self.g_customeremailicon_label.setMinimumSize(QtCore.QSize(21, 21))
        self.g_customeremailicon_label.setMaximumSize(QtCore.QSize(21, 21))
        self.g_customeremailicon_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customeremailicon_label.setText("")
        self.g_customeremailicon_label.setPixmap(QtGui.QPixmap(":/sales_screen/email"))
        self.g_customeremailicon_label.setScaledContents(True)
        self.g_customeremailicon_label.setObjectName("g_customeremailicon_label")
        self.horizontalLayout_7.addWidget(self.g_customeremailicon_label)
        self.g_customeremail_label = QtWidgets.QLabel(self.g_customeremail_frame)
        self.g_customeremail_label.setMaximumSize(QtCore.QSize(16777215, 21))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_customeremail_label.setFont(font)
        self.g_customeremail_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customeremail_label.setStyleSheet("")
        self.g_customeremail_label.setObjectName("g_customeremail_label")
        self.horizontalLayout_7.addWidget(self.g_customeremail_label)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem3)
        self.g_astro_button = QtWidgets.QPushButton(self.g_customeremail_frame)
        self.g_astro_button.setMinimumSize(QtCore.QSize(80, 21))
        self.g_astro_button.setMaximumSize(QtCore.QSize(60, 21))
        self.g_astro_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_astro_button.setText("")
        icon7 = QtGui.QIcon()
        icon7.addPixmap(QtGui.QPixmap(":/logos/astro"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_astro_button.setIcon(icon7)
        self.g_astro_button.setIconSize(QtCore.QSize(60, 21))
        self.g_astro_button.setObjectName("g_astro_button")
        self.horizontalLayout_7.addWidget(self.g_astro_button)
        self.verticalLayout_9.addWidget(self.g_customeremail_frame)
        self.g_customerwarning_frame = QtWidgets.QFrame(self.g_customerdetails_frame)
        self.g_customerwarning_frame.setObjectName("g_customerwarning_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_customerwarning_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(8)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_customerwarningicon_label = QtWidgets.QLabel(self.g_customerwarning_frame)
        self.g_customerwarningicon_label.setMinimumSize(QtCore.QSize(21, 21))
        self.g_customerwarningicon_label.setMaximumSize(QtCore.QSize(21, 21))
        self.g_customerwarningicon_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customerwarningicon_label.setText("")
        self.g_customerwarningicon_label.setPixmap(QtGui.QPixmap(":/sales_screen/warning"))
        self.g_customerwarningicon_label.setScaledContents(True)
        self.g_customerwarningicon_label.setObjectName("g_customerwarningicon_label")
        self.horizontalLayout_8.addWidget(self.g_customerwarningicon_label)
        self.g_customerwarning_label = QtWidgets.QLabel(self.g_customerwarning_frame)
        self.g_customerwarning_label.setMaximumSize(QtCore.QSize(16777215, 21))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_customerwarning_label.setFont(font)
        self.g_customerwarning_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customerwarning_label.setStyleSheet("")
        self.g_customerwarning_label.setObjectName("g_customerwarning_label")
        self.horizontalLayout_8.addWidget(self.g_customerwarning_label)
        spacerItem4 = QtWidgets.QSpacerItem(40, 21, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_8.addItem(spacerItem4)
        self.verticalLayout_9.addWidget(self.g_customerwarning_frame)
        self.g_customernotes_frame = QtWidgets.QFrame(self.g_customerdetails_frame)
        self.g_customernotes_frame.setObjectName("g_customernotes_frame")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.g_customernotes_frame)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setSpacing(8)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.g_customernotesicon_label = QtWidgets.QLabel(self.g_customernotes_frame)
        self.g_customernotesicon_label.setMinimumSize(QtCore.QSize(21, 21))
        self.g_customernotesicon_label.setMaximumSize(QtCore.QSize(21, 21))
        self.g_customernotesicon_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_customernotesicon_label.setText("")
        self.g_customernotesicon_label.setPixmap(QtGui.QPixmap(":/sales_screen/info"))
        self.g_customernotesicon_label.setScaledContents(True)
        self.g_customernotesicon_label.setObjectName("g_customernotesicon_label")
        self.horizontalLayout_9.addWidget(self.g_customernotesicon_label)
        self.g_customernotes_label = QtWidgets.QLabel(self.g_customernotes_frame)
        self.g_customernotes_label.setMaximumSize(QtCore.QSize(16777215, 21))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_customernotes_label.setFont(font)
        self.g_customernotes_label.setObjectName("g_customernotes_label")
        self.horizontalLayout_9.addWidget(self.g_customernotes_label)
        spacerItem5 = QtWidgets.QSpacerItem(68, 21, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem5)
        self.verticalLayout_9.addWidget(self.g_customernotes_frame)
        self.g_customerbonusbucks_frame = QtWidgets.QFrame(self.g_customerdetails_frame)
        self.g_customerbonusbucks_frame.setObjectName("g_customerbonusbucks_frame")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.g_customerbonusbucks_frame)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setSpacing(8)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.g_customerbonusbucksicon_label = QtWidgets.QLabel(self.g_customerbonusbucks_frame)
        self.g_customerbonusbucksicon_label.setMinimumSize(QtCore.QSize(21, 21))
        self.g_customerbonusbucksicon_label.setMaximumSize(QtCore.QSize(21, 21))
        self.g_customerbonusbucksicon_label.setText("")
        self.g_customerbonusbucksicon_label.setPixmap(QtGui.QPixmap(":/sales_screen/money"))
        self.g_customerbonusbucksicon_label.setScaledContents(True)
        self.g_customerbonusbucksicon_label.setObjectName("g_customerbonusbucksicon_label")
        self.horizontalLayout_10.addWidget(self.g_customerbonusbucksicon_label)
        self.g_customerbonusbucks_label = QtWidgets.QLabel(self.g_customerbonusbucks_frame)
        self.g_customerbonusbucks_label.setMaximumSize(QtCore.QSize(16777215, 21))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_customerbonusbucks_label.setFont(font)
        self.g_customerbonusbucks_label.setObjectName("g_customerbonusbucks_label")
        self.horizontalLayout_10.addWidget(self.g_customerbonusbucks_label)
        spacerItem6 = QtWidgets.QSpacerItem(78, 21, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_10.addItem(spacerItem6)
        self.verticalLayout_9.addWidget(self.g_customerbonusbucks_frame)
        spacerItem7 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_9.addItem(spacerItem7)
        self.verticalLayout_8.addWidget(self.g_customerdetails_frame)
        self.horizontalLayout_16.addWidget(self.g_customer_frame)
        self.verticalLayout_3 = QtWidgets.QVBoxLayout()
        self.verticalLayout_3.setSpacing(0)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_totals_table = QtWidgets.QTableWidget(self.g_customertotals_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.MinimumExpanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_totals_table.sizePolicy().hasHeightForWidth())
        self.g_totals_table.setSizePolicy(sizePolicy)
        self.g_totals_table.setMinimumSize(QtCore.QSize(231, 181))
        self.g_totals_table.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_totals_table.setMouseTracking(True)
        self.g_totals_table.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_totals_table.setStyleSheet("")
        self.g_totals_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_totals_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_totals_table.setLineWidth(0)
        self.g_totals_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_totals_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_totals_table.setAutoScrollMargin(16)
        self.g_totals_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_totals_table.setAlternatingRowColors(False)
        self.g_totals_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_totals_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_totals_table.setShowGrid(False)
        self.g_totals_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_totals_table.setObjectName("g_totals_table")
        self.g_totals_table.setColumnCount(3)
        self.g_totals_table.setRowCount(8)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(2, 2, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(3, 2, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(4, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon4)
        self.g_totals_table.setItem(4, 2, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(5, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(5, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(5, 2, item)
        item = QtWidgets.QTableWidgetItem()
        brush = QtGui.QBrush(QtGui.QColor(7, 60, 131))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setBackground(brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        item.setFlags(QtCore.Qt.ItemIsEnabled)
        self.g_totals_table.setItem(6, 0, item)
        item = QtWidgets.QTableWidgetItem()
        brush = QtGui.QBrush(QtGui.QColor(7, 60, 131))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setBackground(brush)
        self.g_totals_table.setItem(6, 1, item)
        item = QtWidgets.QTableWidgetItem()
        brush = QtGui.QBrush(QtGui.QColor(7, 60, 131))
        brush.setStyle(QtCore.Qt.SolidPattern)
        item.setBackground(brush)
        self.g_totals_table.setItem(6, 2, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_totals_table.setItem(7, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_totals_table.setItem(7, 1, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setFamily("Segoe UI")
        font.setPointSize(10)
        item.setFont(font)
        self.g_totals_table.setItem(7, 2, item)
        self.g_totals_table.horizontalHeader().setVisible(False)
        self.g_totals_table.horizontalHeader().setDefaultSectionSize(95)
        self.g_totals_table.horizontalHeader().setStretchLastSection(True)
        self.g_totals_table.verticalHeader().setVisible(False)
        self.g_totals_table.verticalHeader().setDefaultSectionSize(16)
        self.g_totals_table.verticalHeader().setMinimumSectionSize(16)
        self.g_totals_table.verticalHeader().setStretchLastSection(False)
        self.verticalLayout_3.addWidget(self.g_totals_table)
        self.g_deliverydate_label = QtWidgets.QLabel(self.g_customertotals_frame)
        self.g_deliverydate_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_deliverydate_label.setObjectName("g_deliverydate_label")
        self.verticalLayout_3.addWidget(self.g_deliverydate_label)
        self.g_layawaydue_label = QtWidgets.QLabel(self.g_customertotals_frame)
        self.g_layawaydue_label.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_layawaydue_label.setFont(font)
        self.g_layawaydue_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_layawaydue_label.setTextInteractionFlags(QtCore.Qt.NoTextInteraction)
        self.g_layawaydue_label.setObjectName("g_layawaydue_label")
        self.verticalLayout_3.addWidget(self.g_layawaydue_label)
        self.g_pay_button = QtWidgets.QPushButton(self.g_customertotals_frame)
        self.g_pay_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_pay_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_pay_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_pay_button.setStyleSheet("")
        icon8 = QtGui.QIcon()
        icon8.addPixmap(QtGui.QPixmap(":/sales_screen/pay"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_pay_button.setIcon(icon8)
        self.g_pay_button.setIconSize(QtCore.QSize(24, 24))
        self.g_pay_button.setObjectName("g_pay_button")
        self.verticalLayout_3.addWidget(self.g_pay_button)
        self.g_refund_button = QtWidgets.QPushButton(self.g_customertotals_frame)
        self.g_refund_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_refund_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_refund_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_refund_button.setStyleSheet("")
        self.g_refund_button.setIcon(icon8)
        self.g_refund_button.setIconSize(QtCore.QSize(24, 24))
        self.g_refund_button.setObjectName("g_refund_button")
        self.verticalLayout_3.addWidget(self.g_refund_button)
        self.horizontalLayout_16.addLayout(self.verticalLayout_3)
        self.verticalLayout.addWidget(self.g_customertotals_frame)
        self.g_posfunctions_frame = QtWidgets.QFrame(self.g_left_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_posfunctions_frame.sizePolicy().hasHeightForWidth())
        self.g_posfunctions_frame.setSizePolicy(sizePolicy)
        self.g_posfunctions_frame.setMinimumSize(QtCore.QSize(554, 0))
        self.g_posfunctions_frame.setMaximumSize(QtCore.QSize(554, 16777215))
        self.g_posfunctions_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_posfunctions_frame.setStyleSheet("")
        self.g_posfunctions_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_posfunctions_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_posfunctions_frame.setObjectName("g_posfunctions_frame")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_posfunctions_frame)
        self.verticalLayout_4.setContentsMargins(13, 0, 13, 9)
        self.verticalLayout_4.setSpacing(0)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_posbuttons_frame = QtWidgets.QFrame(self.g_posfunctions_frame)
        self.g_posbuttons_frame.setObjectName("g_posbuttons_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_posbuttons_frame)
        self.horizontalLayout_3.setContentsMargins(0, 9, 0, 0)
        self.horizontalLayout_3.setSpacing(9)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_drawerfunctions_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_drawerfunctions_button.setMinimumSize(QtCore.QSize(0, 40))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_drawerfunctions_button.setFont(font)
        self.g_drawerfunctions_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_drawerfunctions_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_drawerfunctions_button.setStyleSheet("")
        self.g_drawerfunctions_button.setIconSize(QtCore.QSize(20, 20))
        self.g_drawerfunctions_button.setObjectName("g_drawerfunctions_button")
        self.horizontalLayout_3.addWidget(self.g_drawerfunctions_button)
        self.g_openhold_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_openhold_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_openhold_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_openhold_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_openhold_button.setStyleSheet("")
        self.g_openhold_button.setIconSize(QtCore.QSize(24, 24))
        self.g_openhold_button.setObjectName("g_openhold_button")
        self.horizontalLayout_3.addWidget(self.g_openhold_button)
        self.g_return_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_return_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_return_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_return_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_return_button.setStyleSheet("")
        self.g_return_button.setIconSize(QtCore.QSize(24, 24))
        self.g_return_button.setObjectName("g_return_button")
        self.horizontalLayout_3.addWidget(self.g_return_button)
        self.g_cancelreturn_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_cancelreturn_button.setEnabled(True)
        self.g_cancelreturn_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_cancelreturn_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancelreturn_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_cancelreturn_button.setStyleSheet("")
        icon9 = QtGui.QIcon()
        icon9.addPixmap(QtGui.QPixmap(":/sales_screen/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancelreturn_button.setIcon(icon9)
        self.g_cancelreturn_button.setIconSize(QtCore.QSize(16, 16))
        self.g_cancelreturn_button.setObjectName("g_cancelreturn_button")
        self.horizontalLayout_3.addWidget(self.g_cancelreturn_button)
        self.g_holdreturn_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_holdreturn_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_holdreturn_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_holdreturn_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_holdreturn_button.setStyleSheet("")
        self.g_holdreturn_button.setIconSize(QtCore.QSize(24, 24))
        self.g_holdreturn_button.setObjectName("g_holdreturn_button")
        self.horizontalLayout_3.addWidget(self.g_holdreturn_button)
        self.g_returndiscount_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_returndiscount_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_returndiscount_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_returndiscount_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_returndiscount_button.setStyleSheet("")
        self.g_returndiscount_button.setIconSize(QtCore.QSize(24, 24))
        self.g_returndiscount_button.setObjectName("g_returndiscount_button")
        self.horizontalLayout_3.addWidget(self.g_returndiscount_button)
        self.g_cancelsale_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_cancelsale_button.setEnabled(True)
        self.g_cancelsale_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_cancelsale_button.setMaximumSize(QtCore.QSize(120, 16777215))
        self.g_cancelsale_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancelsale_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_cancelsale_button.setStyleSheet("")
        self.g_cancelsale_button.setIcon(icon9)
        self.g_cancelsale_button.setObjectName("g_cancelsale_button")
        self.horizontalLayout_3.addWidget(self.g_cancelsale_button)
        self.g_salediscount_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_salediscount_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_salediscount_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_salediscount_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_salediscount_button.setStyleSheet("")
        self.g_salediscount_button.setIconSize(QtCore.QSize(24, 24))
        self.g_salediscount_button.setObjectName("g_salediscount_button")
        self.horizontalLayout_3.addWidget(self.g_salediscount_button)
        self.g_holdsale_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_holdsale_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_holdsale_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_holdsale_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_holdsale_button.setStyleSheet("")
        self.g_holdsale_button.setIconSize(QtCore.QSize(24, 24))
        self.g_holdsale_button.setObjectName("g_holdsale_button")
        self.horizontalLayout_3.addWidget(self.g_holdsale_button)
        self.g_quote_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_quote_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_quote_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_quote_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_quote_button.setStyleSheet("")
        self.g_quote_button.setIconSize(QtCore.QSize(24, 24))
        self.g_quote_button.setObjectName("g_quote_button")
        self.horizontalLayout_3.addWidget(self.g_quote_button)
        self.g_layaway_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_layaway_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_layaway_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_layaway_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_layaway_button.setStyleSheet("")
        self.g_layaway_button.setIconSize(QtCore.QSize(24, 24))
        self.g_layaway_button.setObjectName("g_layaway_button")
        self.horizontalLayout_3.addWidget(self.g_layaway_button)
        self.g_cancellayaway_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_cancellayaway_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_cancellayaway_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancellayaway_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_cancellayaway_button.setStyleSheet("")
        self.g_cancellayaway_button.setIcon(icon9)
        self.g_cancellayaway_button.setIconSize(QtCore.QSize(16, 16))
        self.g_cancellayaway_button.setObjectName("g_cancellayaway_button")
        self.horizontalLayout_3.addWidget(self.g_cancellayaway_button)
        self.g_abandonlayaway_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_abandonlayaway_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_abandonlayaway_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_abandonlayaway_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_abandonlayaway_button.setStyleSheet("")
        self.g_abandonlayaway_button.setIconSize(QtCore.QSize(24, 24))
        self.g_abandonlayaway_button.setObjectName("g_abandonlayaway_button")
        self.horizontalLayout_3.addWidget(self.g_abandonlayaway_button)
        self.g_closelayaway_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_closelayaway_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_closelayaway_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_closelayaway_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_closelayaway_button.setStyleSheet("")
        self.g_closelayaway_button.setIconSize(QtCore.QSize(24, 24))
        self.g_closelayaway_button.setObjectName("g_closelayaway_button")
        self.horizontalLayout_3.addWidget(self.g_closelayaway_button)
        self.g_layawaydiscount_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_layawaydiscount_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_layawaydiscount_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_layawaydiscount_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_layawaydiscount_button.setStyleSheet("")
        self.g_layawaydiscount_button.setIconSize(QtCore.QSize(24, 24))
        self.g_layawaydiscount_button.setObjectName("g_layawaydiscount_button")
        self.horizontalLayout_3.addWidget(self.g_layawaydiscount_button)
        self.g_massaction_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_massaction_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_massaction_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_massaction_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_massaction_button.setIconSize(QtCore.QSize(24, 24))
        self.g_massaction_button.setObjectName("g_massaction_button")
        self.horizontalLayout_3.addWidget(self.g_massaction_button)
        self.g_back_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_back_button.setMinimumSize(QtCore.QSize(0, 40))
        icon10 = QtGui.QIcon()
        icon10.addPixmap(QtGui.QPixmap(":/sales_screen/backspace"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_back_button.setIcon(icon10)
        self.g_back_button.setIconSize(QtCore.QSize(24, 24))
        self.g_back_button.setObjectName("g_back_button")
        self.horizontalLayout_3.addWidget(self.g_back_button)
        self.g_start_delivery_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_start_delivery_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_start_delivery_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_start_delivery_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_start_delivery_button.setIconSize(QtCore.QSize(24, 24))
        self.g_start_delivery_button.setObjectName("g_start_delivery_button")
        self.horizontalLayout_3.addWidget(self.g_start_delivery_button)
        self.g_picklist_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_picklist_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_picklist_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_picklist_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_picklist_button.setIconSize(QtCore.QSize(24, 24))
        self.g_picklist_button.setObjectName("g_picklist_button")
        self.horizontalLayout_3.addWidget(self.g_picklist_button)
        self.g_reschedule_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_reschedule_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_reschedule_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_reschedule_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_reschedule_button.setObjectName("g_reschedule_button")
        self.horizontalLayout_3.addWidget(self.g_reschedule_button)
        self.g_payatpickup_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_payatpickup_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_payatpickup_button.setObjectName("g_payatpickup_button")
        self.horizontalLayout_3.addWidget(self.g_payatpickup_button)
        self.g_reorder_button = QtWidgets.QPushButton(self.g_posbuttons_frame)
        self.g_reorder_button.setMinimumSize(QtCore.QSize(0, 40))
        self.g_reorder_button.setObjectName("g_reorder_button")
        self.horizontalLayout_3.addWidget(self.g_reorder_button)
        self.verticalLayout_4.addWidget(self.g_posbuttons_frame)
        self.verticalLayout.addWidget(self.g_posfunctions_frame)
        self.g_register_items_frame.raise_()
        self.g_posfunctions_frame.raise_()
        self.g_customertotals_frame.raise_()
        self.g_leftright_glayout.addWidget(self.g_left_frame, 2, 0, 1, 1)
        self.g_progress_frame = QtWidgets.QFrame(SalesScreen)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 120, 80))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.g_leftright_glayout.addWidget(self.g_progress_frame, 0, 0, 1, 2)
        self.g_right_frame = QtWidgets.QFrame(SalesScreen)
        self.g_right_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_right_frame.setStyleSheet("")
        self.g_right_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_right_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_right_frame.setObjectName("g_right_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_right_frame)
        self.verticalLayout_2.setContentsMargins(3, 0, -1, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_quicklinks_tabwidget = QtWidgets.QTabWidget(self.g_right_frame)
        self.g_quicklinks_tabwidget.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.g_quicklinks_tabwidget.setFont(font)
        self.g_quicklinks_tabwidget.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_quicklinks_tabwidget.setStyleSheet("#__g_quicklinks_tabwidget {\n"
"border:0px;\n"
"}\n"
"\n"
"")
        self.g_quicklinks_tabwidget.setTabShape(QtWidgets.QTabWidget.Rounded)
        self.g_quicklinks_tabwidget.setObjectName("g_quicklinks_tabwidget")
        self.g_keypad_widget = QtWidgets.QWidget()
        self.g_keypad_widget.setObjectName("g_keypad_widget")
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout(self.g_keypad_widget)
        self.horizontalLayout_15.setContentsMargins(9, -1, -1, -1)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.g_search_frame = QtWidgets.QFrame(self.g_keypad_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_search_frame.sizePolicy().hasHeightForWidth())
        self.g_search_frame.setSizePolicy(sizePolicy)
        self.g_search_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_search_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_search_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_search_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_search_frame.setObjectName("g_search_frame")
        self.verticalLayout_11 = QtWidgets.QVBoxLayout(self.g_search_frame)
        self.verticalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_11.setSpacing(9)
        self.verticalLayout_11.setObjectName("verticalLayout_11")
        self.g_search_label = QtWidgets.QLabel(self.g_search_frame)
        self.g_search_label.setMinimumSize(QtCore.QSize(0, 20))
        self.g_search_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_search_label.setFont(font)
        self.g_search_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_search_label.setObjectName("g_search_label")
        self.verticalLayout_11.addWidget(self.g_search_label)
        self.g_searchreturn_label = QtWidgets.QLabel(self.g_search_frame)
        self.g_searchreturn_label.setMinimumSize(QtCore.QSize(0, 20))
        self.g_searchreturn_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_searchreturn_label.setFont(font)
        self.g_searchreturn_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_searchreturn_label.setObjectName("g_searchreturn_label")
        self.verticalLayout_11.addWidget(self.g_searchreturn_label)
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.g_productsearchmode_combobox = QtWidgets.QComboBox(self.g_search_frame)
        self.g_productsearchmode_combobox.setEnabled(True)
        self.g_productsearchmode_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_productsearchmode_combobox.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_productsearchmode_combobox.setMinimumContentsLength(0)
        self.g_productsearchmode_combobox.setObjectName("g_productsearchmode_combobox")
        self.g_productsearchmode_combobox.addItem("")
        self.g_productsearchmode_combobox.addItem("")
        self.g_productsearchmode_combobox.addItem("")
        self.g_productsearchmode_combobox.addItem("")
        self.horizontalLayout_13.addWidget(self.g_productsearchmode_combobox)
        self.g_search_box_frame = QtWidgets.QFrame(self.g_search_frame)
        self.g_search_box_frame.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_search_box_frame.sizePolicy().hasHeightForWidth())
        self.g_search_box_frame.setSizePolicy(sizePolicy)
        self.g_search_box_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_search_box_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_search_box_frame.setStyleSheet("")
        self.g_search_box_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_search_box_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_search_box_frame.setObjectName("g_search_box_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_search_box_frame)
        self.horizontalLayout.setContentsMargins(9, 0, -1, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_productsearch_lineedit = QtWidgets.QLineEdit(self.g_search_box_frame)
        self.g_productsearch_lineedit.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.g_productsearch_lineedit.setStyleSheet("")
        self.g_productsearch_lineedit.setText("")
        self.g_productsearch_lineedit.setObjectName("g_productsearch_lineedit")
        self.horizontalLayout.addWidget(self.g_productsearch_lineedit)
        self.g_clearsearch_button = QtWidgets.QPushButton(self.g_search_box_frame)
        self.g_clearsearch_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_clearsearch_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_clearsearch_button.setStyleSheet("")
        self.g_clearsearch_button.setText("")
        self.g_clearsearch_button.setIcon(icon2)
        self.g_clearsearch_button.setIconSize(QtCore.QSize(18, 18))
        self.g_clearsearch_button.setObjectName("g_clearsearch_button")
        self.horizontalLayout.addWidget(self.g_clearsearch_button)
        self.horizontalLayout_13.addWidget(self.g_search_box_frame)
        self.g_osk_button = QtWidgets.QPushButton(self.g_search_frame)
        self.g_osk_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_osk_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_osk_button.setStyleSheet("")
        self.g_osk_button.setText("")
        icon11 = QtGui.QIcon()
        icon11.addPixmap(QtGui.QPixmap(":/sales_screen/black_keyboard_small"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_osk_button.setIcon(icon11)
        self.g_osk_button.setIconSize(QtCore.QSize(40, 21))
        self.g_osk_button.setObjectName("g_osk_button")
        self.horizontalLayout_13.addWidget(self.g_osk_button)
        self.verticalLayout_11.addLayout(self.horizontalLayout_13)
        spacerItem8 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_11.addItem(spacerItem8)
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        spacerItem9 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem9)
        self.verticalLayout_7 = QtWidgets.QVBoxLayout()
        self.verticalLayout_7.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
        self.verticalLayout_7.setContentsMargins(-1, -1, -1, 12)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.g_registerclosed_label = QtWidgets.QLabel(self.g_search_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_registerclosed_label.setFont(font)
        self.g_registerclosed_label.setScaledContents(False)
        self.g_registerclosed_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_registerclosed_label.setWordWrap(True)
        self.g_registerclosed_label.setObjectName("g_registerclosed_label")
        self.verticalLayout_7.addWidget(self.g_registerclosed_label)
        self.g_itemkeypad_frame = QtWidgets.QFrame(self.g_search_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_itemkeypad_frame.sizePolicy().hasHeightForWidth())
        self.g_itemkeypad_frame.setSizePolicy(sizePolicy)
        self.g_itemkeypad_frame.setMinimumSize(QtCore.QSize(271, 0))
        self.g_itemkeypad_frame.setMaximumSize(QtCore.QSize(271, 345))
        self.g_itemkeypad_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_itemkeypad_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_itemkeypad_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_itemkeypad_frame.setObjectName("g_itemkeypad_frame")
        self.verticalLayout_10 = QtWidgets.QVBoxLayout(self.g_itemkeypad_frame)
        self.verticalLayout_10.setObjectName("verticalLayout_10")
        self.__g_keypad_item_buttons_frame = QtWidgets.QFrame(self.g_itemkeypad_frame)
        self.__g_keypad_item_buttons_frame.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_keypad_item_buttons_frame.sizePolicy().hasHeightForWidth())
        self.__g_keypad_item_buttons_frame.setSizePolicy(sizePolicy)
        self.__g_keypad_item_buttons_frame.setMinimumSize(QtCore.QSize(200, 250))
        self.__g_keypad_item_buttons_frame.setMaximumSize(QtCore.QSize(400, 500))
        self.__g_keypad_item_buttons_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.__g_keypad_item_buttons_frame.setStyleSheet("")
        self.__g_keypad_item_buttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_keypad_item_buttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_keypad_item_buttons_frame.setObjectName("__g_keypad_item_buttons_frame")
        self.gridLayout_6 = QtWidgets.QGridLayout(self.__g_keypad_item_buttons_frame)
        self.gridLayout_6.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_6.setSpacing(5)
        self.gridLayout_6.setObjectName("gridLayout_6")
        self.g_zero_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_zero_button.sizePolicy().hasHeightForWidth())
        self.g_zero_button.setSizePolicy(sizePolicy)
        self.g_zero_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_zero_button.setObjectName("g_zero_button")
        self.gridLayout_6.addWidget(self.g_zero_button, 3, 0, 1, 1)
        self.g_doublezero_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_doublezero_button.sizePolicy().hasHeightForWidth())
        self.g_doublezero_button.setSizePolicy(sizePolicy)
        self.g_doublezero_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_doublezero_button.setObjectName("g_doublezero_button")
        self.gridLayout_6.addWidget(self.g_doublezero_button, 3, 1, 1, 1)
        self.g_nine_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_nine_button.sizePolicy().hasHeightForWidth())
        self.g_nine_button.setSizePolicy(sizePolicy)
        self.g_nine_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_nine_button.setObjectName("g_nine_button")
        self.gridLayout_6.addWidget(self.g_nine_button, 0, 2, 1, 1)
        self.g_four_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_four_button.sizePolicy().hasHeightForWidth())
        self.g_four_button.setSizePolicy(sizePolicy)
        self.g_four_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_four_button.setObjectName("g_four_button")
        self.gridLayout_6.addWidget(self.g_four_button, 1, 0, 1, 1)
        self.g_five_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_five_button.sizePolicy().hasHeightForWidth())
        self.g_five_button.setSizePolicy(sizePolicy)
        self.g_five_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_five_button.setObjectName("g_five_button")
        self.gridLayout_6.addWidget(self.g_five_button, 1, 1, 1, 1)
        self.g_eight_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_eight_button.sizePolicy().hasHeightForWidth())
        self.g_eight_button.setSizePolicy(sizePolicy)
        self.g_eight_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_eight_button.setObjectName("g_eight_button")
        self.gridLayout_6.addWidget(self.g_eight_button, 0, 1, 1, 1)
        self.g_six_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_six_button.sizePolicy().hasHeightForWidth())
        self.g_six_button.setSizePolicy(sizePolicy)
        self.g_six_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_six_button.setObjectName("g_six_button")
        self.gridLayout_6.addWidget(self.g_six_button, 1, 2, 1, 1)
        self.g_seven_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_seven_button.sizePolicy().hasHeightForWidth())
        self.g_seven_button.setSizePolicy(sizePolicy)
        self.g_seven_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_seven_button.setObjectName("g_seven_button")
        self.gridLayout_6.addWidget(self.g_seven_button, 0, 0, 1, 1)
        self.g_enter_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_enter_button.sizePolicy().hasHeightForWidth())
        self.g_enter_button.setSizePolicy(sizePolicy)
        palette = QtGui.QPalette()
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(187, 226, 187))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ToolTipText, brush)
        self.g_enter_button.setPalette(palette)
        self.g_enter_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_enter_button.setObjectName("g_enter_button")
        self.gridLayout_6.addWidget(self.g_enter_button, 2, 3, 2, 1)
        self.g_one_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_one_button.sizePolicy().hasHeightForWidth())
        self.g_one_button.setSizePolicy(sizePolicy)
        self.g_one_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_one_button.setObjectName("g_one_button")
        self.gridLayout_6.addWidget(self.g_one_button, 2, 0, 1, 1)
        self.g_two_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_two_button.sizePolicy().hasHeightForWidth())
        self.g_two_button.setSizePolicy(sizePolicy)
        self.g_two_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_two_button.setObjectName("g_two_button")
        self.gridLayout_6.addWidget(self.g_two_button, 2, 1, 1, 1)
        self.g_three_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_three_button.sizePolicy().hasHeightForWidth())
        self.g_three_button.setSizePolicy(sizePolicy)
        self.g_three_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_three_button.setObjectName("g_three_button")
        self.gridLayout_6.addWidget(self.g_three_button, 2, 2, 1, 1)
        self.g_decimal_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_decimal_button.sizePolicy().hasHeightForWidth())
        self.g_decimal_button.setSizePolicy(sizePolicy)
        self.g_decimal_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_decimal_button.setObjectName("g_decimal_button")
        self.gridLayout_6.addWidget(self.g_decimal_button, 3, 2, 1, 1)
        self.g_backspace_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_backspace_button.sizePolicy().hasHeightForWidth())
        self.g_backspace_button.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_backspace_button.setFont(font)
        self.g_backspace_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_backspace_button.setText("")
        self.g_backspace_button.setIcon(icon10)
        self.g_backspace_button.setIconSize(QtCore.QSize(32, 32))
        self.g_backspace_button.setObjectName("g_backspace_button")
        self.gridLayout_6.addWidget(self.g_backspace_button, 0, 3, 1, 1)
        self.g_asterisk_button = QtWidgets.QPushButton(self.__g_keypad_item_buttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_asterisk_button.sizePolicy().hasHeightForWidth())
        self.g_asterisk_button.setSizePolicy(sizePolicy)
        self.g_asterisk_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_asterisk_button.setObjectName("g_asterisk_button")
        self.gridLayout_6.addWidget(self.g_asterisk_button, 1, 3, 1, 1)
        self.g_decimal_button.raise_()
        self.g_enter_button.raise_()
        self.g_nine_button.raise_()
        self.g_five_button.raise_()
        self.g_eight_button.raise_()
        self.g_seven_button.raise_()
        self.g_two_button.raise_()
        self.g_six_button.raise_()
        self.g_three_button.raise_()
        self.g_four_button.raise_()
        self.g_zero_button.raise_()
        self.g_one_button.raise_()
        self.g_doublezero_button.raise_()
        self.g_asterisk_button.raise_()
        self.g_backspace_button.raise_()
        self.verticalLayout_10.addWidget(self.__g_keypad_item_buttons_frame)
        self.verticalLayout_7.addWidget(self.g_itemkeypad_frame)
        self.horizontalLayout_14.addLayout(self.verticalLayout_7)
        spacerItem10 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem10)
        self.verticalLayout_11.addLayout(self.horizontalLayout_14)
        spacerItem11 = QtWidgets.QSpacerItem(20, 100, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_11.addItem(spacerItem11)
        self.horizontalLayout_15.addWidget(self.g_search_frame)
        self.g_quicklinks_tabwidget.addTab(self.g_keypad_widget, "")
        self.g_invoicenotes_widget = QtWidgets.QWidget()
        self.g_invoicenotes_widget.setObjectName("g_invoicenotes_widget")
        self.verticalLayout_13 = QtWidgets.QVBoxLayout(self.g_invoicenotes_widget)
        self.verticalLayout_13.setObjectName("verticalLayout_13")
        self.g_invoicenotes_textedit = QtWidgets.QPlainTextEdit(self.g_invoicenotes_widget)
        self.g_invoicenotes_textedit.setMaximumSize(QtCore.QSize(16777215, 200))
        self.g_invoicenotes_textedit.setTabChangesFocus(True)
        self.g_invoicenotes_textedit.setTextInteractionFlags(QtCore.Qt.LinksAccessibleByKeyboard|QtCore.Qt.LinksAccessibleByMouse|QtCore.Qt.TextBrowserInteraction|QtCore.Qt.TextEditable|QtCore.Qt.TextEditorInteraction|QtCore.Qt.TextSelectableByKeyboard|QtCore.Qt.TextSelectableByMouse)
        self.g_invoicenotes_textedit.setObjectName("g_invoicenotes_textedit")
        self.verticalLayout_13.addWidget(self.g_invoicenotes_textedit)
        spacerItem12 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_13.addItem(spacerItem12)
        self.g_quicklinks_tabwidget.addTab(self.g_invoicenotes_widget, "")
        self.g_addons_widget = QtWidgets.QWidget()
        self.g_addons_widget.setObjectName("g_addons_widget")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.g_addons_widget)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.g_addons_label = QtWidgets.QLabel(self.g_addons_widget)
        self.g_addons_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.g_addons_label.setFont(font)
        self.g_addons_label.setObjectName("g_addons_label")
        self.verticalLayout_5.addWidget(self.g_addons_label)
        self.g_requiredaddons_groupbox = QtWidgets.QGroupBox(self.g_addons_widget)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.g_requiredaddons_groupbox.setFont(font)
        self.g_requiredaddons_groupbox.setObjectName("g_requiredaddons_groupbox")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.g_requiredaddons_groupbox)
        self.verticalLayout_6.setContentsMargins(18, 18, 18, 18)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.g_requiredaddons_table = QtWidgets.QTableWidget(self.g_requiredaddons_groupbox)
        font = QtGui.QFont()
        font.setFamily("MS Shell Dlg 2")
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.g_requiredaddons_table.setFont(font)
        self.g_requiredaddons_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_requiredaddons_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_requiredaddons_table.setShowGrid(False)
        self.g_requiredaddons_table.setRowCount(3)
        self.g_requiredaddons_table.setObjectName("g_requiredaddons_table")
        self.g_requiredaddons_table.setColumnCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_requiredaddons_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_requiredaddons_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_requiredaddons_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_requiredaddons_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_requiredaddons_table.setItem(2, 0, item)
        self.g_requiredaddons_table.horizontalHeader().setVisible(False)
        self.g_requiredaddons_table.horizontalHeader().setStretchLastSection(True)
        self.g_requiredaddons_table.verticalHeader().setVisible(False)
        self.g_requiredaddons_table.verticalHeader().setDefaultSectionSize(23)
        self.g_requiredaddons_table.verticalHeader().setHighlightSections(False)
        self.verticalLayout_6.addWidget(self.g_requiredaddons_table)
        self.verticalLayout_5.addWidget(self.g_requiredaddons_groupbox)
        self.g_optionaladdons_groupbox = QtWidgets.QGroupBox(self.g_addons_widget)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.g_optionaladdons_groupbox.setFont(font)
        self.g_optionaladdons_groupbox.setObjectName("g_optionaladdons_groupbox")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_optionaladdons_groupbox)
        self.horizontalLayout_2.setContentsMargins(18, 18, 18, 18)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_optionaladdons_table = QtWidgets.QTableWidget(self.g_optionaladdons_groupbox)
        font = QtGui.QFont()
        font.setFamily("MS Shell Dlg 2")
        font.setPointSize(10)
        font.setBold(True)
        font.setWeight(75)
        self.g_optionaladdons_table.setFont(font)
        self.g_optionaladdons_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_optionaladdons_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_optionaladdons_table.setShowGrid(False)
        self.g_optionaladdons_table.setRowCount(2)
        self.g_optionaladdons_table.setObjectName("g_optionaladdons_table")
        self.g_optionaladdons_table.setColumnCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_optionaladdons_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_optionaladdons_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setCheckState(QtCore.Qt.Checked)
        self.g_optionaladdons_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setCheckState(QtCore.Qt.Checked)
        self.g_optionaladdons_table.setItem(1, 0, item)
        self.g_optionaladdons_table.horizontalHeader().setVisible(False)
        self.g_optionaladdons_table.horizontalHeader().setStretchLastSection(True)
        self.g_optionaladdons_table.verticalHeader().setVisible(False)
        self.g_optionaladdons_table.verticalHeader().setDefaultSectionSize(23)
        self.g_optionaladdons_table.verticalHeader().setHighlightSections(False)
        self.horizontalLayout_2.addWidget(self.g_optionaladdons_table)
        self.verticalLayout_5.addWidget(self.g_optionaladdons_groupbox)
        self.g_quicklinks_tabwidget.addTab(self.g_addons_widget, "")
        self.__g_shortcuts_widget = QtWidgets.QWidget()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_shortcuts_widget.sizePolicy().hasHeightForWidth())
        self.__g_shortcuts_widget.setSizePolicy(sizePolicy)
        self.__g_shortcuts_widget.setStyleSheet("")
        self.__g_shortcuts_widget.setObjectName("__g_shortcuts_widget")
        self.gridLayout_12 = QtWidgets.QGridLayout(self.__g_shortcuts_widget)
        self.gridLayout_12.setContentsMargins(9, 9, 9, 9)
        self.gridLayout_12.setObjectName("gridLayout_12")
        self.__g_shortcuts_scrollarea = QtWidgets.QScrollArea(self.__g_shortcuts_widget)
        self.__g_shortcuts_scrollarea.setStyleSheet(".QScrollArea {\n"
"    border-top: 1px solid #000;\n"
"    background-color: #DDE4E9;\n"
"}\n"
"\n"
"\n"
".QToolButton {\n"
"    /* font-color:white; */\n"
"    font-size: 11pt;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QToolButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}")
        self.__g_shortcuts_scrollarea.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.__g_shortcuts_scrollarea.setFrameShadow(QtWidgets.QFrame.Plain)
        self.__g_shortcuts_scrollarea.setWidgetResizable(True)
        self.__g_shortcuts_scrollarea.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)
        self.__g_shortcuts_scrollarea.setObjectName("__g_shortcuts_scrollarea")
        self.__g_shortcuts_scrollarea_widget = QtWidgets.QWidget()
        self.__g_shortcuts_scrollarea_widget.setGeometry(QtCore.QRect(0, 0, 106, 301))
        self.__g_shortcuts_scrollarea_widget.setStyleSheet("")
        self.__g_shortcuts_scrollarea_widget.setObjectName("__g_shortcuts_scrollarea_widget")
        self.gridLayout_7 = QtWidgets.QGridLayout(self.__g_shortcuts_scrollarea_widget)
        self.gridLayout_7.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_7.setSpacing(5)
        self.gridLayout_7.setObjectName("gridLayout_7")
        self.g_rewards_groupbox = QtWidgets.QGroupBox(self.__g_shortcuts_scrollarea_widget)
        self.g_rewards_groupbox.setMinimumSize(QtCore.QSize(0, 95))
        self.g_rewards_groupbox.setMaximumSize(QtCore.QSize(16777215, 205))
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.g_rewards_groupbox.setFont(font)
        self.g_rewards_groupbox.setStyleSheet("")
        self.g_rewards_groupbox.setObjectName("g_rewards_groupbox")
        self.gridLayout_4 = QtWidgets.QGridLayout(self.g_rewards_groupbox)
        self.gridLayout_4.setContentsMargins(9, 9, 9, 9)
        self.gridLayout_4.setSpacing(0)
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.__g_rewards_tablewidget = QtWidgets.QTableWidget(self.g_rewards_groupbox)
        self.__g_rewards_tablewidget.setMinimumSize(QtCore.QSize(0, 150))
        self.__g_rewards_tablewidget.setMaximumSize(QtCore.QSize(16777215, 184))
        palette = QtGui.QPalette()
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.WindowText, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ButtonText, brush)
        brush = QtGui.QBrush(QtGui.QColor(13, 95, 208))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Highlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.HighlightedText, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.WindowText, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ButtonText, brush)
        brush = QtGui.QBrush(QtGui.QColor(13, 95, 208))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Highlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.HighlightedText, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.WindowText, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ButtonText, brush)
        brush = QtGui.QBrush(QtGui.QColor(13, 95, 208))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Highlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.HighlightedText, brush)
        self.__g_rewards_tablewidget.setPalette(palette)
        self.__g_rewards_tablewidget.setFocusPolicy(QtCore.Qt.NoFocus)
        self.__g_rewards_tablewidget.setStyleSheet("")
        self.__g_rewards_tablewidget.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.__g_rewards_tablewidget.setFrameShadow(QtWidgets.QFrame.Plain)
        self.__g_rewards_tablewidget.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.__g_rewards_tablewidget.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.__g_rewards_tablewidget.setAlternatingRowColors(True)
        self.__g_rewards_tablewidget.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.__g_rewards_tablewidget.setIconSize(QtCore.QSize(80, 80))
        self.__g_rewards_tablewidget.setGridStyle(QtCore.Qt.NoPen)
        self.__g_rewards_tablewidget.setObjectName("__g_rewards_tablewidget")
        self.__g_rewards_tablewidget.setColumnCount(3)
        self.__g_rewards_tablewidget.setRowCount(6)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.__g_rewards_tablewidget.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.__g_rewards_tablewidget.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.__g_rewards_tablewidget.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        icon12 = QtGui.QIcon()
        icon12.addPixmap(QtGui.QPixmap(":/sales_screen/use_now"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        item.setIcon(icon12)
        self.__g_rewards_tablewidget.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.__g_rewards_tablewidget.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon12)
        self.__g_rewards_tablewidget.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.__g_rewards_tablewidget.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon12)
        self.__g_rewards_tablewidget.setItem(2, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.__g_rewards_tablewidget.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon12)
        self.__g_rewards_tablewidget.setItem(3, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.__g_rewards_tablewidget.setItem(4, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon12)
        self.__g_rewards_tablewidget.setItem(4, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.__g_rewards_tablewidget.setItem(5, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.__g_rewards_tablewidget.setItem(5, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setIcon(icon12)
        self.__g_rewards_tablewidget.setItem(5, 2, item)
        self.__g_rewards_tablewidget.horizontalHeader().setVisible(False)
        self.__g_rewards_tablewidget.horizontalHeader().setDefaultSectionSize(150)
        self.__g_rewards_tablewidget.horizontalHeader().setStretchLastSection(True)
        self.__g_rewards_tablewidget.verticalHeader().setVisible(False)
        self.gridLayout_4.addWidget(self.__g_rewards_tablewidget, 0, 1, 1, 1)
        self.gridLayout_7.addWidget(self.g_rewards_groupbox, 6, 1, 1, 1)
        spacerItem13 = QtWidgets.QSpacerItem(20, 5, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.gridLayout_7.addItem(spacerItem13, 5, 1, 1, 1)
        self.__g_links_groupbox = QtWidgets.QGroupBox(self.__g_shortcuts_scrollarea_widget)
        self.__g_links_groupbox.setMinimumSize(QtCore.QSize(0, 93))
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.__g_links_groupbox.setFont(font)
        self.__g_links_groupbox.setObjectName("__g_links_groupbox")
        self.gridLayout_8 = QtWidgets.QGridLayout(self.__g_links_groupbox)
        self.gridLayout_8.setObjectName("gridLayout_8")
        self.__g_links_scrollarea = QtWidgets.QScrollArea(self.__g_links_groupbox)
        self.__g_links_scrollarea.setStyleSheet("#g_links_scrollarea, #__g_links_scrollarea {\n"
"    border:0px;\n"
"}")
        self.__g_links_scrollarea.setWidgetResizable(True)
        self.__g_links_scrollarea.setObjectName("__g_links_scrollarea")
        self.__g_links_scrollarea_widget = QtWidgets.QWidget()
        self.__g_links_scrollarea_widget.setGeometry(QtCore.QRect(0, 0, 280, 192))
        self.__g_links_scrollarea_widget.setStyleSheet("")
        self.__g_links_scrollarea_widget.setObjectName("__g_links_scrollarea_widget")
        self.gridLayout_13 = QtWidgets.QGridLayout(self.__g_links_scrollarea_widget)
        self.gridLayout_13.setObjectName("gridLayout_13")
        self.__g_special_order_toolbutton = QtWidgets.QToolButton(self.__g_links_scrollarea_widget)
        self.__g_special_order_toolbutton.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_special_order_toolbutton.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.__g_special_order_toolbutton.setFont(font)
        self.__g_special_order_toolbutton.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_special_order_toolbutton.setStyleSheet("")
        self.__g_special_order_toolbutton.setIconSize(QtCore.QSize(45, 45))
        self.__g_special_order_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_special_order_toolbutton.setObjectName("__g_special_order_toolbutton")
        self.gridLayout_13.addWidget(self.__g_special_order_toolbutton, 1, 2, 1, 1)
        self.__g_time_clock_toolbutton = QtWidgets.QToolButton(self.__g_links_scrollarea_widget)
        self.__g_time_clock_toolbutton.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_time_clock_toolbutton.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.__g_time_clock_toolbutton.setFont(font)
        self.__g_time_clock_toolbutton.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_time_clock_toolbutton.setStyleSheet("#toolButton_6 {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"color:white;\n"
"font-weight:bold;\n"
"font-size: 9pt;\n"
"}")
        self.__g_time_clock_toolbutton.setIconSize(QtCore.QSize(45, 45))
        self.__g_time_clock_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_time_clock_toolbutton.setObjectName("__g_time_clock_toolbutton")
        self.gridLayout_13.addWidget(self.__g_time_clock_toolbutton, 0, 0, 1, 1)
        self.__g_wags_apply_now_toolbutton = QtWidgets.QToolButton(self.__g_links_scrollarea_widget)
        self.__g_wags_apply_now_toolbutton.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_wags_apply_now_toolbutton.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.__g_wags_apply_now_toolbutton.setFont(font)
        self.__g_wags_apply_now_toolbutton.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_wags_apply_now_toolbutton.setStyleSheet("")
        self.__g_wags_apply_now_toolbutton.setIconSize(QtCore.QSize(45, 45))
        self.__g_wags_apply_now_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_wags_apply_now_toolbutton.setObjectName("__g_wags_apply_now_toolbutton")
        self.gridLayout_13.addWidget(self.__g_wags_apply_now_toolbutton, 0, 1, 1, 1)
        self.__g_wags_info_toolbutton = QtWidgets.QToolButton(self.__g_links_scrollarea_widget)
        self.__g_wags_info_toolbutton.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_wags_info_toolbutton.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.__g_wags_info_toolbutton.setFont(font)
        self.__g_wags_info_toolbutton.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_wags_info_toolbutton.setStyleSheet("")
        self.__g_wags_info_toolbutton.setIconSize(QtCore.QSize(45, 45))
        self.__g_wags_info_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_wags_info_toolbutton.setObjectName("__g_wags_info_toolbutton")
        self.gridLayout_13.addWidget(self.__g_wags_info_toolbutton, 0, 2, 1, 1)
        self.__g_lookup_stock_toolbutton = QtWidgets.QToolButton(self.__g_links_scrollarea_widget)
        self.__g_lookup_stock_toolbutton.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_lookup_stock_toolbutton.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.__g_lookup_stock_toolbutton.setFont(font)
        self.__g_lookup_stock_toolbutton.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_lookup_stock_toolbutton.setStyleSheet("")
        self.__g_lookup_stock_toolbutton.setIconSize(QtCore.QSize(45, 45))
        self.__g_lookup_stock_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_lookup_stock_toolbutton.setObjectName("__g_lookup_stock_toolbutton")
        self.gridLayout_13.addWidget(self.__g_lookup_stock_toolbutton, 1, 0, 1, 1)
        self.__g_adjust_stock_toolbutton = QtWidgets.QToolButton(self.__g_links_scrollarea_widget)
        self.__g_adjust_stock_toolbutton.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_adjust_stock_toolbutton.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.__g_adjust_stock_toolbutton.setFont(font)
        self.__g_adjust_stock_toolbutton.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_adjust_stock_toolbutton.setStyleSheet("")
        self.__g_adjust_stock_toolbutton.setIconSize(QtCore.QSize(45, 45))
        self.__g_adjust_stock_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_adjust_stock_toolbutton.setObjectName("__g_adjust_stock_toolbutton")
        self.gridLayout_13.addWidget(self.__g_adjust_stock_toolbutton, 1, 1, 1, 1)
        self.__g_links_scrollarea.setWidget(self.__g_links_scrollarea_widget)
        self.gridLayout_8.addWidget(self.__g_links_scrollarea, 0, 2, 1, 1)
        self.gridLayout_7.addWidget(self.__g_links_groupbox, 8, 1, 1, 1)
        self.g_promotions_groupbox = QtWidgets.QGroupBox(self.__g_shortcuts_scrollarea_widget)
        self.g_promotions_groupbox.setMinimumSize(QtCore.QSize(0, 93))
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.g_promotions_groupbox.setFont(font)
        self.g_promotions_groupbox.setObjectName("g_promotions_groupbox")
        self.gridLayout_9 = QtWidgets.QGridLayout(self.g_promotions_groupbox)
        self.gridLayout_9.setObjectName("gridLayout_9")
        self.__g_promotions_scrollarea = QtWidgets.QScrollArea(self.g_promotions_groupbox)
        self.__g_promotions_scrollarea.setStyleSheet("#g_promotions_scrollarea, #__g_promotions_scrollarea {\n"
"    border:0px;\n"
"}")
        self.__g_promotions_scrollarea.setWidgetResizable(True)
        self.__g_promotions_scrollarea.setObjectName("__g_promotions_scrollarea")
        self.scrollAreaWidgetContents_5 = QtWidgets.QWidget()
        self.scrollAreaWidgetContents_5.setGeometry(QtCore.QRect(0, 0, 280, 192))
        self.scrollAreaWidgetContents_5.setStyleSheet("#scrollAreaWidgetContents_5 {\n"
"                                  background-color: #DDE4E9;\n"
"                                  }\n"
"                              ")
        self.scrollAreaWidgetContents_5.setObjectName("scrollAreaWidgetContents_5")
        self.gridLayout_11 = QtWidgets.QGridLayout(self.scrollAreaWidgetContents_5)
        self.gridLayout_11.setObjectName("gridLayout_11")
        self.toolButton_21 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_5)
        self.toolButton_21.setMinimumSize(QtCore.QSize(80, 80))
        self.toolButton_21.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.toolButton_21.setFont(font)
        self.toolButton_21.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.toolButton_21.setStyleSheet("#toolButton_6 {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"color:white;\n"
"font-weight:bold;\n"
"font-size: 9pt;\n"
"}")
        self.toolButton_21.setIconSize(QtCore.QSize(45, 45))
        self.toolButton_21.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.toolButton_21.setObjectName("toolButton_21")
        self.gridLayout_11.addWidget(self.toolButton_21, 0, 0, 1, 1)
        self.__g_product_ipad4_toolbutton_78 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_5)
        self.__g_product_ipad4_toolbutton_78.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_78.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.__g_product_ipad4_toolbutton_78.setFont(font)
        self.__g_product_ipad4_toolbutton_78.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_78.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_78.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_78.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_78.setObjectName("__g_product_ipad4_toolbutton_78")
        self.gridLayout_11.addWidget(self.__g_product_ipad4_toolbutton_78, 0, 1, 1, 1)
        self.__g_product_ipad4_toolbutton_70 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_5)
        self.__g_product_ipad4_toolbutton_70.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_70.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.__g_product_ipad4_toolbutton_70.setFont(font)
        self.__g_product_ipad4_toolbutton_70.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_70.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_70.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_70.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_70.setObjectName("__g_product_ipad4_toolbutton_70")
        self.gridLayout_11.addWidget(self.__g_product_ipad4_toolbutton_70, 0, 2, 1, 1)
        self.__g_product_ipad4_toolbutton_77 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_5)
        self.__g_product_ipad4_toolbutton_77.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_77.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.__g_product_ipad4_toolbutton_77.setFont(font)
        self.__g_product_ipad4_toolbutton_77.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_77.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_77.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_77.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_77.setObjectName("__g_product_ipad4_toolbutton_77")
        self.gridLayout_11.addWidget(self.__g_product_ipad4_toolbutton_77, 1, 0, 1, 1)
        self.__g_product_ipad4_toolbutton_84 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_5)
        self.__g_product_ipad4_toolbutton_84.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_84.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.__g_product_ipad4_toolbutton_84.setFont(font)
        self.__g_product_ipad4_toolbutton_84.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_84.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_84.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_84.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_84.setObjectName("__g_product_ipad4_toolbutton_84")
        self.gridLayout_11.addWidget(self.__g_product_ipad4_toolbutton_84, 1, 1, 1, 1)
        self.__g_product_ipad4_toolbutton_83 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_5)
        self.__g_product_ipad4_toolbutton_83.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_83.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.__g_product_ipad4_toolbutton_83.setFont(font)
        self.__g_product_ipad4_toolbutton_83.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_83.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_83.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_83.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_83.setObjectName("__g_product_ipad4_toolbutton_83")
        self.gridLayout_11.addWidget(self.__g_product_ipad4_toolbutton_83, 1, 2, 1, 1)
        self.__g_promotions_scrollarea.setWidget(self.scrollAreaWidgetContents_5)
        self.gridLayout_9.addWidget(self.__g_promotions_scrollarea, 3, 0, 1, 1)
        self.gridLayout_7.addWidget(self.g_promotions_groupbox, 7, 1, 1, 1)
        self.__g_shortcuts_scrollarea.setWidget(self.__g_shortcuts_scrollarea_widget)
        self.gridLayout_12.addWidget(self.__g_shortcuts_scrollarea, 0, 0, 1, 1)
        self.g_quicklinks_tabwidget.addTab(self.__g_shortcuts_widget, "")
        self.__g_products_widget = QtWidgets.QWidget()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_products_widget.sizePolicy().hasHeightForWidth())
        self.__g_products_widget.setSizePolicy(sizePolicy)
        self.__g_products_widget.setStyleSheet("")
        self.__g_products_widget.setObjectName("__g_products_widget")
        self.gridLayout_3 = QtWidgets.QGridLayout(self.__g_products_widget)
        self.gridLayout_3.setContentsMargins(9, 9, 9, 9)
        self.gridLayout_3.setHorizontalSpacing(6)
        self.gridLayout_3.setVerticalSpacing(0)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.scrollArea = QtWidgets.QScrollArea(self.__g_products_widget)
        self.scrollArea.setStyleSheet(".QScrollArea {\n"
"    border-top: 1px solid #000;\n"
"\n"
"    background-color: #DDE4E9;\n"
"\n"
"}")
        self.scrollArea.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.scrollArea.setFrameShadow(QtWidgets.QFrame.Plain)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setObjectName("scrollArea")
        self.scrollAreaWidgetContents_2 = QtWidgets.QWidget()
        self.scrollAreaWidgetContents_2.setGeometry(QtCore.QRect(0, 0, 420, 590))
        self.scrollAreaWidgetContents_2.setStyleSheet("#scrollAreaWidgetContents_2 {\n"
"                           border:0px;\n"
"                           background: rgba(0,0,0,0.0);\n"
"                           }\n"
"                       ")
        self.scrollAreaWidgetContents_2.setObjectName("scrollAreaWidgetContents_2")
        self.gridLayout_5 = QtWidgets.QGridLayout(self.scrollAreaWidgetContents_2)
        self.gridLayout_5.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_5.setSpacing(5)
        self.gridLayout_5.setObjectName("gridLayout_5")
        self.__g_product_ipad4_toolbutton_10 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_10.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_10.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_10.setFont(font)
        self.__g_product_ipad4_toolbutton_10.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_10.setStyleSheet("border: 2px solid #007f00;")
        icon13 = QtGui.QIcon()
        icon13.addPixmap(QtGui.QPixmap(":/sales_screen/ipad"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.__g_product_ipad4_toolbutton_10.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_10.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_10.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_10.setObjectName("__g_product_ipad4_toolbutton_10")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_10, 2, 2, 1, 1)
        self.__g_product_ipad4_toolbutton_14 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_14.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_14.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_14.setFont(font)
        self.__g_product_ipad4_toolbutton_14.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_14.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_14.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_14.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_14.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_14.setObjectName("__g_product_ipad4_toolbutton_14")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_14, 3, 2, 1, 1)
        self.__g_product_ipad4_toolbutton_6 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_6.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_6.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_6.setFont(font)
        self.__g_product_ipad4_toolbutton_6.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_6.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_6.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_6.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_6.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_6.setObjectName("__g_product_ipad4_toolbutton_6")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_6, 4, 1, 1, 1)
        self.__g_product_ipad4_toolbutton_22 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_22.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_22.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_22.setFont(font)
        self.__g_product_ipad4_toolbutton_22.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_22.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_22.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_22.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_22.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_22.setObjectName("__g_product_ipad4_toolbutton_22")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_22, 4, 3, 1, 1)
        self.__g_product_ipad4_toolbutton_20 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_20.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_20.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_20.setFont(font)
        self.__g_product_ipad4_toolbutton_20.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_20.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_20.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_20.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_20.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_20.setObjectName("__g_product_ipad4_toolbutton_20")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_20, 1, 2, 1, 1)
        self.toolButton_8 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.toolButton_8.setMinimumSize(QtCore.QSize(80, 80))
        self.toolButton_8.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.toolButton_8.setFont(font)
        self.toolButton_8.setStyleSheet("#toolButton_6 {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"color:white;\n"
"font-weight:bold;\n"
"font-size: 9pt;\n"
"}")
        self.toolButton_8.setIcon(icon13)
        self.toolButton_8.setIconSize(QtCore.QSize(45, 45))
        self.toolButton_8.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.toolButton_8.setObjectName("toolButton_8")
        self.gridLayout_5.addWidget(self.toolButton_8, 3, 3, 1, 1)
        self.__g_product_ipad4_toolbutton_24 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_24.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_24.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_24.setFont(font)
        self.__g_product_ipad4_toolbutton_24.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_24.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_24.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_24.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_24.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_24.setObjectName("__g_product_ipad4_toolbutton_24")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_24, 4, 0, 1, 1)
        self.toolButton_13 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.toolButton_13.setMinimumSize(QtCore.QSize(80, 80))
        self.toolButton_13.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.toolButton_13.setFont(font)
        self.toolButton_13.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.toolButton_13.setStyleSheet("#toolButton_6 {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"color:white;\n"
"font-weight:bold;\n"
"font-size: 9pt;\n"
"}")
        self.toolButton_13.setIcon(icon13)
        self.toolButton_13.setIconSize(QtCore.QSize(45, 45))
        self.toolButton_13.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.toolButton_13.setObjectName("toolButton_13")
        self.gridLayout_5.addWidget(self.toolButton_13, 1, 0, 1, 1)
        self.toolButton_16 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.toolButton_16.setMinimumSize(QtCore.QSize(80, 80))
        self.toolButton_16.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.toolButton_16.setFont(font)
        self.toolButton_16.setStyleSheet("")
        self.toolButton_16.setIcon(icon13)
        self.toolButton_16.setIconSize(QtCore.QSize(45, 45))
        self.toolButton_16.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.toolButton_16.setObjectName("toolButton_16")
        self.gridLayout_5.addWidget(self.toolButton_16, 1, 3, 1, 1)
        self.__g_product_ipad4_toolbutton_3 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_3.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_3.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_3.setFont(font)
        self.__g_product_ipad4_toolbutton_3.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_3.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_3.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_3.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_3.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_3.setObjectName("__g_product_ipad4_toolbutton_3")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_3, 3, 0, 1, 1)
        self.__g_product_ipad4_toolbutton_13 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_13.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_13.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_13.setFont(font)
        self.__g_product_ipad4_toolbutton_13.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_13.setStyleSheet("border: 2px solid #007f00;")
        self.__g_product_ipad4_toolbutton_13.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_13.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_13.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_13.setObjectName("__g_product_ipad4_toolbutton_13")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_13, 2, 3, 1, 1)
        self.__g_product_ipad4_toolbutton_15 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_15.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_15.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_15.setFont(font)
        self.__g_product_ipad4_toolbutton_15.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_15.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_15.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_15.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_15.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_15.setObjectName("__g_product_ipad4_toolbutton_15")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_15, 3, 1, 1, 1)
        self.__g_product_ipad4_toolbutton_19 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_19.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_19.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_19.setFont(font)
        self.__g_product_ipad4_toolbutton_19.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_19.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_19.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_19.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_19.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_19.setObjectName("__g_product_ipad4_toolbutton_19")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_19, 4, 2, 1, 1)
        self.__g_product_ipad4_toolbutton_16 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_16.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_16.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_16.setFont(font)
        self.__g_product_ipad4_toolbutton_16.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_16.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_16.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_16.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_16.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_16.setObjectName("__g_product_ipad4_toolbutton_16")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_16, 1, 1, 1, 1)
        self.toolButton_11 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.toolButton_11.setMinimumSize(QtCore.QSize(80, 80))
        self.toolButton_11.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.toolButton_11.setFont(font)
        self.toolButton_11.setStyleSheet("border: 2px solid #007f00;")
        self.toolButton_11.setIcon(icon13)
        self.toolButton_11.setIconSize(QtCore.QSize(45, 45))
        self.toolButton_11.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.toolButton_11.setObjectName("toolButton_11")
        self.gridLayout_5.addWidget(self.toolButton_11, 2, 1, 1, 1)
        self.__g_product_ipad4_toolbutton_9 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_9.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_9.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_9.setFont(font)
        self.__g_product_ipad4_toolbutton_9.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_9.setStyleSheet("border: 2px solid #007f00;")
        self.__g_product_ipad4_toolbutton_9.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_9.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_9.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_9.setObjectName("__g_product_ipad4_toolbutton_9")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_9, 2, 0, 1, 1)
        self.__g_product_ipad4_toolbutton_21 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_21.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_21.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_21.setFont(font)
        self.__g_product_ipad4_toolbutton_21.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_21.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_21.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_21.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_21.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_21.setObjectName("__g_product_ipad4_toolbutton_21")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_21, 8, 0, 1, 1)
        self.__g_product_ipad4_toolbutton_27 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_27.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_27.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_27.setFont(font)
        self.__g_product_ipad4_toolbutton_27.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_27.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_27.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_27.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_27.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_27.setObjectName("__g_product_ipad4_toolbutton_27")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_27, 5, 1, 1, 1)
        self.__g_product_ipad4_toolbutton_26 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_26.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_26.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_26.setFont(font)
        self.__g_product_ipad4_toolbutton_26.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_26.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_26.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_26.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_26.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_26.setObjectName("__g_product_ipad4_toolbutton_26")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_26, 9, 0, 1, 1)
        self.__g_product_ipad4_toolbutton_11 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_11.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_11.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_11.setFont(font)
        self.__g_product_ipad4_toolbutton_11.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_11.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_11.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_11.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_11.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_11.setObjectName("__g_product_ipad4_toolbutton_11")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_11, 5, 2, 1, 1)
        self.__g_product_ipad4_toolbutton_12 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_12.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_12.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_12.setFont(font)
        self.__g_product_ipad4_toolbutton_12.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_12.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_12.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_12.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_12.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_12.setObjectName("__g_product_ipad4_toolbutton_12")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_12, 5, 0, 1, 1)
        self.__g_product_ipad4_toolbutton_34 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_34.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_34.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_34.setFont(font)
        self.__g_product_ipad4_toolbutton_34.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_34.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_34.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_34.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_34.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_34.setObjectName("__g_product_ipad4_toolbutton_34")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_34, 9, 3, 1, 1)
        self.__g_product_ipad4_toolbutton_31 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_31.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_31.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_31.setFont(font)
        self.__g_product_ipad4_toolbutton_31.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_31.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_31.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_31.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_31.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_31.setObjectName("__g_product_ipad4_toolbutton_31")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_31, 8, 1, 1, 1)
        self.__g_product_ipad4_toolbutton_35 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_35.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_35.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_35.setFont(font)
        self.__g_product_ipad4_toolbutton_35.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_35.setStyleSheet("border: 2px solid #ca0e0e;")
        self.__g_product_ipad4_toolbutton_35.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_35.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_35.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_35.setObjectName("__g_product_ipad4_toolbutton_35")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_35, 9, 4, 1, 1)
        self.__g_product_ipad4_toolbutton_33 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_33.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_33.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_33.setFont(font)
        self.__g_product_ipad4_toolbutton_33.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_33.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_33.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_33.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_33.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_33.setObjectName("__g_product_ipad4_toolbutton_33")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_33, 9, 2, 1, 1)
        self.__g_product_ipad4_toolbutton_32 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_32.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_32.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_32.setFont(font)
        self.__g_product_ipad4_toolbutton_32.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_32.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_32.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_32.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_32.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_32.setObjectName("__g_product_ipad4_toolbutton_32")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_32, 9, 1, 1, 1)
        self.__g_product_ipad4_toolbutton_23 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_23.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_23.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_23.setFont(font)
        self.__g_product_ipad4_toolbutton_23.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_23.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_23.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_23.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_23.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_23.setObjectName("__g_product_ipad4_toolbutton_23")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_23, 1, 4, 1, 1)
        self.__g_product_ipad4_toolbutton_29 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_29.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_29.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_29.setFont(font)
        self.__g_product_ipad4_toolbutton_29.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_29.setStyleSheet("border: 2px solid #ca0e0e;")
        self.__g_product_ipad4_toolbutton_29.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_29.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_29.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_29.setObjectName("__g_product_ipad4_toolbutton_29")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_29, 8, 3, 1, 1)
        self.__g_product_ipad4_toolbutton_28 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_28.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_28.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_28.setFont(font)
        self.__g_product_ipad4_toolbutton_28.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_28.setStyleSheet("border: 2px solid #ca0e0e;")
        self.__g_product_ipad4_toolbutton_28.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_28.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_28.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_28.setObjectName("__g_product_ipad4_toolbutton_28")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_28, 8, 4, 1, 1)
        self.__g_product_ipad4_toolbutton_30 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_30.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_30.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_30.setFont(font)
        self.__g_product_ipad4_toolbutton_30.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_30.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_30.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_30.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_30.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_30.setObjectName("__g_product_ipad4_toolbutton_30")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_30, 8, 2, 1, 1)
        self.__g_product_ipad4_toolbutton_18 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_18.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_18.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_18.setFont(font)
        self.__g_product_ipad4_toolbutton_18.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_18.setStyleSheet("border: 2px solid #ca0e0e;")
        self.__g_product_ipad4_toolbutton_18.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_18.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_18.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_18.setObjectName("__g_product_ipad4_toolbutton_18")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_18, 5, 4, 1, 1)
        self.__g_product_ipad4_toolbutton_17 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_17.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_17.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_17.setFont(font)
        self.__g_product_ipad4_toolbutton_17.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_17.setStyleSheet("border: 2px solid #ca0e0e;")
        self.__g_product_ipad4_toolbutton_17.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_17.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_17.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_17.setObjectName("__g_product_ipad4_toolbutton_17")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_17, 5, 3, 1, 1)
        self.toolButton_9 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.toolButton_9.setMinimumSize(QtCore.QSize(80, 80))
        self.toolButton_9.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.toolButton_9.setFont(font)
        self.toolButton_9.setStyleSheet("#toolButton_6 {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"color:white;\n"
"font-weight:bold;\n"
"font-size: 9pt;\n"
"}")
        self.toolButton_9.setIcon(icon13)
        self.toolButton_9.setIconSize(QtCore.QSize(45, 45))
        self.toolButton_9.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.toolButton_9.setObjectName("toolButton_9")
        self.gridLayout_5.addWidget(self.toolButton_9, 3, 4, 1, 1)
        self.__g_product_ipad4_toolbutton_25 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_25.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_25.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_25.setFont(font)
        self.__g_product_ipad4_toolbutton_25.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_25.setStyleSheet("")
        self.__g_product_ipad4_toolbutton_25.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_25.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_25.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_25.setObjectName("__g_product_ipad4_toolbutton_25")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_25, 4, 4, 1, 1)
        self.__g_product_ipad4_toolbutton_36 = QtWidgets.QToolButton(self.scrollAreaWidgetContents_2)
        self.__g_product_ipad4_toolbutton_36.setMinimumSize(QtCore.QSize(80, 80))
        self.__g_product_ipad4_toolbutton_36.setMaximumSize(QtCore.QSize(80, 80))
        font = QtGui.QFont()
        font.setFamily("Tahoma")
        font.setPointSize(8)
        self.__g_product_ipad4_toolbutton_36.setFont(font)
        self.__g_product_ipad4_toolbutton_36.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.__g_product_ipad4_toolbutton_36.setStyleSheet("border: 2px solid #007f00;")
        self.__g_product_ipad4_toolbutton_36.setIcon(icon13)
        self.__g_product_ipad4_toolbutton_36.setIconSize(QtCore.QSize(45, 45))
        self.__g_product_ipad4_toolbutton_36.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.__g_product_ipad4_toolbutton_36.setObjectName("__g_product_ipad4_toolbutton_36")
        self.gridLayout_5.addWidget(self.__g_product_ipad4_toolbutton_36, 2, 4, 1, 1)
        self.scrollArea.setWidget(self.scrollAreaWidgetContents_2)
        self.gridLayout_3.addWidget(self.scrollArea, 0, 4, 1, 1)
        self.g_quicklinks_tabwidget.addTab(self.__g_products_widget, "")
        self.g_bundle_widget = QtWidgets.QWidget()
        self.g_bundle_widget.setObjectName("g_bundle_widget")
        self.verticalLayout_12 = QtWidgets.QVBoxLayout(self.g_bundle_widget)
        self.verticalLayout_12.setContentsMargins(10, 10, 10, 10)
        self.verticalLayout_12.setObjectName("verticalLayout_12")
        self.g_bundle_frame = QtWidgets.QFrame(self.g_bundle_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bundle_frame.sizePolicy().hasHeightForWidth())
        self.g_bundle_frame.setSizePolicy(sizePolicy)
        self.g_bundle_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bundle_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bundle_frame.setObjectName("g_bundle_frame")
        self.verticalLayout_12.addWidget(self.g_bundle_frame)
        self.g_quicklinks_tabwidget.addTab(self.g_bundle_widget, "")
        self.verticalLayout_2.addWidget(self.g_quicklinks_tabwidget)
        self.g_leftright_glayout.addWidget(self.g_right_frame, 2, 1, 1, 1)

        self.retranslateUi(SalesScreen)
        self.g_quicklinks_tabwidget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(SalesScreen)

    def retranslateUi(self, SalesScreen):
        _translate = QtCore.QCoreApplication.translate
        SalesScreen.setWindowTitle(_translate("SalesScreen", "Dialog"))
        SalesScreen.setProperty("under_construction_title_text", _translate("SalesScreen", "Coming soon ..."))
        SalesScreen.setProperty("cancel_sale_title_text", _translate("SalesScreen", "Cancel Sale"))
        SalesScreen.setProperty("delete_customer_title_text", _translate("SalesScreen", "Remove Customer"))
        SalesScreen.setProperty("remove_items_title_text", _translate("SalesScreen", "Remove Items"))
        SalesScreen.setProperty("selection_required_title_text", _translate("SalesScreen", "Selection Required"))
        SalesScreen.setProperty("search_error_title_text", _translate("SalesScreen", "Search Error"))
        SalesScreen.setProperty("missing_search_term_msg_text", _translate("SalesScreen", "Please enter a barcode or search term."))
        SalesScreen.setProperty("no_search_results_msg_text", _translate("SalesScreen", "No results were found for %(search_term)s."))
        SalesScreen.setProperty("sales_toolbutton_tooltip_text", _translate("SalesScreen", "Place sales or process returns"))
        SalesScreen.setProperty("sales_toolbutton_title_text", _translate("SalesScreen", "Sales"))
        SalesScreen.setProperty("sales_toolbutton_stylesheet_text", _translate("SalesScreen", "\n"
"QToolButton {\n"
"    background: #db552d;\n"
"    border: 2px solid #B74726;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #B74726;\n"
"}\n"
"\n"
"QToolButton:disabled {\n"
"    background: grey;\n"
"    border: 2px solid grey;\n"
"}\n"
"\n"
""))
        SalesScreen.setProperty("sales_indicator_stylesheet_text", _translate("SalesScreen", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"            the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #B74726;\n"
"    border: 0;\n"
"}"))
        SalesScreen.setProperty("items_required_title_text", _translate("SalesScreen", "No Items"))
        SalesScreen.setProperty("addons_tab_title_text", _translate("SalesScreen", "Add Ons"))
        SalesScreen.setProperty("too_many_results_msg_text", _translate("SalesScreen", "Your search returned too many results. Please refine your search term."))
        SalesScreen.setProperty("searching_products_msg_text", _translate("SalesScreen", "Searching products ..."))
        SalesScreen.setProperty("starting_transaction_msg_text", _translate("SalesScreen", "Starting transaction ..."))
        SalesScreen.setProperty("updating_transaction_msg_text", _translate("SalesScreen", "Updating transaction ..."))
        SalesScreen.setProperty("closing_transaction_msg_text", _translate("SalesScreen", "Closing transaction ..."))
        SalesScreen.setProperty("cancelling_transaction_msg_text", _translate("SalesScreen", "Cancelling transaction ..."))
        SalesScreen.setProperty("items_required_msg_text", _translate("SalesScreen", "Please add one or more items first."))
        SalesScreen.setProperty("cancel_return_msg_text", _translate("SalesScreen", "Are you sure you want to cancel the return?"))
        SalesScreen.setProperty("cancel_sale_msg_text", _translate("SalesScreen", "Are you sure you want to cancel the sale?"))
        SalesScreen.setProperty("cancel_return_title_text", _translate("SalesScreen", "Cancel Return"))
        SalesScreen.setProperty("creating_new_tray_msg_text", _translate("SalesScreen", "Creating a new tray for your device ..."))
        SalesScreen.setProperty("validating_tray_msg_text", _translate("SalesScreen", "Validating your tray configuration ..."))
        SalesScreen.setProperty("retrieving_last_tray_session_msg_text", _translate("SalesScreen", "Retrieving the last tray session ..."))
        SalesScreen.setProperty("add_product_title_text", _translate("SalesScreen", "Add Product"))
        SalesScreen.setProperty("customer_required_title_text", _translate("SalesScreen", "Customer Required"))
        SalesScreen.setProperty("customer_required_msg_text", _translate("SalesScreen", "One or more products require a customer.\n"
"\n"
"Please add one before continuing."))
        SalesScreen.setProperty("retrieving_product_info_msg_text", _translate("SalesScreen", "Retrieving product information ..."))
        SalesScreen.setProperty("open_register_required_msg_text", _translate("SalesScreen", "You must open this register in order to add/return products."))
        SalesScreen.setProperty("register_closed_title_text", _translate("SalesScreen", "Register Closed"))
        SalesScreen.setProperty("retrieving_payments_msg_text", _translate("SalesScreen", "Retrieving tray session payments ..."))
        SalesScreen.setProperty("remove_items_msg_text", _translate("SalesScreen", "Are you sure you want to remove these items?"))
        SalesScreen.setProperty("delete_customer_msg_text", _translate("SalesScreen", "Are you sure you want to remove the customer?"))
        SalesScreen.setProperty("selection_required_msg_text", _translate("SalesScreen", "Please select one or more items with prices to apply a discount."))
        SalesScreen.setProperty("under_construction_msg_text", _translate("SalesScreen", "This feature will be implemented in the near future."))
        SalesScreen.setProperty("read_payment_methods_url_text", _translate("SalesScreen", "/apps/cash_register/queries/read__tbl__payment_methods"))
        SalesScreen.setProperty("cash_method_required_msg_text", _translate("SalesScreen", "A cash payment method must be present in order to open the register."))
        SalesScreen.setProperty("error_title_text", _translate("SalesScreen", "Error"))
        SalesScreen.setProperty("receipt_printer_required_msg_text", _translate("SalesScreen", "Please choose a receipt printer using the Settings screen."))
        SalesScreen.setProperty("retrieving_payment_methods_msg_text", _translate("SalesScreen", "Retrieving payment methods ..."))
        SalesScreen.setProperty("read_transaction_lines_url_text", _translate("SalesScreen", "/apps/cash_register/queries/read__tbl__sale_transaction_lines"))
        SalesScreen.setProperty("read_transaction_payments_url_text", _translate("SalesScreen", "/apps/cash_register/queries/read__tbl__transaction_payments"))
        SalesScreen.setProperty("reading_invoice_msg_text", _translate("SalesScreen", "Reading invoice details ..."))
        SalesScreen.setProperty("read_transactions_url_text", _translate("SalesScreen", "/apps/cash_register/queries/read__tbl__transactions"))
        SalesScreen.setProperty("invalid_quantity_msg_text", _translate("SalesScreen", "Please enter decimals or whole numbers."))
        SalesScreen.setProperty("invalid_quantity_title_text", _translate("SalesScreen", "Invalid Quantity"))
        SalesScreen.setProperty("transaction_not_found_msg_text", _translate("SalesScreen", "No %(invoice_type)s transaction with invoice number %(invoice_number)s could be found."))
        SalesScreen.setProperty("zero_quantity_forbidden_msg_text", _translate("SalesScreen", "A product may not have a quantity of 0.\n"
"\n"
"Do you wish to remove this product\n"
"from the sale instead?"))
        SalesScreen.setProperty("row_not_deletable_msg_text", _translate("SalesScreen", "This product is a required addon and cannot be deleted individually."))
        SalesScreen.setProperty("this_tray_url_text", _translate("SalesScreen", "/components/sales_screen/trays/thistray"))
        SalesScreen.setProperty("no_tray_msg_text", _translate("SalesScreen", "No tray has been setup or enabled for\n"
"this device.\n"
"\n"
"You will now be directed to the Settings\n"
"screen to create or enable a tray."))
        SalesScreen.setProperty("action_mismatch_title_text", _translate("SalesScreen", "Action Type Mismatch"))
        SalesScreen.setProperty("ipc_return_when_sale_msg_text", _translate("SalesScreen", "Please complete your sale before adding products to return."))
        SalesScreen.setProperty("ipc_sale_when_return_msg_text", _translate("SalesScreen", "Please complete your return before adding products to sale."))
        SalesScreen.setProperty("retrieving_transaction_info_msg_text", _translate("SalesScreen", "Retrieving transaction information ..."))
        SalesScreen.setProperty("layaway_missing_customer_msg_text", _translate("SalesScreen", "A customer is required to create\n"
"a layaway.\n"
"\n"
"Please select or enter a customer\n"
"and try again."))
        SalesScreen.setProperty("read_settings_url_text", _translate("SalesScreen", "/apps/cash_register/queries/read__qpt__vw_applicable_settings"))
        SalesScreen.setProperty("abandon_layaway_msg_text", _translate("SalesScreen", "You are about to mark this layaway\n"
"             as abandoned.\n"
"\n"
"             This will add the cancellation fee\n"
"             onto this layaway, and add any\n"
"             balance to an abandoned layaway\n"
"             fine.\n"
"\n"
"             This action cannot be undone.\n"
"         "))
        SalesScreen.setProperty("cancel_layaway_msg_text", _translate("SalesScreen", "You are about to cancel this layaway.\n"
"This will add a cancellation fee onto the sale.\n"
"\n"
"All other products will be removed.\n"
"This cannot be undone.\n"
"\n"
"After you cancel, you will be taken to the\n"
"payments screen to refund the customer\n"
"any extra they may have paid."))
        SalesScreen.setProperty("abandon_layaway_button_text", _translate("SalesScreen", "Abandon"))
        SalesScreen.setProperty("cancel_layaway_button_text", _translate("SalesScreen", "Cancel Layaway"))
        SalesScreen.setProperty("cancel_layaway_title_text", _translate("SalesScreen", "Cancel Layaway"))
        SalesScreen.setProperty("abandon_layaway_title_text", _translate("SalesScreen", "Abandon Layaway"))
        SalesScreen.setProperty("layaway_min_deposit_required_msg_text", _translate("SalesScreen", "The sale balance is less than the minimum\n"
"deposit amount for opening a layaway."))
        SalesScreen.setProperty("layaway_days_left_notice_text", _translate("SalesScreen", "Layaway Due in %(days)s Days"))
        SalesScreen.setProperty("transaction_invalid_msg_text", _translate("SalesScreen", "This transaction could not be re-opened."))
        SalesScreen.setProperty("left_frame_display_mode_stylesheet_text", _translate("SalesScreen", "\n"
"#g_left_frame {\n"
"    border: 1px solid #073c83;\n"
"    border-radius: 0px;\n"
"    border-bottom: 0px;\n"
"    border-top: 0px;\n"
"    border-right: 0px;\n"
"    background-color: #DDE4E9;\n"
"}\n"
"\n"
"#g_totals_table,\n"
"#g_customer_frame {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_totals_table {\n"
"    min-height: 0px;\n"
"}\n"
"\n"
"#g_totals_table,\n"
"#g_customertotals_frame {\n"
"    max-height: 150px;\n"
"}"))
        SalesScreen.setProperty("reading_settings_msg_text", _translate("SalesScreen", "Reading application settings ..."))
        SalesScreen.setProperty("recalculating_taxes_msg_text", _translate("SalesScreen", "Recalculating taxes ..."))
        SalesScreen.setProperty("refreshing_transaction_msg_text", _translate("SalesScreen", "Refreshing transaction ..."))
        SalesScreen.setProperty("read_integration_settings_url", _translate("SalesScreen", "/apps/cash_register/queries/read__tbl__integration_settings"))
        SalesScreen.setProperty("loading_transaction_msg_text", _translate("SalesScreen", "Loading transaction ..."))
        SalesScreen.setProperty("tax_edit_product_selection_required_msg_text", _translate("SalesScreen", "Select the products you want to change the\n"
"tax on before clicking the change tax icon."))
        SalesScreen.setProperty("product_search_url_text", _translate("SalesScreen", "/apps/cash_register/queries/read__qpt__sales_prod_search"))
        SalesScreen.setProperty("read_extended_actions_url_text", _translate("SalesScreen", "/apps/cash_register/queries/read__tbl__extended_actions"))
        SalesScreen.setProperty("delete_transactions_url_text", _translate("SalesScreen", "/apps/cash_register/queries/delete__tbl__sale_transactions"))
        SalesScreen.setProperty("create_transactions_url_text", _translate("SalesScreen", "/apps/cash_register/queries/create__tbl__transactions"))
        SalesScreen.setProperty("update_transactions_url_text", _translate("SalesScreen", "/apps/cash_register/queries/update__tbl__transactions"))
        SalesScreen.setProperty("create_transaction_lines_url_text", _translate("SalesScreen", "/apps/cash_register/queries/create__tbl__sale_transaction_lines"))
        SalesScreen.setProperty("update_transaction_lines_url_text", _translate("SalesScreen", "/apps/cash_register/queries/update__tbl__sale_transaction_lines"))
        SalesScreen.setProperty("delete_transaction_lines_url_text", _translate("SalesScreen", "/apps/cash_register/queries/delete__tbl__sale_transaction_lines"))
        SalesScreen.setProperty("read_locations_url_text", _translate("SalesScreen", "/apps/cash_register/queries/get__locations"))
        SalesScreen.setProperty("read_customers_url_text", _translate("SalesScreen", "/apps/any/queries/read__qpt__entities"))
        SalesScreen.setProperty("read_cashiers_url_text", _translate("SalesScreen", "/apps/cash_register/queries/read__qpt__list_employees"))
        SalesScreen.setProperty("create_transaction_return_url_text", _translate("SalesScreen", "/apps/cash_register/queries/create__qpt__create_transaction_return"))
        SalesScreen.setProperty("read_return_lowest_price_url_text", _translate("SalesScreen", "/apps/cash_register/queries/read__qpt__sale_return_lowest_price"))
        SalesScreen.setProperty("retrieving_price_info_msg_text", _translate("SalesScreen", "Retrieving price information ..."))
        SalesScreen.setProperty("read_transaction_returns_url_text", _translate("SalesScreen", "/apps/cash_register/queries/read__qpt__sale_transaction_line_returns"))
        SalesScreen.setProperty("same_customer_for_returns_msg_text", _translate("SalesScreen", "Only invoices belonging to the same customer\n"
"can be used to create a return."))
        SalesScreen.setProperty("existing_return_opened_msg_text", _translate("SalesScreen", "A previous return was already started\n"
"for the selected invoices and has now\n"
"been opened."))
        SalesScreen.setProperty("warning_title_text", _translate("SalesScreen", "Warning"))
        SalesScreen.setProperty("invoices_added_to_open_return_msg_text", _translate("SalesScreen", "The selected invoices have been\n"
"added to the current open return."))
        SalesScreen.setProperty("invoice_completely_returned_msg_text", _translate("SalesScreen", "All products in invoice %(invoice_number)s \n"
"have already been returned."))
        SalesScreen.setProperty("invoice_partially_returned_msg_text", _translate("SalesScreen", "One or more products in invoice %(invoice_number)s\n"
"have already been returned.\n"
"\n"
"Only products that haven\'t been\n"
"returned will be shown."))
        SalesScreen.setProperty("invoice_not_found_msg_text", _translate("SalesScreen", "Invoice %(invoice_number)s could not be found."))
        SalesScreen.setProperty("update_check_promotions_url_text", _translate("SalesScreen", "/apps/cash_register/queries/update__qpt__check_promotions"))
        SalesScreen.setProperty("read_rewards_transaction_lines_url_text", _translate("SalesScreen", "/apps/any/queries/read__qpt__rewards_transaction_lines"))
        SalesScreen.setProperty("update_commission_txnline_url_text", _translate("SalesScreen", "/apps/any/queries/update__qpt__commission_txnline"))
        SalesScreen.setProperty("confirm_tip_removal_msg_text", _translate("SalesScreen", "Tip Will Be Removed From The Current Transaction. Continue?"))
        SalesScreen.setProperty("tip_removed_msg_text", _translate("SalesScreen", "Tip Has Been Removed."))
        SalesScreen.setProperty("action_complete_title_text", _translate("SalesScreen", "Action Complete"))
        SalesScreen.setProperty("read_pet_transactions_url_text", _translate("SalesScreen", "/apps/any/queries/read__tbl__pet_transactions"))
        SalesScreen.setProperty("pet_not_eligible_to_return_msg_text", _translate("SalesScreen", "Pet is not eligible for return"))
        SalesScreen.setProperty("open_tray_or_put_on_hold_msg_text", _translate("SalesScreen", "Tray \"%(tray_name)s\" is currently closed. You can put this invoice on hold, and complete it on another register, or open this register."))
        SalesScreen.setProperty("register_already_closed_msg_text", _translate("SalesScreen", "Register is already closed."))
        SalesScreen.setProperty("this_tray_session_details_url_text", _translate("SalesScreen", "/components/sales_screen/trays/thistray/tray_sessions/{}/tray_session_details"))
        SalesScreen.setProperty("negative_discount_msg_text", _translate("SalesScreen", "The Discount Applied Would Result in the Following Negative Net Prices:\n"
"%s"))
        SalesScreen.setProperty("nonresalable_exceeds_qty_text", _translate("SalesScreen", "Non Resalable item quantity changed to %(qty)s"))
        SalesScreen.setProperty("read_transaction_line_return_url_text", _translate("SalesScreen", "/apps/cash_register/queries/read__tbl__transaction_line_returns"))
        SalesScreen.setProperty("update_transaction_line_return_url_text", _translate("SalesScreen", "/apps/cash_register/queries/update__qpt__transaction_line_returns"))
        SalesScreen.setProperty("create_transaction_line_returns_url_text", _translate("SalesScreen", "/apps/cash_register/queries/create__tbl__transaction_line_returns"))
        SalesScreen.setProperty("current_session_url_text", _translate("SalesScreen", "/apps/any/sessions/mine"))
        SalesScreen.setProperty("read_trays_url_text", _translate("SalesScreen", "/apps/any/queries/read__tbl__trays"))
        SalesScreen.setProperty("read_entity_users_url_text", _translate("SalesScreen", "/apps/any/queries/read__qpt__entity_users"))
        SalesScreen.setProperty("create_transaction_payments_url_text", _translate("SalesScreen", "/apps/cash_register/queries/create__tbl__transaction_payments"))
        SalesScreen.setProperty("printing_now_msg_text", _translate("SalesScreen", "Printing your receipt. Please wait ..."))
        SalesScreen.setProperty("read_sale_transactions_url_text", _translate("SalesScreen", "/apps/cash_register/queries/read__tbl__sale_transactions"))
        SalesScreen.setProperty("update_sale_transactions_url_text", _translate("SalesScreen", "/apps/cash_register/queries/update__tbl__sale_transactions"))
        SalesScreen.setProperty("read_tsd_url_text", _translate("SalesScreen", "/apps/any/queries/read__tbl__tray_session_details"))
        SalesScreen.setProperty("read_payment_types_url_text", _translate("SalesScreen", "/apps/any/queries/read__tbl__payment_types"))
        SalesScreen.setProperty("read_previous_tsd_url_text", _translate("SalesScreen", "/apps/any/queries/read__qpt__tray_session_counts_with_previous_counts"))
        SalesScreen.setProperty("create_sale_transactions_url_text", _translate("SalesScreen", "/apps/cash_register/queries/create__tbl__sale_transactions"))
        SalesScreen.setProperty("opening_tray_msg_text", _translate("SalesScreen", "Opening a tray session..."))
        SalesScreen.setProperty("create_tray_session_details_url_text", _translate("SalesScreen", "/apps/any/queries/create__gen__tray_session_details"))
        SalesScreen.setProperty("closing_register_msg_text", _translate("SalesScreen", "Closing a tray session..."))
        SalesScreen.setProperty("read_tray_session_details_url_text", _translate("SalesScreen", "/apps/any/queries/read__tbl__tray_session_details"))
        SalesScreen.setProperty("no_count_at_opening_methods_msg_text", _translate("SalesScreen", "You don\'t have payment methods to count at tray opening stage."))
        SalesScreen.setProperty("read_products_url_text", _translate("SalesScreen", "/apps/any/queries/read__tbl__products"))
        SalesScreen.setProperty("incorrect_search_term_msg_text", _translate("SalesScreen", "Your search term %(search_term)s is incorrect: %(reason)s."))
        SalesScreen.setProperty("cancel_hold_transaction_msg_text", _translate("SalesScreen", "There is an invoice in progress. Finish, cancel, or place that invoice on hold before closing the Cash Register."))
        SalesScreen.setProperty("operation_inprogress_msg_text", _translate("SalesScreen", "Operation in progress..."))
        SalesScreen.setProperty("ha_customer_required_msg_text", _translate("SalesScreen", "Customer must be associated with a House Account"))
        SalesScreen.setProperty("list_house_account_customers_url_text", _translate("SalesScreen", "/apps/any/queries/read__qpt__list_house_account_customers"))
        SalesScreen.setProperty("astro_partial_return_msg_text", _translate("SalesScreen", "Not all items were returned to Astro in this transaction"))
        SalesScreen.setProperty("pay_customer_required_msg_text", _translate("SalesScreen", "You must select a customer in order to\n"
"             use this payment method.\n"
"         "))
        SalesScreen.setProperty("setting_retrieval_error_msg_text", _translate("SalesScreen", "A required integration setting failed to load.\n"
"\n"
"             Please contact Pinogy support at 877-360-7381.\n"
"         "))
        SalesScreen.setProperty("retrieving_settings_msg_text", _translate("SalesScreen", "Retrieving settings ..."))
        SalesScreen.setProperty("processing_payment_msg_text", _translate("SalesScreen", "Processing payment ..."))
        SalesScreen.setProperty("no_bonus_bucks_msg_text", _translate("SalesScreen", "The selected customer does not\n"
"             have any Bonus Bucks.\n"
"         "))
        SalesScreen.setProperty("voiding_payment_msg_text", _translate("SalesScreen", "Voiding payment ..."))
        SalesScreen.setProperty("pps_location_without_key_msg_text", _translate("SalesScreen", "This location has no user and password specified to use Pinogy Payment Services."))
        SalesScreen.setProperty("pps_no_pinpad_msg_text", _translate("SalesScreen", "There is no Pinogy Payment Services pinpad associated with this device."))
        SalesScreen.setProperty("trancloud_device_unknown_msg_text", _translate("SalesScreen", "There is a Trancloud device configured with your POS to charge Credit and Debit cards but there are no\n"
"          configured pinpads which are required to do this.\n"
"\n"
"          Contact Pinogy support at 877-360-7381 for assistance.\n"
"      "))
        SalesScreen.setProperty("trancloud_no_pinpad_msg_text", _translate("SalesScreen", "There is no pinpad associated with this device ({}).\n"
"\n"
"             If you need to charge or refund a Credit or Debit card, the following devices have a pinpad configured.\n"
"         "))
        SalesScreen.setProperty("multiple_trancloud_devices_msg_text", _translate("SalesScreen", "Multiple PIN-pads have been assigned to this POS device.\n"
"\n"
"             Only a single PIN-pad can be used per device.\n"
"\n"
"             Please recheck your Vantiv/Mercury configuration using\n"
"             the Settings and Configuration app.\n"
"         "))
        SalesScreen.setProperty("missing_gateway_credentials_msg_text", _translate("SalesScreen", "One or more payment gateways for this\n"
"             location contain missing or incomplete\n"
"             merchant credentials.\n"
"\n"
"             Please provide all gateway credentials\n"
"             using the Settings and Configuration tool.\n"
"         "))
        SalesScreen.setProperty("proceed_with_inquire_msg_text", _translate("SalesScreen", "We need to double check prior payment on this invoice.\n"
"         "))
        SalesScreen.setProperty("inquire_msg_text", _translate("SalesScreen", "Checking prior payments..."))
        self.g_warning_message_label.setText(_translate("SalesScreen", "AstroLoyalty\'s API is not currently working. Astro transactions will need to be marked manually"))
        item = self.g_register_table.verticalHeaderItem(0)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(1)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(2)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(3)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(4)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(5)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(6)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(7)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(8)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(9)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(10)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(11)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(12)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(13)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(14)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(15)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(16)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(17)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.verticalHeaderItem(18)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_register_table.horizontalHeaderItem(0)
        item.setText(_translate("SalesScreen", "Qty"))
        item = self.g_register_table.horizontalHeaderItem(1)
        item.setText(_translate("SalesScreen", "Description"))
        item = self.g_register_table.horizontalHeaderItem(2)
        item.setText(_translate("SalesScreen", "Tax"))
        item = self.g_register_table.horizontalHeaderItem(3)
        item.setText(_translate("SalesScreen", "Price"))
        item = self.g_register_table.horizontalHeaderItem(4)
        item.setText(_translate("SalesScreen", "pencil-edit column (this column header text will not be shown in the actual UI)"))
        item = self.g_register_table.horizontalHeaderItem(5)
        item.setText(_translate("SalesScreen", "delete button column (this column header text will not be shown in the actual UI)"))
        __sortingEnabled = self.g_register_table.isSortingEnabled()
        self.g_register_table.setSortingEnabled(False)
        item = self.g_register_table.item(0, 0)
        item.setText(_translate("SalesScreen", "1"))
        item = self.g_register_table.item(0, 1)
        item.setText(_translate("SalesScreen", "AKC Reunite"))
        item = self.g_register_table.item(0, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(0, 3)
        item.setText(_translate("SalesScreen", "49.99"))
        item = self.g_register_table.item(1, 1)
        item.setText(_translate("SalesScreen", "      Australian Shepherd - MC *********"))
        item = self.g_register_table.item(2, 0)
        item.setText(_translate("SalesScreen", "1"))
        item = self.g_register_table.item(2, 1)
        item.setText(_translate("SalesScreen", "iPad 2 Wifi 32GB"))
        item = self.g_register_table.item(2, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(2, 3)
        item.setText(_translate("SalesScreen", "900.00"))
        item = self.g_register_table.item(3, 1)
        item.setText(_translate("SalesScreen", "      Discount: 100.00 off 1000.00"))
        item = self.g_register_table.item(4, 0)
        item.setText(_translate("SalesScreen", "1"))
        item = self.g_register_table.item(4, 1)
        item.setText(_translate("SalesScreen", "748592145687"))
        item = self.g_register_table.item(4, 2)
        item.setText(_translate("SalesScreen", "N"))
        item = self.g_register_table.item(4, 3)
        item.setText(_translate("SalesScreen", "350.00"))
        item = self.g_register_table.item(5, 0)
        item.setText(_translate("SalesScreen", "1"))
        item = self.g_register_table.item(5, 1)
        item.setText(_translate("SalesScreen", "*********012"))
        item = self.g_register_table.item(5, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(5, 3)
        item.setText(_translate("SalesScreen", "500.00"))
        item = self.g_register_table.item(6, 0)
        item.setText(_translate("SalesScreen", "2"))
        item = self.g_register_table.item(6, 1)
        item.setText(_translate("SalesScreen", "Miscellaneous"))
        item = self.g_register_table.item(6, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(6, 3)
        item.setText(_translate("SalesScreen", "400.00"))
        item = self.g_register_table.item(7, 1)
        item.setText(_translate("SalesScreen", "      Discount: 50.00 off 250.00"))
        item = self.g_register_table.item(8, 1)
        item.setText(_translate("SalesScreen", "      2 @ 200.00"))
        item = self.g_register_table.item(9, 0)
        item.setText(_translate("SalesScreen", "1"))
        item = self.g_register_table.item(9, 1)
        item.setText(_translate("SalesScreen", "Pet - Border Collie"))
        item = self.g_register_table.item(9, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(9, 3)
        item.setText(_translate("SalesScreen", "1000.00"))
        item = self.g_register_table.item(10, 1)
        item.setText(_translate("SalesScreen", "      AKC Registration"))
        item = self.g_register_table.item(10, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(10, 3)
        item.setText(_translate("SalesScreen", "99.99"))
        item = self.g_register_table.item(11, 1)
        item.setText(_translate("SalesScreen", "      AKC Enroll"))
        item = self.g_register_table.item(11, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(11, 3)
        item.setText(_translate("SalesScreen", "49.99"))
        item = self.g_register_table.item(12, 1)
        item.setText(_translate("SalesScreen", "      AKC Reunite"))
        item = self.g_register_table.item(12, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(12, 3)
        item.setText(_translate("SalesScreen", "29.99"))
        item = self.g_register_table.item(13, 1)
        item.setText(_translate("SalesScreen", "      Pet Tag"))
        item = self.g_register_table.item(13, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(13, 3)
        item.setText(_translate("SalesScreen", "12.99"))
        item = self.g_register_table.item(14, 0)
        item.setText(_translate("SalesScreen", "1"))
        item = self.g_register_table.item(14, 1)
        item.setText(_translate("SalesScreen", "German Shepherd"))
        item = self.g_register_table.item(14, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(14, 3)
        item.setText(_translate("SalesScreen", "1300.00"))
        item = self.g_register_table.item(15, 1)
        item.setText(_translate("SalesScreen", "      Discount: 200.00 off 1500.00"))
        item = self.g_register_table.item(16, 1)
        item.setText(_translate("SalesScreen", "      AKC Registration"))
        item = self.g_register_table.item(16, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(16, 3)
        item.setText(_translate("SalesScreen", "90.00"))
        item = self.g_register_table.item(17, 1)
        item.setText(_translate("SalesScreen", "            Discount: 10% off 99.99"))
        item = self.g_register_table.item(17, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(18, 1)
        item.setText(_translate("SalesScreen", "      AKC Reunite"))
        item = self.g_register_table.item(18, 2)
        item.setText(_translate("SalesScreen", "Y"))
        item = self.g_register_table.item(18, 3)
        item.setText(_translate("SalesScreen", "29.99"))
        self.g_register_table.setSortingEnabled(__sortingEnabled)
        self.g_nocustomer_label.setText(_translate("SalesScreen", "No Customer"))
        self.g_customername_label.setText(_translate("SalesScreen", "Amy and Teresa Canderlora"))
        self.g_customerphone_label.setText(_translate("SalesScreen", "************"))
        self.g_customeremail_label.setText(_translate("SalesScreen", "<EMAIL>"))
        self.g_customerwarning_label.setText(_translate("SalesScreen", "No personal checks"))
        self.g_customernotes_label.setText(_translate("SalesScreen", "Notes for this customer"))
        self.g_customerbonusbucksicon_label.setToolTip(_translate("SalesScreen", "Bonus bucks remaining"))
        self.g_customerbonusbucks_label.setToolTip(_translate("SalesScreen", "Bonus bucks remaining"))
        self.g_customerbonusbucks_label.setText(_translate("SalesScreen", "25.00"))
        item = self.g_totals_table.verticalHeaderItem(0)
        item.setText(_translate("SalesScreen", "SubTotal"))
        item = self.g_totals_table.verticalHeaderItem(1)
        item.setText(_translate("SalesScreen", "Discounts"))
        item = self.g_totals_table.verticalHeaderItem(2)
        item.setText(_translate("SalesScreen", "Coupons"))
        item = self.g_totals_table.verticalHeaderItem(3)
        item.setText(_translate("SalesScreen", "Tax"))
        item = self.g_totals_table.verticalHeaderItem(4)
        item.setText(_translate("SalesScreen", "Tip"))
        item = self.g_totals_table.verticalHeaderItem(5)
        item.setText(_translate("SalesScreen", "Fee"))
        item = self.g_totals_table.verticalHeaderItem(6)
        item.setText(_translate("SalesScreen", "Optional Border Row"))
        item = self.g_totals_table.verticalHeaderItem(7)
        item.setText(_translate("SalesScreen", "Total"))
        item = self.g_totals_table.horizontalHeaderItem(0)
        item.setText(_translate("SalesScreen", "Words"))
        item = self.g_totals_table.horizontalHeaderItem(1)
        item.setText(_translate("SalesScreen", "Numbers"))
        __sortingEnabled = self.g_totals_table.isSortingEnabled()
        self.g_totals_table.setSortingEnabled(False)
        item = self.g_totals_table.item(0, 0)
        item.setText(_translate("SalesScreen", "Subtotal"))
        item = self.g_totals_table.item(0, 1)
        item.setText(_translate("SalesScreen", "0.00"))
        item = self.g_totals_table.item(1, 0)
        item.setText(_translate("SalesScreen", "Discounts"))
        item = self.g_totals_table.item(1, 1)
        item.setText(_translate("SalesScreen", "0.00"))
        item = self.g_totals_table.item(2, 0)
        item.setText(_translate("SalesScreen", "Coupons"))
        item = self.g_totals_table.item(2, 1)
        item.setText(_translate("SalesScreen", "-3.00"))
        item = self.g_totals_table.item(3, 0)
        item.setText(_translate("SalesScreen", "Tax"))
        item = self.g_totals_table.item(3, 1)
        item.setText(_translate("SalesScreen", "0.00"))
        item = self.g_totals_table.item(4, 0)
        item.setText(_translate("SalesScreen", "Tip"))
        item = self.g_totals_table.item(4, 1)
        item.setText(_translate("SalesScreen", "0.00"))
        item = self.g_totals_table.item(5, 0)
        item.setText(_translate("SalesScreen", "Fee"))
        item = self.g_totals_table.item(5, 1)
        item.setText(_translate("SalesScreen", "0.00"))
        item = self.g_totals_table.item(7, 0)
        item.setText(_translate("SalesScreen", "Total"))
        item = self.g_totals_table.item(7, 1)
        item.setText(_translate("SalesScreen", "0.00"))
        self.g_totals_table.setSortingEnabled(__sortingEnabled)
        self.g_deliverydate_label.setText(_translate("SalesScreen", "Fri, Mar 20"))
        self.g_layawaydue_label.setText(_translate("SalesScreen", "Layaway Due in %(days)s Days"))
        self.g_pay_button.setText(_translate("SalesScreen", "  Pay"))
        self.g_refund_button.setText(_translate("SalesScreen", "  Refund"))
        self.g_drawerfunctions_button.setText(_translate("SalesScreen", "Tray Functions"))
        self.g_openhold_button.setText(_translate("SalesScreen", "Open Hold"))
        self.g_return_button.setText(_translate("SalesScreen", "Start Return"))
        self.g_cancelreturn_button.setText(_translate("SalesScreen", "  Cancel"))
        self.g_holdreturn_button.setText(_translate("SalesScreen", "Hold"))
        self.g_returndiscount_button.setText(_translate("SalesScreen", "Discount"))
        self.g_cancelsale_button.setText(_translate("SalesScreen", "  Cancel"))
        self.g_salediscount_button.setText(_translate("SalesScreen", "Discount"))
        self.g_holdsale_button.setText(_translate("SalesScreen", "Hold"))
        self.g_quote_button.setText(_translate("SalesScreen", "Quote"))
        self.g_layaway_button.setText(_translate("SalesScreen", "Layaway"))
        self.g_cancellayaway_button.setText(_translate("SalesScreen", "  Cancel"))
        self.g_abandonlayaway_button.setText(_translate("SalesScreen", "Abandon"))
        self.g_closelayaway_button.setText(_translate("SalesScreen", "Hold Layaway"))
        self.g_layawaydiscount_button.setText(_translate("SalesScreen", "Discount"))
        self.g_massaction_button.setText(_translate("SalesScreen", "Mass Action"))
        self.g_back_button.setText(_translate("SalesScreen", "Back"))
        self.g_start_delivery_button.setText(_translate("SalesScreen", "Start Order"))
        self.g_picklist_button.setText(_translate("SalesScreen", "Pick List"))
        self.g_reschedule_button.setText(_translate("SalesScreen", "Reschedule"))
        self.g_payatpickup_button.setText(_translate("SalesScreen", "Pay at Pickup"))
        self.g_reorder_button.setText(_translate("SalesScreen", "Reorder"))
        self.g_search_label.setText(_translate("SalesScreen", "Quick Entry / Search"))
        self.g_searchreturn_label.setText(_translate("SalesScreen", "Quick Entry / Search / Return"))
        self.g_productsearchmode_combobox.setItemText(0, _translate("SalesScreen", "Smart Search"))
        self.g_productsearchmode_combobox.setItemText(1, _translate("SalesScreen", "Barcode"))
        self.g_productsearchmode_combobox.setItemText(2, _translate("SalesScreen", "SKU"))
        self.g_productsearchmode_combobox.setItemText(3, _translate("SalesScreen", "Description"))
        self.g_productsearch_lineedit.setPlaceholderText(_translate("SalesScreen", "Scan barcode or enter search term and press Enter"))
        self.g_osk_button.setToolTip(_translate("SalesScreen", "Show the on-screen keyboard."))
        self.g_registerclosed_label.setText(_translate("SalesScreen", "<html><head/><body><p>To Start, Open a Tray<br/>Hint: Click on<br/>Tray Functions</p></body></html>"))
        self.g_zero_button.setText(_translate("SalesScreen", "0"))
        self.g_doublezero_button.setText(_translate("SalesScreen", "00"))
        self.g_nine_button.setText(_translate("SalesScreen", "9"))
        self.g_four_button.setText(_translate("SalesScreen", "4"))
        self.g_five_button.setText(_translate("SalesScreen", "5"))
        self.g_eight_button.setText(_translate("SalesScreen", "8"))
        self.g_six_button.setText(_translate("SalesScreen", "6"))
        self.g_seven_button.setText(_translate("SalesScreen", "7"))
        self.g_enter_button.setText(_translate("SalesScreen", "Enter"))
        self.g_one_button.setText(_translate("SalesScreen", "1"))
        self.g_two_button.setText(_translate("SalesScreen", "2"))
        self.g_three_button.setText(_translate("SalesScreen", "3"))
        self.g_decimal_button.setText(_translate("SalesScreen", "."))
        self.g_asterisk_button.setText(_translate("SalesScreen", "*"))
        self.g_quicklinks_tabwidget.setTabText(self.g_quicklinks_tabwidget.indexOf(self.g_keypad_widget), _translate("SalesScreen", "Search"))
        self.g_invoicenotes_textedit.setPlaceholderText(_translate("SalesScreen", "Enter any notes for this invoice here."))
        self.g_quicklinks_tabwidget.setTabText(self.g_quicklinks_tabwidget.indexOf(self.g_invoicenotes_widget), _translate("SalesScreen", "Notes"))
        self.g_addons_label.setText(_translate("SalesScreen", "Product Add Ons"))
        self.g_requiredaddons_groupbox.setTitle(_translate("SalesScreen", "Required"))
        item = self.g_requiredaddons_table.verticalHeaderItem(0)
        item.setText(_translate("SalesScreen", "1"))
        item = self.g_requiredaddons_table.horizontalHeaderItem(0)
        item.setText(_translate("SalesScreen", "New Column"))
        __sortingEnabled = self.g_requiredaddons_table.isSortingEnabled()
        self.g_requiredaddons_table.setSortingEnabled(False)
        item = self.g_requiredaddons_table.item(0, 0)
        item.setText(_translate("SalesScreen", "AKC Registration"))
        item = self.g_requiredaddons_table.item(1, 0)
        item.setText(_translate("SalesScreen", "AKC Enroll"))
        item = self.g_requiredaddons_table.item(2, 0)
        item.setText(_translate("SalesScreen", "AKC Reunite"))
        self.g_requiredaddons_table.setSortingEnabled(__sortingEnabled)
        self.g_optionaladdons_groupbox.setTitle(_translate("SalesScreen", "Optional"))
        item = self.g_optionaladdons_table.verticalHeaderItem(0)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.g_optionaladdons_table.horizontalHeaderItem(0)
        item.setText(_translate("SalesScreen", "New Column"))
        __sortingEnabled = self.g_optionaladdons_table.isSortingEnabled()
        self.g_optionaladdons_table.setSortingEnabled(False)
        item = self.g_optionaladdons_table.item(0, 0)
        item.setText(_translate("SalesScreen", "Pet Tag"))
        item = self.g_optionaladdons_table.item(1, 0)
        item.setText(_translate("SalesScreen", "Health Insurance"))
        self.g_optionaladdons_table.setSortingEnabled(__sortingEnabled)
        self.g_quicklinks_tabwidget.setTabText(self.g_quicklinks_tabwidget.indexOf(self.g_addons_widget), _translate("SalesScreen", "Add Ons"))
        self.g_rewards_groupbox.setTitle(_translate("SalesScreen", "Rewards"))
        item = self.__g_rewards_tablewidget.verticalHeaderItem(0)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.__g_rewards_tablewidget.verticalHeaderItem(1)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.__g_rewards_tablewidget.verticalHeaderItem(2)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.__g_rewards_tablewidget.verticalHeaderItem(3)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.__g_rewards_tablewidget.verticalHeaderItem(4)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.__g_rewards_tablewidget.verticalHeaderItem(5)
        item.setText(_translate("SalesScreen", "New Row"))
        item = self.__g_rewards_tablewidget.horizontalHeaderItem(0)
        item.setText(_translate("SalesScreen", "Reward Name"))
        item = self.__g_rewards_tablewidget.horizontalHeaderItem(1)
        item.setText(_translate("SalesScreen", "Amount"))
        __sortingEnabled = self.__g_rewards_tablewidget.isSortingEnabled()
        self.__g_rewards_tablewidget.setSortingEnabled(False)
        item = self.__g_rewards_tablewidget.item(0, 0)
        item.setText(_translate("SalesScreen", "Blue Buffalo Dry Cat Food"))
        item = self.__g_rewards_tablewidget.item(0, 1)
        item.setText(_translate("SalesScreen", "$1.74"))
        item = self.__g_rewards_tablewidget.item(1, 0)
        item.setText(_translate("SalesScreen", "Blue Buffalo Wet Cat Food"))
        item = self.__g_rewards_tablewidget.item(1, 1)
        item.setText(_translate("SalesScreen", "$2.14"))
        item = self.__g_rewards_tablewidget.item(2, 0)
        item.setText(_translate("SalesScreen", "Blue Buffalo Dry Dog Food"))
        item = self.__g_rewards_tablewidget.item(2, 1)
        item.setText(_translate("SalesScreen", "$2.06"))
        item = self.__g_rewards_tablewidget.item(3, 0)
        item.setText(_translate("SalesScreen", "Blue Buffalo Wet Dog Food"))
        item = self.__g_rewards_tablewidget.item(3, 1)
        item.setText(_translate("SalesScreen", "$1.63"))
        item = self.__g_rewards_tablewidget.item(4, 0)
        item.setText(_translate("SalesScreen", "Vitakraft SunSeed Small Animal Food"))
        item = self.__g_rewards_tablewidget.item(4, 1)
        item.setText(_translate("SalesScreen", "$3.54"))
        item = self.__g_rewards_tablewidget.item(5, 0)
        item.setText(_translate("SalesScreen", "ZuPreem Hay"))
        item = self.__g_rewards_tablewidget.item(5, 1)
        item.setText(_translate("SalesScreen", "$1.92"))
        self.__g_rewards_tablewidget.setSortingEnabled(__sortingEnabled)
        self.__g_links_groupbox.setTitle(_translate("SalesScreen", "Links"))
        self.__g_special_order_toolbutton.setText(_translate("SalesScreen", "Special\n"
"Order"))
        self.__g_time_clock_toolbutton.setText(_translate("SalesScreen", "Time\n"
"Clock"))
        self.__g_wags_apply_now_toolbutton.setText(_translate("SalesScreen", "WAGS\n"
"Apply\n"
"Now"))
        self.__g_wags_info_toolbutton.setText(_translate("SalesScreen", "WAGS\n"
"Info"))
        self.__g_lookup_stock_toolbutton.setText(_translate("SalesScreen", "Lookup\n"
"Stock"))
        self.__g_adjust_stock_toolbutton.setText(_translate("SalesScreen", "Adjust\n"
"Stock"))
        self.g_promotions_groupbox.setTitle(_translate("SalesScreen", "Promotions"))
        self.toolButton_21.setText(_translate("SalesScreen", "Promo\n"
"1"))
        self.__g_product_ipad4_toolbutton_78.setText(_translate("SalesScreen", "Promo\n"
"2"))
        self.__g_product_ipad4_toolbutton_70.setText(_translate("SalesScreen", "Promo\n"
"3"))
        self.__g_product_ipad4_toolbutton_77.setText(_translate("SalesScreen", "Promo\n"
"4"))
        self.__g_product_ipad4_toolbutton_84.setText(_translate("SalesScreen", "Promo\n"
"5"))
        self.__g_product_ipad4_toolbutton_83.setText(_translate("SalesScreen", "Promo\n"
"6"))
        self.g_quicklinks_tabwidget.setTabText(self.g_quicklinks_tabwidget.indexOf(self.__g_shortcuts_widget), _translate("SalesScreen", "Shortcuts"))
        self.__g_product_ipad4_toolbutton_10.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_14.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_6.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_22.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_20.setText(_translate("SalesScreen", "*********012\n"
"*********012"))
        self.toolButton_8.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_24.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.toolButton_13.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.toolButton_16.setText(_translate("SalesScreen", "iPad4 4G "))
        self.__g_product_ipad4_toolbutton_3.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_13.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_15.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_19.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_16.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.toolButton_11.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_9.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_21.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_27.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_26.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_11.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_12.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_34.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_31.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_35.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_33.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_32.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_23.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_29.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_28.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_30.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_18.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_17.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.toolButton_9.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_25.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.__g_product_ipad4_toolbutton_36.setText(_translate("SalesScreen", "iPad4 4G\n"
"128Gb"))
        self.g_quicklinks_tabwidget.setTabText(self.g_quicklinks_tabwidget.indexOf(self.__g_products_widget), _translate("SalesScreen", "Products"))
        self.g_quicklinks_tabwidget.setTabText(self.g_quicklinks_tabwidget.indexOf(self.g_bundle_widget), _translate("SalesScreen", "Bundle Info"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    SalesScreen = QtWidgets.QDialog()
    ui = Ui_SalesScreen()
    ui.setupUi(SalesScreen)
    SalesScreen.show()
    sys.exit(app.exec_())
