# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'closetraycount_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_CloseTrayCountDialog(object):
    def setupUi(self, CloseTrayCountDialog):
        CloseTrayCountDialog.setObjectName("CloseTrayCountDialog")
        CloseTrayCountDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        CloseTrayCountDialog.resize(683, 570)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(CloseTrayCountDialog.sizePolicy().hasHeightForWidth())
        CloseTrayCountDialog.setSizePolicy(sizePolicy)
        CloseTrayCountDialog.setMinimumSize(QtCore.QSize(683, 500))
        CloseTrayCountDialog.setMaximumSize(QtCore.QSize(16777215, 16777215))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/All Icons/money_bill_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        CloseTrayCountDialog.setWindowIcon(icon)
        CloseTrayCountDialog.setStyleSheet("\n"
"QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 11pt;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"\n"
"QTableWidget {\n"
"    border: 0px;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"\n"
"#g_dialog_frame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-size: 12px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
" QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected, \n"
"QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QTabWidget {\n"
"    border:0px;\n"
"}\n"
"\n"
"#g_transfer_widget, \n"
"#g_reset_widget,\n"
"#g_verify_widget,\n"
"#g_incoming_widget,\n"
"#g_outgoing_widget {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius:0px;\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"#g_desc_label {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color: #dddddd;\n"
"    color: #333333;\n"
"}\n"
"\n"
"#g_closeregister_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"#g_closeregister_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_amounttotal_lineedit {\n"
"    color: #000000\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-weight: bold;\n"
"    font-size: 17px;\n"
"}\n"
"\n"
"#g_notifyemail_label {\n"
"    font-weight: bold;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_container_frame_2 QPushButton {\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"}")
        CloseTrayCountDialog.setModal(True)
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(CloseTrayCountDialog)
        self.verticalLayout_3.setContentsMargins(12, 12, 12, 12)
        self.verticalLayout_3.setSpacing(12)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_main_label = QtWidgets.QLabel(CloseTrayCountDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_3.addWidget(self.g_main_label)
        self.g_notifyemail_label = QtWidgets.QLabel(CloseTrayCountDialog)
        self.g_notifyemail_label.setMinimumSize(QtCore.QSize(0, 25))
        self.g_notifyemail_label.setMaximumSize(QtCore.QSize(16777215, 25))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_notifyemail_label.setFont(font)
        self.g_notifyemail_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_notifyemail_label.setWordWrap(True)
        self.g_notifyemail_label.setObjectName("g_notifyemail_label")
        self.verticalLayout_3.addWidget(self.g_notifyemail_label)
        self.g_countyourdrawer_groupbox = QtWidgets.QGroupBox(CloseTrayCountDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_countyourdrawer_groupbox.sizePolicy().hasHeightForWidth())
        self.g_countyourdrawer_groupbox.setSizePolicy(sizePolicy)
        self.g_countyourdrawer_groupbox.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_countyourdrawer_groupbox.setFont(font)
        self.g_countyourdrawer_groupbox.setObjectName("g_countyourdrawer_groupbox")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_countyourdrawer_groupbox)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.frame = QtWidgets.QFrame(self.g_countyourdrawer_groupbox)
        self.frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_cashdrawertotals_table = QtWidgets.QTableWidget(self.frame)
        self.g_cashdrawertotals_table.setMinimumSize(QtCore.QSize(0, 0))
        self.g_cashdrawertotals_table.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_cashdrawertotals_table.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_cashdrawertotals_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_cashdrawertotals_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_cashdrawertotals_table.setSizeAdjustPolicy(QtWidgets.QAbstractScrollArea.AdjustToContentsOnFirstShow)
        self.g_cashdrawertotals_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_cashdrawertotals_table.setAlternatingRowColors(True)
        self.g_cashdrawertotals_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_cashdrawertotals_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_cashdrawertotals_table.setRowCount(4)
        self.g_cashdrawertotals_table.setObjectName("g_cashdrawertotals_table")
        self.g_cashdrawertotals_table.setColumnCount(3)
        item = QtWidgets.QTableWidgetItem()
        self.g_cashdrawertotals_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_cashdrawertotals_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_cashdrawertotals_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_cashdrawertotals_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cashdrawertotals_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cashdrawertotals_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_cashdrawertotals_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cashdrawertotals_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cashdrawertotals_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_cashdrawertotals_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cashdrawertotals_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_cashdrawertotals_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cashdrawertotals_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_cashdrawertotals_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_cashdrawertotals_table.setItem(3, 1, item)
        self.g_cashdrawertotals_table.horizontalHeader().setVisible(False)
        self.g_cashdrawertotals_table.horizontalHeader().setDefaultSectionSize(220)
        self.g_cashdrawertotals_table.horizontalHeader().setStretchLastSection(True)
        self.g_cashdrawertotals_table.verticalHeader().setVisible(False)
        self.verticalLayout_2.addWidget(self.g_cashdrawertotals_table)
        self.frame_15 = QtWidgets.QFrame(self.frame)
        self.frame_15.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_15.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_15.setObjectName("frame_15")
        self.horizontalLayout_18 = QtWidgets.QHBoxLayout(self.frame_15)
        self.horizontalLayout_18.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_18.setObjectName("horizontalLayout_18")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_18.addItem(spacerItem)
        self.g_total_label = QtWidgets.QLabel(self.frame_15)
        self.g_total_label.setMinimumSize(QtCore.QSize(45, 0))
        self.g_total_label.setMaximumSize(QtCore.QSize(45, 16777215))
        self.g_total_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_total_label.setObjectName("g_total_label")
        self.horizontalLayout_18.addWidget(self.g_total_label)
        self.g_amounttotal_lineedit = QtWidgets.QLineEdit(self.frame_15)
        self.g_amounttotal_lineedit.setMinimumSize(QtCore.QSize(200, 0))
        self.g_amounttotal_lineedit.setMaximumSize(QtCore.QSize(200, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_amounttotal_lineedit.setFont(font)
        self.g_amounttotal_lineedit.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_amounttotal_lineedit.setObjectName("g_amounttotal_lineedit")
        self.horizontalLayout_18.addWidget(self.g_amounttotal_lineedit)
        self.verticalLayout_2.addWidget(self.frame_15)
        self.horizontalLayout_2.addWidget(self.frame)
        self.frame_2 = QtWidgets.QFrame(self.g_countyourdrawer_groupbox)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_container_frame_2 = QtWidgets.QFrame(self.frame_2)
        self.g_container_frame_2.setMinimumSize(QtCore.QSize(271, 345))
        self.g_container_frame_2.setMaximumSize(QtCore.QSize(271, 345))
        self.g_container_frame_2.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_container_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_container_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_container_frame_2.setObjectName("g_container_frame_2")
        self.g_readout_lineedit = QtWidgets.QLineEdit(self.g_container_frame_2)
        self.g_readout_lineedit.setGeometry(QtCore.QRect(10, 10, 251, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_readout_lineedit.setFont(font)
        self.g_readout_lineedit.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_readout_lineedit.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_readout_lineedit.setObjectName("g_readout_lineedit")
        self.__g_keypad_item_buttons_frame_2 = QtWidgets.QFrame(self.g_container_frame_2)
        self.__g_keypad_item_buttons_frame_2.setEnabled(True)
        self.__g_keypad_item_buttons_frame_2.setGeometry(QtCore.QRect(10, 86, 250, 250))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_keypad_item_buttons_frame_2.sizePolicy().hasHeightForWidth())
        self.__g_keypad_item_buttons_frame_2.setSizePolicy(sizePolicy)
        self.__g_keypad_item_buttons_frame_2.setMinimumSize(QtCore.QSize(200, 250))
        self.__g_keypad_item_buttons_frame_2.setMaximumSize(QtCore.QSize(400, 500))
        self.__g_keypad_item_buttons_frame_2.setFocusPolicy(QtCore.Qt.NoFocus)
        self.__g_keypad_item_buttons_frame_2.setStyleSheet("")
        self.__g_keypad_item_buttons_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_keypad_item_buttons_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_keypad_item_buttons_frame_2.setObjectName("__g_keypad_item_buttons_frame_2")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.__g_keypad_item_buttons_frame_2)
        self.gridLayout_2.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_2.setSpacing(5)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.g_zero_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_zero_button.setGeometry(QtCore.QRect(10, 240, 59, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_zero_button.sizePolicy().hasHeightForWidth())
        self.g_zero_button.setSizePolicy(sizePolicy)
        self.g_zero_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_zero_button.setObjectName("g_zero_button")
        self.g_four_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_four_button.setGeometry(QtCore.QRect(10, 113, 59, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_four_button.sizePolicy().hasHeightForWidth())
        self.g_four_button.setSizePolicy(sizePolicy)
        self.g_four_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_four_button.setObjectName("g_four_button")
        self.g_seven_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_seven_button.setGeometry(QtCore.QRect(10, 49, 59, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_seven_button.sizePolicy().hasHeightForWidth())
        self.g_seven_button.setSizePolicy(sizePolicy)
        self.g_seven_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_seven_button.setObjectName("g_seven_button")
        self.g_one_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_one_button.setGeometry(QtCore.QRect(10, 177, 59, 58))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_one_button.sizePolicy().hasHeightForWidth())
        self.g_one_button.setSizePolicy(sizePolicy)
        self.g_one_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_one_button.setObjectName("g_one_button")
        self.g_doublezero_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_doublezero_button.setGeometry(QtCore.QRect(74, 240, 59, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_doublezero_button.sizePolicy().hasHeightForWidth())
        self.g_doublezero_button.setSizePolicy(sizePolicy)
        self.g_doublezero_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_doublezero_button.setObjectName("g_doublezero_button")
        self.g_enter_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_enter_button.setGeometry(QtCore.QRect(201, 177, 59, 122))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_enter_button.sizePolicy().hasHeightForWidth())
        self.g_enter_button.setSizePolicy(sizePolicy)
        palette = QtGui.QPalette()
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255, 128))
        brush.setStyle(QtCore.Qt.NoBrush)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.PlaceholderText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255, 128))
        brush.setStyle(QtCore.Qt.NoBrush)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.PlaceholderText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(187, 226, 187))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255, 128))
        brush.setStyle(QtCore.Qt.NoBrush)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.PlaceholderText, brush)
        self.g_enter_button.setPalette(palette)
        self.g_enter_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_enter_button.setObjectName("g_enter_button")
        self.g_nine_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_nine_button.setGeometry(QtCore.QRect(138, 49, 58, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_nine_button.sizePolicy().hasHeightForWidth())
        self.g_nine_button.setSizePolicy(sizePolicy)
        self.g_nine_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_nine_button.setObjectName("g_nine_button")
        self.g_two_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_two_button.setGeometry(QtCore.QRect(74, 177, 59, 58))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_two_button.sizePolicy().hasHeightForWidth())
        self.g_two_button.setSizePolicy(sizePolicy)
        self.g_two_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_two_button.setObjectName("g_two_button")
        self.g_decimal_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_decimal_button.setGeometry(QtCore.QRect(138, 240, 58, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_decimal_button.sizePolicy().hasHeightForWidth())
        self.g_decimal_button.setSizePolicy(sizePolicy)
        self.g_decimal_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_decimal_button.setObjectName("g_decimal_button")
        self.g_eight_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_eight_button.setGeometry(QtCore.QRect(74, 49, 59, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_eight_button.sizePolicy().hasHeightForWidth())
        self.g_eight_button.setSizePolicy(sizePolicy)
        self.g_eight_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_eight_button.setObjectName("g_eight_button")
        self.g_six_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_six_button.setGeometry(QtCore.QRect(138, 113, 58, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_six_button.sizePolicy().hasHeightForWidth())
        self.g_six_button.setSizePolicy(sizePolicy)
        self.g_six_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_six_button.setObjectName("g_six_button")
        self.g_five_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_five_button.setGeometry(QtCore.QRect(74, 113, 59, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_five_button.sizePolicy().hasHeightForWidth())
        self.g_five_button.setSizePolicy(sizePolicy)
        self.g_five_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_five_button.setObjectName("g_five_button")
        self.g_three_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_three_button.setGeometry(QtCore.QRect(138, 177, 58, 58))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_three_button.sizePolicy().hasHeightForWidth())
        self.g_three_button.setSizePolicy(sizePolicy)
        self.g_three_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_three_button.setObjectName("g_three_button")
        self.g_backspace_button = QtWidgets.QPushButton(self.g_container_frame_2)
        self.g_backspace_button.setGeometry(QtCore.QRect(200, 50, 59, 123))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_backspace_button.sizePolicy().hasHeightForWidth())
        self.g_backspace_button.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_backspace_button.setFont(font)
        self.g_backspace_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_backspace_button.setText("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/invoices_screen/backspace"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_backspace_button.setIcon(icon1)
        self.g_backspace_button.setIconSize(QtCore.QSize(32, 32))
        self.g_backspace_button.setObjectName("g_backspace_button")
        self.verticalLayout.addWidget(self.g_container_frame_2)
        spacerItem1 = QtWidgets.QSpacerItem(20, 58, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem1)
        self.horizontalLayout_2.addWidget(self.frame_2)
        self.verticalLayout_3.addWidget(self.g_countyourdrawer_groupbox)
        self.g_dialogbuttons_frame = QtWidgets.QFrame(CloseTrayCountDialog)
        self.g_dialogbuttons_frame.setStyleSheet("#g_dialog_buttons_frame, #g_dialog_buttons_frame {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"")
        self.g_dialogbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_dialogbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_dialogbuttons_frame.setObjectName("g_dialogbuttons_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_dialogbuttons_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(12)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        spacerItem2 = QtWidgets.QSpacerItem(116, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem2)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(175, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/invoices_screen/icons/flat_x_white_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon2)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_6.addWidget(self.g_cancel_button)
        self.g_closeregister_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_closeregister_button.setMinimumSize(QtCore.QSize(175, 40))
        self.g_closeregister_button.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_closeregister_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_closeregister_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/invoices_screen/icons/flat_checkmark_white_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_closeregister_button.setIcon(icon3)
        self.g_closeregister_button.setIconSize(QtCore.QSize(24, 24))
        self.g_closeregister_button.setObjectName("g_closeregister_button")
        self.horizontalLayout_6.addWidget(self.g_closeregister_button)
        self.verticalLayout_3.addWidget(self.g_dialogbuttons_frame)

        self.retranslateUi(CloseTrayCountDialog)
        QtCore.QMetaObject.connectSlotsByName(CloseTrayCountDialog)

    def retranslateUi(self, CloseTrayCountDialog):
        _translate = QtCore.QCoreApplication.translate
        CloseTrayCountDialog.setWindowTitle(_translate("CloseTrayCountDialog", "Closing Tray Count"))
        CloseTrayCountDialog.setProperty("invalid_value_msg_text", _translate("CloseTrayCountDialog", "Please enter a valid numeric value."))
        CloseTrayCountDialog.setProperty("invalid_value_title_text", _translate("CloseTrayCountDialog", "Error"))
        CloseTrayCountDialog.setProperty("closing_register_msg_text", _translate("CloseTrayCountDialog", "Closing register ..."))
        CloseTrayCountDialog.setProperty("cash_withdrawal_unavailable_msg_text", _translate("CloseTrayCountDialog", "There is no available cash \n"
"to withdraw at this time."))
        CloseTrayCountDialog.setProperty("cash_withdrawal_unavailable_title_text", _translate("CloseTrayCountDialog", "Cash Withdrawal Unavailable"))
        CloseTrayCountDialog.setProperty("less_than_opening_msg_text", _translate("CloseTrayCountDialog", "Cash amount cannot be less\n"
"than the opening amount."))
        CloseTrayCountDialog.setProperty("exceeds_cash_available_msg_text", _translate("CloseTrayCountDialog", "The cash amount cannot exceed the\n"
"amount available for withdrawal."))
        self.g_main_label.setText(_translate("CloseTrayCountDialog", "Closing Tray Count"))
        self.g_notifyemail_label.setText(_translate("CloseTrayCountDialog", "Enter the value of each <payment_method> and then press \"Return Total\" at the bottom to return the value to the previous screen."))
        self.g_countyourdrawer_groupbox.setTitle(_translate("CloseTrayCountDialog", "Count"))
        item = self.g_cashdrawertotals_table.verticalHeaderItem(0)
        item.setText(_translate("CloseTrayCountDialog", "New Row"))
        item = self.g_cashdrawertotals_table.verticalHeaderItem(1)
        item.setText(_translate("CloseTrayCountDialog", "New Row"))
        item = self.g_cashdrawertotals_table.verticalHeaderItem(2)
        item.setText(_translate("CloseTrayCountDialog", "New Row"))
        item = self.g_cashdrawertotals_table.horizontalHeaderItem(0)
        item.setText(_translate("CloseTrayCountDialog", "Number"))
        item = self.g_cashdrawertotals_table.horizontalHeaderItem(1)
        item.setText(_translate("CloseTrayCountDialog", "Amount"))
        item = self.g_cashdrawertotals_table.horizontalHeaderItem(2)
        item.setText(_translate("CloseTrayCountDialog", "Delete"))
        __sortingEnabled = self.g_cashdrawertotals_table.isSortingEnabled()
        self.g_cashdrawertotals_table.setSortingEnabled(False)
        item = self.g_cashdrawertotals_table.item(0, 0)
        item.setText(_translate("CloseTrayCountDialog", "1"))
        item = self.g_cashdrawertotals_table.item(0, 1)
        item.setText(_translate("CloseTrayCountDialog", "5.73"))
        item = self.g_cashdrawertotals_table.item(1, 0)
        item.setText(_translate("CloseTrayCountDialog", "2"))
        item = self.g_cashdrawertotals_table.item(1, 1)
        item.setText(_translate("CloseTrayCountDialog", "4.54"))
        item = self.g_cashdrawertotals_table.item(2, 0)
        item.setText(_translate("CloseTrayCountDialog", "3"))
        item = self.g_cashdrawertotals_table.item(2, 1)
        item.setText(_translate("CloseTrayCountDialog", "2.30"))
        item = self.g_cashdrawertotals_table.item(3, 0)
        item.setText(_translate("CloseTrayCountDialog", "Total"))
        item = self.g_cashdrawertotals_table.item(3, 1)
        item.setText(_translate("CloseTrayCountDialog", "12.57"))
        self.g_cashdrawertotals_table.setSortingEnabled(__sortingEnabled)
        self.g_total_label.setText(_translate("CloseTrayCountDialog", "Total"))
        self.g_readout_lineedit.setPlaceholderText(_translate("CloseTrayCountDialog", "Enter Amount"))
        self.g_zero_button.setText(_translate("CloseTrayCountDialog", "0"))
        self.g_four_button.setText(_translate("CloseTrayCountDialog", "4"))
        self.g_seven_button.setText(_translate("CloseTrayCountDialog", "7"))
        self.g_one_button.setText(_translate("CloseTrayCountDialog", "1"))
        self.g_doublezero_button.setText(_translate("CloseTrayCountDialog", "00"))
        self.g_enter_button.setText(_translate("CloseTrayCountDialog", "Enter"))
        self.g_nine_button.setText(_translate("CloseTrayCountDialog", "9"))
        self.g_two_button.setText(_translate("CloseTrayCountDialog", "2"))
        self.g_decimal_button.setText(_translate("CloseTrayCountDialog", "."))
        self.g_eight_button.setText(_translate("CloseTrayCountDialog", "8"))
        self.g_six_button.setText(_translate("CloseTrayCountDialog", "6"))
        self.g_five_button.setText(_translate("CloseTrayCountDialog", "5"))
        self.g_three_button.setText(_translate("CloseTrayCountDialog", "3"))
        self.g_cancel_button.setText(_translate("CloseTrayCountDialog", "  Cancel"))
        self.g_cancel_button.setShortcut(_translate("CloseTrayCountDialog", "Enter"))
        self.g_closeregister_button.setText(_translate("CloseTrayCountDialog", "Return Total"))
        self.g_closeregister_button.setShortcut(_translate("CloseTrayCountDialog", "Enter"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    CloseTrayCountDialog = QtWidgets.QDialog()
    ui = Ui_CloseTrayCountDialog()
    ui.setupUi(CloseTrayCountDialog)
    CloseTrayCountDialog.show()
    sys.exit(app.exec_())
