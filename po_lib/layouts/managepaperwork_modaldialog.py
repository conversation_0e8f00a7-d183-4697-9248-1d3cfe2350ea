# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managepaperwork_modaldialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_ManagePaperwork(object):
    def setupUi(self, ManagePaperwork):
        ManagePaperwork.setObjectName("ManagePaperwork")
        ManagePaperwork.resize(670, 709)
        ManagePaperwork.setMinimumSize(QtCore.QSize(630, 600))
        ManagePaperwork.setStyleSheet("#ManagePaperwork {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox {\n"
"    padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/pet_tracker/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/pet_tracker/checked\');\n"
"    border:0px;\n"
"}\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    background-image: url(:/pet_tracker/drop-down-arrow);\n"
"}\n"
"\n"
"QDateTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
" \n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/pet_tracker/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"PyCheckComboBox {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"PyCheckComboBox::item, QAbstractItemView::item {\n"
"     margin-top: 10px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
".QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QListWidget {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    padding: 2px;\n"
"    background: transparent;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-size: 13px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"/*\n"
".QTabWidget::tab-bar {\n"
"     left: 1px; \n"
" }\n"
"*/\n"
"\n"
".QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"    font-size: 13px;\n"
"}\n"
"\n"
".QTabBar::tab:selected, .QTabBar::tab:hover {\n"
"    background-color: white;\n"
"}\n"
"\n"
".QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
"}\n"
"\n"
"QRadioButton {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_paperwork_widget,\n"
"#g_papeworksettings_widget {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius:0px;\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"#g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"\n"
"#g_appliesto_list {\n"
"    background-color: #007f00;\n"
"    border:0px;\n"
"}\n"
"\n"
"#g_addons_checkcombobox::drop-down,\n"
"#g_location_checkcombobox::drop-down,\n"
"#g_type_combobox::drop-down,\n"
"#g_breed_checkcombobox::drop-down,\n"
"#g_breed_combobox::drop-down,\n"
"#g_type_checkcombobox::drop-down,\n"
"#g_paperworktype_combobox::drop-down,\n"
"#g_entity_combobox::drop-down,\n"
"#g_classification_combobox::drop-down {    \n"
"    background-image: url(:/pet_tracker/drop-down-arrow);\n"
"}\n"
"\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_presalehelp_label,\n"
"#g_esignhelp_label {\n"
"    font-weight: normal;\n"
"}\n"
"\n"
"QFrame QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QFrame QLineEdit,\n"
"#g_autoprintoptionshelp_label,\n"
"#g_autoprintoptionshelp_label_2,\n"
"#g_autoprintaddqtyhelp_label,\n"
"#g_autoprintsaleqtyhelp_label {\n"
"    font-size: 11px;\n"
"    font-weight: normal;\n"
"}\n"
"\n"
"#g_filesize_label {\n"
"    font-size: 10px;\n"
"    font-weight: normal;\n"
"}\n"
"\n"
"#g_pagenav_frame QLineEdit,\n"
"#g_pagenav_frame QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: normal;\n"
"}")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(ManagePaperwork)
        self.verticalLayout_4.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_top_frame = QtWidgets.QFrame(ManagePaperwork)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setMaximumSize(QtCore.QSize(16777215, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setSpacing(0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_7.addWidget(self.g_progress_frame)
        self.verticalLayout_4.addWidget(self.g_top_frame)
        self.g_titlepagenav_frame = QtWidgets.QFrame(ManagePaperwork)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_6.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_6.addItem(spacerItem)
        self.horizontalLayout_5.addWidget(self.g_title_frame)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout_2 = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout_2.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout_2.setSpacing(6)
        self.g_pagenav_hlayout_2.setObjectName("g_pagenav_hlayout_2")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout_2.addItem(spacerItem1)
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/pet_tracker/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout_2.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/pet_tracker/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout_2.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagenav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout_2.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_slash_label.setObjectName("g_slash_label")
        self.g_pagenav_hlayout_2.addWidget(self.g_slash_label)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout_2.addWidget(self.g_totalpages_label)
        self.horizontalLayout_5.addWidget(self.g_pagenav_frame)
        self.verticalLayout_4.addWidget(self.g_titlepagenav_frame)
        self.g_main_tabs = QtWidgets.QTabWidget(ManagePaperwork)
        self.g_main_tabs.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_main_tabs.sizePolicy().hasHeightForWidth())
        self.g_main_tabs.setSizePolicy(sizePolicy)
        self.g_main_tabs.setMinimumSize(QtCore.QSize(0, 200))
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        font.setKerning(True)
        self.g_main_tabs.setFont(font)
        self.g_main_tabs.setAutoFillBackground(False)
        self.g_main_tabs.setStyleSheet("")
        self.g_main_tabs.setTabPosition(QtWidgets.QTabWidget.North)
        self.g_main_tabs.setTabShape(QtWidgets.QTabWidget.Rounded)
        self.g_main_tabs.setIconSize(QtCore.QSize(50, 50))
        self.g_main_tabs.setElideMode(QtCore.Qt.ElideNone)
        self.g_main_tabs.setUsesScrollButtons(True)
        self.g_main_tabs.setDocumentMode(True)
        self.g_main_tabs.setTabsClosable(False)
        self.g_main_tabs.setMovable(False)
        self.g_main_tabs.setObjectName("g_main_tabs")
        self.g_paperwork_widget = QtWidgets.QWidget()
        self.g_paperwork_widget.setObjectName("g_paperwork_widget")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_paperwork_widget)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_quickfilters_frame = QtWidgets.QFrame(self.g_paperwork_widget)
        self.g_quickfilters_frame.setMinimumSize(QtCore.QSize(0, 31))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_quickfilters_frame.setFont(font)
        self.g_quickfilters_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_quickfilters_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_quickfilters_frame.setObjectName("g_quickfilters_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_quickfilters_frame)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.frame_2 = QtWidgets.QFrame(self.g_quickfilters_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_2.sizePolicy().hasHeightForWidth())
        self.frame_2.setSizePolicy(sizePolicy)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout_7.setContentsMargins(0, 9, 9, 9)
        self.verticalLayout_7.setSpacing(0)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.g_name_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_name_frame.sizePolicy().hasHeightForWidth())
        self.g_name_frame.setSizePolicy(sizePolicy)
        self.g_name_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_name_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_name_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_name_frame.setObjectName("g_name_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_name_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setSpacing(6)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_displayname_label = QtWidgets.QLabel(self.g_name_frame)
        self.g_displayname_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_displayname_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_displayname_label.setFont(font)
        self.g_displayname_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_displayname_label.setObjectName("g_displayname_label")
        self.horizontalLayout_3.addWidget(self.g_displayname_label)
        self.g_displayname_lineedit = QtWidgets.QLineEdit(self.g_name_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_displayname_lineedit.sizePolicy().hasHeightForWidth())
        self.g_displayname_lineedit.setSizePolicy(sizePolicy)
        self.g_displayname_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_displayname_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_displayname_lineedit.setFont(font)
        self.g_displayname_lineedit.setText("")
        self.g_displayname_lineedit.setReadOnly(False)
        self.g_displayname_lineedit.setProperty("qp_ppw_paperwork_displayname", "")
        self.g_displayname_lineedit.setObjectName("g_displayname_lineedit")
        self.horizontalLayout_3.addWidget(self.g_displayname_lineedit)
        self.verticalLayout_7.addWidget(self.g_name_frame)
        self.g_classification_frame = QtWidgets.QFrame(self.frame_2)
        self.g_classification_frame.setMinimumSize(QtCore.QSize(0, 36))
        self.g_classification_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_classification_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_classification_frame.setObjectName("g_classification_frame")
        self.horizontalLayout_41 = QtWidgets.QHBoxLayout(self.g_classification_frame)
        self.horizontalLayout_41.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout_41.setSpacing(6)
        self.horizontalLayout_41.setObjectName("horizontalLayout_41")
        self.g_classification_label = QtWidgets.QLabel(self.g_classification_frame)
        self.g_classification_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_classification_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_classification_label.setFont(font)
        self.g_classification_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_classification_label.setObjectName("g_classification_label")
        self.horizontalLayout_41.addWidget(self.g_classification_label)
        self.g_classification_combobox = QtWidgets.QComboBox(self.g_classification_frame)
        self.g_classification_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_classification_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_classification_combobox.setCurrentText("")
        self.g_classification_combobox.setProperty("qp_ppw_pet_ppw_class_id", "")
        self.g_classification_combobox.setObjectName("g_classification_combobox")
        self.horizontalLayout_41.addWidget(self.g_classification_combobox)
        self.verticalLayout_7.addWidget(self.g_classification_frame)
        self.g_entity_type_frame = QtWidgets.QFrame(self.frame_2)
        self.g_entity_type_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_entity_type_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_entity_type_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_entity_type_frame.setObjectName("g_entity_type_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_entity_type_frame)
        self.horizontalLayout_6.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.label = QtWidgets.QLabel(self.g_entity_type_frame)
        self.label.setMinimumSize(QtCore.QSize(110, 0))
        self.label.setMaximumSize(QtCore.QSize(0, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.label.setFont(font)
        self.label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.label.setObjectName("label")
        self.horizontalLayout_6.addWidget(self.label)
        self.g_breeder_rb = QtWidgets.QRadioButton(self.g_entity_type_frame)
        self.g_breeder_rb.setObjectName("g_breeder_rb")
        self.horizontalLayout_6.addWidget(self.g_breeder_rb)
        self.g_broker_rb = QtWidgets.QRadioButton(self.g_entity_type_frame)
        self.g_broker_rb.setObjectName("g_broker_rb")
        self.horizontalLayout_6.addWidget(self.g_broker_rb)
        self.g_transporter_rb = QtWidgets.QRadioButton(self.g_entity_type_frame)
        self.g_transporter_rb.setObjectName("g_transporter_rb")
        self.horizontalLayout_6.addWidget(self.g_transporter_rb)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem2)
        self.verticalLayout_7.addWidget(self.g_entity_type_frame)
        self.g_singlepettype_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_singlepettype_frame.sizePolicy().hasHeightForWidth())
        self.g_singlepettype_frame.setSizePolicy(sizePolicy)
        self.g_singlepettype_frame.setMinimumSize(QtCore.QSize(0, 36))
        self.g_singlepettype_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_singlepettype_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_singlepettype_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_singlepettype_frame.setObjectName("g_singlepettype_frame")
        self.horizontalLayout_54 = QtWidgets.QHBoxLayout(self.g_singlepettype_frame)
        self.horizontalLayout_54.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout_54.setSpacing(6)
        self.horizontalLayout_54.setObjectName("horizontalLayout_54")
        self.g_entity_label_4 = QtWidgets.QLabel(self.g_singlepettype_frame)
        self.g_entity_label_4.setMinimumSize(QtCore.QSize(110, 0))
        self.g_entity_label_4.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_entity_label_4.setFont(font)
        self.g_entity_label_4.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entity_label_4.setObjectName("g_entity_label_4")
        self.horizontalLayout_54.addWidget(self.g_entity_label_4)
        self.g_type_combobox = QtWidgets.QComboBox(self.g_singlepettype_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_type_combobox.sizePolicy().hasHeightForWidth())
        self.g_type_combobox.setSizePolicy(sizePolicy)
        self.g_type_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_type_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_type_combobox.setEditable(False)
        self.g_type_combobox.setProperty("qp_ppw_pbreeder_entity_ids", "")
        self.g_type_combobox.setProperty("qp_ppw_pbroker_entity_ids", "")
        self.g_type_combobox.setProperty("qp_ppw_ptrnsp_entity_ids", "")
        self.g_type_combobox.setObjectName("g_type_combobox")
        self.g_type_combobox.addItem("")
        self.horizontalLayout_54.addWidget(self.g_type_combobox)
        self.verticalLayout_7.addWidget(self.g_singlepettype_frame)
        self.g_applicablepettypes_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_applicablepettypes_frame.sizePolicy().hasHeightForWidth())
        self.g_applicablepettypes_frame.setSizePolicy(sizePolicy)
        self.g_applicablepettypes_frame.setMinimumSize(QtCore.QSize(0, 36))
        self.g_applicablepettypes_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_applicablepettypes_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_applicablepettypes_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_applicablepettypes_frame.setObjectName("g_applicablepettypes_frame")
        self.horizontalLayout_55 = QtWidgets.QHBoxLayout(self.g_applicablepettypes_frame)
        self.horizontalLayout_55.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout_55.setSpacing(6)
        self.horizontalLayout_55.setObjectName("horizontalLayout_55")
        self.g_entity_label_5 = QtWidgets.QLabel(self.g_applicablepettypes_frame)
        self.g_entity_label_5.setMinimumSize(QtCore.QSize(110, 0))
        self.g_entity_label_5.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_entity_label_5.setFont(font)
        self.g_entity_label_5.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entity_label_5.setObjectName("g_entity_label_5")
        self.horizontalLayout_55.addWidget(self.g_entity_label_5)
        self.g_type_checkcombobox = PyCheckCombobox(self.g_applicablepettypes_frame)
        self.g_type_checkcombobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_type_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_type_checkcombobox.setToolTip("")
        self.g_type_checkcombobox.setWhatsThis("")
        self.g_type_checkcombobox.setCurrentText("")
        self.g_type_checkcombobox.setObjectName("g_type_checkcombobox")
        self.horizontalLayout_55.addWidget(self.g_type_checkcombobox)
        self.verticalLayout_7.addWidget(self.g_applicablepettypes_frame)
        self.g_breeder_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_breeder_frame.sizePolicy().hasHeightForWidth())
        self.g_breeder_frame.setSizePolicy(sizePolicy)
        self.g_breeder_frame.setMinimumSize(QtCore.QSize(0, 36))
        self.g_breeder_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_breeder_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_breeder_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_breeder_frame.setObjectName("g_breeder_frame")
        self.horizontalLayout_51 = QtWidgets.QHBoxLayout(self.g_breeder_frame)
        self.horizontalLayout_51.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout_51.setSpacing(6)
        self.horizontalLayout_51.setObjectName("horizontalLayout_51")
        self.g_entity_label = QtWidgets.QLabel(self.g_breeder_frame)
        self.g_entity_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_entity_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_entity_label.setFont(font)
        self.g_entity_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entity_label.setObjectName("g_entity_label")
        self.horizontalLayout_51.addWidget(self.g_entity_label)
        self.g_entity_combobox = QtWidgets.QComboBox(self.g_breeder_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_entity_combobox.sizePolicy().hasHeightForWidth())
        self.g_entity_combobox.setSizePolicy(sizePolicy)
        self.g_entity_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_entity_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_entity_combobox.setEditable(False)
        self.g_entity_combobox.setProperty("qp_ppw_pbreeder_entity_ids", "")
        self.g_entity_combobox.setProperty("qp_ppw_pbroker_entity_ids", "")
        self.g_entity_combobox.setProperty("qp_ppw_ptrnsp_entity_ids", "")
        self.g_entity_combobox.setObjectName("g_entity_combobox")
        self.g_entity_combobox.addItem("")
        self.horizontalLayout_51.addWidget(self.g_entity_combobox)
        self.verticalLayout_7.addWidget(self.g_breeder_frame)
        self.g_breed_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_breed_frame.sizePolicy().hasHeightForWidth())
        self.g_breed_frame.setSizePolicy(sizePolicy)
        self.g_breed_frame.setMinimumSize(QtCore.QSize(0, 36))
        self.g_breed_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_breed_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_breed_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_breed_frame.setObjectName("g_breed_frame")
        self.horizontalLayout_53 = QtWidgets.QHBoxLayout(self.g_breed_frame)
        self.horizontalLayout_53.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout_53.setSpacing(6)
        self.horizontalLayout_53.setObjectName("horizontalLayout_53")
        self.g_entity_label_3 = QtWidgets.QLabel(self.g_breed_frame)
        self.g_entity_label_3.setMinimumSize(QtCore.QSize(110, 0))
        self.g_entity_label_3.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_entity_label_3.setFont(font)
        self.g_entity_label_3.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entity_label_3.setObjectName("g_entity_label_3")
        self.horizontalLayout_53.addWidget(self.g_entity_label_3)
        self.g_breed_combobox = QtWidgets.QComboBox(self.g_breed_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_breed_combobox.sizePolicy().hasHeightForWidth())
        self.g_breed_combobox.setSizePolicy(sizePolicy)
        self.g_breed_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_breed_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_breed_combobox.setEditable(False)
        self.g_breed_combobox.setProperty("qp_ppw_pbreeder_entity_ids", "")
        self.g_breed_combobox.setProperty("qp_ppw_pbroker_entity_ids", "")
        self.g_breed_combobox.setProperty("qp_ppw_ptrnsp_entity_ids", "")
        self.g_breed_combobox.setObjectName("g_breed_combobox")
        self.g_breed_combobox.addItem("")
        self.horizontalLayout_53.addWidget(self.g_breed_combobox)
        self.verticalLayout_7.addWidget(self.g_breed_frame)
        self.g_applicablebreeds_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_applicablebreeds_frame.sizePolicy().hasHeightForWidth())
        self.g_applicablebreeds_frame.setSizePolicy(sizePolicy)
        self.g_applicablebreeds_frame.setMinimumSize(QtCore.QSize(0, 36))
        self.g_applicablebreeds_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_applicablebreeds_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_applicablebreeds_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_applicablebreeds_frame.setObjectName("g_applicablebreeds_frame")
        self.horizontalLayout_56 = QtWidgets.QHBoxLayout(self.g_applicablebreeds_frame)
        self.horizontalLayout_56.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout_56.setSpacing(6)
        self.horizontalLayout_56.setObjectName("horizontalLayout_56")
        self.g_entity_label_6 = QtWidgets.QLabel(self.g_applicablebreeds_frame)
        self.g_entity_label_6.setMinimumSize(QtCore.QSize(110, 0))
        self.g_entity_label_6.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_entity_label_6.setFont(font)
        self.g_entity_label_6.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entity_label_6.setObjectName("g_entity_label_6")
        self.horizontalLayout_56.addWidget(self.g_entity_label_6)
        self.g_breed_checkcombobox = PyCheckCombobox(self.g_applicablebreeds_frame)
        self.g_breed_checkcombobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_breed_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_breed_checkcombobox.setToolTip("")
        self.g_breed_checkcombobox.setWhatsThis("")
        self.g_breed_checkcombobox.setCurrentText("")
        self.g_breed_checkcombobox.setObjectName("g_breed_checkcombobox")
        self.horizontalLayout_56.addWidget(self.g_breed_checkcombobox)
        self.verticalLayout_7.addWidget(self.g_applicablebreeds_frame)
        self.g_location_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_location_frame.sizePolicy().hasHeightForWidth())
        self.g_location_frame.setSizePolicy(sizePolicy)
        self.g_location_frame.setMinimumSize(QtCore.QSize(0, 36))
        self.g_location_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_location_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_location_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_location_frame.setObjectName("g_location_frame")
        self.horizontalLayout_57 = QtWidgets.QHBoxLayout(self.g_location_frame)
        self.horizontalLayout_57.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout_57.setSpacing(6)
        self.horizontalLayout_57.setObjectName("horizontalLayout_57")
        self.g_entity_label_7 = QtWidgets.QLabel(self.g_location_frame)
        self.g_entity_label_7.setMinimumSize(QtCore.QSize(110, 0))
        self.g_entity_label_7.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_entity_label_7.setFont(font)
        self.g_entity_label_7.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entity_label_7.setObjectName("g_entity_label_7")
        self.horizontalLayout_57.addWidget(self.g_entity_label_7)
        self.g_location_checkcombobox = PyCheckCombobox(self.g_location_frame)
        self.g_location_checkcombobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_location_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_location_checkcombobox.setToolTip("")
        self.g_location_checkcombobox.setWhatsThis("")
        self.g_location_checkcombobox.setCurrentText("")
        self.g_location_checkcombobox.setObjectName("g_location_checkcombobox")
        self.horizontalLayout_57.addWidget(self.g_location_checkcombobox)
        self.verticalLayout_7.addWidget(self.g_location_frame)
        self.g_addons_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_addons_frame.sizePolicy().hasHeightForWidth())
        self.g_addons_frame.setSizePolicy(sizePolicy)
        self.g_addons_frame.setMinimumSize(QtCore.QSize(0, 36))
        self.g_addons_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_addons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_addons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_addons_frame.setObjectName("g_addons_frame")
        self.horizontalLayout_58 = QtWidgets.QHBoxLayout(self.g_addons_frame)
        self.horizontalLayout_58.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout_58.setSpacing(6)
        self.horizontalLayout_58.setObjectName("horizontalLayout_58")
        self.g_addons_label = QtWidgets.QLabel(self.g_addons_frame)
        self.g_addons_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_addons_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_addons_label.setFont(font)
        self.g_addons_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_addons_label.setObjectName("g_addons_label")
        self.horizontalLayout_58.addWidget(self.g_addons_label)
        self.g_addons_checkcombobox = PyCheckCombobox(self.g_addons_frame)
        self.g_addons_checkcombobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_addons_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_addons_checkcombobox.setToolTip("")
        self.g_addons_checkcombobox.setWhatsThis("")
        self.g_addons_checkcombobox.setCurrentText("")
        self.g_addons_checkcombobox.setObjectName("g_addons_checkcombobox")
        self.horizontalLayout_58.addWidget(self.g_addons_checkcombobox)
        self.verticalLayout_7.addWidget(self.g_addons_frame)
        self.g_inspectionid_frame = QtWidgets.QFrame(self.frame_2)
        self.g_inspectionid_frame.setMinimumSize(QtCore.QSize(0, 36))
        self.g_inspectionid_frame.setMaximumSize(QtCore.QSize(16777215, 36))
        self.g_inspectionid_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_inspectionid_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_inspectionid_frame.setObjectName("g_inspectionid_frame")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.g_inspectionid_frame)
        self.verticalLayout_5.setContentsMargins(0, 5, 0, 5)
        self.verticalLayout_5.setSpacing(6)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.g_inspection_frame = QtWidgets.QFrame(self.g_inspectionid_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_inspection_frame.sizePolicy().hasHeightForWidth())
        self.g_inspection_frame.setSizePolicy(sizePolicy)
        self.g_inspection_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_inspection_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_inspection_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_inspection_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_inspection_frame.setObjectName("g_inspection_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_inspection_frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setSpacing(6)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_inspectionid_label = QtWidgets.QLabel(self.g_inspection_frame)
        self.g_inspectionid_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_inspectionid_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_inspectionid_label.setFont(font)
        self.g_inspectionid_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_inspectionid_label.setObjectName("g_inspectionid_label")
        self.horizontalLayout_4.addWidget(self.g_inspectionid_label)
        self.g_inspectionid_lineedit = QtWidgets.QLineEdit(self.g_inspection_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_inspectionid_lineedit.sizePolicy().hasHeightForWidth())
        self.g_inspectionid_lineedit.setSizePolicy(sizePolicy)
        self.g_inspectionid_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_inspectionid_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_inspectionid_lineedit.setFont(font)
        self.g_inspectionid_lineedit.setText("")
        self.g_inspectionid_lineedit.setReadOnly(False)
        self.g_inspectionid_lineedit.setProperty("qp_ppw_paperwork_displayname", "")
        self.g_inspectionid_lineedit.setObjectName("g_inspectionid_lineedit")
        self.horizontalLayout_4.addWidget(self.g_inspectionid_lineedit)
        self.g_value_label_4 = QtWidgets.QLabel(self.g_inspection_frame)
        self.g_value_label_4.setMinimumSize(QtCore.QSize(110, 0))
        self.g_value_label_4.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_value_label_4.setFont(font)
        self.g_value_label_4.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_value_label_4.setObjectName("g_value_label_4")
        self.horizontalLayout_4.addWidget(self.g_value_label_4)
        self.g_inspectiondate_timeedit = QtWidgets.QDateEdit(self.g_inspection_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_inspectiondate_timeedit.sizePolicy().hasHeightForWidth())
        self.g_inspectiondate_timeedit.setSizePolicy(sizePolicy)
        self.g_inspectiondate_timeedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_inspectiondate_timeedit.setMaximumSize(QtCore.QSize(150, 31))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_inspectiondate_timeedit.setFont(font)
        self.g_inspectiondate_timeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_inspectiondate_timeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 6), QtCore.QTime(16, 0, 0)))
        self.g_inspectiondate_timeedit.setCalendarPopup(True)
        self.g_inspectiondate_timeedit.setObjectName("g_inspectiondate_timeedit")
        self.horizontalLayout_4.addWidget(self.g_inspectiondate_timeedit)
        self.verticalLayout_5.addWidget(self.g_inspection_frame)
        self.verticalLayout_7.addWidget(self.g_inspectionid_frame)
        self.g_upload_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_upload_frame.sizePolicy().hasHeightForWidth())
        self.g_upload_frame.setSizePolicy(sizePolicy)
        self.g_upload_frame.setMinimumSize(QtCore.QSize(0, 36))
        self.g_upload_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_upload_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_upload_frame.setObjectName("g_upload_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_upload_frame)
        self.horizontalLayout.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout.setSpacing(6)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_uploadnew_label = QtWidgets.QLabel(self.g_upload_frame)
        self.g_uploadnew_label.setMinimumSize(QtCore.QSize(110, 27))
        self.g_uploadnew_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_uploadnew_label.setFont(font)
        self.g_uploadnew_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_uploadnew_label.setObjectName("g_uploadnew_label")
        self.horizontalLayout.addWidget(self.g_uploadnew_label)
        self.g_uploadfilename_lineedit = QtWidgets.QLineEdit(self.g_upload_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_uploadfilename_lineedit.sizePolicy().hasHeightForWidth())
        self.g_uploadfilename_lineedit.setSizePolicy(sizePolicy)
        self.g_uploadfilename_lineedit.setMinimumSize(QtCore.QSize(332, 31))
        self.g_uploadfilename_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_uploadfilename_lineedit.setFont(font)
        self.g_uploadfilename_lineedit.setReadOnly(False)
        self.g_uploadfilename_lineedit.setProperty("qp_ppw_paperwork_name", "")
        self.g_uploadfilename_lineedit.setObjectName("g_uploadfilename_lineedit")
        self.horizontalLayout.addWidget(self.g_uploadfilename_lineedit)
        self.g_selectfile_button = QtWidgets.QPushButton(self.g_upload_frame)
        self.g_selectfile_button.setMinimumSize(QtCore.QSize(120, 31))
        self.g_selectfile_button.setMaximumSize(QtCore.QSize(120, 31))
        self.g_selectfile_button.setStyleSheet("")
        self.g_selectfile_button.setIconSize(QtCore.QSize(24, 24))
        self.g_selectfile_button.setObjectName("g_selectfile_button")
        self.horizontalLayout.addWidget(self.g_selectfile_button)
        self.verticalLayout_7.addWidget(self.g_upload_frame)
        self.g_filesize_label = QtWidgets.QLabel(self.frame_2)
        self.g_filesize_label.setMinimumSize(QtCore.QSize(0, 0))
        self.g_filesize_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_filesize_label.setFont(font)
        self.g_filesize_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_filesize_label.setObjectName("g_filesize_label")
        self.verticalLayout_7.addWidget(self.g_filesize_label)
        spacerItem3 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_7.addItem(spacerItem3)
        self.g_name_frame.raise_()
        self.g_classification_frame.raise_()
        self.g_breeder_frame.raise_()
        self.g_upload_frame.raise_()
        self.g_entity_type_frame.raise_()
        self.g_breed_frame.raise_()
        self.g_filesize_label.raise_()
        self.g_singlepettype_frame.raise_()
        self.g_applicablepettypes_frame.raise_()
        self.g_applicablebreeds_frame.raise_()
        self.g_location_frame.raise_()
        self.g_addons_frame.raise_()
        self.g_inspectionid_frame.raise_()
        self.verticalLayout_2.addWidget(self.frame_2)
        self.g_autoprintoptions_groupbox = QtWidgets.QGroupBox(self.g_quickfilters_frame)
        self.g_autoprintoptions_groupbox.setMinimumSize(QtCore.QSize(0, 240))
        self.g_autoprintoptions_groupbox.setMaximumSize(QtCore.QSize(16777215, 240))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_autoprintoptions_groupbox.setFont(font)
        self.g_autoprintoptions_groupbox.setObjectName("g_autoprintoptions_groupbox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_autoprintoptions_groupbox)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_autoprintoptionshelp_label_2 = QtWidgets.QLabel(self.g_autoprintoptions_groupbox)
        self.g_autoprintoptionshelp_label_2.setAlignment(QtCore.Qt.AlignCenter)
        self.g_autoprintoptionshelp_label_2.setObjectName("g_autoprintoptionshelp_label_2")
        self.verticalLayout.addWidget(self.g_autoprintoptionshelp_label_2)
        self.g_autoprintoptionshelp_label = QtWidgets.QLabel(self.g_autoprintoptions_groupbox)
        self.g_autoprintoptionshelp_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_autoprintoptionshelp_label.setObjectName("g_autoprintoptionshelp_label")
        self.verticalLayout.addWidget(self.g_autoprintoptionshelp_label)
        self.g_autoprintaddqty_frame = QtWidgets.QFrame(self.g_autoprintoptions_groupbox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_autoprintaddqty_frame.sizePolicy().hasHeightForWidth())
        self.g_autoprintaddqty_frame.setSizePolicy(sizePolicy)
        self.g_autoprintaddqty_frame.setMinimumSize(QtCore.QSize(0, 41))
        self.g_autoprintaddqty_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_autoprintaddqty_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_autoprintaddqty_frame.setObjectName("g_autoprintaddqty_frame")
        self.horizontalLayout_20 = QtWidgets.QHBoxLayout(self.g_autoprintaddqty_frame)
        self.horizontalLayout_20.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_20.setObjectName("horizontalLayout_20")
        self.g_autoprintaddqty_label = QtWidgets.QLabel(self.g_autoprintaddqty_frame)
        self.g_autoprintaddqty_label.setMinimumSize(QtCore.QSize(165, 22))
        self.g_autoprintaddqty_label.setMaximumSize(QtCore.QSize(16777215, 22))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_autoprintaddqty_label.setFont(font)
        self.g_autoprintaddqty_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_autoprintaddqty_label.setObjectName("g_autoprintaddqty_label")
        self.horizontalLayout_20.addWidget(self.g_autoprintaddqty_label)
        self.g_autoprintaddqty_lineedit = QtWidgets.QLineEdit(self.g_autoprintaddqty_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_autoprintaddqty_lineedit.sizePolicy().hasHeightForWidth())
        self.g_autoprintaddqty_lineedit.setSizePolicy(sizePolicy)
        self.g_autoprintaddqty_lineedit.setMinimumSize(QtCore.QSize(50, 31))
        self.g_autoprintaddqty_lineedit.setMaximumSize(QtCore.QSize(50, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_autoprintaddqty_lineedit.setFont(font)
        self.g_autoprintaddqty_lineedit.setReadOnly(False)
        self.g_autoprintaddqty_lineedit.setPlaceholderText("")
        self.g_autoprintaddqty_lineedit.setProperty("qp_ppw_create_print_qty", "")
        self.g_autoprintaddqty_lineedit.setObjectName("g_autoprintaddqty_lineedit")
        self.horizontalLayout_20.addWidget(self.g_autoprintaddqty_lineedit)
        self.g_autoprintaddqtyhelp_label = QtWidgets.QLabel(self.g_autoprintaddqty_frame)
        self.g_autoprintaddqtyhelp_label.setWordWrap(True)
        self.g_autoprintaddqtyhelp_label.setObjectName("g_autoprintaddqtyhelp_label")
        self.horizontalLayout_20.addWidget(self.g_autoprintaddqtyhelp_label)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_20.addItem(spacerItem4)
        self.verticalLayout.addWidget(self.g_autoprintaddqty_frame)
        self.g_esign_frame = QtWidgets.QFrame(self.g_autoprintoptions_groupbox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_esign_frame.sizePolicy().hasHeightForWidth())
        self.g_esign_frame.setSizePolicy(sizePolicy)
        self.g_esign_frame.setMinimumSize(QtCore.QSize(0, 41))
        self.g_esign_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_esign_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_esign_frame.setObjectName("g_esign_frame")
        self.horizontalLayout_24 = QtWidgets.QHBoxLayout(self.g_esign_frame)
        self.horizontalLayout_24.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_24.setObjectName("horizontalLayout_24")
        self.g_esign_label = QtWidgets.QLabel(self.g_esign_frame)
        self.g_esign_label.setMinimumSize(QtCore.QSize(165, 22))
        self.g_esign_label.setMaximumSize(QtCore.QSize(16777215, 22))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_esign_label.setFont(font)
        self.g_esign_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_esign_label.setObjectName("g_esign_label")
        self.horizontalLayout_24.addWidget(self.g_esign_label)
        self.frame = QtWidgets.QFrame(self.g_esign_frame)
        self.frame.setMinimumSize(QtCore.QSize(50, 0))
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout_9.setContentsMargins(0, 5, 13, 10)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.q_esign_checkBox = QtWidgets.QCheckBox(self.frame)
        self.q_esign_checkBox.setMinimumSize(QtCore.QSize(0, 0))
        self.q_esign_checkBox.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.q_esign_checkBox.setText("")
        self.q_esign_checkBox.setObjectName("q_esign_checkBox")
        self.horizontalLayout_9.addWidget(self.q_esign_checkBox)
        self.horizontalLayout_24.addWidget(self.frame)
        self.g_esignhelp_label = QtWidgets.QLabel(self.g_esign_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_esignhelp_label.setFont(font)
        self.g_esignhelp_label.setWordWrap(True)
        self.g_esignhelp_label.setObjectName("g_esignhelp_label")
        self.horizontalLayout_24.addWidget(self.g_esignhelp_label)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_24.addItem(spacerItem5)
        self.verticalLayout.addWidget(self.g_esign_frame)
        self.g_presaleprintaddqty_frame = QtWidgets.QFrame(self.g_autoprintoptions_groupbox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_presaleprintaddqty_frame.sizePolicy().hasHeightForWidth())
        self.g_presaleprintaddqty_frame.setSizePolicy(sizePolicy)
        self.g_presaleprintaddqty_frame.setMinimumSize(QtCore.QSize(0, 41))
        self.g_presaleprintaddqty_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_presaleprintaddqty_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_presaleprintaddqty_frame.setObjectName("g_presaleprintaddqty_frame")
        self.horizontalLayout_22 = QtWidgets.QHBoxLayout(self.g_presaleprintaddqty_frame)
        self.horizontalLayout_22.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_22.setObjectName("horizontalLayout_22")
        self.g_presale_label = QtWidgets.QLabel(self.g_presaleprintaddqty_frame)
        self.g_presale_label.setMinimumSize(QtCore.QSize(165, 22))
        self.g_presale_label.setMaximumSize(QtCore.QSize(16777215, 22))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_presale_label.setFont(font)
        self.g_presale_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_presale_label.setObjectName("g_presale_label")
        self.horizontalLayout_22.addWidget(self.g_presale_label)
        self.g_presaleqty_lineedit = QtWidgets.QLineEdit(self.g_presaleprintaddqty_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_presaleqty_lineedit.sizePolicy().hasHeightForWidth())
        self.g_presaleqty_lineedit.setSizePolicy(sizePolicy)
        self.g_presaleqty_lineedit.setMinimumSize(QtCore.QSize(50, 31))
        self.g_presaleqty_lineedit.setMaximumSize(QtCore.QSize(50, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_presaleqty_lineedit.setFont(font)
        self.g_presaleqty_lineedit.setReadOnly(False)
        self.g_presaleqty_lineedit.setPlaceholderText("")
        self.g_presaleqty_lineedit.setProperty("qp_ppw_create_print_qty", "")
        self.g_presaleqty_lineedit.setObjectName("g_presaleqty_lineedit")
        self.horizontalLayout_22.addWidget(self.g_presaleqty_lineedit)
        self.g_presalehelp_label = QtWidgets.QLabel(self.g_presaleprintaddqty_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        font.setKerning(False)
        self.g_presalehelp_label.setFont(font)
        self.g_presalehelp_label.setWordWrap(False)
        self.g_presalehelp_label.setObjectName("g_presalehelp_label")
        self.horizontalLayout_22.addWidget(self.g_presalehelp_label)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_22.addItem(spacerItem6)
        self.verticalLayout.addWidget(self.g_presaleprintaddqty_frame)
        self.g_autoprintsaleqty_frame = QtWidgets.QFrame(self.g_autoprintoptions_groupbox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_autoprintsaleqty_frame.sizePolicy().hasHeightForWidth())
        self.g_autoprintsaleqty_frame.setSizePolicy(sizePolicy)
        self.g_autoprintsaleqty_frame.setMinimumSize(QtCore.QSize(0, 41))
        self.g_autoprintsaleqty_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_autoprintsaleqty_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_autoprintsaleqty_frame.setObjectName("g_autoprintsaleqty_frame")
        self.horizontalLayout_21 = QtWidgets.QHBoxLayout(self.g_autoprintsaleqty_frame)
        self.horizontalLayout_21.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_21.setObjectName("horizontalLayout_21")
        self.g_autoprintsaleqty_label = QtWidgets.QLabel(self.g_autoprintsaleqty_frame)
        self.g_autoprintsaleqty_label.setMinimumSize(QtCore.QSize(165, 22))
        self.g_autoprintsaleqty_label.setMaximumSize(QtCore.QSize(16777215, 22))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_autoprintsaleqty_label.setFont(font)
        self.g_autoprintsaleqty_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_autoprintsaleqty_label.setObjectName("g_autoprintsaleqty_label")
        self.horizontalLayout_21.addWidget(self.g_autoprintsaleqty_label)
        self.g_autoprintsaleqty_lineedit = QtWidgets.QLineEdit(self.g_autoprintsaleqty_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_autoprintsaleqty_lineedit.sizePolicy().hasHeightForWidth())
        self.g_autoprintsaleqty_lineedit.setSizePolicy(sizePolicy)
        self.g_autoprintsaleqty_lineedit.setMinimumSize(QtCore.QSize(50, 31))
        self.g_autoprintsaleqty_lineedit.setMaximumSize(QtCore.QSize(50, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_autoprintsaleqty_lineedit.setFont(font)
        self.g_autoprintsaleqty_lineedit.setReadOnly(False)
        self.g_autoprintsaleqty_lineedit.setPlaceholderText("")
        self.g_autoprintsaleqty_lineedit.setProperty("qp_ppw_sale_print_qty", "")
        self.g_autoprintsaleqty_lineedit.setObjectName("g_autoprintsaleqty_lineedit")
        self.horizontalLayout_21.addWidget(self.g_autoprintsaleqty_lineedit)
        self.g_autoprintsaleqtyhelp_label = QtWidgets.QLabel(self.g_autoprintsaleqty_frame)
        self.g_autoprintsaleqtyhelp_label.setWordWrap(True)
        self.g_autoprintsaleqtyhelp_label.setObjectName("g_autoprintsaleqtyhelp_label")
        self.horizontalLayout_21.addWidget(self.g_autoprintsaleqtyhelp_label)
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_21.addItem(spacerItem7)
        self.verticalLayout.addWidget(self.g_autoprintsaleqty_frame)
        self.g_g_order_lineedit_frame = QtWidgets.QFrame(self.g_autoprintoptions_groupbox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_g_order_lineedit_frame.sizePolicy().hasHeightForWidth())
        self.g_g_order_lineedit_frame.setSizePolicy(sizePolicy)
        self.g_g_order_lineedit_frame.setMinimumSize(QtCore.QSize(0, 41))
        self.g_g_order_lineedit_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_g_order_lineedit_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_g_order_lineedit_frame.setObjectName("g_g_order_lineedit_frame")
        self.horizontalLayout_23 = QtWidgets.QHBoxLayout(self.g_g_order_lineedit_frame)
        self.horizontalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_23.setObjectName("horizontalLayout_23")
        self.g_order_lineedit_label = QtWidgets.QLabel(self.g_g_order_lineedit_frame)
        self.g_order_lineedit_label.setMinimumSize(QtCore.QSize(165, 22))
        self.g_order_lineedit_label.setMaximumSize(QtCore.QSize(16777215, 22))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_order_lineedit_label.setFont(font)
        self.g_order_lineedit_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_order_lineedit_label.setObjectName("g_order_lineedit_label")
        self.horizontalLayout_23.addWidget(self.g_order_lineedit_label)
        self.g_order_lineedit = QtWidgets.QLineEdit(self.g_g_order_lineedit_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_order_lineedit.sizePolicy().hasHeightForWidth())
        self.g_order_lineedit.setSizePolicy(sizePolicy)
        self.g_order_lineedit.setMinimumSize(QtCore.QSize(50, 31))
        self.g_order_lineedit.setMaximumSize(QtCore.QSize(50, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_order_lineedit.setFont(font)
        self.g_order_lineedit.setReadOnly(False)
        self.g_order_lineedit.setPlaceholderText("")
        self.g_order_lineedit.setProperty("qp_ppw_create_print_qty", "")
        self.g_order_lineedit.setObjectName("g_order_lineedit")
        self.horizontalLayout_23.addWidget(self.g_order_lineedit)
        self.g_presalehelp_label_2 = QtWidgets.QLabel(self.g_g_order_lineedit_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        font.setKerning(False)
        self.g_presalehelp_label_2.setFont(font)
        self.g_presalehelp_label_2.setText("")
        self.g_presalehelp_label_2.setWordWrap(False)
        self.g_presalehelp_label_2.setObjectName("g_presalehelp_label_2")
        self.horizontalLayout_23.addWidget(self.g_presalehelp_label_2)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_23.addItem(spacerItem8)
        self.verticalLayout.addWidget(self.g_g_order_lineedit_frame)
        self.verticalLayout_2.addWidget(self.g_autoprintoptions_groupbox)
        self.verticalLayout_3.addWidget(self.g_quickfilters_frame)
        self.g_main_tabs.addTab(self.g_paperwork_widget, "")
        self.verticalLayout_4.addWidget(self.g_main_tabs)
        self.g_bottombar_frame = QtWidgets.QFrame(ManagePaperwork)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.g_bottombar_frame.setSizePolicy(sizePolicy)
        self.g_bottombar_frame.setMinimumSize(QtCore.QSize(247, 39))
        self.g_bottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottombar_frame.setObjectName("g_bottombar_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_bottombar_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        spacerItem9 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem9)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_bottombar_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/pet_tracker/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_2.addWidget(self.g_cancel_button)
        self.g_preview_button = QtWidgets.QPushButton(self.g_bottombar_frame)
        self.g_preview_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_preview_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_preview_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/pet_tracker/printer"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_preview_button.setIcon(icon1)
        self.g_preview_button.setIconSize(QtCore.QSize(24, 24))
        self.g_preview_button.setObjectName("g_preview_button")
        self.horizontalLayout_2.addWidget(self.g_preview_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_bottombar_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_save_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/pet_tracker/check"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon2)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_2.addWidget(self.g_save_button)
        self.verticalLayout_4.addWidget(self.g_bottombar_frame)

        self.retranslateUi(ManagePaperwork)
        self.g_main_tabs.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(ManagePaperwork)

    def retranslateUi(self, ManagePaperwork):
        _translate = QtCore.QCoreApplication.translate
        ManagePaperwork.setWindowTitle(_translate("ManagePaperwork", "Manage Paperwork"))
        self.g_main_label.setText(_translate("ManagePaperwork", "Manage Paperwork"))
        self.g_previouspage_label.setToolTip(_translate("ManagePaperwork", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("ManagePaperwork", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("ManagePaperwork", "999"))
        self.g_slash_label.setText(_translate("ManagePaperwork", "/"))
        self.g_totalpages_label.setToolTip(_translate("ManagePaperwork", "Total Pages"))
        self.g_totalpages_label.setText(_translate("ManagePaperwork", "999"))
        self.g_displayname_label.setText(_translate("ManagePaperwork", "Display Name"))
        self.g_displayname_lineedit.setPlaceholderText(_translate("ManagePaperwork", "Enter Name for Paperwork"))
        self.g_classification_label.setText(_translate("ManagePaperwork", "Classification"))
        self.label.setText(_translate("ManagePaperwork", "Type:"))
        self.g_breeder_rb.setText(_translate("ManagePaperwork", "Breeder"))
        self.g_broker_rb.setText(_translate("ManagePaperwork", "Broker"))
        self.g_transporter_rb.setText(_translate("ManagePaperwork", "Transporter"))
        self.g_entity_label_4.setText(_translate("ManagePaperwork", "Applied to Type"))
        self.g_type_combobox.setCurrentText(_translate("ManagePaperwork", "Select One"))
        self.g_type_combobox.setItemText(0, _translate("ManagePaperwork", "Select One"))
        self.g_entity_label_5.setText(_translate("ManagePaperwork", "Applied to Types"))
        self.g_entity_label.setText(_translate("ManagePaperwork", "Breeder"))
        self.g_entity_combobox.setCurrentText(_translate("ManagePaperwork", "Select One"))
        self.g_entity_combobox.setItemText(0, _translate("ManagePaperwork", "Select One"))
        self.g_entity_label_3.setText(_translate("ManagePaperwork", "Breed"))
        self.g_breed_combobox.setCurrentText(_translate("ManagePaperwork", "Select One"))
        self.g_breed_combobox.setItemText(0, _translate("ManagePaperwork", "Select One"))
        self.g_entity_label_6.setText(_translate("ManagePaperwork", "Breeds"))
        self.g_entity_label_7.setText(_translate("ManagePaperwork", "Locations"))
        self.g_addons_label.setText(_translate("ManagePaperwork", "Add Ons"))
        self.g_inspectionid_label.setText(_translate("ManagePaperwork", "Inspection ID"))
        self.g_inspectionid_lineedit.setPlaceholderText(_translate("ManagePaperwork", "Enter the Inspection ID found in the upper right corner of the USDA Inspection."))
        self.g_value_label_4.setText(_translate("ManagePaperwork", "Inspection Date"))
        self.g_uploadnew_label.setText(_translate("ManagePaperwork", "Upload New"))
        self.g_uploadfilename_lineedit.setPlaceholderText(_translate("ManagePaperwork", "No file chosen."))
        self.g_selectfile_button.setText(_translate("ManagePaperwork", "Select File"))
        self.g_filesize_label.setText(_translate("ManagePaperwork", " File size should not exceed 500 kb."))
        self.g_autoprintoptions_groupbox.setTitle(_translate("ManagePaperwork", "Auto-Print Options"))
        self.g_autoprintoptionshelp_label_2.setText(_translate("ManagePaperwork", "Imported pets will not print with the selected \"Print Options\""))
        self.g_autoprintoptionshelp_label.setText(_translate("ManagePaperwork", "The following options only apply if this paper is selected to auto-print in \"Manage Pet Types.\""))
        self.g_autoprintaddqty_label.setText(_translate("ManagePaperwork", "Create Print Qty"))
        self.g_autoprintaddqtyhelp_label.setText(_translate("ManagePaperwork", "Number of copies that will print when a pet is created."))
        self.g_esign_label.setText(_translate("ManagePaperwork", "E-Sign"))
        self.g_esignhelp_label.setText(_translate("ManagePaperwork", "Paperwork is E-Sign."))
        self.g_presale_label.setText(_translate("ManagePaperwork", "Pre Sale Print Qty"))
        self.g_presalehelp_label.setText(_translate("ManagePaperwork", "Number of copies that will print before sale."))
        self.g_autoprintsaleqty_label.setText(_translate("ManagePaperwork", "Post Sale Print Qty"))
        self.g_autoprintsaleqtyhelp_label.setText(_translate("ManagePaperwork", "Number of copies that will print when a pet is sold."))
        self.g_order_lineedit_label.setText(_translate("ManagePaperwork", "Sort Order"))
        self.g_main_tabs.setTabText(self.g_main_tabs.indexOf(self.g_paperwork_widget), _translate("ManagePaperwork", "Paperwork"))
        self.g_cancel_button.setText(_translate("ManagePaperwork", " Cancel"))
        self.g_preview_button.setText(_translate("ManagePaperwork", " Preview"))
        self.g_save_button.setText(_translate("ManagePaperwork", " Save"))
from po_lib.checkcombobox import PyCheckCombobox
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManagePaperwork = QtWidgets.QDialog()
    ui = Ui_ManagePaperwork()
    ui.setupUi(ManagePaperwork)
    ManagePaperwork.show()
    sys.exit(app.exec_())
