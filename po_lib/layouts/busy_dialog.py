# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'busy_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_BusyDialog(object):
    def setupUi(self, BusyDialog):
        BusyDialog.setObjectName("BusyDialog")
        BusyDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        BusyDialog.resize(274, 84)
        BusyDialog.setStyleSheet("#BusyDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_message_label {\n"
"    font-weight: 11px;\n"
"}")
        BusyDialog.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(BusyDialog)
        self.verticalLayout.setSpacing(12)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_message_label = QtWidgets.QLabel(BusyDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.MinimumExpanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_message_label.sizePolicy().hasHeightForWidth())
        self.g_message_label.setSizePolicy(sizePolicy)
        self.g_message_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_message_label.setWordWrap(True)
        self.g_message_label.setObjectName("g_message_label")
        self.verticalLayout.addWidget(self.g_message_label)
        self.g_progress_bar = QtWidgets.QProgressBar(BusyDialog)
        self.g_progress_bar.setMaximum(0)
        self.g_progress_bar.setProperty("value", -1)
        self.g_progress_bar.setTextVisible(False)
        self.g_progress_bar.setObjectName("g_progress_bar")
        self.verticalLayout.addWidget(self.g_progress_bar)

        self.retranslateUi(BusyDialog)
        QtCore.QMetaObject.connectSlotsByName(BusyDialog)

    def retranslateUi(self, BusyDialog):
        _translate = QtCore.QCoreApplication.translate
        BusyDialog.setWindowTitle(_translate("BusyDialog", "Please Wait"))
        self.g_message_label.setText(_translate("BusyDialog", "Contacting Pinogy server and validating configuration ... "))


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    BusyDialog = QtWidgets.QDialog()
    ui = Ui_BusyDialog()
    ui.setupUi(BusyDialog)
    BusyDialog.show()
    sys.exit(app.exec_())
