# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managereportsettings_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ManageReportSettings(object):
    def setupUi(self, ManageReportSettings):
        ManageReportSettings.setObjectName("ManageReportSettings")
        ManageReportSettings.resize(526, 492)
        ManageReportSettings.setMinimumSize(QtCore.QSize(250, 450))
        ManageReportSettings.setMaximumSize(QtCore.QSize(637, 600))
        ManageReportSettings.setStyleSheet("#ManageReportSettings {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_settings_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border:1px solid #073c83;\n"
"    border-radius:10px;\n"
"    background-color:#DDE4E9;\n"
"}\n"
"\n"
".QListWidget {\n"
"    padding:2px;\n"
"    background: transparent;\n"
"}\n"
"\n"
".QListWidget::item {\n"
"    padding-bottom:10px;\n"
"    padding-right:20px;\n"
"}\n"
"\n"
".QListWidget::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
".QListWidget::indicator:unchecked {\n"
"    image: url(\':/pet_tracker/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
".QListWidget::indicator:checked {\n"
"    image: url(\':/pet_tracker/checked\');\n"
"    border:0px;\n"
"} \n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"    color: #000;\n"
"}\n"
"\n"
"QLabel {\n"
"    color: #000;\n"
"}\n"
"\n"
"QCheckBox {\n"
"    padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/pet_tracker/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/pet_tracker/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    /*background-color: #000;*/\n"
"}\n"
"\n"
"QDateTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
" }\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"} \n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-size: 13px;\n"
"    font-weight: bold;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"#g_tabs QTabWidget::tab-bar {\n"
"     left: 5px; \n"
" }\n"
"\n"
"#g_manage_tabs QTabBar::tab,\n"
"#g_registries_tabs QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"#g_manage_tabs QTabBar::tab:selected, QTabBar::tab:hover,\n"
"#g_registries_tabs QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"#g_manage_tabs QTabBar::tab:selected,\n"
"#g_registries_tabs QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
" \n"
" \n"
"#g_makeup_widget,\n"
"#g_attributes_widget,\n"
"#g_notes_widget,\n"
"#g_images_widget {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius:0px;\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"#g_petmedicalcondition_tab,\n"
"#g_pettreatment_tab {\n"
"    border:1px solid #073c83;\n"
"}\n"
"\n"
"#g_addpaperwork_button,\n"
"#g_massaction_button,\n"
"#g_addnote_button, \n"
"#g_save_button,\n"
"#g_addregistry_button,\n"
"#g_addmedcondition_button,\n"
"#g_addtreatment_button,\n"
"#g_addmicrochip_button,\n"
"#g_addweight_button,\n"
"#g_addsibling_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_addpaperwork_button:pressed,\n"
"#g_massaction_button:pressed,\n"
"#g_addnote_button:pressed,\n"
"#g_save_button:pressed,\n"
"#g_addregistry_button:pressed,\n"
"#g_addmedcondition_button:pressed,\n"
"#g_addtreatment_button:pressed,\n"
"#g_addmicrochip_button:pressed,\n"
"#g_addweight_button:pressed,\n"
"#g_addsibling_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_removepaperwork_button,\n"
"#g_deletenote_button,\n"
"#g_cancel_button,\n"
"#g_deletemedcondition_button,\n"
"#g_deletetreatment_button,\n"
"#g_deleteregistry_button,\n"
"#g_deletemicrochip_button,\n"
"#g_cancelweight_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_removepaperwork_button:pressed,\n"
"#g_deletenote_button:pressed,\n"
"#g_cancel_button:pressed,\n"
"#g_deletemedcondition_button:pressed,\n"
"#g_deletetreatment_button:pressed,\n"
"#g_deleteregistry_button:pressed,\n"
"#g_deletemicrochip_button:pressed,\n"
"#g_cancelweight_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
" \n"
"\n"
"#g_location_combobox::drop-down,\n"
"#g_breeder_combobox::drop-down,\n"
"#g_supplier_combobox::drop-down,\n"
"#g_breedname_combobox::drop-down,\n"
"#g_microchip_combobox::drop-down,\n"
"#g_types_combobox::drop-down,\n"
"#g_dob_dateedit::drop-down,\n"
"#g_medcondition_combobox::drop-down,\n"
"#g_recorddate_timeedit::drop-down,\n"
"#g_treatmentype_combobox::drop-down,\n"
"#g_applieddate_datetimeedit::drop-down,\n"
"#g_weighdate_datetimeedit::drop-down,\n"
"#g_weightmeasure_combobox::drop-down,\n"
"#g_sex_combobox::drop-down,\n"
"#g_status_combobox::drop-down {    \n"
"    background-image: url(:/pet_tracker/drop-down-arrow);\n"
"}\n"
"\n"
"#g_registrycontainer_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"QCheckBox {\n"
"    padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/finish_screen/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/finish_screen/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
".QListWidget {\n"
"    padding: 10px;\n"
"    background: transparent;\n"
"}\n"
"\n"
"#g_search_frame, #__g_search_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_search_box_frame, #__g_search_box_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_clearsearch_button, #__g_clearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_search_lineedit, #__g_search_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_paperwork_combobox,\n"
"#g_search_combobox, #__g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_paperwork_combobox::drop-down,\n"
"#g_search_combobox::drop-down, #__g_search_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"#g_searchtext_lineedit, #__g_searchtext_lineedit {\n"
"    border: 0px;\n"
"}\n"
" \n"
"#g_paperwork_combobox::drop-down,\n"
"#g_pettypes_combobox::drop-down,\n"
"#g_heightmeasure_combobox::drop-down,\n"
"#g_weightmeasure_combobox::drop-down,\n"
"#g_search_combobox::drop-down {\n"
"    background-image: url(:/pet_tracker/drop-down-arrow);\n"
"}\n"
"\n"
"QFrame QLabel {\n"
"    font-size: 11px;\n"
"}")
        self.verticalLayout = QtWidgets.QVBoxLayout(ManageReportSettings)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_bottombar_frame = QtWidgets.QFrame(ManageReportSettings)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.g_bottombar_frame.setSizePolicy(sizePolicy)
        self.g_bottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_bottombar_frame.setStyleSheet("")
        self.g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottombar_frame.setObjectName("g_bottombar_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_bottombar_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.verticalLayout.addWidget(self.g_bottombar_frame)
        self.g_settings_frame = QtWidgets.QFrame(ManageReportSettings)
        self.g_settings_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_settings_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_settings_frame.setObjectName("g_settings_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_settings_frame)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_settings_groupbox = QtWidgets.QGroupBox(self.g_settings_frame)
        self.g_settings_groupbox.setMinimumSize(QtCore.QSize(0, 80))
        self.g_settings_groupbox.setMaximumSize(QtCore.QSize(16777215, 80))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_settings_groupbox.setFont(font)
        self.g_settings_groupbox.setObjectName("g_settings_groupbox")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_settings_groupbox)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_reporthelp_label = QtWidgets.QLabel(self.g_settings_groupbox)
        self.g_reporthelp_label.setObjectName("g_reporthelp_label")
        self.verticalLayout_4.addWidget(self.g_reporthelp_label)
        self.g_settings_checkcombobox = PyCheckCombobox(self.g_settings_groupbox)
        self.g_settings_checkcombobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_settings_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_settings_checkcombobox.setToolTip("")
        self.g_settings_checkcombobox.setWhatsThis("")
        self.g_settings_checkcombobox.setObjectName("g_settings_checkcombobox")
        self.verticalLayout_4.addWidget(self.g_settings_checkcombobox)
        self.verticalLayout_2.addWidget(self.g_settings_groupbox)
        self.g_application_groupbox = QtWidgets.QGroupBox(self.g_settings_frame)
        self.g_application_groupbox.setMinimumSize(QtCore.QSize(0, 80))
        self.g_application_groupbox.setMaximumSize(QtCore.QSize(16777215, 80))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_application_groupbox.setFont(font)
        self.g_application_groupbox.setObjectName("g_application_groupbox")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.g_application_groupbox)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.g_apphelp_label = QtWidgets.QLabel(self.g_application_groupbox)
        self.g_apphelp_label.setObjectName("g_apphelp_label")
        self.verticalLayout_6.addWidget(self.g_apphelp_label)
        self.g_application_checkcombobox = PyCheckCombobox(self.g_application_groupbox)
        self.g_application_checkcombobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_application_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_application_checkcombobox.setToolTip("")
        self.g_application_checkcombobox.setWhatsThis("")
        self.g_application_checkcombobox.setObjectName("g_application_checkcombobox")
        self.verticalLayout_6.addWidget(self.g_application_checkcombobox)
        self.verticalLayout_2.addWidget(self.g_application_groupbox)
        self.g_permission_groupbox = QtWidgets.QGroupBox(self.g_settings_frame)
        self.g_permission_groupbox.setMinimumSize(QtCore.QSize(0, 80))
        self.g_permission_groupbox.setMaximumSize(QtCore.QSize(16777215, 80))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_permission_groupbox.setFont(font)
        self.g_permission_groupbox.setObjectName("g_permission_groupbox")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.g_permission_groupbox)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.g_permhelp_label = QtWidgets.QLabel(self.g_permission_groupbox)
        self.g_permhelp_label.setObjectName("g_permhelp_label")
        self.verticalLayout_7.addWidget(self.g_permhelp_label)
        self.g_permission_checkcombobox = PyCheckCombobox(self.g_permission_groupbox)
        self.g_permission_checkcombobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_permission_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_permission_checkcombobox.setToolTip("")
        self.g_permission_checkcombobox.setWhatsThis("")
        self.g_permission_checkcombobox.setObjectName("g_permission_checkcombobox")
        self.verticalLayout_7.addWidget(self.g_permission_checkcombobox)
        self.verticalLayout_2.addWidget(self.g_permission_groupbox)
        spacerItem = QtWidgets.QSpacerItem(599, 478, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_2.addItem(spacerItem)
        self.g_controlbuttons_frame_2 = QtWidgets.QFrame(self.g_settings_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame_2.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame_2.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame_2.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame_2.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame_2.setObjectName("g_controlbuttons_frame_2")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame_2)
        self.horizontalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        spacerItem1 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_12.addItem(spacerItem1)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_controlbuttons_frame_2)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/report_modaldialog/delete"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_12.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_controlbuttons_frame_2)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/report_modaldialog/check"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_12.addWidget(self.g_save_button)
        self.verticalLayout_2.addWidget(self.g_controlbuttons_frame_2)
        self.verticalLayout.addWidget(self.g_settings_frame)

        self.retranslateUi(ManageReportSettings)
        QtCore.QMetaObject.connectSlotsByName(ManageReportSettings)

    def retranslateUi(self, ManageReportSettings):
        _translate = QtCore.QCoreApplication.translate
        ManageReportSettings.setWindowTitle(_translate("ManageReportSettings", "Manage Report Settings"))
        self.g_settings_groupbox.setTitle(_translate("ManageReportSettings", "Report Information"))
        self.g_reporthelp_label.setText(_translate("ManageReportSettings", "Select Which Optional Fields to Show in This Report."))
        self.g_application_groupbox.setTitle(_translate("ManageReportSettings", "Application"))
        self.g_apphelp_label.setText(_translate("ManageReportSettings", "Select the Applications that this Report is visible in."))
        self.g_permission_groupbox.setTitle(_translate("ManageReportSettings", "Permissions"))
        self.g_permhelp_label.setText(_translate("ManageReportSettings", "Select the Roles that this Report is visible to."))
        self.g_cancel_button.setText(_translate("ManageReportSettings", "Cancel"))
        self.g_save_button.setText(_translate("ManageReportSettings", " Save"))
from po_lib.checkcombobox import PyCheckCombobox
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageReportSettings = QtWidgets.QDialog()
    ui = Ui_ManageReportSettings()
    ui.setupUi(ManageReportSettings)
    ManageReportSettings.show()
    sys.exit(app.exec_())
