# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'prepayment_popup.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_PrepaymentPopupDialog(object):
    def setupUi(self, PrepaymentPopupDialog):
        PrepaymentPopupDialog.setObjectName("PrepaymentPopupDialog")
        PrepaymentPopupDialog.resize(1015, 670)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(PrepaymentPopupDialog.sizePolicy().hasHeightForWidth())
        PrepaymentPopupDialog.setSizePolicy(sizePolicy)
        PrepaymentPopupDialog.setMinimumSize(QtCore.QSize(1015, 670))
        PrepaymentPopupDialog.setMaximumSize(QtCore.QSize(16777215, 16777215))
        PrepaymentPopupDialog.setContextMenuPolicy(QtCore.Qt.DefaultContextMenu)
        PrepaymentPopupDialog.setAutoFillBackground(False)
        PrepaymentPopupDialog.setStyleSheet("#PrepaymentPopupDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QLabel {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font: 17px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"QGroupBox {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"QRadioButton {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"QCheckBox {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"    padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTableView {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"    font-size: 12px;\n"
"    font-weight: normal;\n"
"}\n"
"\n"
"QTableView::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size: 12px;\n"
"    font-weight: bold;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"QTabWidget::tab-bar {\n"
"   left: 0;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QTabWidget {\n"
"    border:0px;\n"
"}\n"
"\n"
"#g_redeem_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_redeem_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(PrepaymentPopupDialog)
        self.verticalLayout_2.setContentsMargins(9, 0, 9, 9)
        self.verticalLayout_2.setSpacing(6)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_top_frame_2 = QtWidgets.QFrame(PrepaymentPopupDialog)
        self.g_top_frame_2.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame_2.setObjectName("g_top_frame_2")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_top_frame_2)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_8.addWidget(self.g_progress_frame)
        self.verticalLayout_2.addWidget(self.g_top_frame_2)
        self.g_titlepagenav_frame = QtWidgets.QFrame(PrepaymentPopupDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_titlepagenav_frame.sizePolicy().hasHeightForWidth())
        self.g_titlepagenav_frame.setSizePolicy(sizePolicy)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_33 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_33.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_33.setSpacing(6)
        self.horizontalLayout_33.setObjectName("horizontalLayout_33")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        self.g_sub_label = QtWidgets.QLabel(self.g_title_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_sub_label.setFont(font)
        self.g_sub_label.setObjectName("g_sub_label")
        self.verticalLayout.addWidget(self.g_sub_label)
        self.horizontalLayout_33.addWidget(self.g_title_frame)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_33.addItem(spacerItem)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(150, 0))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(150, 16777215))
        self.g_pagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.verticalLayout_11 = QtWidgets.QVBoxLayout(self.g_pagenav_frame)
        self.verticalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_11.setSpacing(0)
        self.verticalLayout_11.setObjectName("verticalLayout_11")
        self.g_pagenav_hlayout_3 = QtWidgets.QHBoxLayout()
        self.g_pagenav_hlayout_3.setSpacing(6)
        self.g_pagenav_hlayout_3.setObjectName("g_pagenav_hlayout_3")
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_previouspage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/customers_screen_dialog/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout_3.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_nextpage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/customers_screen_dialog/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout_3.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagenav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout_3.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label_4 = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_slash_label_4.setMinimumSize(QtCore.QSize(7, 0))
        self.g_slash_label_4.setMaximumSize(QtCore.QSize(7, 16777215))
        self.g_slash_label_4.setObjectName("g_slash_label_4")
        self.g_pagenav_hlayout_3.addWidget(self.g_slash_label_4)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_totalpages_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_totalpages_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout_3.addWidget(self.g_totalpages_label)
        self.verticalLayout_11.addLayout(self.g_pagenav_hlayout_3)
        self.horizontalLayout_33.addWidget(self.g_pagenav_frame)
        self.verticalLayout_2.addWidget(self.g_titlepagenav_frame)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.label = QtWidgets.QLabel(PrepaymentPopupDialog)
        self.label.setObjectName("label")
        self.horizontalLayout_2.addWidget(self.label)
        self.g_barcode_lineedit = QtWidgets.QLineEdit(PrepaymentPopupDialog)
        self.g_barcode_lineedit.setObjectName("g_barcode_lineedit")
        self.horizontalLayout_2.addWidget(self.g_barcode_lineedit)
        self.verticalLayout_2.addLayout(self.horizontalLayout_2)
        self.g_main_frame = QtWidgets.QFrame(PrepaymentPopupDialog)
        self.g_main_frame.setMinimumSize(QtCore.QSize(0, 287))
        self.g_main_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_main_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_main_frame.setObjectName("g_main_frame")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_main_frame)
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_3.setSpacing(1)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_productstoadd_groupbox = QtWidgets.QGroupBox(self.g_main_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_productstoadd_groupbox.sizePolicy().hasHeightForWidth())
        self.g_productstoadd_groupbox.setSizePolicy(sizePolicy)
        self.g_productstoadd_groupbox.setMinimumSize(QtCore.QSize(0, 285))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_productstoadd_groupbox.setFont(font)
        self.g_productstoadd_groupbox.setObjectName("g_productstoadd_groupbox")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.g_productstoadd_groupbox)
        self.verticalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_8.setSpacing(0)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.frame_3 = QtWidgets.QFrame(self.g_productstoadd_groupbox)
        self.frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.frame_3)
        self.verticalLayout_4.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout_4.setSpacing(6)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_rewards_tableview = QtWidgets.QTableView(self.frame_3)
        self.g_rewards_tableview.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_rewards_tableview.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_rewards_tableview.setSizeAdjustPolicy(QtWidgets.QAbstractScrollArea.AdjustToContentsOnFirstShow)
        self.g_rewards_tableview.setEditTriggers(QtWidgets.QAbstractItemView.AllEditTriggers)
        self.g_rewards_tableview.setProperty("showDropIndicator", True)
        self.g_rewards_tableview.setAlternatingRowColors(True)
        self.g_rewards_tableview.setSelectionMode(QtWidgets.QAbstractItemView.MultiSelection)
        self.g_rewards_tableview.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_rewards_tableview.setShowGrid(False)
        self.g_rewards_tableview.setSortingEnabled(True)
        self.g_rewards_tableview.setObjectName("g_rewards_tableview")
        self.g_rewards_tableview.horizontalHeader().setDefaultSectionSize(90)
        self.g_rewards_tableview.horizontalHeader().setStretchLastSection(True)
        self.verticalLayout_4.addWidget(self.g_rewards_tableview)
        self.g_detailcontrol_frame = QtWidgets.QFrame(self.frame_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_detailcontrol_frame.sizePolicy().hasHeightForWidth())
        self.g_detailcontrol_frame.setSizePolicy(sizePolicy)
        self.g_detailcontrol_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_detailcontrol_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_detailcontrol_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_detailcontrol_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_detailcontrol_frame.setObjectName("g_detailcontrol_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_detailcontrol_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setSpacing(6)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.frame_6 = QtWidgets.QFrame(self.g_detailcontrol_frame)
        self.frame_6.setMinimumSize(QtCore.QSize(0, 0))
        self.frame_6.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.frame_6.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_6.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_6.setObjectName("frame_6")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.frame_6)
        self.horizontalLayout_10.setContentsMargins(6, 0, 10, 0)
        self.horizontalLayout_10.setSpacing(6)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.g_totalresults_label = QtWidgets.QLabel(self.frame_6)
        self.g_totalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_totalresults_label.setFont(font)
        self.g_totalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_totalresults_label.setObjectName("g_totalresults_label")
        self.horizontalLayout_10.addWidget(self.g_totalresults_label)
        self.horizontalLayout.addWidget(self.frame_6)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.g_detailcontrolbuttons_frame = QtWidgets.QFrame(self.g_detailcontrol_frame)
        self.g_detailcontrolbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_detailcontrolbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_detailcontrolbuttons_frame.setObjectName("g_detailcontrolbuttons_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_detailcontrolbuttons_frame)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setSpacing(6)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_skip_button = QtWidgets.QPushButton(self.g_detailcontrolbuttons_frame)
        self.g_skip_button.setMinimumSize(QtCore.QSize(140, 40))
        self.g_skip_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_skip_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/sales_screen/right_arrow"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_skip_button.setIcon(icon)
        self.g_skip_button.setIconSize(QtCore.QSize(24, 24))
        self.g_skip_button.setObjectName("g_skip_button")
        self.horizontalLayout_5.addWidget(self.g_skip_button)
        self.g_redeem_button = QtWidgets.QPushButton(self.g_detailcontrolbuttons_frame)
        self.g_redeem_button.setMinimumSize(QtCore.QSize(140, 40))
        self.g_redeem_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_redeem_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/sales_screen/save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_redeem_button.setIcon(icon1)
        self.g_redeem_button.setIconSize(QtCore.QSize(24, 24))
        self.g_redeem_button.setObjectName("g_redeem_button")
        self.horizontalLayout_5.addWidget(self.g_redeem_button)
        self.horizontalLayout.addWidget(self.g_detailcontrolbuttons_frame)
        self.verticalLayout_4.addWidget(self.g_detailcontrol_frame)
        self.verticalLayout_8.addWidget(self.frame_3)
        self.verticalLayout_3.addWidget(self.g_productstoadd_groupbox)
        self.verticalLayout_2.addWidget(self.g_main_frame)

        self.retranslateUi(PrepaymentPopupDialog)
        QtCore.QMetaObject.connectSlotsByName(PrepaymentPopupDialog)

    def retranslateUi(self, PrepaymentPopupDialog):
        _translate = QtCore.QCoreApplication.translate
        PrepaymentPopupDialog.setWindowTitle(_translate("PrepaymentPopupDialog", "PrepaymentPopupDialog"))
        self.g_main_label.setText(_translate("PrepaymentPopupDialog", "Astro Frequent Buyer Reward"))
        self.g_sub_label.setText(_translate("PrepaymentPopupDialog", "Based on the products on this sale, this customer has earned a free product. Choose it from the list below."))
        self.g_previouspage_label.setToolTip(_translate("PrepaymentPopupDialog", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("PrepaymentPopupDialog", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("PrepaymentPopupDialog", "999"))
        self.g_slash_label_4.setText(_translate("PrepaymentPopupDialog", "/"))
        self.g_totalpages_label.setToolTip(_translate("PrepaymentPopupDialog", "Total Pages"))
        self.g_totalpages_label.setText(_translate("PrepaymentPopupDialog", "999"))
        self.label.setText(_translate("PrepaymentPopupDialog", "Barcode"))
        self.g_barcode_lineedit.setPlaceholderText(_translate("PrepaymentPopupDialog", "Scan/enter barcode"))
        self.g_productstoadd_groupbox.setTitle(_translate("PrepaymentPopupDialog", "Products"))
        self.g_totalresults_label.setText(_translate("PrepaymentPopupDialog", "Loading results."))
        self.g_skip_button.setText(_translate("PrepaymentPopupDialog", "Skip"))
        self.g_redeem_button.setText(_translate("PrepaymentPopupDialog", "Redeem"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    PrepaymentPopupDialog = QtWidgets.QDialog()
    ui = Ui_PrepaymentPopupDialog()
    ui.setupUi(PrepaymentPopupDialog)
    PrepaymentPopupDialog.show()
    sys.exit(app.exec_())
