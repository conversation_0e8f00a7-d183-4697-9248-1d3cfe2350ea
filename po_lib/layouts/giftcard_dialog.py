# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'giftcard_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_GiftCardDialog(object):
    def setupUi(self, GiftCardDialog):
        GiftCardDialog.setObjectName("GiftCardDialog")
        GiftCardDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        GiftCardDialog.resize(415, 127)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(GiftCardDialog.sizePolicy().hasHeightForWidth())
        GiftCardDialog.setSizePolicy(sizePolicy)
        GiftCardDialog.setMinimumSize(QtCore.QSize(415, 127))
        GiftCardDialog.setMaximumSize(QtCore.QSize(415, 127))
        GiftCardDialog.setStyleSheet("\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    background-color: #fff;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#GiftCardDialog,\n"
"#__GiftCardDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#GiftCardDialog QPushButton,\n"
"#__GiftCardDialog QPushButton {\n"
"    color: white;\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"#g_ok_button, \n"
"#__g_ok_button {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"}\n"
"\n"
"#g_ok_button:pressed, \n"
"#__g_ok_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_ok_button:disabled,\n"
"#__g_ok_button:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}\n"
"\n"
"#g_cancel_button, \n"
"#__g_cancel_button {\n"
"    background-color: #ee1111;\n"
"    border: 2px solid #ca0e0e;\n"
"}\n"
"\n"
"#g_cancel_button:pressed, \n"
"#__g_cancel_button:pressed {\n"
"    background-color: #ca0e0e;\n"
"}\n"
"\n"
"")
        GiftCardDialog.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(GiftCardDialog)
        self.verticalLayout.setContentsMargins(18, 18, 18, 18)
        self.verticalLayout.setSpacing(18)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_giftcardnumber_lineedit = QtWidgets.QLineEdit(GiftCardDialog)
        self.g_giftcardnumber_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_giftcardnumber_lineedit.setClearButtonEnabled(True)
        self.g_giftcardnumber_lineedit.setObjectName("g_giftcardnumber_lineedit")
        self.verticalLayout.addWidget(self.g_giftcardnumber_lineedit)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(18)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(GiftCardDialog)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 16777215))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/giftcard_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_ok_button = QtWidgets.QPushButton(GiftCardDialog)
        self.g_ok_button.setEnabled(True)
        self.g_ok_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_ok_button.setMaximumSize(QtCore.QSize(120, 16777215))
        self.g_ok_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/giftcard_dialog/ok"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_ok_button.setIcon(icon1)
        self.g_ok_button.setIconSize(QtCore.QSize(24, 24))
        self.g_ok_button.setObjectName("g_ok_button")
        self.horizontalLayout.addWidget(self.g_ok_button)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(GiftCardDialog)
        QtCore.QMetaObject.connectSlotsByName(GiftCardDialog)
        GiftCardDialog.setTabOrder(self.g_giftcardnumber_lineedit, self.g_ok_button)
        GiftCardDialog.setTabOrder(self.g_ok_button, self.g_cancel_button)

    def retranslateUi(self, GiftCardDialog):
        _translate = QtCore.QCoreApplication.translate
        GiftCardDialog.setWindowTitle(_translate("GiftCardDialog", "Gift Card Redemption"))
        self.g_giftcardnumber_lineedit.setPlaceholderText(_translate("GiftCardDialog", "Enter Gift Card Code or Number ..."))
        self.g_cancel_button.setText(_translate("GiftCardDialog", "  Cancel"))
        self.g_ok_button.setText(_translate("GiftCardDialog", "  Ok"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    GiftCardDialog = QtWidgets.QDialog()
    ui = Ui_GiftCardDialog()
    ui.setupUi(GiftCardDialog)
    GiftCardDialog.show()
    sys.exit(app.exec_())
