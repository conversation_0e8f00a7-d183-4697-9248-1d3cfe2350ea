fpe_specs = {
    # Manage Report Settings
    "fpe_actions": {
        "prepare_form": [
            ("change_state", "g_add_schedule_button", {"default_state": "disabled"}),
            "$g_custom_frame.hide",

            ("push", "$storage.check_every", False),
            ("push", "$g_notifyemail_label",
                     "> Your {$form.input.report_name} Report will be emailed to {$form.input.user_info.user_primary_email[0]}."),

            ("push", "$g_repeat_combobox", [
                {"name": "Daily", "value": "daily"},
                {"name": "Weekly", "value": "weekly"},
                {"name": "Day of Month", "value": "monthly"},
                {"name": "Month of Year", "value": "yearly"},
            ]),

            ("push", "$storage.dayofweek", [
                {"name": "Sunday", "value": 0, "check": False}, {"name": "Monday", "value": 1, "check": False},
                {"name": "Tuesday", "value": 2, "check": False}, {"name": "Wednesday", "value": 3, "check": False},
                {"name": "Thursday", "value": 4, "check": False}, {"name": "Friday", "value": 5, "check": False},
                {"name": "Saturday", "value": 6, "check": False}
            ]),

            ("push", "$storage.dayofmonth", [
                {"name": "1", "value": 1, "check": False}, {"name": "2", "value": 2, "check": False},
                {"name": "3", "value": 3, "check": False}, {"name": "4", "value": 4, "check": False},
                {"name": "5", "value": 5, "check": False}, {"name": "6", "value": 6, "check": False},
                {"name": "7", "value": 7, "check": False}, {"name": "8", "value": 8, "check": False},
                {"name": "9", "value": 9, "check": False}, {"name": "10", "value": 10, "check": False},
                {"name": "11", "value": 11, "check": False}, {"name": "12", "value": 12, "check": False},
                {"name": "13", "value": 13, "check": False}, {"name": "14", "value": 14, "check": False},
                {"name": "15", "value": 15, "check": False}, {"name": "16", "value": 16, "check": False},
                {"name": "17", "value": 17, "check": False}, {"name": "18", "value": 18, "check": False},
                {"name": "19", "value": 19, "check": False}, {"name": "20", "value": 20, "check": False},
                {"name": "21", "value": 21, "check": False}, {"name": "22", "value": 22, "check": False},
                {"name": "23", "value": 23, "check": False}, {"name": "24", "value": 24, "check": False},
                {"name": "25", "value": 25, "check": False}, {"name": "26", "value": 26, "check": False},
                {"name": "27", "value": 27, "check": False}, {"name": "28", "value": 28, "check": False},
                {"name": "29", "value": 29, "check": False}, {"name": "30", "value": 30, "check": False},
                {"name": "31", "value": 31, "check": False}
            ]),

            ("push", "$c_month_checkcombobox", [
                {"name": "1", "value": 1, "check": False}, {"name": "2", "value": 2, "check": False},
                {"name": "3", "value": 3, "check": False}, {"name": "4", "value": 4, "check": False},
                {"name": "5", "value": 5, "check": False}, {"name": "6", "value": 6, "check": False},
                {"name": "7", "value": 7, "check": False}, {"name": "8", "value": 8, "check": False},
                {"name": "9", "value": 9, "check": False}, {"name": "10", "value": 10, "check": False},
                {"name": "11", "value": 11, "check": False}, {"name": "12", "value": 12, "check": False}
            ]),

            ("push", "$storage.monthofyear", [
                {"name": "1", "value": 1, "check": False}, {"name": "2", "value": 2, "check": False},
                {"name": "3", "value": 3, "check": False}, {"name": "4", "value": 4, "check": False},
                {"name": "5", "value": 5, "check": False}, {"name": "6", "value": 6, "check": False},
                {"name": "7", "value": 7, "check": False}, {"name": "8", "value": 8, "check": False},
                {"name": "9", "value": 9, "check": False}, {"name": "10", "value": 10, "check": False},
                {"name": "11", "value": 11, "check": False}, {"name": "12", "value": 12, "check": False}
            ]),

            ("push", "$g_time_combobox", [
                {'name': "7:00 am",  'value': "7"}, {'name': "8:00 am",  'value': "8"},
                {'name': "9:00 am",  'value': "9"}, {'name': "10:00 am", 'value': "10"},
                {'name': "11:00 am", 'value': "11"}, {'name': "12:00 pm", 'value': "12"},
                {'name': "1:00 pm",  'value': "13"}, {'name': "2:00 pm",  'value': "14"},
                {'name': "3:00 pm",  'value': "15"}, {'name': "4:00 pm",  'value': "16"},
                {'name': "5:00 pm",  'value': "17"}, {'name': "6:00 pm",  'value': "18"},
                {'name': "7:00 pm",  'value': "19"}, {'name': "8:00 pm",  'value': "20"},
                {'name': "9:00 pm",  'value': "21"}, {'name': "10:00 pm", 'value': "22"},
                {'name': "11:00 pm", 'value': "23"}, {'name': "12:00 am", 'value': "0"},
                {'name': "1:00 am",  'value': "1"}, {'name': "2:00 am",  'value': "2"},
                {'name': "3:00 am",  'value': "3"}, {'name': "4:00 am",  'value': "4"},
                {'name': "5:00 am",  'value': "5"}, {'name': "6:00 am",  'value': "6"}
            ]),

            ("push", "$g_time_combobox.selected", "1:00 am"),
            ("push", "$g_date_dateedit", "#date"),
            ("push", "$storage.time", ""),
            ("if", "$form.input.status == 'create'",
                ["$g_update_schedule_button.hide", "$g_add_schedule_button.show",
                 "$g_pagenav_frame.hide"],
                ["$g_update_schedule_button.show", "$g_add_schedule_button.hide",
                 ("push", "$paginator.total", "$form.input.schedule_id#length"),
                 ("if", "$paginator.total < 2", "$g_pagenav_frame.hide")]),
            ("if", "$form.input.status == 'create'",
                ("push","$g_main_label", "Create Schedule"))
        ],

        "load_data": [
            ("if", "$form.input.status == 'create'",
                ("transfer", "get_report_create", ("change_state", "g_add_schedule_button", {"default_state": "enabled"})),
                ("transfers", ["get_pet_status", "read_scheduled_setting"])),
        ],

        "update_schedule": [
            # Report FPE
            ("if", "$storage.ntfsubs_filter.get('pull').get('qp_entity_id')",
                ("push", "$stack#get($storage.spec#eval#get('layout').name).location.selected",
                 "$storage.ntfsubs_filter.get('pull').get('qp_entity_id')"),
                "$stack#get($storage.spec#eval#get('layout').name).location.set_index(0)"),

            ("if", "$storage.ntfsubs_filter.get('pull').get('qp_date_limit')",
                # ("if", "$storage.ntfsubs_filter.get('pull').get('qp_date_range')",
                "$stack#get($storage.spec#eval#get('layout').name).perioddays.check($storage.ntfsubs_filter.get('pull').get('qp_date_range'))",

                ("if", "$storage.ntfsubs_filter.get('pull').get('qp_date_range')",
                    "$stack#get($storage.spec#eval#get('layout').name).period.check($storage.ntfsubs_filter.get('pull').get('qp_date_range'))")),

            ("if", "$storage.ntfsubs_filter.get('pull').get('qp_month_range')",
              "$stack#get($storage.spec#eval#get('layout').name).periodsingle.check($storage.ntfsubs_filter.get('pull').get('qp_month_range'))"),

            ("if", "$storage.ntfsubs_filter.get('pull').get('qp_pet_type_id')",
                ("push", "$stack#get($storage.spec#eval#get('layout').name).pet_type.selected",
                 "$storage.ntfsubs_filter.get('pull').get('qp_pet_type_id')")),

            ("if", "$storage.ntfsubs_filter.get('pull').get('qp_show_returned')",
                ("push", "$stack#get($storage.spec#eval#get('layout').name).show_returned.selected",
                 "$storage.ntfsubs_filter.get('pull').get('qp_show_returned')")),

            ("if", "$storage.ntfsubs_filter.get('pull').get('qp_pstatus_name')",
                ("push", "$stack#get($storage.spec#eval#get('layout').name).statuses.selected",
                 "$storage.pet_status#where_list('pstatus_name', $storage.ntfsubs_filter.get('pull').get('qp_pstatus_name')).pet_status_id")),

            ("if", "$storage.ntfsubs_filter.get('pull').get('qp_txn_type')",
                ("push", "$stack#get($storage.spec#eval#get('layout').name).TxnType.selected",
                 "$storage.ntfsubs_filter.get('pull').get('qp_txn_type')")),

            ("if", "$storage.ntfsubs_filter.get('pull').get('qp_prod_category')",
             ("push", "$stack#get($storage.spec#eval#get('layout').name).cat_type.selected",
              "$storage.ntfsubs_filter.get('pull').get('qp_prod_category')")),

            ("if", "$storage.ntfsubs_filter.get('pull').get('qp_min_total_purchase')",
             ("push", "$stack#get($storage.spec#eval#get('layout').name).min_total_purchase.data",
              "$storage.ntfsubs_filter.get('pull').get('qp_min_total_purchase')")),

            ("if", "$storage.ntfsubs_filter.get('pull').get('qp_txn_reason')",
                ("push", "$stack#get($storage.spec#eval#get('layout').name).txn_reason.selected",
                    "$storage.ntfsubs_filter.get('pull').get('qp_txn_reason')")),

            ("if", "$storage.ntfsubs_filter.get('pull').get('qp_prlocs_auto_order')",
                ("if", "$storage.ntfsubs_filter.get('pull').get('qp_prlocs_auto_order') == True",
                    ("push", "$stack#get($storage.spec#eval#get('layout').name).auto_checkbox.data", True))),

            ("if", "$storage.ntfsubs_filter.get('pull').get('qp_inv_loc_onhand_nz')",
                ("if", "$storage.ntfsubs_filter.get('pull').get('qp_inv_loc_onhand_nz') == True",
                    ("push", "$stack#get($storage.spec#eval#get('layout').name).onhand_checkbox.data", True))),

            # Report Scheduling
            ("push", "$g_repeat_combobox.selected", "$storage.ntfsubs_frequency.get('repeat')"),
            ("push", "$g_time_combobox.selected", "$storage.ntfsubs_frequency.get('hour')[0]"),
            ("push", "$g_date_dateedit", "$storage.ntfsubs_frequency.get('startdate')"),

            ("if", "$storage.ntfsubs_frequency.get('dow')",
                ["$g_day_cbx.set_all_unchecked()",
                 "$g_day_cbx.set_checked($storage.dayofweek#where_list('value', $storage.ntfsubs_frequency.get('dow')).name, True)"]),

            ("if", "$storage.ntfsubs_frequency.get('day')",
                ["$g_day_cbx.set_all_unchecked()",
                 "$g_day_cbx.set_checked($storage.dayofmonth#where_list('value', $storage.ntfsubs_frequency.get('day')).name#listinttostring, True)"]),

            ("if", "$storage.ntfsubs_frequency.get('month')",
             ["$c_month_checkcombobox.set_all_unchecked()",
              "$c_month_checkcombobox.set_checked($storage.monthofyear#where_list('value', $storage.ntfsubs_frequency.get('month')).name#listinttostring, True)"]),

            ("change_state", "g_add_schedule_button", {"default_state": "enabled"}),
        ],

        "pull_schedule": [
            ("push", "$storage.pull", ("#render", "$storage.spec#eval#get('pull')")),
            ("push", "$storage.setting", ("#render", "$storage.spec#eval#get('setting')")),

            ("if", "$g_repeat_combobox.selected.value == 'daily'",
             ("push", "$storage.time", "#dict('repeat', 'daily',"
                                       "'hour', $g_time_combobox.selected.value#list,"
                                       "'startdate', $g_date_dateedit.data)")),

            ("if", "$g_repeat_combobox.selected.value == 'monthly'",
             ("push", "$storage.time", "#dict('repeat', 'monthly',"
                                       "'day', $g_day_cbx.checked.value,"
                                       "'hour', $g_time_combobox.selected.value#list,"
                                       "'startdate', $g_date_dateedit.data)")),

            ("if", "$g_repeat_combobox.selected.value == 'weekly'",
             ("push", "$storage.time", "#dict('repeat', 'weekly',"
                                       "'dow', $g_day_cbx.checked.value,"
                                       "'hour', $g_time_combobox.selected.value#list,"
                                       "'startdate', $g_date_dateedit.data)")),

            ("if", "$g_repeat_combobox.selected.value == 'yearly'",
             ("push", "$storage.time", "#dict('repeat', 'yearly',"
                                       "'day', $g_day_cbx.checked.value,"
                                       "'hour', $g_time_combobox.selected.value#list,"
                                       "'month', $c_month_checkcombobox.checked.value,"
                                       "'startdate', $g_date_dateedit.data)")),

            # "save_schedule"
        ],

        "save_schedule": [
            "pull_schedule",
            {"validate": {
                "on_success": [
                    ("transfer", "create_schedule")
                ],
                "on_failure": "show_errors"
            }}
        ],

        "save_and_move": [
            "pull_schedule",
            {"validate": {
                "on_success": {
                    "transfer": {
                        "name": "update_schedule",

                        "on_success": [
                            ("if", "$paginator.is_last", "$form.accept"),
                            ("if", "not $paginator.is_last", "$paginator.next")]
            }}}}
        ],
    },

    "fpe_transfers": {
        "get_report_create": {
            "method": "post",
            "resource": "/apps/any/queries/read__tbl__reports",

            "pull": {
                "qp_report_id": "$form.input.id",
            },

            "push": {
                "$storage.name": "$result[0].rpt_name",
                "$storage.id": "$result[0].report_id",
                "$storage.file": "$result[0].rpt_template_file_id",
                "$storage.spec": "$result[0].rpt_fpe",
            },

            "on_success": [
                ("$stack.add_layout", "$storage.spec#eval#get('layout')"),
                ("$stack.switch", "$storage.spec#eval#get('layout').name"),
                ("exec", "$storage.spec#eval#get('action')"),
                ("push", "$g_main_label", "> Create Schedule: {$storage.name}")
            ]
        },

        "read_scheduled_setting": {
            "method": "post",
            "resource": "/apps/any/queries/read__tbl__notify_subscriptions",

            "pull": {
                "qp_ntfsubs_user_entity_id": "$form.input.user_id",
                "qp_notify_subscription_id": "$form.input.schedule_id[$paginator.index]"
            },

            "push": {
                "$storage.ntfsubs_filter": "$result[0].ntfsubs_filter",
                "$storage.ntfsubs_frequency": "$result[0].ntfsubs_frequency"
            },

            "on_success": [
                ("push", "$g_main_label", "> Update Report: {$storage.ntfsubs_filter.get('report_name')}")
            ],

            "on_finish": [
                ("transfer", "get_report_update")
            ]
        },

        "get_report_update": {
            "method": "post",
            "resource": "/apps/any/queries/read__tbl__reports",

            "pull": {
                "qp_report_id": "$storage.ntfsubs_filter.get('report_id')",
            },

            "push": {
                "$storage.name": "$result[0].rpt_name",
                "$storage.id": "$result[0].report_id",
                "$storage.file": "$result[0].rpt_template_file_id",
                "$storage.spec": "$result[0].rpt_fpe",
            },

            "on_success": [
                ("$stack.add_layout", "$storage.spec#eval#get('layout')"),
                ("$stack.switch", "$storage.spec#eval#get('layout').name"),
                ("exec", "$storage.spec#eval#get('action')"),
            ],

            "on_finish": [
                ("transfer", "read_scheduled_setting2")
            ]
        },

        "read_scheduled_setting2": {
            "method": "post",
            "resource": "/apps/any/queries/read__tbl__notify_subscriptions",

            "pull": {
                "qp_ntfsubs_user_entity_id": "$form.input.user_id",
                "qp_notify_subscription_id": "$form.input.schedule_id[$paginator.index]"
            },

            "push": {
                "$storage.ntfsubs_filter": "$result[0].ntfsubs_filter",
                "$storage.ntfsubs_frequency": "$result[0].ntfsubs_frequency"
            },

            "on_finish": [
                "update_schedule"
            ]
        },

        "create_schedule": {
            "method": "post",
            "resource": "/apps/any/queries/create__tbl__notify_subscriptions",

            "pull": {
                "qp_ntfsubs_user_entity_id": "$form.input.user_id",
                "qp_ntfsubs_name": "$storage.spec#eval#get('layout').name",
                "qp_ntfsubs_notify_self": True,
                "qp_ntfsubs_method": "Email",
                "qp_ntfsubs_frequency": "$storage.time",
                "qp_ntfsubs_filter": "#dict('report_id', $form.input.id,"
                                     "'report_name', $storage.name,"
                                     "'resource', $storage.spec#eval#get('resource'),"
                                     "'method', $storage.spec#eval#get('method'),"
                                     "'pull', $storage.pull,"
                                     "'output_format', $storage.spec#eval#get('output_format'),"
                                     "'settings', $storage.setting,"
                                     "'repeat', $g_repeat_combobox.selected.name,"
                                     "'days', $g_day_cbx.checked.name,"
                                     "'times', $g_time_combobox.selected.name,"
                                     "'app_name', $app.name)"
            },

            "on_finish": "$form.close",

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        "update_schedule": {
            "method": "post",
            "resource": "/apps/any/queries/update__tbl__notify_subscriptions",

            "pull": {
                "qp_notify_subscription_id": "$form.input.schedule_id[$paginator.index]",
                "qp_ntfsubs_frequency": "$storage.time",
                "qp_ntfsubs_filter": "#dict('report_id', $storage.ntfsubs_filter.get('report_id'),"
                                     "'report_name', $storage.ntfsubs_filter.get('report_name'),"
                                     "'resource', $storage.spec#eval#get('resource'),"
                                     "'method', $storage.spec#eval#get('method'),"
                                     "'pull', $storage.pull,"
                                     "'output_format', $storage.spec#eval#get('output_format'),"
                                     "'settings', $storage.setting,"
                                     "'repeat', $g_repeat_combobox.selected.name,"
                                     "'days', $g_day_cbx.checked.name,"
                                     "'times', $g_time_combobox.selected.name,"
                                     "'app_name', $app.name)"
            },

            "on_success": [("if", "$paginator.is_last", "$form.accept")],

            "on_failure": ("error_message", "An error occurred when saving the record!")
        },

        "get_pet_status": {
            "method": "post",
            "resource": "/apps/any/queries/read__tbl__pet_statuses",
            "cache": ("app_cache", "pet_status", {"days": 1}),
            "pull": {
                "qp_pstatus_name": "Available,Isolation,On Hold",
                "order_by": "pstatus_name"
            },
            "on_success": ("push", "$storage.pet_status", "$result")
        },

        # Notifications
        "read_notifications": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__tbl__notify_subscriptions",

            "pull": {
                "qp_ntfsubs_user_entity_id": "$storage.user_id",
                "qp_ntfsubs_name": "NotificationPetSold"
            },

            "push": {
                "$storage.notify_subscription_id": "$result.notify_subscription_id or None",
                "$storage.notify": "$result",
            },

            "on_success": [
                ("if", "$result",
                 ("if", "$result.ntfsubs_notify_self[0]",
                  [
                      ("push", "$g_popicklist_checkbox", "$result.ntfsubs_notify_self[0]"),
                  ],
                  "$grp_nofications_objects.hide"
                  ),
                 [
                     ("push", "$g_popicklist_checkbox", None),
                     ("push", "$storage.notify_subscription_id", None)
                 ]
                 )
            ]
        },

        "update_notifications": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/update__tbl__notify_subscriptions",

            "pull": {
                "qp_notify_subscription_id": "$storage.notify_subscription_id",
                "qp_ntfsubs_notify_self": "$g_popicklist_checkbox",
            },

            "on_finish": [
                "$app.main_window.message.info('Your notification changes have been saved.')"
            ],

            "on_failure": ("error_message", "An error occurred when saving the Notifications!")
        },

        "create_notifications": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/create__tbl__notify_subscriptions",

            "pull": {
                "qp_ntfsubs_user_entity_id": "$storage.user_id",
                "qp_ntfsubs_frequency": "$storage.frequency.replace('b1','{').replace('b2','}')",
                "qp_ntfsubs_filter": "$storage.filter.replace('b1','{').replace('b2','}').replace('\"True\"','true').replace('\"False\"','false')",
                "qp_ntfsubs_notify_self": "g_popicklist_checkbox",
                "qp_ntfsubs_method": "Email",
                "qp_ntfsubs_name": "NotificationCustomerPickList"
            },

            "on_finish": [
                ("transfer", "read_notifications")
            ],

            "on_failure": ("error_message", "An error occurred when saving the Notifications!")
        },

    },

    "fpe_custom_widgets": [
        {
            "type": "paginator",
            "name": "paginator",

            "left_arrow": "g_previouspage_label",
            "right_arrow": "g_nextpage_label",
            "page_input": "g_currentpage_lineedit",
            "total_label": "g_totalpages_label",

            "initial_page": 1,
            "per_page": 1,

            "on_change": "pull_schedule",
            "on_changed": "load_data"
        },
        {
            "type": "check_combobox",
            "name": "g_day_cbx",
            "widget": "g_day_checkcombobox",
        },
        {
            "type": "check_combobox",
            "name": "c_month_checkcombobox",
            "widget": "g_month_checkcombobox",
        },
        {
            "type": "stack",
            "name": "stack",

            "schedule": True,
            "container": "g_reportoptions_stack",
        },
        {
            "type": "progress_bar",
            "name": "progress_bar",

            "frame": "g_progress_frame",
            "completed": "g_completed_frame"
        }
    ],

    "fpe_validations": {
        "g_day_cbx": {
            "rule": "$g_day_cbx.has_value and $storage.check_every == True or $storage.check_every == False",
            "message": "At least one day should be selected"
        },
    },

    "fpe_groups": {
    },

    # Widgets
    "form": {
        "title": "Manage Report Schedule",
        "resizable": "false",
        "on_load": [
            "prepare_form",
            "load_data"
        ],
    },

    "g_date_dateedit": {
        "on_change": [
            ("if", "$g_repeat_combobox.selected.value == 'weekly'",
                ["$g_day_cbx.set_all_unchecked()",
                 "$g_day_cbx.set_checked($g_date_dateedit.value#dayofweek, True)"])
        ]
    },

    "g_day_cbx": {
        "bind": {
            "show": "name",
            "value": "value",
            "check": "check"
        },
    },

    "c_month_checkcombobox": {
        "bind": {
            "show": "name",
            "value": "value",
            "check": "check"
        },
    },

    "g_time_combobox": {
        "blank": False,
        "bind": {
            "show": "name",
            "match": "value",
        },
    },

    "g_repeat_combobox": {
        "blank": False,
        "bind": {
            "show": "name",
            "match": "value",
        },
        "on_change": [
            ("if", "$g_repeat_combobox.selected.value == 'monthly'",
                [("push", "$storage.check_every", True),
                 ("push", "$g_day_cbx", "$storage.dayofmonth"),
                 "$g_month_checkcombobox.hide",
                 "$g_months_label.hide",
                 "$g_custom_frame.show",
                 "$g_day_cbx.set_all_unchecked()",
                 "$g_day_cbx.set_checked('1', True)"]),

            ("if", "$g_repeat_combobox.selected.value == 'weekly'",
                [("push", "$storage.check_every", True),
                 ("push", "$g_day_cbx", "$storage.dayofweek"),
                 "$g_custom_frame.show",
                 "$g_day_cbx.set_all_unchecked()",
                 "$g_day_cbx.set_checked($g_date_dateedit.value#dayofweek, True)"]),

            ("if", "$g_repeat_combobox.selected.value == 'daily'",
                [("push", "$storage.check_every", False),
                 ("push", "$g_day_cbx", []),
                 "$g_custom_frame.show",
                 "$g_day_cbx.set_all_unchecked()"]),

            ("if", "$g_repeat_combobox.selected.value == 'yearly'",
                [("push", "$storage.check_every", True),
                 ("push", "$g_day_cbx", "$storage.dayofmonth"),
                 "$g_month_checkcombobox.show",
                 "$g_months_label.show",
                 "$g_custom_frame.show",
                 "$c_month_checkcombobox.set_all_unchecked()",
                 "$c_month_checkcombobox.set_checked('1', True)",
                 "$g_day_cbx.set_all_unchecked()",
                 "$g_day_cbx.set_checked('1', True)"]),
        ]
    },

    "g_cancel_button": {
        "on_release": "$form.close"
    },

    "g_update_schedule_button": {
        "on_release": "save_and_move"
    },

    "g_add_schedule_button": {
        "on_release": "save_schedule"
    }
}
