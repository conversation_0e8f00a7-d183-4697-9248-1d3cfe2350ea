# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'paymentmethod_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_PaymentMethodDialog(object):
    def setupUi(self, PaymentMethodDialog):
        PaymentMethodDialog.setObjectName("PaymentMethodDialog")
        PaymentMethodDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        PaymentMethodDialog.resize(391, 361)
        PaymentMethodDialog.setStyleSheet("\n"
"QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    border: 2px solid #287599;\n"
"    color: white;\n"
"}\n"
"\n"
"#g_ok_button, \n"
"#__g_ok_button {\n"
"    background: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_ok_button:pressed,\n"
"#__g_ok_button:pressed {\n"
"    background: #007f00;\n"
"}\n"
"\n"
"#g_ok_button:disabled,\n"
"#__g_ok_button:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}\n"
"\n"
"#g_cancel_button,\n"
"#__g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed,\n"
"#__g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#label {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_paymentmethods_listbox {\n"
"    font-size: 15px;\n"
"}")
        PaymentMethodDialog.setSizeGripEnabled(False)
        PaymentMethodDialog.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(PaymentMethodDialog)
        self.verticalLayout.setContentsMargins(19, 19, 19, 19)
        self.verticalLayout.setSpacing(19)
        self.verticalLayout.setObjectName("verticalLayout")
        self.label = QtWidgets.QLabel(PaymentMethodDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.label.setFont(font)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.verticalLayout.addWidget(self.label)
        self.g_paymentmethods_listbox = QtWidgets.QListWidget(PaymentMethodDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_paymentmethods_listbox.setFont(font)
        self.g_paymentmethods_listbox.setMidLineWidth(1)
        self.g_paymentmethods_listbox.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_paymentmethods_listbox.setAlternatingRowColors(True)
        self.g_paymentmethods_listbox.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_paymentmethods_listbox.setObjectName("g_paymentmethods_listbox")
        item = QtWidgets.QListWidgetItem()
        self.g_paymentmethods_listbox.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_paymentmethods_listbox.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_paymentmethods_listbox.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_paymentmethods_listbox.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_paymentmethods_listbox.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_paymentmethods_listbox.addItem(item)
        self.verticalLayout.addWidget(self.g_paymentmethods_listbox)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(19)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(PaymentMethodDialog)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 16777215))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/paymentmethod_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_ok_button = QtWidgets.QPushButton(PaymentMethodDialog)
        self.g_ok_button.setEnabled(False)
        self.g_ok_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_ok_button.setMaximumSize(QtCore.QSize(120, 16777215))
        self.g_ok_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/paymentmethod_dialog/ok"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_ok_button.setIcon(icon1)
        self.g_ok_button.setIconSize(QtCore.QSize(24, 24))
        self.g_ok_button.setObjectName("g_ok_button")
        self.horizontalLayout.addWidget(self.g_ok_button)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(PaymentMethodDialog)
        QtCore.QMetaObject.connectSlotsByName(PaymentMethodDialog)

    def retranslateUi(self, PaymentMethodDialog):
        _translate = QtCore.QCoreApplication.translate
        PaymentMethodDialog.setWindowTitle(_translate("PaymentMethodDialog", "Payment Methods"))
        self.label.setText(_translate("PaymentMethodDialog", "Choose A Saved Card"))
        __sortingEnabled = self.g_paymentmethods_listbox.isSortingEnabled()
        self.g_paymentmethods_listbox.setSortingEnabled(False)
        item = self.g_paymentmethods_listbox.item(0)
        item.setText(_translate("PaymentMethodDialog", "American Express"))
        item = self.g_paymentmethods_listbox.item(1)
        item.setText(_translate("PaymentMethodDialog", "Discover"))
        item = self.g_paymentmethods_listbox.item(2)
        item.setText(_translate("PaymentMethodDialog", "Visa"))
        item = self.g_paymentmethods_listbox.item(3)
        item.setText(_translate("PaymentMethodDialog", "MasterCard"))
        item = self.g_paymentmethods_listbox.item(4)
        item.setText(_translate("PaymentMethodDialog", "Diner\'s Club"))
        item = self.g_paymentmethods_listbox.item(5)
        item.setText(_translate("PaymentMethodDialog", "JCB"))
        self.g_paymentmethods_listbox.setSortingEnabled(__sortingEnabled)
        self.g_cancel_button.setText(_translate("PaymentMethodDialog", "  Cancel"))
        self.g_ok_button.setText(_translate("PaymentMethodDialog", "  Ok"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    PaymentMethodDialog = QtWidgets.QDialog()
    ui = Ui_PaymentMethodDialog()
    ui.setupUi(PaymentMethodDialog)
    PaymentMethodDialog.show()
    sys.exit(app.exec_())
