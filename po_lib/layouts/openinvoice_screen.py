# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'openinvoice_screen.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_OpenInvoiceScreen(object):
    def setupUi(self, OpenInvoiceScreen):
        OpenInvoiceScreen.setObjectName("OpenInvoiceScreen")
        OpenInvoiceScreen.resize(928, 539)
        OpenInvoiceScreen.setMinimumSize(QtCore.QSize(0, 0))
        OpenInvoiceScreen.setMaximumSize(QtCore.QSize(16777215, 16777215))
        OpenInvoiceScreen.setStyleSheet("#OpenInvoiceScreen {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"    font-size:13px;\n"
"}\n"
"\n"
"QTabBar::tab:selected, \n"
"QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QCheckBox {\n"
"    padding-bottom:5px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/entity_dialog/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/entity_dialog/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"#g_curbsidesearchbox_frame, \n"
"#g_deliveryearchbox_frame, \n"
"#g_orphanedearchbox_frame, \n"
"#g_returnssearchbox_frame, \n"
"#g_holdssearchbox_frame, \n"
"#g_quotessearchbox_frame, \n"
"#g_layawayssearchbox_frame,\n"
"#g_credovasearchbox_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_curbsidesearchbox_frame, \n"
"#g_deliverysearchbox_frame, \n"
"#g_orphanedearchbox_frame, \n"
"#g_returnssearchbox_frame, \n"
"#g_holdssearchbox_frame, \n"
"#g_quotessearchbox_frame,\n"
"#g_layawayssearchbox_frame,\n"
"#g_credovasearchbox_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_curbsideclearsearch_button, \n"
"#g_deliveryclearsearch_button, \n"
"#g_orphanedclearsearch_button, \n"
"#g_returnsclearsearch_button, \n"
"#g_holdsclearsearch_button, \n"
"#g_quotesclearsearch_button,\n"
"#g_layawaysclearsearch_button,\n"
"#g_credovaclearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_curbsidesearchtext_lineedit,\n"
"#g_deliverysearchtext_lineedit,\n"
"#g_orphanedsearchtext_lineedit,\n"
"#g_returnssearchtext_lineedit,\n"
"#g_holdssearchtext_lineedit, \n"
"#g_quotessearchtext_lineedit,\n"
"#g_layawayssearchtext_lineedit,\n"
"#g_credovasearchtext_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_curbsidesearch_combobox,\n"
"#g_deliverysearch_combobox,\n"
"#g_orphanedsearch_combobox, \n"
"#g_returnssearch_combobox, \n"
"#g_holdssearch_combobox, \n"
"#g_quotessearch_combobox, \n"
"#g_layawayssearch_combobox,\n"
"#g_credovasearch_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" } \n"
"\n"
"\n"
"#g_curbsidesearch_combobox::drop-down,\n"
"#g_deliverysearch_combobox::drop-down,\n"
"#g_orphanedsearch_combobox::drop-down,\n"
"#g_returnssearch_combobox::drop-down,\n"
"#g_holdssearch_combobox::drop-down, \n"
"#g_quotessearch_combobox::drop-down, \n"
"#g_layawayssearch_combobox::drop-down,\n"
"#g_credovasearch_combobox::drop-down  {\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"}\n"
"\n"
"#g_curbsidesearch_combobox::drop-down,\n"
"#g_deliverysearch_combobox::drop-down,\n"
"#g_orphanedsearch_combobox::drop-down,\n"
"#g_returnssearch_combobox::drop-down, \n"
"#g_holdssearch_combobox::drop-down, \n"
"#g_quotessearch_combobox::drop-down, \n"
"#g_layawayssearch_combobox::drop-down,\n"
"#g_credovasearch_combobox::drop-down  {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"#g_main_tabs QTabWidget::tab-bar {\n"
"     left: 5px;\n"
" }\n"
"\n"
"#g_main_tabs QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB;\n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"#g_main_tabs QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"#g_main_tabs QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" } \n"
"\n"
"#g_curbside_cancel_button,\n"
"#g_delivery_cancel_button,\n"
"#g_orphaned_cancel_button,\n"
"#g_returns_cancel_button,\n"
"#g_holds_cancel_button, \n"
"#g_quotes_cancel_button,\n"
"#g_layaways_cancel_button,\n"
"#g_credova_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_curbside_cancel_button:pressed,\n"
"#g_delivery_cancel_button:pressed,\n"
"#g_orphaned_cancel_button:pressed,\n"
"#g_returns_cancel_button:pressed,\n"
"#g_holds_cancel_button:pressed, \n"
"#g_quotes_cancel_button:pressed,\n"
"#g_layaways_cancel_button:pressed,\n"
"#g_credova_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_curbside_select_button,\n"
"#g_delivery_select_button,\n"
"#g_orphaned_select_button,\n"
"#g_returns_select_button,\n"
"#g_holds_select_button, \n"
"#g_quotes_select_button,\n"
"#g_layaways_select_button,\n"
"#g_credova_select_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_curbside_select_button:pressed,\n"
"#g_delivery_select_button:pressed,\n"
"#g_orphaned_select_button:pressed,\n"
"#g_returns_select_button:pressed,\n"
"#g_holds_select_button:pressed, \n"
"#g_quotes_select_button:pressed,\n"
"#g_layaways_select_button:pressed,\n"
"#g_credova_select_button:pressed {\n"
"    background-color: #007f00;\n"
"} \n"
"\n"
"#g_returns_tab, \n"
"#g_holds_tab, \n"
"#g_quotes_tab, \n"
"#g_layaways_tab,\n"
"#g_credova_tab,\n"
"#g_curbside_tab,\n"
"#g_delivery_tab {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius:0px;\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"\n"
"QTableView {\n"
"\n"
"    /* Color of the table text.\n"
"\n"
"    Must be defined explicitly because the text color of the focused\n"
"    (:focus) item will be set to this.  */\n"
"    color: black;\n"
"\n"
"    /* Colors of the selection text and background respectively.\n"
"\n"
"    These must be defined explicitly because the text/background color\n"
"    of the :focus:selected item will be set to this.  */\n"
"    selection-color: white;\n"
"    selection-background-color: rgb(51, 153, 255);\n"
"\n"
"    /* Removes the dotted border from selected cells in a table widget. */\n"
"    outline: 0;\n"
"\n"
"}\n"
"\n"
"\n"
"QTableWidget::item:focus:selected,\n"
"QTableWidget::item:focus {\n"
"\n"
"    /* Do not display a border around any focused item whether it\'s part\n"
"    of a selection or not.  */\n"
"    border: none;\n"
"    \n"
"}\n"
"\n"
"\n"
"QTableWidget::item:focus:selected {\n"
"\n"
"    /* Set the text and background colors to be the same as the selection\n"
"    colors defined earlier.\n"
"\n"
"    Pseudo-states can be chained, in which case a logical AND is\n"
"    implied. */\n"
"    color: white;\n"
"    background-color: rgb(51, 153, 255);\n"
"}\n"
"\n"
"\n"
"QTableWidget::item:focus {\n"
"\n"
"    /* Set the focused item text to be the same \n"
"\n"
"    No \'unselected\' pseudo-state exists, only \'selected\'. */\n"
"\n"
"    color: black;\n"
"\n"
"    /* Alternating row colors can also be styled, but \'transparent\' allows\n"
"    the default alternating row colors to be used. */\n"
"    background-color: transparent;\n"
"}\n"
"\n"
"#__g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_holdstotalresults_label,\n"
"#g_quotestotalresults_label,\n"
"#g_layawaystotalresults_label,\n"
"#g_returnstotalresults_label,\n"
"#g_credovatotalresults_label,\n"
"#g_nav_frame QLabel,\n"
"#g_nav_frame_2 QLabel,\n"
"#g_nav_frame_3 QLabel,\n"
"#g_nav_frame_4 QLabel,\n"
"#g_nav_frame_5 QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: normal;\n"
"}")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/paymentmanagement_screen/payments"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        OpenInvoiceScreen.setProperty("screen_toolbutton_icon", icon)
        self.verticalLayout = QtWidgets.QVBoxLayout(OpenInvoiceScreen)
        self.verticalLayout.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_progress_frame = QtWidgets.QFrame(OpenInvoiceScreen)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.verticalLayout.addWidget(self.g_progress_frame)
        self.__g_main_label = QtWidgets.QLabel(OpenInvoiceScreen)
        self.__g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.__g_main_label.setFont(font)
        self.__g_main_label.setObjectName("__g_main_label")
        self.verticalLayout.addWidget(self.__g_main_label)
        self.g_main_tabs = QtWidgets.QTabWidget(OpenInvoiceScreen)
        self.g_main_tabs.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_main_tabs.sizePolicy().hasHeightForWidth())
        self.g_main_tabs.setSizePolicy(sizePolicy)
        self.g_main_tabs.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        font.setKerning(True)
        self.g_main_tabs.setFont(font)
        self.g_main_tabs.setAutoFillBackground(False)
        self.g_main_tabs.setStyleSheet("")
        self.g_main_tabs.setTabPosition(QtWidgets.QTabWidget.North)
        self.g_main_tabs.setTabShape(QtWidgets.QTabWidget.Rounded)
        self.g_main_tabs.setIconSize(QtCore.QSize(50, 50))
        self.g_main_tabs.setElideMode(QtCore.Qt.ElideNone)
        self.g_main_tabs.setUsesScrollButtons(True)
        self.g_main_tabs.setDocumentMode(True)
        self.g_main_tabs.setTabsClosable(False)
        self.g_main_tabs.setMovable(False)
        self.g_main_tabs.setTabBarAutoHide(False)
        self.g_main_tabs.setObjectName("g_main_tabs")
        self.g_holds_tab = QtWidgets.QWidget()
        self.g_holds_tab.setStyleSheet("")
        self.g_holds_tab.setObjectName("g_holds_tab")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_holds_tab)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_holdsunifiedsearch_frame = QtWidgets.QFrame(self.g_holds_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_holdsunifiedsearch_frame.sizePolicy().hasHeightForWidth())
        self.g_holdsunifiedsearch_frame.setSizePolicy(sizePolicy)
        self.g_holdsunifiedsearch_frame.setMinimumSize(QtCore.QSize(561, 41))
        self.g_holdsunifiedsearch_frame.setMaximumSize(QtCore.QSize(166546, 59))
        self.g_holdsunifiedsearch_frame.setStyleSheet("")
        self.g_holdsunifiedsearch_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_holdsunifiedsearch_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_holdsunifiedsearch_frame.setObjectName("g_holdsunifiedsearch_frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_holdsunifiedsearch_frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setSpacing(14)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_holdssearch_combobox = QtWidgets.QComboBox(self.g_holdsunifiedsearch_frame)
        self.g_holdssearch_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_holdssearch_combobox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_holdssearch_combobox.setStyleSheet("")
        self.g_holdssearch_combobox.setFrame(False)
        self.g_holdssearch_combobox.setObjectName("g_holdssearch_combobox")
        self.g_holdssearch_combobox.addItem("")
        self.g_holdssearch_combobox.addItem("")
        self.g_holdssearch_combobox.addItem("")
        self.g_holdssearch_combobox.addItem("")
        self.horizontalLayout_7.addWidget(self.g_holdssearch_combobox)
        self.g_holdssearchbox_frame = QtWidgets.QFrame(self.g_holdsunifiedsearch_frame)
        self.g_holdssearchbox_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_holdssearchbox_frame.setStyleSheet("")
        self.g_holdssearchbox_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_holdssearchbox_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_holdssearchbox_frame.setObjectName("g_holdssearchbox_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_holdssearchbox_frame)
        self.horizontalLayout_8.setContentsMargins(2, 0, -1, 0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_holdssearchtext_lineedit = QtWidgets.QLineEdit(self.g_holdssearchbox_frame)
        self.g_holdssearchtext_lineedit.setStyleSheet("")
        self.g_holdssearchtext_lineedit.setText("")
        self.g_holdssearchtext_lineedit.setObjectName("g_holdssearchtext_lineedit")
        self.horizontalLayout_8.addWidget(self.g_holdssearchtext_lineedit)
        self.g_holdsclearsearch_button = QtWidgets.QPushButton(self.g_holdssearchbox_frame)
        self.g_holdsclearsearch_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_holdsclearsearch_button.setStyleSheet("")
        self.g_holdsclearsearch_button.setText("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/clear"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_holdsclearsearch_button.setIcon(icon1)
        self.g_holdsclearsearch_button.setIconSize(QtCore.QSize(18, 18))
        self.g_holdsclearsearch_button.setObjectName("g_holdsclearsearch_button")
        self.horizontalLayout_8.addWidget(self.g_holdsclearsearch_button)
        self.horizontalLayout_7.addWidget(self.g_holdssearchbox_frame)
        self.g_onlymyinvoice_checkbox = QtWidgets.QCheckBox(self.g_holdsunifiedsearch_frame)
        self.g_onlymyinvoice_checkbox.setMinimumSize(QtCore.QSize(120, 0))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_onlymyinvoice_checkbox.setFont(font)
        self.g_onlymyinvoice_checkbox.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.g_onlymyinvoice_checkbox.setChecked(True)
        self.g_onlymyinvoice_checkbox.setProperty("qp_txn_loc_entity_id", "")
        self.g_onlymyinvoice_checkbox.setObjectName("g_onlymyinvoice_checkbox")
        self.horizontalLayout_7.addWidget(self.g_onlymyinvoice_checkbox)
        self.g_nav_frame = QtWidgets.QFrame(self.g_holdsunifiedsearch_frame)
        self.g_nav_frame.setMinimumSize(QtCore.QSize(150, 0))
        self.g_nav_frame.setMaximumSize(QtCore.QSize(150, 16777215))
        self.g_nav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_nav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_nav_frame.setObjectName("g_nav_frame")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.g_nav_frame)
        self.verticalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_6.setSpacing(0)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.g_pagenav_hlayout_3 = QtWidgets.QHBoxLayout()
        self.g_pagenav_hlayout_3.setSpacing(6)
        self.g_pagenav_hlayout_3.setObjectName("g_pagenav_hlayout_3")
        self.g_holdspreviouspage_label = QtWidgets.QLabel(self.g_nav_frame)
        self.g_holdspreviouspage_label.setEnabled(False)
        self.g_holdspreviouspage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_holdspreviouspage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_holdspreviouspage_label.setText("")
        self.g_holdspreviouspage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/previous"))
        self.g_holdspreviouspage_label.setScaledContents(False)
        self.g_holdspreviouspage_label.setObjectName("g_holdspreviouspage_label")
        self.g_pagenav_hlayout_3.addWidget(self.g_holdspreviouspage_label)
        self.g_holdsnextpage_label = QtWidgets.QLabel(self.g_nav_frame)
        self.g_holdsnextpage_label.setEnabled(True)
        self.g_holdsnextpage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_holdsnextpage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_holdsnextpage_label.setText("")
        self.g_holdsnextpage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/next"))
        self.g_holdsnextpage_label.setObjectName("g_holdsnextpage_label")
        self.g_pagenav_hlayout_3.addWidget(self.g_holdsnextpage_label)
        self.g_holdscurrentpage_lineedit = QtWidgets.QLineEdit(self.g_nav_frame)
        self.g_holdscurrentpage_lineedit.setEnabled(True)
        self.g_holdscurrentpage_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_holdscurrentpage_lineedit.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_holdscurrentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_holdscurrentpage_lineedit.setObjectName("g_holdscurrentpage_lineedit")
        self.g_pagenav_hlayout_3.addWidget(self.g_holdscurrentpage_lineedit)
        self.__g_holdsslash_label = QtWidgets.QLabel(self.g_nav_frame)
        self.__g_holdsslash_label.setMinimumSize(QtCore.QSize(7, 0))
        self.__g_holdsslash_label.setMaximumSize(QtCore.QSize(7, 16777215))
        self.__g_holdsslash_label.setObjectName("__g_holdsslash_label")
        self.g_pagenav_hlayout_3.addWidget(self.__g_holdsslash_label)
        self.g_holdstotalpages_label = QtWidgets.QLabel(self.g_nav_frame)
        self.g_holdstotalpages_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_holdstotalpages_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_holdstotalpages_label.setObjectName("g_holdstotalpages_label")
        self.g_pagenav_hlayout_3.addWidget(self.g_holdstotalpages_label)
        self.verticalLayout_6.addLayout(self.g_pagenav_hlayout_3)
        self.horizontalLayout_7.addWidget(self.g_nav_frame)
        self.verticalLayout_3.addWidget(self.g_holdsunifiedsearch_frame)
        self.g_holds_table = QtWidgets.QTableWidget(self.g_holds_tab)
        self.g_holds_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_holds_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_holds_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_holds_table.setAlternatingRowColors(True)
        self.g_holds_table.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.g_holds_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_holds_table.setShowGrid(False)
        self.g_holds_table.setObjectName("g_holds_table")
        self.g_holds_table.setColumnCount(7)
        self.g_holds_table.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_holds_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_holds_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_holds_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_holds_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_holds_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_holds_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_holds_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_holds_table.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_holds_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_holds_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_holds_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_holds_table.setItem(0, 5, item)
        self.g_holds_table.horizontalHeader().setDefaultSectionSize(150)
        self.g_holds_table.horizontalHeader().setStretchLastSection(True)
        self.g_holds_table.verticalHeader().setVisible(False)
        self.verticalLayout_3.addWidget(self.g_holds_table)
        self.__g_holdsbottombar_frame = QtWidgets.QFrame(self.g_holds_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_holdsbottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_holdsbottombar_frame.setSizePolicy(sizePolicy)
        self.__g_holdsbottombar_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_holdsbottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_holdsbottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_holdsbottombar_frame.setStyleSheet("")
        self.__g_holdsbottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_holdsbottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_holdsbottombar_frame.setObjectName("__g_holdsbottombar_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.__g_holdsbottombar_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setSpacing(0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_holdstotalresults_label = QtWidgets.QLabel(self.__g_holdsbottombar_frame)
        self.g_holdstotalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_holdstotalresults_label.setFont(font)
        self.g_holdstotalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_holdstotalresults_label.setObjectName("g_holdstotalresults_label")
        self.horizontalLayout_3.addWidget(self.g_holdstotalresults_label)
        spacerItem = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem)
        self.__g_holdsbuttons_frame = QtWidgets.QFrame(self.__g_holdsbottombar_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_holdsbuttons_frame.sizePolicy().hasHeightForWidth())
        self.__g_holdsbuttons_frame.setSizePolicy(sizePolicy)
        self.__g_holdsbuttons_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_holdsbuttons_frame.setMaximumSize(QtCore.QSize(495, 40))
        self.__g_holdsbuttons_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_holdsbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_holdsbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_holdsbuttons_frame.setObjectName("__g_holdsbuttons_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.__g_holdsbuttons_frame)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setSpacing(5)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_holds_cancel_button = QtWidgets.QPushButton(self.__g_holdsbuttons_frame)
        self.g_holds_cancel_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_holds_cancel_button.sizePolicy().hasHeightForWidth())
        self.g_holds_cancel_button.setSizePolicy(sizePolicy)
        self.g_holds_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_holds_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_holds_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_holds_cancel_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_holds_cancel_button.setIcon(icon2)
        self.g_holds_cancel_button.setIconSize(QtCore.QSize(24, 24))
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/customers_screen_dialog/customers"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_holds_cancel_button.setProperty("screen_toolbutton_icon", icon3)
        self.g_holds_cancel_button.setObjectName("g_holds_cancel_button")
        self.horizontalLayout_5.addWidget(self.g_holds_cancel_button)
        self.g_holds_select_button = QtWidgets.QPushButton(self.__g_holdsbuttons_frame)
        self.g_holds_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_holds_select_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_holds_select_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_holds_select_button.setStyleSheet("")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_holds_select_button.setIcon(icon4)
        self.g_holds_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_holds_select_button.setObjectName("g_holds_select_button")
        self.horizontalLayout_5.addWidget(self.g_holds_select_button)
        self.horizontalLayout_3.addWidget(self.__g_holdsbuttons_frame)
        self.verticalLayout_3.addWidget(self.__g_holdsbottombar_frame)
        self.g_main_tabs.addTab(self.g_holds_tab, "")
        self.g_quotes_tab = QtWidgets.QWidget()
        self.g_quotes_tab.setStyleSheet("")
        self.g_quotes_tab.setObjectName("g_quotes_tab")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_quotes_tab)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_quotesunifiedsearch_frame = QtWidgets.QFrame(self.g_quotes_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_quotesunifiedsearch_frame.sizePolicy().hasHeightForWidth())
        self.g_quotesunifiedsearch_frame.setSizePolicy(sizePolicy)
        self.g_quotesunifiedsearch_frame.setMinimumSize(QtCore.QSize(561, 41))
        self.g_quotesunifiedsearch_frame.setMaximumSize(QtCore.QSize(166546, 59))
        self.g_quotesunifiedsearch_frame.setStyleSheet("")
        self.g_quotesunifiedsearch_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_quotesunifiedsearch_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_quotesunifiedsearch_frame.setObjectName("g_quotesunifiedsearch_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_quotesunifiedsearch_frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setSpacing(14)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_quotessearch_combobox = QtWidgets.QComboBox(self.g_quotesunifiedsearch_frame)
        self.g_quotessearch_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_quotessearch_combobox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_quotessearch_combobox.setStyleSheet("")
        self.g_quotessearch_combobox.setFrame(False)
        self.g_quotessearch_combobox.setObjectName("g_quotessearch_combobox")
        self.g_quotessearch_combobox.addItem("")
        self.g_quotessearch_combobox.addItem("")
        self.g_quotessearch_combobox.addItem("")
        self.g_quotessearch_combobox.addItem("")
        self.horizontalLayout_4.addWidget(self.g_quotessearch_combobox)
        self.g_quotessearchbox_frame = QtWidgets.QFrame(self.g_quotesunifiedsearch_frame)
        self.g_quotessearchbox_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_quotessearchbox_frame.setStyleSheet("")
        self.g_quotessearchbox_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_quotessearchbox_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_quotessearchbox_frame.setObjectName("g_quotessearchbox_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_quotessearchbox_frame)
        self.horizontalLayout_6.setContentsMargins(2, 0, -1, 0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_quotessearchtext_lineedit = QtWidgets.QLineEdit(self.g_quotessearchbox_frame)
        self.g_quotessearchtext_lineedit.setStyleSheet("")
        self.g_quotessearchtext_lineedit.setText("")
        self.g_quotessearchtext_lineedit.setObjectName("g_quotessearchtext_lineedit")
        self.horizontalLayout_6.addWidget(self.g_quotessearchtext_lineedit)
        self.g_quotesclearsearch_button = QtWidgets.QPushButton(self.g_quotessearchbox_frame)
        self.g_quotesclearsearch_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_quotesclearsearch_button.setStyleSheet("")
        self.g_quotesclearsearch_button.setText("")
        self.g_quotesclearsearch_button.setIcon(icon1)
        self.g_quotesclearsearch_button.setIconSize(QtCore.QSize(18, 18))
        self.g_quotesclearsearch_button.setObjectName("g_quotesclearsearch_button")
        self.horizontalLayout_6.addWidget(self.g_quotesclearsearch_button)
        self.horizontalLayout_4.addWidget(self.g_quotessearchbox_frame)
        self.g_quoteonlymyinvoice_checkbox = QtWidgets.QCheckBox(self.g_quotesunifiedsearch_frame)
        self.g_quoteonlymyinvoice_checkbox.setMinimumSize(QtCore.QSize(120, 0))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_quoteonlymyinvoice_checkbox.setFont(font)
        self.g_quoteonlymyinvoice_checkbox.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.g_quoteonlymyinvoice_checkbox.setChecked(True)
        self.g_quoteonlymyinvoice_checkbox.setProperty("qp_txn_loc_entity_id", "")
        self.g_quoteonlymyinvoice_checkbox.setObjectName("g_quoteonlymyinvoice_checkbox")
        self.horizontalLayout_4.addWidget(self.g_quoteonlymyinvoice_checkbox)
        self.g_nav_frame_2 = QtWidgets.QFrame(self.g_quotesunifiedsearch_frame)
        self.g_nav_frame_2.setMinimumSize(QtCore.QSize(150, 0))
        self.g_nav_frame_2.setMaximumSize(QtCore.QSize(150, 16777215))
        self.g_nav_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_nav_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_nav_frame_2.setObjectName("g_nav_frame_2")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.g_nav_frame_2)
        self.verticalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_7.setSpacing(0)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.g_pagenav_hlayout_4 = QtWidgets.QHBoxLayout()
        self.g_pagenav_hlayout_4.setSpacing(6)
        self.g_pagenav_hlayout_4.setObjectName("g_pagenav_hlayout_4")
        self.g_quotespreviouspage_label = QtWidgets.QLabel(self.g_nav_frame_2)
        self.g_quotespreviouspage_label.setEnabled(False)
        self.g_quotespreviouspage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_quotespreviouspage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_quotespreviouspage_label.setText("")
        self.g_quotespreviouspage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/previous"))
        self.g_quotespreviouspage_label.setScaledContents(False)
        self.g_quotespreviouspage_label.setObjectName("g_quotespreviouspage_label")
        self.g_pagenav_hlayout_4.addWidget(self.g_quotespreviouspage_label)
        self.g_quotesnextpage_label = QtWidgets.QLabel(self.g_nav_frame_2)
        self.g_quotesnextpage_label.setEnabled(True)
        self.g_quotesnextpage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_quotesnextpage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_quotesnextpage_label.setText("")
        self.g_quotesnextpage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/next"))
        self.g_quotesnextpage_label.setObjectName("g_quotesnextpage_label")
        self.g_pagenav_hlayout_4.addWidget(self.g_quotesnextpage_label)
        self.g_quotescurrentpage_lineedit = QtWidgets.QLineEdit(self.g_nav_frame_2)
        self.g_quotescurrentpage_lineedit.setEnabled(True)
        self.g_quotescurrentpage_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_quotescurrentpage_lineedit.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_quotescurrentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_quotescurrentpage_lineedit.setObjectName("g_quotescurrentpage_lineedit")
        self.g_pagenav_hlayout_4.addWidget(self.g_quotescurrentpage_lineedit)
        self.__g_holdsslash_label_3 = QtWidgets.QLabel(self.g_nav_frame_2)
        self.__g_holdsslash_label_3.setMinimumSize(QtCore.QSize(7, 0))
        self.__g_holdsslash_label_3.setMaximumSize(QtCore.QSize(7, 16777215))
        self.__g_holdsslash_label_3.setObjectName("__g_holdsslash_label_3")
        self.g_pagenav_hlayout_4.addWidget(self.__g_holdsslash_label_3)
        self.g_quotestotalpages_label = QtWidgets.QLabel(self.g_nav_frame_2)
        self.g_quotestotalpages_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_quotestotalpages_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_quotestotalpages_label.setObjectName("g_quotestotalpages_label")
        self.g_pagenav_hlayout_4.addWidget(self.g_quotestotalpages_label)
        self.verticalLayout_7.addLayout(self.g_pagenav_hlayout_4)
        self.horizontalLayout_4.addWidget(self.g_nav_frame_2)
        self.verticalLayout_2.addWidget(self.g_quotesunifiedsearch_frame)
        self.g_quotes_table = QtWidgets.QTableWidget(self.g_quotes_tab)
        self.g_quotes_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_quotes_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_quotes_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_quotes_table.setAlternatingRowColors(True)
        self.g_quotes_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_quotes_table.setShowGrid(False)
        self.g_quotes_table.setObjectName("g_quotes_table")
        self.g_quotes_table.setColumnCount(7)
        self.g_quotes_table.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_quotes_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setItem(0, 5, item)
        self.g_quotes_table.horizontalHeader().setDefaultSectionSize(150)
        self.g_quotes_table.horizontalHeader().setStretchLastSection(True)
        self.g_quotes_table.verticalHeader().setVisible(False)
        self.verticalLayout_2.addWidget(self.g_quotes_table)
        self.__g_quotesbottombar_frame = QtWidgets.QFrame(self.g_quotes_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_quotesbottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_quotesbottombar_frame.setSizePolicy(sizePolicy)
        self.__g_quotesbottombar_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_quotesbottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_quotesbottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_quotesbottombar_frame.setStyleSheet("")
        self.__g_quotesbottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_quotesbottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_quotesbottombar_frame.setObjectName("__g_quotesbottombar_frame")
        self.horizontalLayout_31 = QtWidgets.QHBoxLayout(self.__g_quotesbottombar_frame)
        self.horizontalLayout_31.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_31.setSpacing(0)
        self.horizontalLayout_31.setObjectName("horizontalLayout_31")
        self.g_quotestotalresults_label = QtWidgets.QLabel(self.__g_quotesbottombar_frame)
        self.g_quotestotalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_quotestotalresults_label.setFont(font)
        self.g_quotestotalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_quotestotalresults_label.setObjectName("g_quotestotalresults_label")
        self.horizontalLayout_31.addWidget(self.g_quotestotalresults_label)
        spacerItem1 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_31.addItem(spacerItem1)
        self.__g_quotesbuttons_frame = QtWidgets.QFrame(self.__g_quotesbottombar_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_quotesbuttons_frame.sizePolicy().hasHeightForWidth())
        self.__g_quotesbuttons_frame.setSizePolicy(sizePolicy)
        self.__g_quotesbuttons_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_quotesbuttons_frame.setMaximumSize(QtCore.QSize(495, 40))
        self.__g_quotesbuttons_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_quotesbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_quotesbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_quotesbuttons_frame.setObjectName("__g_quotesbuttons_frame")
        self.horizontalLayout_51 = QtWidgets.QHBoxLayout(self.__g_quotesbuttons_frame)
        self.horizontalLayout_51.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_51.setSpacing(5)
        self.horizontalLayout_51.setObjectName("horizontalLayout_51")
        self.g_quotes_cancel_button = QtWidgets.QPushButton(self.__g_quotesbuttons_frame)
        self.g_quotes_cancel_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_quotes_cancel_button.sizePolicy().hasHeightForWidth())
        self.g_quotes_cancel_button.setSizePolicy(sizePolicy)
        self.g_quotes_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_quotes_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_quotes_cancel_button.setStyleSheet("")
        self.g_quotes_cancel_button.setIcon(icon2)
        self.g_quotes_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_quotes_cancel_button.setProperty("screen_toolbutton_icon", icon3)
        self.g_quotes_cancel_button.setObjectName("g_quotes_cancel_button")
        self.horizontalLayout_51.addWidget(self.g_quotes_cancel_button)
        self.g_quotes_select_button = QtWidgets.QPushButton(self.__g_quotesbuttons_frame)
        self.g_quotes_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_quotes_select_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_quotes_select_button.setStyleSheet("")
        self.g_quotes_select_button.setIcon(icon4)
        self.g_quotes_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_quotes_select_button.setObjectName("g_quotes_select_button")
        self.horizontalLayout_51.addWidget(self.g_quotes_select_button)
        self.horizontalLayout_31.addWidget(self.__g_quotesbuttons_frame)
        self.verticalLayout_2.addWidget(self.__g_quotesbottombar_frame)
        self.g_main_tabs.addTab(self.g_quotes_tab, "")
        self.g_layaways_tab = QtWidgets.QWidget()
        self.g_layaways_tab.setEnabled(True)
        self.g_layaways_tab.setStyleSheet("")
        self.g_layaways_tab.setObjectName("g_layaways_tab")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_layaways_tab)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_layawaysunifiedsearch_frame = QtWidgets.QFrame(self.g_layaways_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_layawaysunifiedsearch_frame.sizePolicy().hasHeightForWidth())
        self.g_layawaysunifiedsearch_frame.setSizePolicy(sizePolicy)
        self.g_layawaysunifiedsearch_frame.setMinimumSize(QtCore.QSize(561, 41))
        self.g_layawaysunifiedsearch_frame.setMaximumSize(QtCore.QSize(166546, 59))
        self.g_layawaysunifiedsearch_frame.setStyleSheet("")
        self.g_layawaysunifiedsearch_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_layawaysunifiedsearch_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_layawaysunifiedsearch_frame.setObjectName("g_layawaysunifiedsearch_frame")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.g_layawaysunifiedsearch_frame)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setSpacing(14)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.g_layawayssearch_combobox = QtWidgets.QComboBox(self.g_layawaysunifiedsearch_frame)
        self.g_layawayssearch_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_layawayssearch_combobox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_layawayssearch_combobox.setStyleSheet("")
        self.g_layawayssearch_combobox.setFrame(False)
        self.g_layawayssearch_combobox.setObjectName("g_layawayssearch_combobox")
        self.g_layawayssearch_combobox.addItem("")
        self.g_layawayssearch_combobox.addItem("")
        self.g_layawayssearch_combobox.addItem("")
        self.g_layawayssearch_combobox.addItem("")
        self.horizontalLayout_9.addWidget(self.g_layawayssearch_combobox)
        self.g_layawayssearchbox_frame = QtWidgets.QFrame(self.g_layawaysunifiedsearch_frame)
        self.g_layawayssearchbox_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_layawayssearchbox_frame.setStyleSheet("")
        self.g_layawayssearchbox_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_layawayssearchbox_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_layawayssearchbox_frame.setObjectName("g_layawayssearchbox_frame")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.g_layawayssearchbox_frame)
        self.horizontalLayout_10.setContentsMargins(2, 0, -1, 0)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.g_layawayssearchtext_lineedit = QtWidgets.QLineEdit(self.g_layawayssearchbox_frame)
        self.g_layawayssearchtext_lineedit.setStyleSheet("")
        self.g_layawayssearchtext_lineedit.setText("")
        self.g_layawayssearchtext_lineedit.setObjectName("g_layawayssearchtext_lineedit")
        self.horizontalLayout_10.addWidget(self.g_layawayssearchtext_lineedit)
        self.g_layawaysclearsearch_button = QtWidgets.QPushButton(self.g_layawayssearchbox_frame)
        self.g_layawaysclearsearch_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_layawaysclearsearch_button.setStyleSheet("")
        self.g_layawaysclearsearch_button.setText("")
        self.g_layawaysclearsearch_button.setIcon(icon1)
        self.g_layawaysclearsearch_button.setIconSize(QtCore.QSize(18, 18))
        self.g_layawaysclearsearch_button.setObjectName("g_layawaysclearsearch_button")
        self.horizontalLayout_10.addWidget(self.g_layawaysclearsearch_button)
        self.horizontalLayout_9.addWidget(self.g_layawayssearchbox_frame)
        self.g_nav_frame_3 = QtWidgets.QFrame(self.g_layawaysunifiedsearch_frame)
        self.g_nav_frame_3.setMinimumSize(QtCore.QSize(150, 0))
        self.g_nav_frame_3.setMaximumSize(QtCore.QSize(150, 16777215))
        self.g_nav_frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_nav_frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_nav_frame_3.setObjectName("g_nav_frame_3")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.g_nav_frame_3)
        self.verticalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_8.setSpacing(0)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.g_pagenav_hlayout_5 = QtWidgets.QHBoxLayout()
        self.g_pagenav_hlayout_5.setSpacing(6)
        self.g_pagenav_hlayout_5.setObjectName("g_pagenav_hlayout_5")
        self.g_layawayspreviouspage_label = QtWidgets.QLabel(self.g_nav_frame_3)
        self.g_layawayspreviouspage_label.setEnabled(False)
        self.g_layawayspreviouspage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_layawayspreviouspage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_layawayspreviouspage_label.setText("")
        self.g_layawayspreviouspage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/previous"))
        self.g_layawayspreviouspage_label.setScaledContents(False)
        self.g_layawayspreviouspage_label.setObjectName("g_layawayspreviouspage_label")
        self.g_pagenav_hlayout_5.addWidget(self.g_layawayspreviouspage_label)
        self.g_layawaysnextpage_label = QtWidgets.QLabel(self.g_nav_frame_3)
        self.g_layawaysnextpage_label.setEnabled(True)
        self.g_layawaysnextpage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_layawaysnextpage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_layawaysnextpage_label.setText("")
        self.g_layawaysnextpage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/next"))
        self.g_layawaysnextpage_label.setObjectName("g_layawaysnextpage_label")
        self.g_pagenav_hlayout_5.addWidget(self.g_layawaysnextpage_label)
        self.g_layawayscurrentpage_lineedit = QtWidgets.QLineEdit(self.g_nav_frame_3)
        self.g_layawayscurrentpage_lineedit.setEnabled(True)
        self.g_layawayscurrentpage_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_layawayscurrentpage_lineedit.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_layawayscurrentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_layawayscurrentpage_lineedit.setObjectName("g_layawayscurrentpage_lineedit")
        self.g_pagenav_hlayout_5.addWidget(self.g_layawayscurrentpage_lineedit)
        self.__g_layawaysslash_label = QtWidgets.QLabel(self.g_nav_frame_3)
        self.__g_layawaysslash_label.setMinimumSize(QtCore.QSize(7, 0))
        self.__g_layawaysslash_label.setMaximumSize(QtCore.QSize(7, 16777215))
        self.__g_layawaysslash_label.setObjectName("__g_layawaysslash_label")
        self.g_pagenav_hlayout_5.addWidget(self.__g_layawaysslash_label)
        self.g_layawaystotalpages_label = QtWidgets.QLabel(self.g_nav_frame_3)
        self.g_layawaystotalpages_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_layawaystotalpages_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_layawaystotalpages_label.setObjectName("g_layawaystotalpages_label")
        self.g_pagenav_hlayout_5.addWidget(self.g_layawaystotalpages_label)
        self.verticalLayout_8.addLayout(self.g_pagenav_hlayout_5)
        self.horizontalLayout_9.addWidget(self.g_nav_frame_3)
        self.verticalLayout_4.addWidget(self.g_layawaysunifiedsearch_frame)
        self.g_layaways_table = QtWidgets.QTableWidget(self.g_layaways_tab)
        self.g_layaways_table.setEnabled(True)
        self.g_layaways_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_layaways_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_layaways_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_layaways_table.setAlternatingRowColors(True)
        self.g_layaways_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_layaways_table.setShowGrid(False)
        self.g_layaways_table.setObjectName("g_layaways_table")
        self.g_layaways_table.setColumnCount(8)
        self.g_layaways_table.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_layaways_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_layaways_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_layaways_table.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table.setHorizontalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_layaways_table.setItem(0, 6, item)
        self.g_layaways_table.horizontalHeader().setDefaultSectionSize(150)
        self.g_layaways_table.horizontalHeader().setStretchLastSection(True)
        self.g_layaways_table.verticalHeader().setVisible(False)
        self.verticalLayout_4.addWidget(self.g_layaways_table)
        self.__g_layawaysbottombar_frame = QtWidgets.QFrame(self.g_layaways_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_layawaysbottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_layawaysbottombar_frame.setSizePolicy(sizePolicy)
        self.__g_layawaysbottombar_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_layawaysbottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_layawaysbottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_layawaysbottombar_frame.setStyleSheet("")
        self.__g_layawaysbottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_layawaysbottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_layawaysbottombar_frame.setObjectName("__g_layawaysbottombar_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.__g_layawaysbottombar_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_layawaystotalresults_label = QtWidgets.QLabel(self.__g_layawaysbottombar_frame)
        self.g_layawaystotalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_layawaystotalresults_label.setFont(font)
        self.g_layawaystotalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_layawaystotalresults_label.setObjectName("g_layawaystotalresults_label")
        self.horizontalLayout.addWidget(self.g_layawaystotalresults_label)
        spacerItem2 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem2)
        self.__g_layawayscontrolbuttons_frame = QtWidgets.QFrame(self.__g_layawaysbottombar_frame)
        self.__g_layawayscontrolbuttons_frame.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_layawayscontrolbuttons_frame.sizePolicy().hasHeightForWidth())
        self.__g_layawayscontrolbuttons_frame.setSizePolicy(sizePolicy)
        self.__g_layawayscontrolbuttons_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_layawayscontrolbuttons_frame.setMaximumSize(QtCore.QSize(495, 40))
        self.__g_layawayscontrolbuttons_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_layawayscontrolbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_layawayscontrolbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_layawayscontrolbuttons_frame.setObjectName("__g_layawayscontrolbuttons_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.__g_layawayscontrolbuttons_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setSpacing(5)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_layaways_cancel_button = QtWidgets.QPushButton(self.__g_layawayscontrolbuttons_frame)
        self.g_layaways_cancel_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_layaways_cancel_button.sizePolicy().hasHeightForWidth())
        self.g_layaways_cancel_button.setSizePolicy(sizePolicy)
        self.g_layaways_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_layaways_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_layaways_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_layaways_cancel_button.setStyleSheet("")
        self.g_layaways_cancel_button.setIcon(icon2)
        self.g_layaways_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_layaways_cancel_button.setProperty("screen_toolbutton_icon", icon3)
        self.g_layaways_cancel_button.setObjectName("g_layaways_cancel_button")
        self.horizontalLayout_2.addWidget(self.g_layaways_cancel_button)
        self.g_layaways_select_button = QtWidgets.QPushButton(self.__g_layawayscontrolbuttons_frame)
        self.g_layaways_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_layaways_select_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_layaways_select_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_layaways_select_button.setStyleSheet("")
        self.g_layaways_select_button.setIcon(icon4)
        self.g_layaways_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_layaways_select_button.setObjectName("g_layaways_select_button")
        self.horizontalLayout_2.addWidget(self.g_layaways_select_button)
        self.horizontalLayout.addWidget(self.__g_layawayscontrolbuttons_frame)
        self.verticalLayout_4.addWidget(self.__g_layawaysbottombar_frame)
        self.g_main_tabs.addTab(self.g_layaways_tab, "")
        self.g_returns_tab = QtWidgets.QWidget()
        self.g_returns_tab.setObjectName("g_returns_tab")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.g_returns_tab)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.g_returnsunifiedsearch_frame = QtWidgets.QFrame(self.g_returns_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_returnsunifiedsearch_frame.sizePolicy().hasHeightForWidth())
        self.g_returnsunifiedsearch_frame.setSizePolicy(sizePolicy)
        self.g_returnsunifiedsearch_frame.setMinimumSize(QtCore.QSize(561, 41))
        self.g_returnsunifiedsearch_frame.setMaximumSize(QtCore.QSize(166546, 59))
        self.g_returnsunifiedsearch_frame.setStyleSheet("")
        self.g_returnsunifiedsearch_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_returnsunifiedsearch_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_returnsunifiedsearch_frame.setObjectName("g_returnsunifiedsearch_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_returnsunifiedsearch_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(14)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.g_returnssearch_combobox = QtWidgets.QComboBox(self.g_returnsunifiedsearch_frame)
        self.g_returnssearch_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_returnssearch_combobox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_returnssearch_combobox.setStyleSheet("")
        self.g_returnssearch_combobox.setFrame(False)
        self.g_returnssearch_combobox.setObjectName("g_returnssearch_combobox")
        self.g_returnssearch_combobox.addItem("")
        self.g_returnssearch_combobox.addItem("")
        self.g_returnssearch_combobox.addItem("")
        self.g_returnssearch_combobox.addItem("")
        self.horizontalLayout_11.addWidget(self.g_returnssearch_combobox)
        self.g_returnssearchbox_frame = QtWidgets.QFrame(self.g_returnsunifiedsearch_frame)
        self.g_returnssearchbox_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_returnssearchbox_frame.setStyleSheet("")
        self.g_returnssearchbox_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_returnssearchbox_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_returnssearchbox_frame.setObjectName("g_returnssearchbox_frame")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.g_returnssearchbox_frame)
        self.horizontalLayout_12.setContentsMargins(2, 0, -1, 0)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.g_returnssearchtext_lineedit = QtWidgets.QLineEdit(self.g_returnssearchbox_frame)
        self.g_returnssearchtext_lineedit.setStyleSheet("")
        self.g_returnssearchtext_lineedit.setText("")
        self.g_returnssearchtext_lineedit.setObjectName("g_returnssearchtext_lineedit")
        self.horizontalLayout_12.addWidget(self.g_returnssearchtext_lineedit)
        self.g_returnsclearsearch_button = QtWidgets.QPushButton(self.g_returnssearchbox_frame)
        self.g_returnsclearsearch_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_returnsclearsearch_button.setStyleSheet("")
        self.g_returnsclearsearch_button.setText("")
        self.g_returnsclearsearch_button.setIcon(icon1)
        self.g_returnsclearsearch_button.setIconSize(QtCore.QSize(18, 18))
        self.g_returnsclearsearch_button.setObjectName("g_returnsclearsearch_button")
        self.horizontalLayout_12.addWidget(self.g_returnsclearsearch_button)
        self.horizontalLayout_11.addWidget(self.g_returnssearchbox_frame)
        self.g_nav_frame_4 = QtWidgets.QFrame(self.g_returnsunifiedsearch_frame)
        self.g_nav_frame_4.setMinimumSize(QtCore.QSize(150, 0))
        self.g_nav_frame_4.setMaximumSize(QtCore.QSize(150, 16777215))
        self.g_nav_frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_nav_frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_nav_frame_4.setObjectName("g_nav_frame_4")
        self.verticalLayout_9 = QtWidgets.QVBoxLayout(self.g_nav_frame_4)
        self.verticalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_9.setSpacing(0)
        self.verticalLayout_9.setObjectName("verticalLayout_9")
        self.g_pagenav_hlayout_6 = QtWidgets.QHBoxLayout()
        self.g_pagenav_hlayout_6.setSpacing(6)
        self.g_pagenav_hlayout_6.setObjectName("g_pagenav_hlayout_6")
        self.g_returnspreviouspage_label = QtWidgets.QLabel(self.g_nav_frame_4)
        self.g_returnspreviouspage_label.setEnabled(False)
        self.g_returnspreviouspage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_returnspreviouspage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_returnspreviouspage_label.setText("")
        self.g_returnspreviouspage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/previous"))
        self.g_returnspreviouspage_label.setScaledContents(False)
        self.g_returnspreviouspage_label.setObjectName("g_returnspreviouspage_label")
        self.g_pagenav_hlayout_6.addWidget(self.g_returnspreviouspage_label)
        self.g_returnsnextpage_label = QtWidgets.QLabel(self.g_nav_frame_4)
        self.g_returnsnextpage_label.setEnabled(True)
        self.g_returnsnextpage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_returnsnextpage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_returnsnextpage_label.setText("")
        self.g_returnsnextpage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/next"))
        self.g_returnsnextpage_label.setObjectName("g_returnsnextpage_label")
        self.g_pagenav_hlayout_6.addWidget(self.g_returnsnextpage_label)
        self.g_returnscurrentpage_lineedit = QtWidgets.QLineEdit(self.g_nav_frame_4)
        self.g_returnscurrentpage_lineedit.setEnabled(True)
        self.g_returnscurrentpage_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_returnscurrentpage_lineedit.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_returnscurrentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_returnscurrentpage_lineedit.setObjectName("g_returnscurrentpage_lineedit")
        self.g_pagenav_hlayout_6.addWidget(self.g_returnscurrentpage_lineedit)
        self.__g_holdsslash_label_2 = QtWidgets.QLabel(self.g_nav_frame_4)
        self.__g_holdsslash_label_2.setMinimumSize(QtCore.QSize(7, 0))
        self.__g_holdsslash_label_2.setMaximumSize(QtCore.QSize(7, 16777215))
        self.__g_holdsslash_label_2.setObjectName("__g_holdsslash_label_2")
        self.g_pagenav_hlayout_6.addWidget(self.__g_holdsslash_label_2)
        self.g_returnstotalpages_label = QtWidgets.QLabel(self.g_nav_frame_4)
        self.g_returnstotalpages_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_returnstotalpages_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_returnstotalpages_label.setObjectName("g_returnstotalpages_label")
        self.g_pagenav_hlayout_6.addWidget(self.g_returnstotalpages_label)
        self.verticalLayout_9.addLayout(self.g_pagenav_hlayout_6)
        self.horizontalLayout_11.addWidget(self.g_nav_frame_4)
        self.verticalLayout_5.addWidget(self.g_returnsunifiedsearch_frame)
        self.g_returns_table = QtWidgets.QTableWidget(self.g_returns_tab)
        self.g_returns_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_returns_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_returns_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_returns_table.setAlternatingRowColors(True)
        self.g_returns_table.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.g_returns_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_returns_table.setShowGrid(False)
        self.g_returns_table.setObjectName("g_returns_table")
        self.g_returns_table.setColumnCount(6)
        self.g_returns_table.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_returns_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_returns_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_returns_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_returns_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_returns_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_returns_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_returns_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_returns_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_returns_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_returns_table.setItem(0, 3, item)
        self.g_returns_table.horizontalHeader().setDefaultSectionSize(150)
        self.g_returns_table.horizontalHeader().setStretchLastSection(True)
        self.g_returns_table.verticalHeader().setVisible(False)
        self.verticalLayout_5.addWidget(self.g_returns_table)
        self.__g_holdsbottombar_frame_2 = QtWidgets.QFrame(self.g_returns_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_holdsbottombar_frame_2.sizePolicy().hasHeightForWidth())
        self.__g_holdsbottombar_frame_2.setSizePolicy(sizePolicy)
        self.__g_holdsbottombar_frame_2.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_holdsbottombar_frame_2.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_holdsbottombar_frame_2.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_holdsbottombar_frame_2.setStyleSheet("")
        self.__g_holdsbottombar_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_holdsbottombar_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_holdsbottombar_frame_2.setObjectName("__g_holdsbottombar_frame_2")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.__g_holdsbottombar_frame_2)
        self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_13.setSpacing(0)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.g_returnstotalresults_label = QtWidgets.QLabel(self.__g_holdsbottombar_frame_2)
        self.g_returnstotalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_returnstotalresults_label.setFont(font)
        self.g_returnstotalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_returnstotalresults_label.setObjectName("g_returnstotalresults_label")
        self.horizontalLayout_13.addWidget(self.g_returnstotalresults_label)
        spacerItem3 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_13.addItem(spacerItem3)
        self.__g_holdsbuttons_frame_2 = QtWidgets.QFrame(self.__g_holdsbottombar_frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_holdsbuttons_frame_2.sizePolicy().hasHeightForWidth())
        self.__g_holdsbuttons_frame_2.setSizePolicy(sizePolicy)
        self.__g_holdsbuttons_frame_2.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_holdsbuttons_frame_2.setMaximumSize(QtCore.QSize(495, 40))
        self.__g_holdsbuttons_frame_2.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_holdsbuttons_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_holdsbuttons_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_holdsbuttons_frame_2.setObjectName("__g_holdsbuttons_frame_2")
        self.horizontalLayout_18 = QtWidgets.QHBoxLayout(self.__g_holdsbuttons_frame_2)
        self.horizontalLayout_18.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_18.setSpacing(5)
        self.horizontalLayout_18.setObjectName("horizontalLayout_18")
        self.g_returns_cancel_button = QtWidgets.QPushButton(self.__g_holdsbuttons_frame_2)
        self.g_returns_cancel_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_returns_cancel_button.sizePolicy().hasHeightForWidth())
        self.g_returns_cancel_button.setSizePolicy(sizePolicy)
        self.g_returns_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_returns_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_returns_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_returns_cancel_button.setStyleSheet("")
        self.g_returns_cancel_button.setIcon(icon2)
        self.g_returns_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_returns_cancel_button.setProperty("screen_toolbutton_icon", icon3)
        self.g_returns_cancel_button.setObjectName("g_returns_cancel_button")
        self.horizontalLayout_18.addWidget(self.g_returns_cancel_button)
        self.g_returns_select_button = QtWidgets.QPushButton(self.__g_holdsbuttons_frame_2)
        self.g_returns_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_returns_select_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_returns_select_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_returns_select_button.setStyleSheet("")
        self.g_returns_select_button.setIcon(icon4)
        self.g_returns_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_returns_select_button.setObjectName("g_returns_select_button")
        self.horizontalLayout_18.addWidget(self.g_returns_select_button)
        self.horizontalLayout_13.addWidget(self.__g_holdsbuttons_frame_2)
        self.verticalLayout_5.addWidget(self.__g_holdsbottombar_frame_2)
        self.g_main_tabs.addTab(self.g_returns_tab, "")
        self.g_credova_tab = QtWidgets.QWidget()
        self.g_credova_tab.setObjectName("g_credova_tab")
        self.verticalLayout_11 = QtWidgets.QVBoxLayout(self.g_credova_tab)
        self.verticalLayout_11.setObjectName("verticalLayout_11")
        self.g_credovaunifiedsearch_frame = QtWidgets.QFrame(self.g_credova_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_credovaunifiedsearch_frame.sizePolicy().hasHeightForWidth())
        self.g_credovaunifiedsearch_frame.setSizePolicy(sizePolicy)
        self.g_credovaunifiedsearch_frame.setMinimumSize(QtCore.QSize(561, 41))
        self.g_credovaunifiedsearch_frame.setMaximumSize(QtCore.QSize(166546, 59))
        self.g_credovaunifiedsearch_frame.setStyleSheet("")
        self.g_credovaunifiedsearch_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_credovaunifiedsearch_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_credovaunifiedsearch_frame.setObjectName("g_credovaunifiedsearch_frame")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout(self.g_credovaunifiedsearch_frame)
        self.horizontalLayout_16.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_16.setSpacing(14)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.g_credovasearch_combobox = QtWidgets.QComboBox(self.g_credovaunifiedsearch_frame)
        self.g_credovasearch_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_credovasearch_combobox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_credovasearch_combobox.setStyleSheet("")
        self.g_credovasearch_combobox.setFrame(False)
        self.g_credovasearch_combobox.setObjectName("g_credovasearch_combobox")
        self.g_credovasearch_combobox.addItem("")
        self.g_credovasearch_combobox.addItem("")
        self.g_credovasearch_combobox.addItem("")
        self.g_credovasearch_combobox.addItem("")
        self.horizontalLayout_16.addWidget(self.g_credovasearch_combobox)
        self.g_credovasearchbox_frame = QtWidgets.QFrame(self.g_credovaunifiedsearch_frame)
        self.g_credovasearchbox_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_credovasearchbox_frame.setStyleSheet("")
        self.g_credovasearchbox_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_credovasearchbox_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_credovasearchbox_frame.setObjectName("g_credovasearchbox_frame")
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout(self.g_credovasearchbox_frame)
        self.horizontalLayout_17.setContentsMargins(2, 0, -1, 0)
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        self.g_credovasearchtext_lineedit = QtWidgets.QLineEdit(self.g_credovasearchbox_frame)
        self.g_credovasearchtext_lineedit.setStyleSheet("")
        self.g_credovasearchtext_lineedit.setText("")
        self.g_credovasearchtext_lineedit.setObjectName("g_credovasearchtext_lineedit")
        self.horizontalLayout_17.addWidget(self.g_credovasearchtext_lineedit)
        self.g_credovaclearsearch_button = QtWidgets.QPushButton(self.g_credovasearchbox_frame)
        self.g_credovaclearsearch_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_credovaclearsearch_button.setStyleSheet("")
        self.g_credovaclearsearch_button.setText("")
        self.g_credovaclearsearch_button.setIcon(icon1)
        self.g_credovaclearsearch_button.setIconSize(QtCore.QSize(18, 18))
        self.g_credovaclearsearch_button.setObjectName("g_credovaclearsearch_button")
        self.horizontalLayout_17.addWidget(self.g_credovaclearsearch_button)
        self.horizontalLayout_16.addWidget(self.g_credovasearchbox_frame)
        self.g_nav_frame_5 = QtWidgets.QFrame(self.g_credovaunifiedsearch_frame)
        self.g_nav_frame_5.setMinimumSize(QtCore.QSize(150, 0))
        self.g_nav_frame_5.setMaximumSize(QtCore.QSize(150, 16777215))
        self.g_nav_frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_nav_frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_nav_frame_5.setObjectName("g_nav_frame_5")
        self.verticalLayout_10 = QtWidgets.QVBoxLayout(self.g_nav_frame_5)
        self.verticalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_10.setSpacing(0)
        self.verticalLayout_10.setObjectName("verticalLayout_10")
        self.g_pagenav_hlayout_7 = QtWidgets.QHBoxLayout()
        self.g_pagenav_hlayout_7.setSpacing(6)
        self.g_pagenav_hlayout_7.setObjectName("g_pagenav_hlayout_7")
        self.g_credovapreviouspage_label = QtWidgets.QLabel(self.g_nav_frame_5)
        self.g_credovapreviouspage_label.setEnabled(False)
        self.g_credovapreviouspage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_credovapreviouspage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_credovapreviouspage_label.setText("")
        self.g_credovapreviouspage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/previous"))
        self.g_credovapreviouspage_label.setScaledContents(False)
        self.g_credovapreviouspage_label.setObjectName("g_credovapreviouspage_label")
        self.g_pagenav_hlayout_7.addWidget(self.g_credovapreviouspage_label)
        self.g_credovanextpage_label = QtWidgets.QLabel(self.g_nav_frame_5)
        self.g_credovanextpage_label.setEnabled(True)
        self.g_credovanextpage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_credovanextpage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_credovanextpage_label.setText("")
        self.g_credovanextpage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/next"))
        self.g_credovanextpage_label.setObjectName("g_credovanextpage_label")
        self.g_pagenav_hlayout_7.addWidget(self.g_credovanextpage_label)
        self.g_credovacurrentpage_lineedit = QtWidgets.QLineEdit(self.g_nav_frame_5)
        self.g_credovacurrentpage_lineedit.setEnabled(True)
        self.g_credovacurrentpage_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_credovacurrentpage_lineedit.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_credovacurrentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_credovacurrentpage_lineedit.setObjectName("g_credovacurrentpage_lineedit")
        self.g_pagenav_hlayout_7.addWidget(self.g_credovacurrentpage_lineedit)
        self.__g_credovaslash_label = QtWidgets.QLabel(self.g_nav_frame_5)
        self.__g_credovaslash_label.setMinimumSize(QtCore.QSize(7, 0))
        self.__g_credovaslash_label.setMaximumSize(QtCore.QSize(7, 16777215))
        self.__g_credovaslash_label.setObjectName("__g_credovaslash_label")
        self.g_pagenav_hlayout_7.addWidget(self.__g_credovaslash_label)
        self.g_credovatotalpages_label = QtWidgets.QLabel(self.g_nav_frame_5)
        self.g_credovatotalpages_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_credovatotalpages_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_credovatotalpages_label.setObjectName("g_credovatotalpages_label")
        self.g_pagenav_hlayout_7.addWidget(self.g_credovatotalpages_label)
        self.verticalLayout_10.addLayout(self.g_pagenav_hlayout_7)
        self.horizontalLayout_16.addWidget(self.g_nav_frame_5)
        self.verticalLayout_11.addWidget(self.g_credovaunifiedsearch_frame)
        self.g_credova_table = QtWidgets.QTableWidget(self.g_credova_tab)
        self.g_credova_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_credova_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_credova_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_credova_table.setAlternatingRowColors(True)
        self.g_credova_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_credova_table.setShowGrid(False)
        self.g_credova_table.setObjectName("g_credova_table")
        self.g_credova_table.setColumnCount(7)
        self.g_credova_table.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_credova_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_credova_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_credova_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_credova_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_credova_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_credova_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_credova_table.setHorizontalHeaderItem(6, item)
        self.g_credova_table.horizontalHeader().setDefaultSectionSize(150)
        self.g_credova_table.horizontalHeader().setStretchLastSection(True)
        self.g_credova_table.verticalHeader().setVisible(False)
        self.verticalLayout_11.addWidget(self.g_credova_table)
        self.__g_credovabottombar_frame = QtWidgets.QFrame(self.g_credova_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_credovabottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_credovabottombar_frame.setSizePolicy(sizePolicy)
        self.__g_credovabottombar_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_credovabottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_credovabottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_credovabottombar_frame.setStyleSheet("")
        self.__g_credovabottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_credovabottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_credovabottombar_frame.setObjectName("__g_credovabottombar_frame")
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout(self.__g_credovabottombar_frame)
        self.horizontalLayout_14.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_14.setSpacing(0)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.g_credovatotalresults_label = QtWidgets.QLabel(self.__g_credovabottombar_frame)
        self.g_credovatotalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_credovatotalresults_label.setFont(font)
        self.g_credovatotalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_credovatotalresults_label.setObjectName("g_credovatotalresults_label")
        self.horizontalLayout_14.addWidget(self.g_credovatotalresults_label)
        spacerItem4 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem4)
        self.__g_credovabuttons_frame = QtWidgets.QFrame(self.__g_credovabottombar_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_credovabuttons_frame.sizePolicy().hasHeightForWidth())
        self.__g_credovabuttons_frame.setSizePolicy(sizePolicy)
        self.__g_credovabuttons_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_credovabuttons_frame.setMaximumSize(QtCore.QSize(495, 40))
        self.__g_credovabuttons_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_credovabuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_credovabuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_credovabuttons_frame.setObjectName("__g_credovabuttons_frame")
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout(self.__g_credovabuttons_frame)
        self.horizontalLayout_15.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_15.setSpacing(5)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.g_credova_cancel_button = QtWidgets.QPushButton(self.__g_credovabuttons_frame)
        self.g_credova_cancel_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_credova_cancel_button.sizePolicy().hasHeightForWidth())
        self.g_credova_cancel_button.setSizePolicy(sizePolicy)
        self.g_credova_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_credova_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_credova_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_credova_cancel_button.setStyleSheet("")
        self.g_credova_cancel_button.setIcon(icon2)
        self.g_credova_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_credova_cancel_button.setProperty("screen_toolbutton_icon", icon3)
        self.g_credova_cancel_button.setObjectName("g_credova_cancel_button")
        self.horizontalLayout_15.addWidget(self.g_credova_cancel_button)
        self.g_credova_select_button = QtWidgets.QPushButton(self.__g_credovabuttons_frame)
        self.g_credova_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_credova_select_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_credova_select_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_credova_select_button.setStyleSheet("")
        self.g_credova_select_button.setIcon(icon4)
        self.g_credova_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_credova_select_button.setObjectName("g_credova_select_button")
        self.horizontalLayout_15.addWidget(self.g_credova_select_button)
        self.horizontalLayout_14.addWidget(self.__g_credovabuttons_frame)
        self.verticalLayout_11.addWidget(self.__g_credovabottombar_frame)
        self.__g_credovabottombar_frame.raise_()
        self.g_credovaunifiedsearch_frame.raise_()
        self.g_credova_table.raise_()
        self.g_main_tabs.addTab(self.g_credova_tab, "")
        self.verticalLayout.addWidget(self.g_main_tabs)

        self.retranslateUi(OpenInvoiceScreen)
        self.g_main_tabs.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(OpenInvoiceScreen)

    def retranslateUi(self, OpenInvoiceScreen):
        _translate = QtCore.QCoreApplication.translate
        OpenInvoiceScreen.setWindowTitle(_translate("OpenInvoiceScreen", "Open Invoice"))
        OpenInvoiceScreen.setProperty("screen_toolbutton_tooltip_text", _translate("OpenInvoiceScreen", "View or manage payments"))
        OpenInvoiceScreen.setProperty("screen_toolbutton_title_text", _translate("OpenInvoiceScreen", "Payments"))
        OpenInvoiceScreen.setProperty("screen_toolbutton_stylesheet_text", _translate("OpenInvoiceScreen", "\n"
"QToolButton {\n"
"    background: #925A24;\n"
"    border: 2px solid #523314;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #523314;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: grey;\n"
"    border: 2px solid grey;\n"
"}"))
        OpenInvoiceScreen.setProperty("screen_indicator_stylesheet_text", _translate("OpenInvoiceScreen", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"        the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #523314;\n"
"    border: 0;\n"
"}"))
        self.__g_main_label.setText(_translate("OpenInvoiceScreen", "Open Invoice"))
        self.g_holdssearch_combobox.setItemText(0, _translate("OpenInvoiceScreen", "Smart Search"))
        self.g_holdssearch_combobox.setItemText(1, _translate("OpenInvoiceScreen", "Search Location"))
        self.g_holdssearch_combobox.setItemText(2, _translate("OpenInvoiceScreen", "Search Phone"))
        self.g_holdssearch_combobox.setItemText(3, _translate("OpenInvoiceScreen", "Search Email"))
        self.g_holdssearchtext_lineedit.setPlaceholderText(_translate("OpenInvoiceScreen", "Enter your search terms then press Enter..."))
        self.g_onlymyinvoice_checkbox.setText(_translate("OpenInvoiceScreen", "Only My Invoices"))
        self.g_holdspreviouspage_label.setToolTip(_translate("OpenInvoiceScreen", "Previous Page"))
        self.g_holdsnextpage_label.setToolTip(_translate("OpenInvoiceScreen", "Next Page"))
        self.g_holdscurrentpage_lineedit.setText(_translate("OpenInvoiceScreen", "999"))
        self.__g_holdsslash_label.setText(_translate("OpenInvoiceScreen", "/"))
        self.g_holdstotalpages_label.setToolTip(_translate("OpenInvoiceScreen", "Total Pages"))
        self.g_holdstotalpages_label.setText(_translate("OpenInvoiceScreen", "999"))
        self.g_holds_table.setSortingEnabled(True)
        item = self.g_holds_table.verticalHeaderItem(0)
        item.setText(_translate("OpenInvoiceScreen", "New Row"))
        item = self.g_holds_table.horizontalHeaderItem(0)
        item.setText(_translate("OpenInvoiceScreen", "Invoice #"))
        item = self.g_holds_table.horizontalHeaderItem(1)
        item.setText(_translate("OpenInvoiceScreen", "Name"))
        item = self.g_holds_table.horizontalHeaderItem(2)
        item.setText(_translate("OpenInvoiceScreen", "Open Date/Time"))
        item = self.g_holds_table.horizontalHeaderItem(3)
        item.setText(_translate("OpenInvoiceScreen", "Subtotal"))
        item = self.g_holds_table.horizontalHeaderItem(4)
        item.setText(_translate("OpenInvoiceScreen", "Total"))
        item = self.g_holds_table.horizontalHeaderItem(5)
        item.setText(_translate("OpenInvoiceScreen", "User"))
        item = self.g_holds_table.horizontalHeaderItem(6)
        item.setText(_translate("OpenInvoiceScreen", "Reason"))
        __sortingEnabled = self.g_holds_table.isSortingEnabled()
        self.g_holds_table.setSortingEnabled(False)
        item = self.g_holds_table.item(0, 0)
        item.setText(_translate("OpenInvoiceScreen", "100210"))
        item = self.g_holds_table.item(0, 2)
        item.setText(_translate("OpenInvoiceScreen", "09/12/2016 11:52:00"))
        item = self.g_holds_table.item(0, 3)
        item.setText(_translate("OpenInvoiceScreen", "5000.00"))
        self.g_holds_table.setSortingEnabled(__sortingEnabled)
        self.g_holdstotalresults_label.setText(_translate("OpenInvoiceScreen", "Loading results."))
        self.g_holds_cancel_button.setText(_translate("OpenInvoiceScreen", "Cancel"))
        self.g_holds_cancel_button.setProperty("screen_toolbutton_tooltip_text", _translate("OpenInvoiceScreen", "View or manage customers"))
        self.g_holds_cancel_button.setProperty("screen_toolbutton_title_text", _translate("OpenInvoiceScreen", "Customers"))
        self.g_holds_cancel_button.setProperty("screen_toolbutton_stylesheet_text", _translate("OpenInvoiceScreen", "\n"
"QToolButton {\n"
"    background: #bb1d48;\n"
"    border: 2px solid #97173A;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #97173A;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}"))
        self.g_holds_cancel_button.setProperty("screen_indicator_stylesheet_text", _translate("OpenInvoiceScreen", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"        the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #97173A;\n"
"    border: 0;\n"
"}"))
        self.g_holds_select_button.setText(_translate("OpenInvoiceScreen", "Select"))
        self.g_main_tabs.setTabText(self.g_main_tabs.indexOf(self.g_holds_tab), _translate("OpenInvoiceScreen", "Sales"))
        self.g_quotessearch_combobox.setItemText(0, _translate("OpenInvoiceScreen", "Smart Search"))
        self.g_quotessearch_combobox.setItemText(1, _translate("OpenInvoiceScreen", "Search Location"))
        self.g_quotessearch_combobox.setItemText(2, _translate("OpenInvoiceScreen", "Search Phone"))
        self.g_quotessearch_combobox.setItemText(3, _translate("OpenInvoiceScreen", "Search Email"))
        self.g_quotessearchtext_lineedit.setPlaceholderText(_translate("OpenInvoiceScreen", "Enter your search terms then press Enter..."))
        self.g_quoteonlymyinvoice_checkbox.setText(_translate("OpenInvoiceScreen", "Only My Invoices"))
        self.g_quotespreviouspage_label.setToolTip(_translate("OpenInvoiceScreen", "Previous Page"))
        self.g_quotesnextpage_label.setToolTip(_translate("OpenInvoiceScreen", "Next Page"))
        self.g_quotescurrentpage_lineedit.setText(_translate("OpenInvoiceScreen", "999"))
        self.__g_holdsslash_label_3.setText(_translate("OpenInvoiceScreen", "/"))
        self.g_quotestotalpages_label.setToolTip(_translate("OpenInvoiceScreen", "Total Pages"))
        self.g_quotestotalpages_label.setText(_translate("OpenInvoiceScreen", "999"))
        self.g_quotes_table.setSortingEnabled(True)
        item = self.g_quotes_table.verticalHeaderItem(0)
        item.setText(_translate("OpenInvoiceScreen", "New Row"))
        item = self.g_quotes_table.horizontalHeaderItem(0)
        item.setText(_translate("OpenInvoiceScreen", "Invoice #"))
        item = self.g_quotes_table.horizontalHeaderItem(1)
        item.setText(_translate("OpenInvoiceScreen", "Name"))
        item = self.g_quotes_table.horizontalHeaderItem(2)
        item.setText(_translate("OpenInvoiceScreen", "Open Date/Time"))
        item = self.g_quotes_table.horizontalHeaderItem(3)
        item.setText(_translate("OpenInvoiceScreen", "Subtotal"))
        item = self.g_quotes_table.horizontalHeaderItem(4)
        item.setText(_translate("OpenInvoiceScreen", "Total"))
        item = self.g_quotes_table.horizontalHeaderItem(5)
        item.setText(_translate("OpenInvoiceScreen", "User"))
        item = self.g_quotes_table.horizontalHeaderItem(6)
        item.setText(_translate("OpenInvoiceScreen", "Reason"))
        __sortingEnabled = self.g_quotes_table.isSortingEnabled()
        self.g_quotes_table.setSortingEnabled(False)
        self.g_quotes_table.setSortingEnabled(__sortingEnabled)
        self.g_quotestotalresults_label.setText(_translate("OpenInvoiceScreen", "Loading results."))
        self.g_quotes_cancel_button.setText(_translate("OpenInvoiceScreen", "Cancel"))
        self.g_quotes_cancel_button.setProperty("screen_toolbutton_tooltip_text", _translate("OpenInvoiceScreen", "View or manage customers"))
        self.g_quotes_cancel_button.setProperty("screen_toolbutton_title_text", _translate("OpenInvoiceScreen", "Customers"))
        self.g_quotes_cancel_button.setProperty("screen_toolbutton_stylesheet_text", _translate("OpenInvoiceScreen", "\n"
"QToolButton {\n"
"  background: #bb1d48;\n"
"  border: 2px solid #97173A;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"  background: #97173A;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"  background: rgb(164, 164, 164);\n"
"  border: 2px solid rgb(164, 164, 164);\n"
"}"))
        self.g_quotes_cancel_button.setProperty("screen_indicator_stylesheet_text", _translate("OpenInvoiceScreen", "\n"
"Line, QFrame {\n"
"  /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"      the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"  background-color: #97173A;\n"
"  border: 0;\n"
"}"))
        self.g_quotes_select_button.setText(_translate("OpenInvoiceScreen", "Select"))
        self.g_main_tabs.setTabText(self.g_main_tabs.indexOf(self.g_quotes_tab), _translate("OpenInvoiceScreen", "Quotes"))
        self.g_layawayssearch_combobox.setItemText(0, _translate("OpenInvoiceScreen", "Smart Search"))
        self.g_layawayssearch_combobox.setItemText(1, _translate("OpenInvoiceScreen", "Search Location"))
        self.g_layawayssearch_combobox.setItemText(2, _translate("OpenInvoiceScreen", "Search Phone"))
        self.g_layawayssearch_combobox.setItemText(3, _translate("OpenInvoiceScreen", "Search Email"))
        self.g_layawayssearchtext_lineedit.setPlaceholderText(_translate("OpenInvoiceScreen", "Enter your search terms then press Enter..."))
        self.g_layawayspreviouspage_label.setToolTip(_translate("OpenInvoiceScreen", "Previous Page"))
        self.g_layawaysnextpage_label.setToolTip(_translate("OpenInvoiceScreen", "Next Page"))
        self.g_layawayscurrentpage_lineedit.setText(_translate("OpenInvoiceScreen", "999"))
        self.__g_layawaysslash_label.setText(_translate("OpenInvoiceScreen", "/"))
        self.g_layawaystotalpages_label.setToolTip(_translate("OpenInvoiceScreen", "Total Pages"))
        self.g_layawaystotalpages_label.setText(_translate("OpenInvoiceScreen", "999"))
        self.g_layaways_table.setSortingEnabled(True)
        item = self.g_layaways_table.verticalHeaderItem(0)
        item.setText(_translate("OpenInvoiceScreen", "New Row"))
        item = self.g_layaways_table.horizontalHeaderItem(0)
        item.setText(_translate("OpenInvoiceScreen", "Layaway #"))
        item = self.g_layaways_table.horizontalHeaderItem(1)
        item.setText(_translate("OpenInvoiceScreen", "Name"))
        item = self.g_layaways_table.horizontalHeaderItem(2)
        item.setText(_translate("OpenInvoiceScreen", "Open Date/Time"))
        item = self.g_layaways_table.horizontalHeaderItem(3)
        item.setText(_translate("OpenInvoiceScreen", "Due Date"))
        item = self.g_layaways_table.horizontalHeaderItem(4)
        item.setText(_translate("OpenInvoiceScreen", "Subtotal"))
        item = self.g_layaways_table.horizontalHeaderItem(5)
        item.setText(_translate("OpenInvoiceScreen", "Total"))
        item = self.g_layaways_table.horizontalHeaderItem(6)
        item.setText(_translate("OpenInvoiceScreen", "Balance"))
        item = self.g_layaways_table.horizontalHeaderItem(7)
        item.setText(_translate("OpenInvoiceScreen", "Reason"))
        __sortingEnabled = self.g_layaways_table.isSortingEnabled()
        self.g_layaways_table.setSortingEnabled(False)
        self.g_layaways_table.setSortingEnabled(__sortingEnabled)
        self.g_layawaystotalresults_label.setText(_translate("OpenInvoiceScreen", "Loading results."))
        self.g_layaways_cancel_button.setText(_translate("OpenInvoiceScreen", "  Cancel"))
        self.g_layaways_cancel_button.setProperty("screen_toolbutton_tooltip_text", _translate("OpenInvoiceScreen", "View or manage customers"))
        self.g_layaways_cancel_button.setProperty("screen_toolbutton_title_text", _translate("OpenInvoiceScreen", "Customers"))
        self.g_layaways_cancel_button.setProperty("screen_toolbutton_stylesheet_text", _translate("OpenInvoiceScreen", "\n"
"QToolButton {\n"
"    background: #bb1d48;\n"
"    border: 2px solid #97173A;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #97173A;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}"))
        self.g_layaways_cancel_button.setProperty("screen_indicator_stylesheet_text", _translate("OpenInvoiceScreen", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"        the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #97173A;\n"
"    border: 0;\n"
"}"))
        self.g_layaways_select_button.setText(_translate("OpenInvoiceScreen", "Select"))
        self.g_main_tabs.setTabText(self.g_main_tabs.indexOf(self.g_layaways_tab), _translate("OpenInvoiceScreen", "Layaways"))
        self.g_returnssearch_combobox.setItemText(0, _translate("OpenInvoiceScreen", "Smart Search"))
        self.g_returnssearch_combobox.setItemText(1, _translate("OpenInvoiceScreen", "Search Location"))
        self.g_returnssearch_combobox.setItemText(2, _translate("OpenInvoiceScreen", "Search Phone"))
        self.g_returnssearch_combobox.setItemText(3, _translate("OpenInvoiceScreen", "Search Email"))
        self.g_returnssearchtext_lineedit.setPlaceholderText(_translate("OpenInvoiceScreen", "Enter your search terms then press Enter..."))
        self.g_returnspreviouspage_label.setToolTip(_translate("OpenInvoiceScreen", "Previous Page"))
        self.g_returnsnextpage_label.setToolTip(_translate("OpenInvoiceScreen", "Next Page"))
        self.g_returnscurrentpage_lineedit.setText(_translate("OpenInvoiceScreen", "999"))
        self.__g_holdsslash_label_2.setText(_translate("OpenInvoiceScreen", "/"))
        self.g_returnstotalpages_label.setToolTip(_translate("OpenInvoiceScreen", "Total Pages"))
        self.g_returnstotalpages_label.setText(_translate("OpenInvoiceScreen", "999"))
        self.g_returns_table.setSortingEnabled(True)
        item = self.g_returns_table.verticalHeaderItem(0)
        item.setText(_translate("OpenInvoiceScreen", "New Row"))
        item = self.g_returns_table.horizontalHeaderItem(0)
        item.setText(_translate("OpenInvoiceScreen", "Invoice #"))
        item = self.g_returns_table.horizontalHeaderItem(1)
        item.setText(_translate("OpenInvoiceScreen", "Name"))
        item = self.g_returns_table.horizontalHeaderItem(2)
        item.setText(_translate("OpenInvoiceScreen", "Open Date/Time"))
        item = self.g_returns_table.horizontalHeaderItem(3)
        item.setText(_translate("OpenInvoiceScreen", "Subtotal"))
        item = self.g_returns_table.horizontalHeaderItem(4)
        item.setText(_translate("OpenInvoiceScreen", "Total"))
        item = self.g_returns_table.horizontalHeaderItem(5)
        item.setText(_translate("OpenInvoiceScreen", "Reason"))
        __sortingEnabled = self.g_returns_table.isSortingEnabled()
        self.g_returns_table.setSortingEnabled(False)
        item = self.g_returns_table.item(0, 0)
        item.setText(_translate("OpenInvoiceScreen", "100210"))
        item = self.g_returns_table.item(0, 2)
        item.setText(_translate("OpenInvoiceScreen", "09/12/2016 11:52:00"))
        item = self.g_returns_table.item(0, 3)
        item.setText(_translate("OpenInvoiceScreen", "5000.00"))
        self.g_returns_table.setSortingEnabled(__sortingEnabled)
        self.g_returnstotalresults_label.setText(_translate("OpenInvoiceScreen", "Loading results."))
        self.g_returns_cancel_button.setText(_translate("OpenInvoiceScreen", "Cancel"))
        self.g_returns_cancel_button.setProperty("screen_toolbutton_tooltip_text", _translate("OpenInvoiceScreen", "View or manage customers"))
        self.g_returns_cancel_button.setProperty("screen_toolbutton_title_text", _translate("OpenInvoiceScreen", "Customers"))
        self.g_returns_cancel_button.setProperty("screen_toolbutton_stylesheet_text", _translate("OpenInvoiceScreen", "\n"
"QToolButton {\n"
"    background: #bb1d48;\n"
"    border: 2px solid #97173A;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #97173A;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}"))
        self.g_returns_cancel_button.setProperty("screen_indicator_stylesheet_text", _translate("OpenInvoiceScreen", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"        the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #97173A;\n"
"    border: 0;\n"
"}"))
        self.g_returns_select_button.setText(_translate("OpenInvoiceScreen", "Select"))
        self.g_main_tabs.setTabText(self.g_main_tabs.indexOf(self.g_returns_tab), _translate("OpenInvoiceScreen", "Returns"))
        self.g_credovasearch_combobox.setItemText(0, _translate("OpenInvoiceScreen", "Smart Search"))
        self.g_credovasearch_combobox.setItemText(1, _translate("OpenInvoiceScreen", "Search Location"))
        self.g_credovasearch_combobox.setItemText(2, _translate("OpenInvoiceScreen", "Search Phone"))
        self.g_credovasearch_combobox.setItemText(3, _translate("OpenInvoiceScreen", "Search Email"))
        self.g_credovasearchtext_lineedit.setPlaceholderText(_translate("OpenInvoiceScreen", "Enter your search terms then press Enter..."))
        self.g_credovapreviouspage_label.setToolTip(_translate("OpenInvoiceScreen", "Previous Page"))
        self.g_credovanextpage_label.setToolTip(_translate("OpenInvoiceScreen", "Next Page"))
        self.g_credovacurrentpage_lineedit.setText(_translate("OpenInvoiceScreen", "999"))
        self.__g_credovaslash_label.setText(_translate("OpenInvoiceScreen", "/"))
        self.g_credovatotalpages_label.setToolTip(_translate("OpenInvoiceScreen", "Total Pages"))
        self.g_credovatotalpages_label.setText(_translate("OpenInvoiceScreen", "999"))
        self.g_credova_table.setSortingEnabled(True)
        item = self.g_credova_table.horizontalHeaderItem(0)
        item.setText(_translate("OpenInvoiceScreen", "Invoice #"))
        item = self.g_credova_table.horizontalHeaderItem(1)
        item.setText(_translate("OpenInvoiceScreen", "Name"))
        item = self.g_credova_table.horizontalHeaderItem(2)
        item.setText(_translate("OpenInvoiceScreen", "Open Date/Time"))
        item = self.g_credova_table.horizontalHeaderItem(3)
        item.setText(_translate("OpenInvoiceScreen", "Subtotal"))
        item = self.g_credova_table.horizontalHeaderItem(4)
        item.setText(_translate("OpenInvoiceScreen", "Total"))
        item = self.g_credova_table.horizontalHeaderItem(5)
        item.setText(_translate("OpenInvoiceScreen", "State"))
        item = self.g_credova_table.horizontalHeaderItem(6)
        item.setText(_translate("OpenInvoiceScreen", "Application Url"))
        self.g_credovatotalresults_label.setText(_translate("OpenInvoiceScreen", "Loading results."))
        self.g_credova_cancel_button.setText(_translate("OpenInvoiceScreen", "Cancel"))
        self.g_credova_cancel_button.setProperty("screen_toolbutton_tooltip_text", _translate("OpenInvoiceScreen", "View or manage customers"))
        self.g_credova_cancel_button.setProperty("screen_toolbutton_title_text", _translate("OpenInvoiceScreen", "Customers"))
        self.g_credova_cancel_button.setProperty("screen_toolbutton_stylesheet_text", _translate("OpenInvoiceScreen", "\n"
"QToolButton {\n"
"    background: #bb1d48;\n"
"    border: 2px solid #97173A;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #97173A;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}"))
        self.g_credova_cancel_button.setProperty("screen_indicator_stylesheet_text", _translate("OpenInvoiceScreen", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"        the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #97173A;\n"
"    border: 0;\n"
"}"))
        self.g_credova_select_button.setText(_translate("OpenInvoiceScreen", "Select"))
        self.g_main_tabs.setTabText(self.g_main_tabs.indexOf(self.g_credova_tab), _translate("OpenInvoiceScreen", "Credova"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    OpenInvoiceScreen = QtWidgets.QDialog()
    ui = Ui_OpenInvoiceScreen()
    ui.setupUi(OpenInvoiceScreen)
    OpenInvoiceScreen.show()
    sys.exit(app.exec_())
