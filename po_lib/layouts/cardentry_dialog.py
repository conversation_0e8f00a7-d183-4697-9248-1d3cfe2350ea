# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'cardentry_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_CardEntryDialog(object):
    def setupUi(self, CardEntryDialog):
        CardEntryDialog.setObjectName("CardEntryDialog")
        CardEntryDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        CardEntryDialog.resize(800, 441)
        CardEntryDialog.setMinimumSize(QtCore.QSize(800, 441))
        CardEntryDialog.setMaximumSize(QtCore.QSize(800, 441))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/cardentry_modaldialog/window_icon"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        CardEntryDialog.setWindowIcon(icon)
        CardEntryDialog.setStyleSheet("#CardEntryModalDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 11pt;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"\n"
"#g_dialog_frame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
" QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QTabWidget {\n"
"    border:0px;\n"
"}\n"
"\n"
"#g_voiceauthorization_widget,\n"
"#g_manualentry_widget,\n"
"#g_swipecard_widget {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius:0px;\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"#g_description_label {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color: #dddddd;\n"
"    color: #333333;\n"
"}\n"
"\n"
"#g_authorize_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_authorize_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"/* Start Keypad Styling */\n"
"\n"
"#g_container_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border:1px solid #073c83;\n"
"    border-radius:10px;\n"
"    background-color:#DDE4E9;\n"
"}\n"
"\n"
"\n"
"#g_keypaditembuttons_frame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"#g_keypaditembuttons_frame .QPushButton {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"    border-radius: 5px;\n"
"    margin:1px;\n"
"    font-weight:bold;\n"
"    font-size:11pt;\n"
"}\n"
"\n"
"#g_keypaditembuttons_frame .QPushButton:hover {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #2980a9, stop: 1 #2980a9);\n"
"    border:2px solid #215f7c;\n"
"}\n"
"\n"
"#g_keypaditembuttons_frame .QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:1px solid #287599;\n"
"}\n"
"\n"
"/* End Keypad Styling */\n"
"")
        CardEntryDialog.setModal(True)
        CardEntryDialog.setProperty("ets_fields_list", ['.ETSNameOnCard', '.ETSCardNumber', '.ETSExpirationMonth', '.ETSExpirationYear', '.ETSBillingStreet', '.ETSBillingZipCode', '.ETSCVV'])
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(CardEntryDialog)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_titlechargeamount_frame = QtWidgets.QFrame(CardEntryDialog)
        self.g_titlechargeamount_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlechargeamount_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlechargeamount_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlechargeamount_frame.setObjectName("g_titlechargeamount_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_titlechargeamount_frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlechargeamount_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        font = QtGui.QFont()
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_4.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem)
        self.horizontalLayout_4.addWidget(self.g_title_frame)
        spacerItem1 = QtWidgets.QSpacerItem(116, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem1)
        self.g_chargeamount_label = QtWidgets.QLabel(self.g_titlechargeamount_frame)
        self.g_chargeamount_label.setMinimumSize(QtCore.QSize(100, 0))
        self.g_chargeamount_label.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_chargeamount_label.setFont(font)
        self.g_chargeamount_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_chargeamount_label.setObjectName("g_chargeamount_label")
        self.horizontalLayout_4.addWidget(self.g_chargeamount_label)
        self.g_chargeamount_lineedit = QtWidgets.QLineEdit(self.g_titlechargeamount_frame)
        self.g_chargeamount_lineedit.setMaximumSize(QtCore.QSize(100, 16777215))
        self.g_chargeamount_lineedit.setStyleSheet("background-color: #dddddd;\n"
"color: #333333;")
        self.g_chargeamount_lineedit.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_chargeamount_lineedit.setReadOnly(True)
        self.g_chargeamount_lineedit.setObjectName("g_chargeamount_lineedit")
        self.horizontalLayout_4.addWidget(self.g_chargeamount_lineedit)
        self.verticalLayout_3.addWidget(self.g_titlechargeamount_frame)
        self.g_cardentry_tabs = QtWidgets.QTabWidget(CardEntryDialog)
        self.g_cardentry_tabs.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_cardentry_tabs.sizePolicy().hasHeightForWidth())
        self.g_cardentry_tabs.setSizePolicy(sizePolicy)
        self.g_cardentry_tabs.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        font.setKerning(True)
        self.g_cardentry_tabs.setFont(font)
        self.g_cardentry_tabs.setAutoFillBackground(False)
        self.g_cardentry_tabs.setStyleSheet("")
        self.g_cardentry_tabs.setTabPosition(QtWidgets.QTabWidget.North)
        self.g_cardentry_tabs.setTabShape(QtWidgets.QTabWidget.Rounded)
        self.g_cardentry_tabs.setIconSize(QtCore.QSize(50, 50))
        self.g_cardentry_tabs.setElideMode(QtCore.Qt.ElideNone)
        self.g_cardentry_tabs.setUsesScrollButtons(True)
        self.g_cardentry_tabs.setDocumentMode(True)
        self.g_cardentry_tabs.setTabsClosable(False)
        self.g_cardentry_tabs.setMovable(False)
        self.g_cardentry_tabs.setObjectName("g_cardentry_tabs")
        self.g_swipecard_widget = QtWidgets.QWidget()
        self.g_swipecard_widget.setStyleSheet("")
        self.g_swipecard_widget.setObjectName("g_swipecard_widget")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_swipecard_widget)
        self.horizontalLayout.setContentsMargins(9, 9, -1, -1)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem2)
        self.g_instruction_label = QtWidgets.QLabel(self.g_swipecard_widget)
        self.g_instruction_label.setMinimumSize(QtCore.QSize(0, 0))
        self.g_instruction_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.g_instruction_label.setFont(font)
        self.g_instruction_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_instruction_label.setObjectName("g_instruction_label")
        self.horizontalLayout.addWidget(self.g_instruction_label)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem3)
        self.g_cardentry_tabs.addTab(self.g_swipecard_widget, "")
        self.g_manualentry_widget = QtWidgets.QWidget()
        self.g_manualentry_widget.setObjectName("g_manualentry_widget")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_manualentry_widget)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_cardinfo_frame = QtWidgets.QFrame(self.g_manualentry_widget)
        self.g_cardinfo_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_cardinfo_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cardinfo_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cardinfo_frame.setObjectName("g_cardinfo_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_cardinfo_frame)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_cardnumber_frame = QtWidgets.QFrame(self.g_cardinfo_frame)
        self.g_cardnumber_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cardnumber_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cardnumber_frame.setObjectName("g_cardnumber_frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_cardnumber_frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_cardnumber_label = QtWidgets.QLabel(self.g_cardnumber_frame)
        self.g_cardnumber_label.setMinimumSize(QtCore.QSize(100, 0))
        self.g_cardnumber_label.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_cardnumber_label.setFont(font)
        self.g_cardnumber_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cardnumber_label.setObjectName("g_cardnumber_label")
        self.horizontalLayout_7.addWidget(self.g_cardnumber_label)
        self.g_cardnumber_lineedit = QtWidgets.QLineEdit(self.g_cardnumber_frame)
        self.g_cardnumber_lineedit.setMinimumSize(QtCore.QSize(0, 0))
        self.g_cardnumber_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_cardnumber_lineedit.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cardnumber_lineedit.setObjectName("g_cardnumber_lineedit")
        self.horizontalLayout_7.addWidget(self.g_cardnumber_lineedit)
        self.verticalLayout.addWidget(self.g_cardnumber_frame)
        self.g_expirationcode_frame = QtWidgets.QFrame(self.g_cardinfo_frame)
        self.g_expirationcode_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_expirationcode_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_expirationcode_frame.setObjectName("g_expirationcode_frame")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.g_expirationcode_frame)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.g_expirationdate_frame = QtWidgets.QFrame(self.g_expirationcode_frame)
        self.g_expirationdate_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_expirationdate_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_expirationdate_frame.setObjectName("g_expirationdate_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_expirationdate_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_expirationdate_label = QtWidgets.QLabel(self.g_expirationdate_frame)
        self.g_expirationdate_label.setMinimumSize(QtCore.QSize(100, 0))
        self.g_expirationdate_label.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_expirationdate_label.setFont(font)
        self.g_expirationdate_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_expirationdate_label.setObjectName("g_expirationdate_label")
        self.horizontalLayout_8.addWidget(self.g_expirationdate_label)
        self.g_month_combobox = QtWidgets.QComboBox(self.g_expirationdate_frame)
        self.g_month_combobox.setMaximumSize(QtCore.QSize(45, 31))
        self.g_month_combobox.setMaxVisibleItems(12)
        self.g_month_combobox.setObjectName("g_month_combobox")
        self.g_month_combobox.addItem("")
        self.g_month_combobox.addItem("")
        self.g_month_combobox.addItem("")
        self.g_month_combobox.addItem("")
        self.g_month_combobox.addItem("")
        self.g_month_combobox.addItem("")
        self.g_month_combobox.addItem("")
        self.g_month_combobox.addItem("")
        self.g_month_combobox.addItem("")
        self.g_month_combobox.addItem("")
        self.g_month_combobox.addItem("")
        self.g_month_combobox.addItem("")
        self.horizontalLayout_8.addWidget(self.g_month_combobox)
        self.g_year_combobox = QtWidgets.QComboBox(self.g_expirationdate_frame)
        self.g_year_combobox.setMaximumSize(QtCore.QSize(55, 31))
        self.g_year_combobox.setObjectName("g_year_combobox")
        self.g_year_combobox.addItem("")
        self.g_year_combobox.addItem("")
        self.g_year_combobox.addItem("")
        self.g_year_combobox.addItem("")
        self.g_year_combobox.addItem("")
        self.horizontalLayout_8.addWidget(self.g_year_combobox)
        self.horizontalLayout_10.addWidget(self.g_expirationdate_frame)
        self.g_securitycode_frame = QtWidgets.QFrame(self.g_expirationcode_frame)
        self.g_securitycode_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_securitycode_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_securitycode_frame.setObjectName("g_securitycode_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_securitycode_frame)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_securitycode_label = QtWidgets.QLabel(self.g_securitycode_frame)
        self.g_securitycode_label.setMinimumSize(QtCore.QSize(0, 0))
        self.g_securitycode_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_securitycode_label.setFont(font)
        self.g_securitycode_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_securitycode_label.setObjectName("g_securitycode_label")
        self.horizontalLayout_5.addWidget(self.g_securitycode_label)
        self.g_securitycode_lineedit = QtWidgets.QLineEdit(self.g_securitycode_frame)
        self.g_securitycode_lineedit.setMinimumSize(QtCore.QSize(60, 0))
        self.g_securitycode_lineedit.setMaximumSize(QtCore.QSize(60, 31))
        self.g_securitycode_lineedit.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_securitycode_lineedit.setObjectName("g_securitycode_lineedit")
        self.horizontalLayout_5.addWidget(self.g_securitycode_lineedit)
        self.horizontalLayout_10.addWidget(self.g_securitycode_frame)
        self.verticalLayout.addWidget(self.g_expirationcode_frame)
        self.g_billingzip_frame = QtWidgets.QFrame(self.g_cardinfo_frame)
        self.g_billingzip_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_billingzip_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_billingzip_frame.setObjectName("g_billingzip_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_billingzip_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.g_billingzip_label = QtWidgets.QLabel(self.g_billingzip_frame)
        self.g_billingzip_label.setMinimumSize(QtCore.QSize(100, 0))
        self.g_billingzip_label.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_billingzip_label.setFont(font)
        self.g_billingzip_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_billingzip_label.setObjectName("g_billingzip_label")
        self.horizontalLayout_11.addWidget(self.g_billingzip_label)
        self.g_billingzip_lineedit = QtWidgets.QLineEdit(self.g_billingzip_frame)
        self.g_billingzip_lineedit.setMinimumSize(QtCore.QSize(0, 0))
        self.g_billingzip_lineedit.setMaximumSize(QtCore.QSize(80, 31))
        self.g_billingzip_lineedit.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_billingzip_lineedit.setObjectName("g_billingzip_lineedit")
        self.horizontalLayout_11.addWidget(self.g_billingzip_lineedit)
        spacerItem4 = QtWidgets.QSpacerItem(116, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem4)
        self.verticalLayout.addWidget(self.g_billingzip_frame)
        spacerItem5 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem5)
        self.horizontalLayout_2.addWidget(self.g_cardinfo_frame)
        self.g_rightcolumn_frame = QtWidgets.QFrame(self.g_manualentry_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_rightcolumn_frame.sizePolicy().hasHeightForWidth())
        self.g_rightcolumn_frame.setSizePolicy(sizePolicy)
        self.g_rightcolumn_frame.setMaximumSize(QtCore.QSize(454, 16777215))
        self.g_rightcolumn_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_rightcolumn_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_rightcolumn_frame.setObjectName("g_rightcolumn_frame")
        self.verticalLayout_13 = QtWidgets.QVBoxLayout(self.g_rightcolumn_frame)
        self.verticalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_13.setObjectName("verticalLayout_13")
        self.g_keypadcontainer_frame = QtWidgets.QFrame(self.g_rightcolumn_frame)
        self.g_keypadcontainer_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_keypadcontainer_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_keypadcontainer_frame.setObjectName("g_keypadcontainer_frame")
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout(self.g_keypadcontainer_frame)
        self.horizontalLayout_14.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem6)
        self.g_container_frame = QtWidgets.QFrame(self.g_keypadcontainer_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_container_frame.sizePolicy().hasHeightForWidth())
        self.g_container_frame.setSizePolicy(sizePolicy)
        self.g_container_frame.setMinimumSize(QtCore.QSize(271, 0))
        self.g_container_frame.setMaximumSize(QtCore.QSize(271, 345))
        self.g_container_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_container_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_container_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_container_frame.setObjectName("g_container_frame")
        self.verticalLayout_10 = QtWidgets.QVBoxLayout(self.g_container_frame)
        self.verticalLayout_10.setContentsMargins(-1, 9, 9, 9)
        self.verticalLayout_10.setSpacing(6)
        self.verticalLayout_10.setObjectName("verticalLayout_10")
        self.g_keypaditembuttons_frame = QtWidgets.QFrame(self.g_container_frame)
        self.g_keypaditembuttons_frame.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_keypaditembuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_keypaditembuttons_frame.setSizePolicy(sizePolicy)
        self.g_keypaditembuttons_frame.setMinimumSize(QtCore.QSize(200, 250))
        self.g_keypaditembuttons_frame.setMaximumSize(QtCore.QSize(400, 500))
        self.g_keypaditembuttons_frame.setStyleSheet("")
        self.g_keypaditembuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_keypaditembuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_keypaditembuttons_frame.setObjectName("g_keypaditembuttons_frame")
        self.gridLayout_6 = QtWidgets.QGridLayout(self.g_keypaditembuttons_frame)
        self.gridLayout_6.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_6.setSpacing(5)
        self.gridLayout_6.setObjectName("gridLayout_6")
        self.g_zero_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_zero_button.sizePolicy().hasHeightForWidth())
        self.g_zero_button.setSizePolicy(sizePolicy)
        self.g_zero_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_zero_button.setObjectName("g_zero_button")
        self.gridLayout_6.addWidget(self.g_zero_button, 3, 0, 1, 1)
        self.g_doublezero_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_doublezero_button.sizePolicy().hasHeightForWidth())
        self.g_doublezero_button.setSizePolicy(sizePolicy)
        self.g_doublezero_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_doublezero_button.setObjectName("g_doublezero_button")
        self.gridLayout_6.addWidget(self.g_doublezero_button, 3, 1, 1, 1)
        self.g_nine_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_nine_button.sizePolicy().hasHeightForWidth())
        self.g_nine_button.setSizePolicy(sizePolicy)
        self.g_nine_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_nine_button.setObjectName("g_nine_button")
        self.gridLayout_6.addWidget(self.g_nine_button, 0, 2, 1, 1)
        self.g_four_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_four_button.sizePolicy().hasHeightForWidth())
        self.g_four_button.setSizePolicy(sizePolicy)
        self.g_four_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_four_button.setObjectName("g_four_button")
        self.gridLayout_6.addWidget(self.g_four_button, 1, 0, 1, 1)
        self.g_five_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_five_button.sizePolicy().hasHeightForWidth())
        self.g_five_button.setSizePolicy(sizePolicy)
        self.g_five_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_five_button.setObjectName("g_five_button")
        self.gridLayout_6.addWidget(self.g_five_button, 1, 1, 1, 1)
        self.g_eight_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_eight_button.sizePolicy().hasHeightForWidth())
        self.g_eight_button.setSizePolicy(sizePolicy)
        self.g_eight_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_eight_button.setObjectName("g_eight_button")
        self.gridLayout_6.addWidget(self.g_eight_button, 0, 1, 1, 1)
        self.g_six_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_six_button.sizePolicy().hasHeightForWidth())
        self.g_six_button.setSizePolicy(sizePolicy)
        self.g_six_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_six_button.setObjectName("g_six_button")
        self.gridLayout_6.addWidget(self.g_six_button, 1, 2, 1, 1)
        self.g_seven_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_seven_button.sizePolicy().hasHeightForWidth())
        self.g_seven_button.setSizePolicy(sizePolicy)
        self.g_seven_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_seven_button.setObjectName("g_seven_button")
        self.gridLayout_6.addWidget(self.g_seven_button, 0, 0, 1, 1)
        self.g_enter_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_enter_button.sizePolicy().hasHeightForWidth())
        self.g_enter_button.setSizePolicy(sizePolicy)
        palette = QtGui.QPalette()
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255, 128))
        brush.setStyle(QtCore.Qt.NoBrush)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.PlaceholderText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255, 128))
        brush.setStyle(QtCore.Qt.NoBrush)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.PlaceholderText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(187, 226, 187))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255, 128))
        brush.setStyle(QtCore.Qt.NoBrush)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.PlaceholderText, brush)
        self.g_enter_button.setPalette(palette)
        self.g_enter_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_enter_button.setObjectName("g_enter_button")
        self.gridLayout_6.addWidget(self.g_enter_button, 2, 3, 2, 1)
        self.g_one_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_one_button.sizePolicy().hasHeightForWidth())
        self.g_one_button.setSizePolicy(sizePolicy)
        self.g_one_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_one_button.setObjectName("g_one_button")
        self.gridLayout_6.addWidget(self.g_one_button, 2, 0, 1, 1)
        self.g_two_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_two_button.sizePolicy().hasHeightForWidth())
        self.g_two_button.setSizePolicy(sizePolicy)
        self.g_two_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_two_button.setObjectName("g_two_button")
        self.gridLayout_6.addWidget(self.g_two_button, 2, 1, 1, 1)
        self.g_three_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_three_button.sizePolicy().hasHeightForWidth())
        self.g_three_button.setSizePolicy(sizePolicy)
        self.g_three_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_three_button.setObjectName("g_three_button")
        self.gridLayout_6.addWidget(self.g_three_button, 2, 2, 1, 1)
        self.g_decimal_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_decimal_button.sizePolicy().hasHeightForWidth())
        self.g_decimal_button.setSizePolicy(sizePolicy)
        self.g_decimal_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_decimal_button.setObjectName("g_decimal_button")
        self.gridLayout_6.addWidget(self.g_decimal_button, 3, 2, 1, 1)
        self.g_backspace_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_backspace_button.sizePolicy().hasHeightForWidth())
        self.g_backspace_button.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(True)
        font.setWeight(75)
        self.g_backspace_button.setFont(font)
        self.g_backspace_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_backspace_button.setText("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/cardentry_modaldialog/backspace"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_backspace_button.setIcon(icon1)
        self.g_backspace_button.setIconSize(QtCore.QSize(32, 32))
        self.g_backspace_button.setObjectName("g_backspace_button")
        self.gridLayout_6.addWidget(self.g_backspace_button, 0, 3, 1, 1)
        self.g_asterisk_button = QtWidgets.QPushButton(self.g_keypaditembuttons_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_asterisk_button.sizePolicy().hasHeightForWidth())
        self.g_asterisk_button.setSizePolicy(sizePolicy)
        self.g_asterisk_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_asterisk_button.setObjectName("g_asterisk_button")
        self.gridLayout_6.addWidget(self.g_asterisk_button, 1, 3, 1, 1)
        self.g_decimal_button.raise_()
        self.g_enter_button.raise_()
        self.g_nine_button.raise_()
        self.g_five_button.raise_()
        self.g_eight_button.raise_()
        self.g_seven_button.raise_()
        self.g_two_button.raise_()
        self.g_six_button.raise_()
        self.g_three_button.raise_()
        self.g_four_button.raise_()
        self.g_zero_button.raise_()
        self.g_one_button.raise_()
        self.g_doublezero_button.raise_()
        self.g_asterisk_button.raise_()
        self.g_backspace_button.raise_()
        self.verticalLayout_10.addWidget(self.g_keypaditembuttons_frame)
        self.horizontalLayout_14.addWidget(self.g_container_frame)
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem7)
        self.verticalLayout_13.addWidget(self.g_keypadcontainer_frame)
        spacerItem8 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_13.addItem(spacerItem8)
        self.horizontalLayout_2.addWidget(self.g_rightcolumn_frame)
        self.g_cardentry_tabs.addTab(self.g_manualentry_widget, "")
        self.g_voiceauthorization_widget = QtWidgets.QWidget()
        self.g_voiceauthorization_widget.setStyleSheet("")
        self.g_voiceauthorization_widget.setObjectName("g_voiceauthorization_widget")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_voiceauthorization_widget)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_voiceauthorizationcenters_groupbox = QtWidgets.QGroupBox(self.g_voiceauthorization_widget)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)
        font.setWeight(75)
        self.g_voiceauthorizationcenters_groupbox.setFont(font)
        self.g_voiceauthorizationcenters_groupbox.setObjectName("g_voiceauthorizationcenters_groupbox")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.g_voiceauthorizationcenters_groupbox)
        self.verticalLayout_5.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout_5.setSpacing(12)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.g_typenumber_frame = QtWidgets.QFrame(self.g_voiceauthorizationcenters_groupbox)
        self.g_typenumber_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_typenumber_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_typenumber_frame.setObjectName("g_typenumber_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_typenumber_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setSpacing(15)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_cardtype_label = QtWidgets.QLabel(self.g_typenumber_frame)
        self.g_cardtype_label.setMinimumSize(QtCore.QSize(120, 0))
        self.g_cardtype_label.setMaximumSize(QtCore.QSize(120, 31))
        font = QtGui.QFont()
        font.setPointSize(8)
        font.setBold(True)
        font.setWeight(75)
        self.g_cardtype_label.setFont(font)
        self.g_cardtype_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cardtype_label.setObjectName("g_cardtype_label")
        self.horizontalLayout_3.addWidget(self.g_cardtype_label)
        self.g_phonenumber_label = QtWidgets.QLabel(self.g_typenumber_frame)
        font = QtGui.QFont()
        font.setPointSize(8)
        font.setBold(True)
        font.setWeight(75)
        self.g_phonenumber_label.setFont(font)
        self.g_phonenumber_label.setObjectName("g_phonenumber_label")
        self.horizontalLayout_3.addWidget(self.g_phonenumber_label)
        spacerItem9 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem9)
        self.verticalLayout_5.addWidget(self.g_typenumber_frame)
        self.g_visamastercard_frame = QtWidgets.QFrame(self.g_voiceauthorizationcenters_groupbox)
        self.g_visamastercard_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_visamastercard_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_visamastercard_frame.setObjectName("g_visamastercard_frame")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.g_visamastercard_frame)
        self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_13.setSpacing(15)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.g_visamastercardtitle_label = QtWidgets.QLabel(self.g_visamastercard_frame)
        self.g_visamastercardtitle_label.setMinimumSize(QtCore.QSize(120, 0))
        self.g_visamastercardtitle_label.setMaximumSize(QtCore.QSize(120, 31))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_visamastercardtitle_label.setFont(font)
        self.g_visamastercardtitle_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_visamastercardtitle_label.setObjectName("g_visamastercardtitle_label")
        self.horizontalLayout_13.addWidget(self.g_visamastercardtitle_label)
        self.g_visamastercard_label = QtWidgets.QLabel(self.g_visamastercard_frame)
        self.g_visamastercard_label.setObjectName("g_visamastercard_label")
        self.horizontalLayout_13.addWidget(self.g_visamastercard_label)
        spacerItem10 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_13.addItem(spacerItem10)
        self.verticalLayout_5.addWidget(self.g_visamastercard_frame)
        self.g_americanexpress_frame = QtWidgets.QFrame(self.g_voiceauthorizationcenters_groupbox)
        self.g_americanexpress_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_americanexpress_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_americanexpress_frame.setObjectName("g_americanexpress_frame")
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout(self.g_americanexpress_frame)
        self.horizontalLayout_17.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_17.setSpacing(15)
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        self.g_americanexpresstitle_label = QtWidgets.QLabel(self.g_americanexpress_frame)
        self.g_americanexpresstitle_label.setMinimumSize(QtCore.QSize(120, 0))
        self.g_americanexpresstitle_label.setMaximumSize(QtCore.QSize(120, 31))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_americanexpresstitle_label.setFont(font)
        self.g_americanexpresstitle_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_americanexpresstitle_label.setObjectName("g_americanexpresstitle_label")
        self.horizontalLayout_17.addWidget(self.g_americanexpresstitle_label)
        self.g_americanexpress_label = QtWidgets.QLabel(self.g_americanexpress_frame)
        self.g_americanexpress_label.setObjectName("g_americanexpress_label")
        self.horizontalLayout_17.addWidget(self.g_americanexpress_label)
        spacerItem11 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_17.addItem(spacerItem11)
        self.verticalLayout_5.addWidget(self.g_americanexpress_frame)
        self.g_discover_frame = QtWidgets.QFrame(self.g_voiceauthorizationcenters_groupbox)
        self.g_discover_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_discover_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_discover_frame.setObjectName("g_discover_frame")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.g_discover_frame)
        self.horizontalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_12.setSpacing(15)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.g_discovertitle_label = QtWidgets.QLabel(self.g_discover_frame)
        self.g_discovertitle_label.setMinimumSize(QtCore.QSize(120, 0))
        self.g_discovertitle_label.setMaximumSize(QtCore.QSize(120, 31))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_discovertitle_label.setFont(font)
        self.g_discovertitle_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_discovertitle_label.setObjectName("g_discovertitle_label")
        self.horizontalLayout_12.addWidget(self.g_discovertitle_label)
        self.g_discover_label = QtWidgets.QLabel(self.g_discover_frame)
        self.g_discover_label.setObjectName("g_discover_label")
        self.horizontalLayout_12.addWidget(self.g_discover_label)
        spacerItem12 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_12.addItem(spacerItem12)
        self.verticalLayout_5.addWidget(self.g_discover_frame)
        self.g_jcb_frame = QtWidgets.QFrame(self.g_voiceauthorizationcenters_groupbox)
        self.g_jcb_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_jcb_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_jcb_frame.setObjectName("g_jcb_frame")
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout(self.g_jcb_frame)
        self.horizontalLayout_15.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_15.setSpacing(15)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.g_jcbtitle_label = QtWidgets.QLabel(self.g_jcb_frame)
        self.g_jcbtitle_label.setMinimumSize(QtCore.QSize(120, 0))
        self.g_jcbtitle_label.setMaximumSize(QtCore.QSize(120, 31))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_jcbtitle_label.setFont(font)
        self.g_jcbtitle_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_jcbtitle_label.setObjectName("g_jcbtitle_label")
        self.horizontalLayout_15.addWidget(self.g_jcbtitle_label)
        self.g_jcb_label = QtWidgets.QLabel(self.g_jcb_frame)
        self.g_jcb_label.setObjectName("g_jcb_label")
        self.horizontalLayout_15.addWidget(self.g_jcb_label)
        spacerItem13 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_15.addItem(spacerItem13)
        self.verticalLayout_5.addWidget(self.g_jcb_frame)
        self.verticalLayout_2.addWidget(self.g_voiceauthorizationcenters_groupbox)
        self.g_merchantauthorization_frame = QtWidgets.QFrame(self.g_voiceauthorization_widget)
        self.g_merchantauthorization_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_merchantauthorization_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_merchantauthorization_frame.setObjectName("g_merchantauthorization_frame")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.g_merchantauthorization_frame)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.g_merchantaccount_label = QtWidgets.QLabel(self.g_merchantauthorization_frame)
        self.g_merchantaccount_label.setMinimumSize(QtCore.QSize(120, 0))
        self.g_merchantaccount_label.setMaximumSize(QtCore.QSize(120, 31))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_merchantaccount_label.setFont(font)
        self.g_merchantaccount_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_merchantaccount_label.setObjectName("g_merchantaccount_label")
        self.horizontalLayout_9.addWidget(self.g_merchantaccount_label)
        self.g_merchantaccount_lineedit = QtWidgets.QLineEdit(self.g_merchantauthorization_frame)
        self.g_merchantaccount_lineedit.setMaximumSize(QtCore.QSize(200, 31))
        self.g_merchantaccount_lineedit.setStyleSheet("background-color: #dddddd;\n"
"color: #333333;")
        self.g_merchantaccount_lineedit.setReadOnly(True)
        self.g_merchantaccount_lineedit.setObjectName("g_merchantaccount_lineedit")
        self.horizontalLayout_9.addWidget(self.g_merchantaccount_lineedit)
        self.g_authorizationnumber_label = QtWidgets.QLabel(self.g_merchantauthorization_frame)
        self.g_authorizationnumber_label.setMinimumSize(QtCore.QSize(120, 0))
        self.g_authorizationnumber_label.setMaximumSize(QtCore.QSize(120, 31))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_authorizationnumber_label.setFont(font)
        self.g_authorizationnumber_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_authorizationnumber_label.setObjectName("g_authorizationnumber_label")
        self.horizontalLayout_9.addWidget(self.g_authorizationnumber_label)
        self.g_authorizationnumber_lineedit = QtWidgets.QLineEdit(self.g_merchantauthorization_frame)
        self.g_authorizationnumber_lineedit.setMaximumSize(QtCore.QSize(200, 31))
        self.g_authorizationnumber_lineedit.setStyleSheet("")
        self.g_authorizationnumber_lineedit.setReadOnly(False)
        self.g_authorizationnumber_lineedit.setObjectName("g_authorizationnumber_lineedit")
        self.horizontalLayout_9.addWidget(self.g_authorizationnumber_lineedit)
        self.g_keypad_button = QtWidgets.QPushButton(self.g_merchantauthorization_frame)
        self.g_keypad_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_keypad_button.setStyleSheet("border:0px;\n"
"background:none;")
        self.g_keypad_button.setText("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/cardentry_modaldialog/keyboard"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_keypad_button.setIcon(icon2)
        self.g_keypad_button.setIconSize(QtCore.QSize(40, 21))
        self.g_keypad_button.setObjectName("g_keypad_button")
        self.horizontalLayout_9.addWidget(self.g_keypad_button)
        spacerItem14 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem14)
        self.verticalLayout_2.addWidget(self.g_merchantauthorization_frame)
        spacerItem15 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_2.addItem(spacerItem15)
        self.g_cardentry_tabs.addTab(self.g_voiceauthorization_widget, "")
        self.verticalLayout_3.addWidget(self.g_cardentry_tabs)
        self.g_dialogbuttons_frame = QtWidgets.QFrame(CardEntryDialog)
        self.g_dialogbuttons_frame.setStyleSheet("#g_dialog_buttons_frame, #g_dialog_buttons_frame {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"")
        self.g_dialogbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_dialogbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_dialogbuttons_frame.setObjectName("g_dialogbuttons_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_dialogbuttons_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        spacerItem16 = QtWidgets.QSpacerItem(116, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem16)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_cancel_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/cardentry_modaldialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon3)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_6.addWidget(self.g_cancel_button)
        self.g_authorize_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_authorize_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_authorize_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_authorize_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_authorize_button.setStyleSheet("")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/cardentry_modaldialog/authorize"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_authorize_button.setIcon(icon4)
        self.g_authorize_button.setIconSize(QtCore.QSize(24, 24))
        self.g_authorize_button.setObjectName("g_authorize_button")
        self.horizontalLayout_6.addWidget(self.g_authorize_button)
        self.verticalLayout_3.addWidget(self.g_dialogbuttons_frame)

        self.retranslateUi(CardEntryDialog)
        self.g_cardentry_tabs.setCurrentIndex(2)
        QtCore.QMetaObject.connectSlotsByName(CardEntryDialog)
        CardEntryDialog.setTabOrder(self.g_chargeamount_lineedit, self.g_cancel_button)
        CardEntryDialog.setTabOrder(self.g_cancel_button, self.g_authorize_button)
        CardEntryDialog.setTabOrder(self.g_authorize_button, self.g_cardnumber_lineedit)
        CardEntryDialog.setTabOrder(self.g_cardnumber_lineedit, self.g_securitycode_lineedit)
        CardEntryDialog.setTabOrder(self.g_securitycode_lineedit, self.g_billingzip_lineedit)
        CardEntryDialog.setTabOrder(self.g_billingzip_lineedit, self.g_month_combobox)
        CardEntryDialog.setTabOrder(self.g_month_combobox, self.g_year_combobox)
        CardEntryDialog.setTabOrder(self.g_year_combobox, self.g_cardentry_tabs)
        CardEntryDialog.setTabOrder(self.g_cardentry_tabs, self.g_merchantaccount_lineedit)
        CardEntryDialog.setTabOrder(self.g_merchantaccount_lineedit, self.g_authorizationnumber_lineedit)
        CardEntryDialog.setTabOrder(self.g_authorizationnumber_lineedit, self.g_keypad_button)

    def retranslateUi(self, CardEntryDialog):
        _translate = QtCore.QCoreApplication.translate
        CardEntryDialog.setWindowTitle(_translate("CardEntryDialog", "Credit / Debit Card Entry"))
        CardEntryDialog.setProperty("missing_fields_msg_text", _translate("CardEntryDialog", "Please complete all fields first."))
        CardEntryDialog.setProperty("missing_fields_title_text", _translate("CardEntryDialog", "Missing Fields"))
        CardEntryDialog.setProperty("authorizing_busy_msg_text", _translate("CardEntryDialog", "Authorizing your credit card payment ..."))
        CardEntryDialog.setProperty("ets_payment_url_text", _translate("CardEntryDialog", "http://apidev2.pinogy.net/etspayment.php?resource={resource}&accesskey={accesskey}&timestamp={timestamp}&signature={signature}&session={session}&amount={amount:.2f}"))
        CardEntryDialog.setProperty("current_session_resource_text", _translate("CardEntryDialog", "http://apidev2.pinogy.net/apps/any/sessions/mine"))
        CardEntryDialog.setProperty("processing_error_msg_text", _translate("CardEntryDialog", "The payment could not be processed."))
        CardEntryDialog.setProperty("processing_error_title_text", _translate("CardEntryDialog", "Processing Error"))
        CardEntryDialog.setProperty("validation_error_title_text", _translate("CardEntryDialog", "Validation Error"))
        CardEntryDialog.setProperty("payment_successful_title_text", _translate("CardEntryDialog", "Payment Successful"))
        self.g_main_label.setText(_translate("CardEntryDialog", "Credit / Debit Card Entry"))
        self.g_chargeamount_label.setText(_translate("CardEntryDialog", "Charge Amount"))
        self.g_instruction_label.setText(_translate("CardEntryDialog", "Swipe Card Now"))
        self.g_cardentry_tabs.setTabText(self.g_cardentry_tabs.indexOf(self.g_swipecard_widget), _translate("CardEntryDialog", "Swipe Card"))
        self.g_cardnumber_label.setText(_translate("CardEntryDialog", "Card #"))
        self.g_expirationdate_label.setText(_translate("CardEntryDialog", "Expiration Date"))
        self.g_month_combobox.setItemText(0, _translate("CardEntryDialog", "01"))
        self.g_month_combobox.setItemText(1, _translate("CardEntryDialog", "02"))
        self.g_month_combobox.setItemText(2, _translate("CardEntryDialog", "03"))
        self.g_month_combobox.setItemText(3, _translate("CardEntryDialog", "04"))
        self.g_month_combobox.setItemText(4, _translate("CardEntryDialog", "05"))
        self.g_month_combobox.setItemText(5, _translate("CardEntryDialog", "06"))
        self.g_month_combobox.setItemText(6, _translate("CardEntryDialog", "07"))
        self.g_month_combobox.setItemText(7, _translate("CardEntryDialog", "08"))
        self.g_month_combobox.setItemText(8, _translate("CardEntryDialog", "09"))
        self.g_month_combobox.setItemText(9, _translate("CardEntryDialog", "10"))
        self.g_month_combobox.setItemText(10, _translate("CardEntryDialog", "11"))
        self.g_month_combobox.setItemText(11, _translate("CardEntryDialog", "12"))
        self.g_year_combobox.setItemText(0, _translate("CardEntryDialog", "2015"))
        self.g_year_combobox.setItemText(1, _translate("CardEntryDialog", "2016"))
        self.g_year_combobox.setItemText(2, _translate("CardEntryDialog", "2017"))
        self.g_year_combobox.setItemText(3, _translate("CardEntryDialog", "2018"))
        self.g_year_combobox.setItemText(4, _translate("CardEntryDialog", "2019"))
        self.g_securitycode_label.setText(_translate("CardEntryDialog", "Security Code"))
        self.g_billingzip_label.setText(_translate("CardEntryDialog", "Billing Zip Code"))
        self.g_zero_button.setText(_translate("CardEntryDialog", "0"))
        self.g_doublezero_button.setText(_translate("CardEntryDialog", "00"))
        self.g_nine_button.setText(_translate("CardEntryDialog", "9"))
        self.g_four_button.setText(_translate("CardEntryDialog", "4"))
        self.g_five_button.setText(_translate("CardEntryDialog", "5"))
        self.g_eight_button.setText(_translate("CardEntryDialog", "8"))
        self.g_six_button.setText(_translate("CardEntryDialog", "6"))
        self.g_seven_button.setText(_translate("CardEntryDialog", "7"))
        self.g_enter_button.setText(_translate("CardEntryDialog", "Enter"))
        self.g_one_button.setText(_translate("CardEntryDialog", "1"))
        self.g_two_button.setText(_translate("CardEntryDialog", "2"))
        self.g_three_button.setText(_translate("CardEntryDialog", "3"))
        self.g_decimal_button.setText(_translate("CardEntryDialog", "."))
        self.g_asterisk_button.setText(_translate("CardEntryDialog", "*"))
        self.g_cardentry_tabs.setTabText(self.g_cardentry_tabs.indexOf(self.g_manualentry_widget), _translate("CardEntryDialog", "Manual Entry"))
        self.g_voiceauthorizationcenters_groupbox.setTitle(_translate("CardEntryDialog", "Voice Authorization Centers"))
        self.g_cardtype_label.setText(_translate("CardEntryDialog", "Card Type"))
        self.g_phonenumber_label.setText(_translate("CardEntryDialog", "Phone Number"))
        self.g_visamastercardtitle_label.setText(_translate("CardEntryDialog", "Visa/Mastercard"))
        self.g_visamastercard_label.setText(_translate("CardEntryDialog", "(*************"))
        self.g_americanexpresstitle_label.setText(_translate("CardEntryDialog", "American Express"))
        self.g_americanexpress_label.setText(_translate("CardEntryDialog", "(*************"))
        self.g_discovertitle_label.setText(_translate("CardEntryDialog", "Discover"))
        self.g_discover_label.setText(_translate("CardEntryDialog", "(*************"))
        self.g_jcbtitle_label.setText(_translate("CardEntryDialog", "JCB"))
        self.g_jcb_label.setText(_translate("CardEntryDialog", "(*************"))
        self.g_merchantaccount_label.setText(_translate("CardEntryDialog", "Merchant Account#"))
        self.g_authorizationnumber_label.setText(_translate("CardEntryDialog", "Authorization #"))
        self.g_cardentry_tabs.setTabText(self.g_cardentry_tabs.indexOf(self.g_voiceauthorization_widget), _translate("CardEntryDialog", "Voice Authorization"))
        self.g_cancel_button.setText(_translate("CardEntryDialog", " Cancel"))
        self.g_authorize_button.setText(_translate("CardEntryDialog", " Authorize"))
        self.g_authorize_button.setShortcut(_translate("CardEntryDialog", "Enter"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    CardEntryDialog = QtWidgets.QDialog()
    ui = Ui_CardEntryDialog()
    ui.setupUi(CardEntryDialog)
    CardEntryDialog.show()
    sys.exit(app.exec_())
