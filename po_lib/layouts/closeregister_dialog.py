# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'closeregister_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_CloseRegisterDialog(object):
    def setupUi(self, CloseRegisterDialog):
        CloseRegisterDialog.setObjectName("CloseRegisterDialog")
        CloseRegisterDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        CloseRegisterDialog.resize(683, 570)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(CloseRegisterDialog.sizePolicy().hasHeightForWidth())
        CloseRegisterDialog.setSizePolicy(sizePolicy)
        CloseRegisterDialog.setMinimumSize(QtCore.QSize(683, 570))
        CloseRegisterDialog.setMaximumSize(QtCore.QSize(16777215, 16777215))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/All Icons/money_bill_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        CloseRegisterDialog.setWindowIcon(icon)
        CloseRegisterDialog.setStyleSheet("\n"
"QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border: 0px;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    font-size:12px;\n"
"    border-bottom: 1px solid #000;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"\n"
"#g_dialog_frame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-weight: bold;\n"
"    font-size: 12px;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
" QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected, \n"
"QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QTabWidget {\n"
"    border:0px;\n"
"}\n"
"\n"
"#g_transfer_widget, \n"
"#g_reset_widget,\n"
"#g_verify_widget,\n"
"#g_incoming_widget,\n"
"#g_outgoing_widget {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius:0px;\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"#g_description_label {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color: #dddddd;\n"
"    color: #333333;\n"
"}\n"
"\n"
"#g_closeregister_button,\n"
"#g_next_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_closeregister_button:pressed,\n"
"#g_next_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-weight: bold;\n"
"    font-size: 17px;\n"
"}\n"
"\n"
"#g_lastopenedtitle_label,\n"
"#g_openingcashtitle_label,\n"
"#g_locationtitle_label,\n"
"#g_registertitle_label {\n"
"    font-weight: bold;\n"
"    font-size: 13px;\n"
"}\n"
"\n"
"#g_lastopened_label,\n"
"#g_openingcash_label,\n"
"#g_locationname_label,\n"
"#g_registername_label {\n"
"    font-size: 13px;\n"
"}\n"
"\n"
"#g_printoptions_label {\n"
"    font-weight: bold;\n"
"    font-size: 11px;\n"
"    text-decoration: underline;\n"
"}\n"
"\n"
"#g_closing_receipt_checkbox, #g_journal_receipt_checkbox {\n"
"    font-size: 11px;\n"
"}")
        CloseRegisterDialog.setModal(True)
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(CloseRegisterDialog)
        self.verticalLayout_3.setContentsMargins(12, 12, 12, 12)
        self.verticalLayout_3.setSpacing(12)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_main_label = QtWidgets.QLabel(CloseRegisterDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_3.addWidget(self.g_main_label)
        self.g_registerdetails_groupbox = QtWidgets.QGroupBox(CloseRegisterDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_registerdetails_groupbox.sizePolicy().hasHeightForWidth())
        self.g_registerdetails_groupbox.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_registerdetails_groupbox.setFont(font)
        self.g_registerdetails_groupbox.setObjectName("g_registerdetails_groupbox")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_registerdetails_groupbox)
        self.horizontalLayout_5.setContentsMargins(12, 12, 12, -1)
        self.horizontalLayout_5.setSpacing(12)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_registerlocation_frame = QtWidgets.QFrame(self.g_registerdetails_groupbox)
        self.g_registerlocation_frame.setMinimumSize(QtCore.QSize(287, 0))
        self.g_registerlocation_frame.setMaximumSize(QtCore.QSize(287, 16777215))
        self.g_registerlocation_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_registerlocation_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_registerlocation_frame.setObjectName("g_registerlocation_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_registerlocation_frame)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(7)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_register_frame = QtWidgets.QFrame(self.g_registerlocation_frame)
        self.g_register_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_register_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_register_frame.setObjectName("g_register_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_register_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_registertitle_label = QtWidgets.QLabel(self.g_register_frame)
        self.g_registertitle_label.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_registertitle_label.setFont(font)
        self.g_registertitle_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_registertitle_label.setObjectName("g_registertitle_label")
        self.horizontalLayout.addWidget(self.g_registertitle_label)
        self.g_registername_label = QtWidgets.QLabel(self.g_register_frame)
        self.g_registername_label.setMinimumSize(QtCore.QSize(200, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_registername_label.setFont(font)
        self.g_registername_label.setText("")
        self.g_registername_label.setObjectName("g_registername_label")
        self.horizontalLayout.addWidget(self.g_registername_label)
        self.verticalLayout.addWidget(self.g_register_frame)
        self.g_location_frame = QtWidgets.QFrame(self.g_registerlocation_frame)
        self.g_location_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_location_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_location_frame.setObjectName("g_location_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_location_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_locationtitle_label = QtWidgets.QLabel(self.g_location_frame)
        self.g_locationtitle_label.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_locationtitle_label.setFont(font)
        self.g_locationtitle_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_locationtitle_label.setObjectName("g_locationtitle_label")
        self.horizontalLayout_2.addWidget(self.g_locationtitle_label)
        self.g_locationname_label = QtWidgets.QLabel(self.g_location_frame)
        self.g_locationname_label.setMinimumSize(QtCore.QSize(200, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_locationname_label.setFont(font)
        self.g_locationname_label.setText("")
        self.g_locationname_label.setObjectName("g_locationname_label")
        self.horizontalLayout_2.addWidget(self.g_locationname_label)
        self.verticalLayout.addWidget(self.g_location_frame)
        self.horizontalLayout_5.addWidget(self.g_registerlocation_frame)
        spacerItem = QtWidgets.QSpacerItem(13, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem)
        self.g_openedcash_frame = QtWidgets.QFrame(self.g_registerdetails_groupbox)
        self.g_openedcash_frame.setMinimumSize(QtCore.QSize(287, 0))
        self.g_openedcash_frame.setMaximumSize(QtCore.QSize(287, 16777215))
        self.g_openedcash_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_openedcash_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_openedcash_frame.setObjectName("g_openedcash_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_openedcash_frame)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setSpacing(7)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_lastopened_frame = QtWidgets.QFrame(self.g_openedcash_frame)
        self.g_lastopened_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_lastopened_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_lastopened_frame.setObjectName("g_lastopened_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_lastopened_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_lastopenedtitle_label = QtWidgets.QLabel(self.g_lastopened_frame)
        self.g_lastopenedtitle_label.setMinimumSize(QtCore.QSize(100, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_lastopenedtitle_label.setFont(font)
        self.g_lastopenedtitle_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_lastopenedtitle_label.setObjectName("g_lastopenedtitle_label")
        self.horizontalLayout_3.addWidget(self.g_lastopenedtitle_label)
        self.g_lastopened_label = QtWidgets.QLabel(self.g_lastopened_frame)
        self.g_lastopened_label.setMinimumSize(QtCore.QSize(200, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_lastopened_label.setFont(font)
        self.g_lastopened_label.setText("")
        self.g_lastopened_label.setObjectName("g_lastopened_label")
        self.horizontalLayout_3.addWidget(self.g_lastopened_label)
        self.verticalLayout_2.addWidget(self.g_lastopened_frame)
        self.g_openingcash_frame = QtWidgets.QFrame(self.g_openedcash_frame)
        self.g_openingcash_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_openingcash_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_openingcash_frame.setObjectName("g_openingcash_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_openingcash_frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_openingcashtitle_label = QtWidgets.QLabel(self.g_openingcash_frame)
        self.g_openingcashtitle_label.setMinimumSize(QtCore.QSize(100, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_openingcashtitle_label.setFont(font)
        self.g_openingcashtitle_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_openingcashtitle_label.setObjectName("g_openingcashtitle_label")
        self.horizontalLayout_4.addWidget(self.g_openingcashtitle_label)
        self.g_openingcash_label = QtWidgets.QLabel(self.g_openingcash_frame)
        self.g_openingcash_label.setMinimumSize(QtCore.QSize(200, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_openingcash_label.setFont(font)
        self.g_openingcash_label.setText("")
        self.g_openingcash_label.setObjectName("g_openingcash_label")
        self.horizontalLayout_4.addWidget(self.g_openingcash_label)
        self.verticalLayout_2.addWidget(self.g_openingcash_frame)
        self.horizontalLayout_5.addWidget(self.g_openedcash_frame)
        self.verticalLayout_3.addWidget(self.g_registerdetails_groupbox)
        self.g_cashdrawertotals_groupbox = QtWidgets.QGroupBox(CloseRegisterDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_cashdrawertotals_groupbox.sizePolicy().hasHeightForWidth())
        self.g_cashdrawertotals_groupbox.setSizePolicy(sizePolicy)
        self.g_cashdrawertotals_groupbox.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_cashdrawertotals_groupbox.setFont(font)
        self.g_cashdrawertotals_groupbox.setObjectName("g_cashdrawertotals_groupbox")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_cashdrawertotals_groupbox)
        self.verticalLayout_4.setContentsMargins(-1, 12, -1, -1)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_cashdrawertotals_table = QtWidgets.QTableWidget(self.g_cashdrawertotals_groupbox)
        self.g_cashdrawertotals_table.setMinimumSize(QtCore.QSize(0, 0))
        self.g_cashdrawertotals_table.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_cashdrawertotals_table.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_cashdrawertotals_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_cashdrawertotals_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_cashdrawertotals_table.setSizeAdjustPolicy(QtWidgets.QAbstractScrollArea.AdjustToContentsOnFirstShow)
        self.g_cashdrawertotals_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_cashdrawertotals_table.setAlternatingRowColors(True)
        self.g_cashdrawertotals_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_cashdrawertotals_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_cashdrawertotals_table.setRowCount(1)
        self.g_cashdrawertotals_table.setObjectName("g_cashdrawertotals_table")
        self.g_cashdrawertotals_table.setColumnCount(2)
        item = QtWidgets.QTableWidgetItem()
        self.g_cashdrawertotals_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_cashdrawertotals_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_cashdrawertotals_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_cashdrawertotals_table.setItem(0, 1, item)
        self.g_cashdrawertotals_table.horizontalHeader().setVisible(True)
        self.g_cashdrawertotals_table.horizontalHeader().setDefaultSectionSize(220)
        self.g_cashdrawertotals_table.horizontalHeader().setStretchLastSection(True)
        self.g_cashdrawertotals_table.verticalHeader().setVisible(False)
        self.verticalLayout_4.addWidget(self.g_cashdrawertotals_table)
        self.verticalLayout_3.addWidget(self.g_cashdrawertotals_groupbox)
        self.g_countyourdrawer_groupbox = QtWidgets.QGroupBox(CloseRegisterDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_countyourdrawer_groupbox.sizePolicy().hasHeightForWidth())
        self.g_countyourdrawer_groupbox.setSizePolicy(sizePolicy)
        self.g_countyourdrawer_groupbox.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_countyourdrawer_groupbox.setFont(font)
        self.g_countyourdrawer_groupbox.setObjectName("g_countyourdrawer_groupbox")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.g_countyourdrawer_groupbox)
        self.verticalLayout_5.setContentsMargins(-1, 12, -1, -1)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.g_countyourdrawer_table = QtWidgets.QTableWidget(self.g_countyourdrawer_groupbox)
        self.g_countyourdrawer_table.setMinimumSize(QtCore.QSize(0, 0))
        self.g_countyourdrawer_table.setMaximumSize(QtCore.QSize(16777215, 16777215))
        palette = QtGui.QPalette()
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ButtonText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(51, 153, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Highlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ButtonText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Highlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(120, 120, 120))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ButtonText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(51, 153, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Highlight, brush)
        self.g_countyourdrawer_table.setPalette(palette)
        self.g_countyourdrawer_table.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_countyourdrawer_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_countyourdrawer_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_countyourdrawer_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_countyourdrawer_table.setSizeAdjustPolicy(QtWidgets.QAbstractScrollArea.AdjustToContentsOnFirstShow)
        self.g_countyourdrawer_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_countyourdrawer_table.setAlternatingRowColors(True)
        self.g_countyourdrawer_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_countyourdrawer_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_countyourdrawer_table.setShowGrid(True)
        self.g_countyourdrawer_table.setObjectName("g_countyourdrawer_table")
        self.g_countyourdrawer_table.setColumnCount(3)
        self.g_countyourdrawer_table.setRowCount(3)
        item = QtWidgets.QTableWidgetItem()
        self.g_countyourdrawer_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_countyourdrawer_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_countyourdrawer_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_countyourdrawer_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_countyourdrawer_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_countyourdrawer_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_countyourdrawer_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_countyourdrawer_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_countyourdrawer_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_countyourdrawer_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setUnderline(True)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 255))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_countyourdrawer_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_countyourdrawer_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_countyourdrawer_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_countyourdrawer_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_countyourdrawer_table.setItem(2, 2, item)
        self.g_countyourdrawer_table.horizontalHeader().setVisible(True)
        self.g_countyourdrawer_table.horizontalHeader().setCascadingSectionResizes(False)
        self.g_countyourdrawer_table.horizontalHeader().setDefaultSectionSize(200)
        self.g_countyourdrawer_table.horizontalHeader().setHighlightSections(False)
        self.g_countyourdrawer_table.horizontalHeader().setStretchLastSection(True)
        self.g_countyourdrawer_table.verticalHeader().setVisible(False)
        self.g_countyourdrawer_table.verticalHeader().setHighlightSections(False)
        self.verticalLayout_5.addWidget(self.g_countyourdrawer_table)
        self.frame = QtWidgets.QFrame(self.g_countyourdrawer_groupbox)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.verticalLayout_5.addWidget(self.frame)
        self.verticalLayout_3.addWidget(self.g_countyourdrawer_groupbox)
        self.g_withdrawthisamount_groupbox = QtWidgets.QGroupBox(CloseRegisterDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_withdrawthisamount_groupbox.sizePolicy().hasHeightForWidth())
        self.g_withdrawthisamount_groupbox.setSizePolicy(sizePolicy)
        self.g_withdrawthisamount_groupbox.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_withdrawthisamount_groupbox.setFont(font)
        self.g_withdrawthisamount_groupbox.setObjectName("g_withdrawthisamount_groupbox")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.g_withdrawthisamount_groupbox)
        self.verticalLayout_6.setContentsMargins(-1, 12, -1, -1)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.g_withdrawthisamount_table = QtWidgets.QTableWidget(self.g_withdrawthisamount_groupbox)
        self.g_withdrawthisamount_table.setMinimumSize(QtCore.QSize(0, 0))
        self.g_withdrawthisamount_table.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_withdrawthisamount_table.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_withdrawthisamount_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_withdrawthisamount_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_withdrawthisamount_table.setSizeAdjustPolicy(QtWidgets.QAbstractScrollArea.AdjustToContentsOnFirstShow)
        self.g_withdrawthisamount_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_withdrawthisamount_table.setAlternatingRowColors(True)
        self.g_withdrawthisamount_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_withdrawthisamount_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_withdrawthisamount_table.setObjectName("g_withdrawthisamount_table")
        self.g_withdrawthisamount_table.setColumnCount(2)
        self.g_withdrawthisamount_table.setRowCount(4)
        item = QtWidgets.QTableWidgetItem()
        self.g_withdrawthisamount_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_withdrawthisamount_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_withdrawthisamount_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_withdrawthisamount_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_withdrawthisamount_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_withdrawthisamount_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_withdrawthisamount_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_withdrawthisamount_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_withdrawthisamount_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_withdrawthisamount_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_withdrawthisamount_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_withdrawthisamount_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_withdrawthisamount_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_withdrawthisamount_table.setItem(3, 1, item)
        self.g_withdrawthisamount_table.horizontalHeader().setVisible(True)
        self.g_withdrawthisamount_table.horizontalHeader().setDefaultSectionSize(220)
        self.g_withdrawthisamount_table.horizontalHeader().setStretchLastSection(True)
        self.g_withdrawthisamount_table.verticalHeader().setVisible(False)
        self.verticalLayout_6.addWidget(self.g_withdrawthisamount_table)
        self.frame_2 = QtWidgets.QFrame(self.g_withdrawthisamount_groupbox)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.g_printoptions_label = QtWidgets.QLabel(self.frame_2)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setUnderline(True)
        font.setWeight(75)
        self.g_printoptions_label.setFont(font)
        self.g_printoptions_label.setObjectName("g_printoptions_label")
        self.verticalLayout_7.addWidget(self.g_printoptions_label)
        self.g_closing_receipt_checkbox = QtWidgets.QCheckBox(self.frame_2)
        self.g_closing_receipt_checkbox.setObjectName("g_closing_receipt_checkbox")
        self.verticalLayout_7.addWidget(self.g_closing_receipt_checkbox)
        self.g_journal_receipt_checkbox = QtWidgets.QCheckBox(self.frame_2)
        self.g_journal_receipt_checkbox.setObjectName("g_journal_receipt_checkbox")
        self.verticalLayout_7.addWidget(self.g_journal_receipt_checkbox)
        self.verticalLayout_6.addWidget(self.frame_2)
        self.verticalLayout_3.addWidget(self.g_withdrawthisamount_groupbox)
        self.g_dialogbuttons_frame = QtWidgets.QFrame(CloseRegisterDialog)
        self.g_dialogbuttons_frame.setStyleSheet("#g_dialog_buttons_frame, #g_dialog_buttons_frame {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"")
        self.g_dialogbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_dialogbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_dialogbuttons_frame.setObjectName("g_dialogbuttons_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_dialogbuttons_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(12)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        spacerItem1 = QtWidgets.QSpacerItem(116, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem1)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(175, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/sales_screen/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon1)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_6.addWidget(self.g_cancel_button)
        self.g_back_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_back_button.setMinimumSize(QtCore.QSize(175, 40))
        self.g_back_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/closeregister_dialog/back"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_back_button.setIcon(icon2)
        self.g_back_button.setIconSize(QtCore.QSize(24, 24))
        self.g_back_button.setObjectName("g_back_button")
        self.horizontalLayout_6.addWidget(self.g_back_button)
        self.g_next_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_next_button.setMinimumSize(QtCore.QSize(175, 40))
        self.g_next_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/closeregister_dialog/next"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_next_button.setIcon(icon3)
        self.g_next_button.setIconSize(QtCore.QSize(24, 24))
        self.g_next_button.setObjectName("g_next_button")
        self.horizontalLayout_6.addWidget(self.g_next_button)
        self.g_closeregister_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_closeregister_button.setMinimumSize(QtCore.QSize(175, 40))
        self.g_closeregister_button.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_closeregister_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_closeregister_button.setStyleSheet("")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/sales_screen/save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_closeregister_button.setIcon(icon4)
        self.g_closeregister_button.setIconSize(QtCore.QSize(24, 24))
        self.g_closeregister_button.setObjectName("g_closeregister_button")
        self.horizontalLayout_6.addWidget(self.g_closeregister_button)
        self.verticalLayout_3.addWidget(self.g_dialogbuttons_frame)

        self.retranslateUi(CloseRegisterDialog)
        QtCore.QMetaObject.connectSlotsByName(CloseRegisterDialog)

    def retranslateUi(self, CloseRegisterDialog):
        _translate = QtCore.QCoreApplication.translate
        CloseRegisterDialog.setWindowTitle(_translate("CloseRegisterDialog", "Closing Cash Tray"))
        CloseRegisterDialog.setProperty("invalid_value_msg_text", _translate("CloseRegisterDialog", "Please enter a valid numeric value."))
        CloseRegisterDialog.setProperty("invalid_value_title_text", _translate("CloseRegisterDialog", "Error"))
        CloseRegisterDialog.setProperty("closing_register_msg_text", _translate("CloseRegisterDialog", "Closing register ..."))
        CloseRegisterDialog.setProperty("cash_withdrawal_unavailable_msg_text", _translate("CloseRegisterDialog", "There is no available cash \n"
"to withdraw at this time."))
        CloseRegisterDialog.setProperty("cash_withdrawal_unavailable_title_text", _translate("CloseRegisterDialog", "Cash Withdrawal Unavailable"))
        CloseRegisterDialog.setProperty("less_than_opening_msg_text", _translate("CloseRegisterDialog", "Cash amount cannot be less\n"
"than the opening amount."))
        CloseRegisterDialog.setProperty("exceeds_cash_available_msg_text", _translate("CloseRegisterDialog", "The cash amount cannot exceed the\n"
"amount available for withdrawal."))
        CloseRegisterDialog.setProperty("current_session_url_text", _translate("CloseRegisterDialog", "/apps/any/sessions/mine"))
        CloseRegisterDialog.setProperty("printing_receipts_msg_text", _translate("CloseRegisterDialog", "Printing receipts ..."))
        CloseRegisterDialog.setProperty("tray_session_payments_url_text", _translate("CloseRegisterDialog", "/apps/any/queries/read__qpt__tray_session_payments"))
        CloseRegisterDialog.setProperty("updating_settings_msg_text", _translate("CloseRegisterDialog", "Updating settings ..."))
        CloseRegisterDialog.setProperty("update_gen_settings_url_text", _translate("CloseRegisterDialog", "/apps/any/queries/update__gen__settings"))
        CloseRegisterDialog.setProperty("create_gen_settings_url_text", _translate("CloseRegisterDialog", "/apps/any/queries/create__gen__settings"))
        CloseRegisterDialog.setProperty("recommended_withdraw_amount_exceeded_msg_text", _translate("CloseRegisterDialog", "Entered amount exceeds the recommended withdraw amount %.2f.\n"
"Are you sure you want to continue?"))
        CloseRegisterDialog.setProperty("warning_title_text", _translate("CloseRegisterDialog", "Warning"))
        self.g_main_label.setText(_translate("CloseRegisterDialog", "Closing Cash Tray"))
        self.g_registerdetails_groupbox.setTitle(_translate("CloseRegisterDialog", "Tray Details"))
        self.g_registertitle_label.setText(_translate("CloseRegisterDialog", "Register"))
        self.g_locationtitle_label.setText(_translate("CloseRegisterDialog", "Location"))
        self.g_lastopenedtitle_label.setText(_translate("CloseRegisterDialog", "Last Opened"))
        self.g_openingcashtitle_label.setText(_translate("CloseRegisterDialog", "Opening Cash"))
        self.g_cashdrawertotals_groupbox.setTitle(_translate("CloseRegisterDialog", "(1/3) Expected Drawer Totals"))
        item = self.g_cashdrawertotals_table.horizontalHeaderItem(0)
        item.setText(_translate("CloseRegisterDialog", "Payment Type"))
        item = self.g_cashdrawertotals_table.horizontalHeaderItem(1)
        item.setText(_translate("CloseRegisterDialog", "Amount"))
        __sortingEnabled = self.g_cashdrawertotals_table.isSortingEnabled()
        self.g_cashdrawertotals_table.setSortingEnabled(False)
        item = self.g_cashdrawertotals_table.item(0, 0)
        item.setText(_translate("CloseRegisterDialog", "Total"))
        self.g_cashdrawertotals_table.setSortingEnabled(__sortingEnabled)
        self.g_countyourdrawer_groupbox.setTitle(_translate("CloseRegisterDialog", "(2/3) Count Your Drawer"))
        item = self.g_countyourdrawer_table.verticalHeaderItem(0)
        item.setText(_translate("CloseRegisterDialog", "New Row"))
        item = self.g_countyourdrawer_table.verticalHeaderItem(1)
        item.setText(_translate("CloseRegisterDialog", "New Row"))
        item = self.g_countyourdrawer_table.verticalHeaderItem(2)
        item.setText(_translate("CloseRegisterDialog", "New Row"))
        item = self.g_countyourdrawer_table.horizontalHeaderItem(0)
        item.setText(_translate("CloseRegisterDialog", "Method"))
        item = self.g_countyourdrawer_table.horizontalHeaderItem(1)
        item.setText(_translate("CloseRegisterDialog", "Amount"))
        item = self.g_countyourdrawer_table.horizontalHeaderItem(2)
        item.setText(_translate("CloseRegisterDialog", "Over/(Under)"))
        __sortingEnabled = self.g_countyourdrawer_table.isSortingEnabled()
        self.g_countyourdrawer_table.setSortingEnabled(False)
        item = self.g_countyourdrawer_table.item(0, 0)
        item.setText(_translate("CloseRegisterDialog", "Cash"))
        item = self.g_countyourdrawer_table.item(1, 0)
        item.setText(_translate("CloseRegisterDialog", "Credit Card"))
        item = self.g_countyourdrawer_table.item(2, 0)
        item.setText(_translate("CloseRegisterDialog", "Total"))
        self.g_countyourdrawer_table.setSortingEnabled(__sortingEnabled)
        self.g_withdrawthisamount_groupbox.setTitle(_translate("CloseRegisterDialog", "(3/3) Withdraw This Amount"))
        item = self.g_withdrawthisamount_table.verticalHeaderItem(0)
        item.setText(_translate("CloseRegisterDialog", "New Row"))
        item = self.g_withdrawthisamount_table.verticalHeaderItem(1)
        item.setText(_translate("CloseRegisterDialog", "New Row"))
        item = self.g_withdrawthisamount_table.verticalHeaderItem(2)
        item.setText(_translate("CloseRegisterDialog", "New Row"))
        item = self.g_withdrawthisamount_table.verticalHeaderItem(3)
        item.setText(_translate("CloseRegisterDialog", "New Row"))
        item = self.g_withdrawthisamount_table.horizontalHeaderItem(0)
        item.setText(_translate("CloseRegisterDialog", "Method"))
        item = self.g_withdrawthisamount_table.horizontalHeaderItem(1)
        item.setText(_translate("CloseRegisterDialog", "Amount"))
        __sortingEnabled = self.g_withdrawthisamount_table.isSortingEnabled()
        self.g_withdrawthisamount_table.setSortingEnabled(False)
        item = self.g_withdrawthisamount_table.item(0, 0)
        item.setText(_translate("CloseRegisterDialog", "Cash"))
        item = self.g_withdrawthisamount_table.item(1, 0)
        item.setText(_translate("CloseRegisterDialog", "Credit Card"))
        item = self.g_withdrawthisamount_table.item(2, 0)
        item.setText(_translate("CloseRegisterDialog", "Check"))
        item = self.g_withdrawthisamount_table.item(3, 0)
        item.setText(_translate("CloseRegisterDialog", "Total"))
        self.g_withdrawthisamount_table.setSortingEnabled(__sortingEnabled)
        self.g_printoptions_label.setText(_translate("CloseRegisterDialog", "Print Options"))
        self.g_closing_receipt_checkbox.setText(_translate("CloseRegisterDialog", "Closing Reports"))
        self.g_journal_receipt_checkbox.setText(_translate("CloseRegisterDialog", "Journal"))
        self.g_cancel_button.setText(_translate("CloseRegisterDialog", "  Cancel"))
        self.g_cancel_button.setShortcut(_translate("CloseRegisterDialog", "Enter"))
        self.g_back_button.setText(_translate("CloseRegisterDialog", "  Back"))
        self.g_next_button.setText(_translate("CloseRegisterDialog", "  Next"))
        self.g_closeregister_button.setText(_translate("CloseRegisterDialog", "  Close Tray"))
        self.g_closeregister_button.setShortcut(_translate("CloseRegisterDialog", "Enter"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    CloseRegisterDialog = QtWidgets.QDialog()
    ui = Ui_CloseRegisterDialog()
    ui.setupUi(CloseRegisterDialog)
    CloseRegisterDialog.show()
    sys.exit(app.exec_())
