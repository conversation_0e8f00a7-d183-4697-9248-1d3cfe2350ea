# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managepermissions_modaldialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ManagePermissions(object):
    def setupUi(self, ManagePermissions):
        ManagePermissions.setObjectName("ManagePermissions")
        ManagePermissions.resize(624, 731)
        ManagePermissions.setMinimumSize(QtCore.QSize(0, 350))
        ManagePermissions.setMaximumSize(QtCore.QSize(16777215, 16777215))
        ManagePermissions.setStyleSheet("#ManagePermissions {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_selectsupplier_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border:1px solid #073c83;\n"
"    border-radius:10px;\n"
"    background-color:#DDE4E9;\n"
"}\n"
"\n"
"QLabel {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font: 17px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_general_label,\n"
"#g_cashregister_label,\n"
"#g_pettracker_label,\n"
"#g_products_label,\n"
"#g_pr_label {\n"
"    font: 17px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
".QScrollArea {\n"
"    background-color: #f4f4f4;\n"
"    border-radius:5px;\n"
"    border:1px solid #073c83;\n"
"    padding:4px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/locationmanagement_screen/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"\n"
"QTabWidget::tab-bar {\n"
"   left: 0;\n"
"}\n"
"\n"
"QTabWidget {\n"
"    border:0px;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"#g_general_tab,\n"
"#g_register_tab,\n"
"#g_pettracker_tab,\n"
"#g_products_tab,\n"
"#g_pr_tab {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius:0px;\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"#g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(ManagePermissions)
        self.verticalLayout_2.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_selectsupplier_frame = QtWidgets.QFrame(ManagePermissions)
        self.g_selectsupplier_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_selectsupplier_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_selectsupplier_frame.setObjectName("g_selectsupplier_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_selectsupplier_frame)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_titlepagenav_frame = QtWidgets.QFrame(self.g_selectsupplier_frame)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_33 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_33.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_33.setObjectName("horizontalLayout_33")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout_12 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_12.setObjectName("verticalLayout_12")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_12.addWidget(self.g_main_label)
        self.horizontalLayout_33.addWidget(self.g_title_frame)
        self.verticalLayout.addWidget(self.g_titlepagenav_frame)
        self.g_main_tabWidget = QtWidgets.QTabWidget(self.g_selectsupplier_frame)
        self.g_main_tabWidget.setObjectName("g_main_tabWidget")
        self.g_general_tab = QtWidgets.QWidget()
        self.g_general_tab.setObjectName("g_general_tab")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_general_tab)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_integration_frame_2 = QtWidgets.QFrame(self.g_general_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_integration_frame_2.sizePolicy().hasHeightForWidth())
        self.g_integration_frame_2.setSizePolicy(sizePolicy)
        self.g_integration_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_integration_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_integration_frame_2.setObjectName("g_integration_frame_2")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_integration_frame_2)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_custexport_label = QtWidgets.QLabel(self.g_integration_frame_2)
        self.g_custexport_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_custexport_label.setMaximumSize(QtCore.QSize(170, 16777215))
        self.g_custexport_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_custexport_label.setObjectName("g_custexport_label")
        self.horizontalLayout_6.addWidget(self.g_custexport_label)
        self.g_general_customerexport_combobox = QtWidgets.QComboBox(self.g_integration_frame_2)
        self.g_general_customerexport_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_general_customerexport_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_general_customerexport_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_general_customerexport_combobox.setObjectName("g_general_customerexport_combobox")
        self.g_general_customerexport_combobox.addItem("")
        self.g_general_customerexport_combobox.addItem("")
        self.horizontalLayout_6.addWidget(self.g_general_customerexport_combobox)
        self.verticalLayout_3.addWidget(self.g_integration_frame_2)
        self.g_integration_frame = QtWidgets.QFrame(self.g_general_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_integration_frame.sizePolicy().hasHeightForWidth())
        self.g_integration_frame.setSizePolicy(sizePolicy)
        self.g_integration_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_integration_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_integration_frame.setObjectName("g_integration_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_integration_frame)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_cost_label = QtWidgets.QLabel(self.g_integration_frame)
        self.g_cost_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_cost_label.setMaximumSize(QtCore.QSize(170, 16777215))
        self.g_cost_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cost_label.setObjectName("g_cost_label")
        self.horizontalLayout_5.addWidget(self.g_cost_label)
        self.g_general_costmarkupmargin_combobox = QtWidgets.QComboBox(self.g_integration_frame)
        self.g_general_costmarkupmargin_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_general_costmarkupmargin_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_general_costmarkupmargin_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_general_costmarkupmargin_combobox.setObjectName("g_general_costmarkupmargin_combobox")
        self.g_general_costmarkupmargin_combobox.addItem("")
        self.g_general_costmarkupmargin_combobox.addItem("")
        self.g_general_costmarkupmargin_combobox.addItem("")
        self.horizontalLayout_5.addWidget(self.g_general_costmarkupmargin_combobox)
        self.verticalLayout_3.addWidget(self.g_integration_frame)
        self.g_subscription_frame = QtWidgets.QFrame(self.g_general_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_subscription_frame.sizePolicy().hasHeightForWidth())
        self.g_subscription_frame.setSizePolicy(sizePolicy)
        self.g_subscription_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_subscription_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_subscription_frame.setObjectName("g_subscription_frame")
        self.horizontalLayout_31 = QtWidgets.QHBoxLayout(self.g_subscription_frame)
        self.horizontalLayout_31.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_31.setObjectName("horizontalLayout_31")
        self.g_subscription_label = QtWidgets.QLabel(self.g_subscription_frame)
        self.g_subscription_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_subscription_label.setMaximumSize(QtCore.QSize(170, 16777215))
        self.g_subscription_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_subscription_label.setObjectName("g_subscription_label")
        self.horizontalLayout_31.addWidget(self.g_subscription_label)
        self.g_subscription_combobox = QtWidgets.QComboBox(self.g_subscription_frame)
        self.g_subscription_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_subscription_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_subscription_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_subscription_combobox.setObjectName("g_subscription_combobox")
        self.g_subscription_combobox.addItem("")
        self.g_subscription_combobox.addItem("")
        self.g_subscription_combobox.addItem("")
        self.horizontalLayout_31.addWidget(self.g_subscription_combobox)
        self.verticalLayout_3.addWidget(self.g_subscription_frame)
        self.g_deletepetesigndocuments_frame = QtWidgets.QFrame(self.g_general_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_deletepetesigndocuments_frame.sizePolicy().hasHeightForWidth())
        self.g_deletepetesigndocuments_frame.setSizePolicy(sizePolicy)
        self.g_deletepetesigndocuments_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_deletepetesigndocuments_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_deletepetesigndocuments_frame.setObjectName("g_deletepetesigndocuments_frame")
        self.horizontalLayout_37 = QtWidgets.QHBoxLayout(self.g_deletepetesigndocuments_frame)
        self.horizontalLayout_37.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_37.setObjectName("horizontalLayout_37")
        self.g_deletepetesigndocuments_label = QtWidgets.QLabel(self.g_deletepetesigndocuments_frame)
        self.g_deletepetesigndocuments_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_deletepetesigndocuments_label.setMaximumSize(QtCore.QSize(170, 16777215))
        self.g_deletepetesigndocuments_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_deletepetesigndocuments_label.setObjectName("g_deletepetesigndocuments_label")
        self.horizontalLayout_37.addWidget(self.g_deletepetesigndocuments_label)
        self.g_deletepetesigndocuments_combobox = QtWidgets.QComboBox(self.g_deletepetesigndocuments_frame)
        self.g_deletepetesigndocuments_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_deletepetesigndocuments_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_deletepetesigndocuments_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_deletepetesigndocuments_combobox.setObjectName("g_deletepetesigndocuments_combobox")
        self.g_deletepetesigndocuments_combobox.addItem("")
        self.g_deletepetesigndocuments_combobox.addItem("")
        self.g_deletepetesigndocuments_combobox.addItem("")
        self.horizontalLayout_37.addWidget(self.g_deletepetesigndocuments_combobox)
        self.verticalLayout_3.addWidget(self.g_deletepetesigndocuments_frame)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_3.addItem(spacerItem)
        self.g_main_tabWidget.addTab(self.g_general_tab, "")
        self.g_register_tab = QtWidgets.QWidget()
        self.g_register_tab.setObjectName("g_register_tab")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_register_tab)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_integration_frame_5 = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_integration_frame_5.sizePolicy().hasHeightForWidth())
        self.g_integration_frame_5.setSizePolicy(sizePolicy)
        self.g_integration_frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_integration_frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_integration_frame_5.setObjectName("g_integration_frame_5")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.g_integration_frame_5)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.g_closetray_label = QtWidgets.QLabel(self.g_integration_frame_5)
        self.g_closetray_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_closetray_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_closetray_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_closetray_label.setObjectName("g_closetray_label")
        self.horizontalLayout_9.addWidget(self.g_closetray_label)
        self.g_cr_closetray_combobox = QtWidgets.QComboBox(self.g_integration_frame_5)
        self.g_cr_closetray_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_cr_closetray_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_cr_closetray_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_cr_closetray_combobox.setObjectName("g_cr_closetray_combobox")
        self.g_cr_closetray_combobox.addItem("")
        self.g_cr_closetray_combobox.addItem("")
        self.horizontalLayout_9.addWidget(self.g_cr_closetray_combobox)
        self.verticalLayout_4.addWidget(self.g_integration_frame_5)
        self.g_integration_frame_4 = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_integration_frame_4.sizePolicy().hasHeightForWidth())
        self.g_integration_frame_4.setSizePolicy(sizePolicy)
        self.g_integration_frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_integration_frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_integration_frame_4.setObjectName("g_integration_frame_4")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_integration_frame_4)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_opentray_label = QtWidgets.QLabel(self.g_integration_frame_4)
        self.g_opentray_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_opentray_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_opentray_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_opentray_label.setObjectName("g_opentray_label")
        self.horizontalLayout_8.addWidget(self.g_opentray_label)
        self.g_cr_opentray_combobox = QtWidgets.QComboBox(self.g_integration_frame_4)
        self.g_cr_opentray_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_cr_opentray_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_cr_opentray_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_cr_opentray_combobox.setObjectName("g_cr_opentray_combobox")
        self.g_cr_opentray_combobox.addItem("")
        self.g_cr_opentray_combobox.addItem("")
        self.horizontalLayout_8.addWidget(self.g_cr_opentray_combobox)
        self.verticalLayout_4.addWidget(self.g_integration_frame_4)
        self.g_integration_frame_3 = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_integration_frame_3.sizePolicy().hasHeightForWidth())
        self.g_integration_frame_3.setSizePolicy(sizePolicy)
        self.g_integration_frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_integration_frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_integration_frame_3.setObjectName("g_integration_frame_3")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_integration_frame_3)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_pettycash_label = QtWidgets.QLabel(self.g_integration_frame_3)
        self.g_pettycash_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_pettycash_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_pettycash_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_pettycash_label.setObjectName("g_pettycash_label")
        self.horizontalLayout_7.addWidget(self.g_pettycash_label)
        self.g_cr_pettycash_combobox = QtWidgets.QComboBox(self.g_integration_frame_3)
        self.g_cr_pettycash_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_cr_pettycash_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_cr_pettycash_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_cr_pettycash_combobox.setObjectName("g_cr_pettycash_combobox")
        self.g_cr_pettycash_combobox.addItem("")
        self.g_cr_pettycash_combobox.addItem("")
        self.horizontalLayout_7.addWidget(self.g_cr_pettycash_combobox)
        self.verticalLayout_4.addWidget(self.g_integration_frame_3)
        self.g_integration_frame_6 = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_integration_frame_6.sizePolicy().hasHeightForWidth())
        self.g_integration_frame_6.setSizePolicy(sizePolicy)
        self.g_integration_frame_6.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_integration_frame_6.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_integration_frame_6.setObjectName("g_integration_frame_6")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.g_integration_frame_6)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.g_void_label = QtWidgets.QLabel(self.g_integration_frame_6)
        self.g_void_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_void_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_void_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_void_label.setObjectName("g_void_label")
        self.horizontalLayout_10.addWidget(self.g_void_label)
        self.g_cr_voidpayment_combobox = QtWidgets.QComboBox(self.g_integration_frame_6)
        self.g_cr_voidpayment_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_cr_voidpayment_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_cr_voidpayment_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_cr_voidpayment_combobox.setObjectName("g_cr_voidpayment_combobox")
        self.g_cr_voidpayment_combobox.addItem("")
        self.g_cr_voidpayment_combobox.addItem("")
        self.horizontalLayout_10.addWidget(self.g_cr_voidpayment_combobox)
        self.verticalLayout_4.addWidget(self.g_integration_frame_6)
        self.g_integration_frame_7 = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_integration_frame_7.sizePolicy().hasHeightForWidth())
        self.g_integration_frame_7.setSizePolicy(sizePolicy)
        self.g_integration_frame_7.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_integration_frame_7.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_integration_frame_7.setObjectName("g_integration_frame_7")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.g_integration_frame_7)
        self.horizontalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.g_change_label = QtWidgets.QLabel(self.g_integration_frame_7)
        self.g_change_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_change_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_change_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_change_label.setObjectName("g_change_label")
        self.horizontalLayout_12.addWidget(self.g_change_label)
        self.g_cr_makechangewrongchange_combobox = QtWidgets.QComboBox(self.g_integration_frame_7)
        self.g_cr_makechangewrongchange_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_cr_makechangewrongchange_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_cr_makechangewrongchange_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_cr_makechangewrongchange_combobox.setObjectName("g_cr_makechangewrongchange_combobox")
        self.g_cr_makechangewrongchange_combobox.addItem("")
        self.g_cr_makechangewrongchange_combobox.addItem("")
        self.horizontalLayout_12.addWidget(self.g_cr_makechangewrongchange_combobox)
        self.verticalLayout_4.addWidget(self.g_integration_frame_7)
        self.g_taxexemptions_frame = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_taxexemptions_frame.sizePolicy().hasHeightForWidth())
        self.g_taxexemptions_frame.setSizePolicy(sizePolicy)
        self.g_taxexemptions_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_taxexemptions_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_taxexemptions_frame.setObjectName("g_taxexemptions_frame")
        self.horizontalLayout_25 = QtWidgets.QHBoxLayout(self.g_taxexemptions_frame)
        self.horizontalLayout_25.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_25.setObjectName("horizontalLayout_25")
        self.g_taxexemptions_label = QtWidgets.QLabel(self.g_taxexemptions_frame)
        self.g_taxexemptions_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_taxexemptions_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_taxexemptions_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_taxexemptions_label.setObjectName("g_taxexemptions_label")
        self.horizontalLayout_25.addWidget(self.g_taxexemptions_label)
        self.g_taxexemptions_combobox = QtWidgets.QComboBox(self.g_taxexemptions_frame)
        self.g_taxexemptions_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_taxexemptions_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_taxexemptions_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_taxexemptions_combobox.setObjectName("g_taxexemptions_combobox")
        self.g_taxexemptions_combobox.addItem("")
        self.g_taxexemptions_combobox.addItem("")
        self.horizontalLayout_25.addWidget(self.g_taxexemptions_combobox)
        self.verticalLayout_4.addWidget(self.g_taxexemptions_frame)
        self.g_placeinvoiceonhold_frame = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_placeinvoiceonhold_frame.sizePolicy().hasHeightForWidth())
        self.g_placeinvoiceonhold_frame.setSizePolicy(sizePolicy)
        self.g_placeinvoiceonhold_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_placeinvoiceonhold_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_placeinvoiceonhold_frame.setObjectName("g_placeinvoiceonhold_frame")
        self.horizontalLayout_27 = QtWidgets.QHBoxLayout(self.g_placeinvoiceonhold_frame)
        self.horizontalLayout_27.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_27.setObjectName("horizontalLayout_27")
        self.g_placeinvoiceonhold_label = QtWidgets.QLabel(self.g_placeinvoiceonhold_frame)
        self.g_placeinvoiceonhold_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_placeinvoiceonhold_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_placeinvoiceonhold_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_placeinvoiceonhold_label.setObjectName("g_placeinvoiceonhold_label")
        self.horizontalLayout_27.addWidget(self.g_placeinvoiceonhold_label)
        self.g_placeinvoiceonhold_combobox = QtWidgets.QComboBox(self.g_placeinvoiceonhold_frame)
        self.g_placeinvoiceonhold_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_placeinvoiceonhold_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_placeinvoiceonhold_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_placeinvoiceonhold_combobox.setObjectName("g_placeinvoiceonhold_combobox")
        self.g_placeinvoiceonhold_combobox.addItem("")
        self.g_placeinvoiceonhold_combobox.addItem("")
        self.horizontalLayout_27.addWidget(self.g_placeinvoiceonhold_combobox)
        self.verticalLayout_4.addWidget(self.g_placeinvoiceonhold_frame)
        self.g_cr_canapprovediscount_frame = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_cr_canapprovediscount_frame.sizePolicy().hasHeightForWidth())
        self.g_cr_canapprovediscount_frame.setSizePolicy(sizePolicy)
        self.g_cr_canapprovediscount_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cr_canapprovediscount_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cr_canapprovediscount_frame.setObjectName("g_cr_canapprovediscount_frame")
        self.horizontalLayout_36 = QtWidgets.QHBoxLayout(self.g_cr_canapprovediscount_frame)
        self.horizontalLayout_36.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_36.setObjectName("horizontalLayout_36")
        self.g_cr_canapprovediscount_label = QtWidgets.QLabel(self.g_cr_canapprovediscount_frame)
        self.g_cr_canapprovediscount_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_cr_canapprovediscount_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_cr_canapprovediscount_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_cr_canapprovediscount_label.setObjectName("g_cr_canapprovediscount_label")
        self.horizontalLayout_36.addWidget(self.g_cr_canapprovediscount_label)
        self.g_cr_canapprovediscount_combobox = QtWidgets.QComboBox(self.g_cr_canapprovediscount_frame)
        self.g_cr_canapprovediscount_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_cr_canapprovediscount_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_cr_canapprovediscount_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_cr_canapprovediscount_combobox.setObjectName("g_cr_canapprovediscount_combobox")
        self.g_cr_canapprovediscount_combobox.addItem("")
        self.g_cr_canapprovediscount_combobox.addItem("")
        self.horizontalLayout_36.addWidget(self.g_cr_canapprovediscount_combobox)
        self.verticalLayout_4.addWidget(self.g_cr_canapprovediscount_frame)
        self.g_removecustomerfrominvoice_frame = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_removecustomerfrominvoice_frame.sizePolicy().hasHeightForWidth())
        self.g_removecustomerfrominvoice_frame.setSizePolicy(sizePolicy)
        self.g_removecustomerfrominvoice_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_removecustomerfrominvoice_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_removecustomerfrominvoice_frame.setObjectName("g_removecustomerfrominvoice_frame")
        self.horizontalLayout_28 = QtWidgets.QHBoxLayout(self.g_removecustomerfrominvoice_frame)
        self.horizontalLayout_28.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_28.setObjectName("horizontalLayout_28")
        self.g_removecustomerfrominvoice_label = QtWidgets.QLabel(self.g_removecustomerfrominvoice_frame)
        self.g_removecustomerfrominvoice_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_removecustomerfrominvoice_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_removecustomerfrominvoice_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_removecustomerfrominvoice_label.setObjectName("g_removecustomerfrominvoice_label")
        self.horizontalLayout_28.addWidget(self.g_removecustomerfrominvoice_label)
        self.g_removecustomerfrominvoice_combobox = QtWidgets.QComboBox(self.g_removecustomerfrominvoice_frame)
        self.g_removecustomerfrominvoice_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_removecustomerfrominvoice_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_removecustomerfrominvoice_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_removecustomerfrominvoice_combobox.setObjectName("g_removecustomerfrominvoice_combobox")
        self.g_removecustomerfrominvoice_combobox.addItem("")
        self.g_removecustomerfrominvoice_combobox.addItem("")
        self.horizontalLayout_28.addWidget(self.g_removecustomerfrominvoice_combobox)
        self.verticalLayout_4.addWidget(self.g_removecustomerfrominvoice_frame)
        self.g_removeproductfrominvoice_frame = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_removeproductfrominvoice_frame.sizePolicy().hasHeightForWidth())
        self.g_removeproductfrominvoice_frame.setSizePolicy(sizePolicy)
        self.g_removeproductfrominvoice_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_removeproductfrominvoice_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_removeproductfrominvoice_frame.setObjectName("g_removeproductfrominvoice_frame")
        self.horizontalLayout_29 = QtWidgets.QHBoxLayout(self.g_removeproductfrominvoice_frame)
        self.horizontalLayout_29.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_29.setObjectName("horizontalLayout_29")
        self.g_removeproductfrominvoice_label = QtWidgets.QLabel(self.g_removeproductfrominvoice_frame)
        self.g_removeproductfrominvoice_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_removeproductfrominvoice_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_removeproductfrominvoice_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_removeproductfrominvoice_label.setObjectName("g_removeproductfrominvoice_label")
        self.horizontalLayout_29.addWidget(self.g_removeproductfrominvoice_label)
        self.g_removeproductfrominvoice_combobox = QtWidgets.QComboBox(self.g_removeproductfrominvoice_frame)
        self.g_removeproductfrominvoice_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_removeproductfrominvoice_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_removeproductfrominvoice_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_removeproductfrominvoice_combobox.setObjectName("g_removeproductfrominvoice_combobox")
        self.g_removeproductfrominvoice_combobox.addItem("")
        self.g_removeproductfrominvoice_combobox.addItem("")
        self.horizontalLayout_29.addWidget(self.g_removeproductfrominvoice_combobox)
        self.verticalLayout_4.addWidget(self.g_removeproductfrominvoice_frame)
        self.g_bonusbucksbalance_frame = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bonusbucksbalance_frame.sizePolicy().hasHeightForWidth())
        self.g_bonusbucksbalance_frame.setSizePolicy(sizePolicy)
        self.g_bonusbucksbalance_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bonusbucksbalance_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bonusbucksbalance_frame.setObjectName("g_bonusbucksbalance_frame")
        self.horizontalLayout_30 = QtWidgets.QHBoxLayout(self.g_bonusbucksbalance_frame)
        self.horizontalLayout_30.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_30.setObjectName("horizontalLayout_30")
        self.g_bonusbucksbalance_label = QtWidgets.QLabel(self.g_bonusbucksbalance_frame)
        self.g_bonusbucksbalance_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_bonusbucksbalance_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_bonusbucksbalance_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_bonusbucksbalance_label.setObjectName("g_bonusbucksbalance_label")
        self.horizontalLayout_30.addWidget(self.g_bonusbucksbalance_label)
        self.g_bonusbucksbalance_combobox = QtWidgets.QComboBox(self.g_bonusbucksbalance_frame)
        self.g_bonusbucksbalance_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_bonusbucksbalance_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_bonusbucksbalance_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_bonusbucksbalance_combobox.setObjectName("g_bonusbucksbalance_combobox")
        self.g_bonusbucksbalance_combobox.addItem("")
        self.g_bonusbucksbalance_combobox.addItem("")
        self.horizontalLayout_30.addWidget(self.g_bonusbucksbalance_combobox)
        self.verticalLayout_4.addWidget(self.g_bonusbucksbalance_frame)
        self.g_frequentbuyer_frame = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_frequentbuyer_frame.sizePolicy().hasHeightForWidth())
        self.g_frequentbuyer_frame.setSizePolicy(sizePolicy)
        self.g_frequentbuyer_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_frequentbuyer_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_frequentbuyer_frame.setObjectName("g_frequentbuyer_frame")
        self.horizontalLayout_32 = QtWidgets.QHBoxLayout(self.g_frequentbuyer_frame)
        self.horizontalLayout_32.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_32.setObjectName("horizontalLayout_32")
        self.g_frequentbuyer_label = QtWidgets.QLabel(self.g_frequentbuyer_frame)
        self.g_frequentbuyer_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_frequentbuyer_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_frequentbuyer_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_frequentbuyer_label.setObjectName("g_frequentbuyer_label")
        self.horizontalLayout_32.addWidget(self.g_frequentbuyer_label)
        self.g_frequentbuyer_combobox = QtWidgets.QComboBox(self.g_frequentbuyer_frame)
        self.g_frequentbuyer_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_frequentbuyer_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_frequentbuyer_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_frequentbuyer_combobox.setObjectName("g_frequentbuyer_combobox")
        self.g_frequentbuyer_combobox.addItem("")
        self.g_frequentbuyer_combobox.addItem("")
        self.horizontalLayout_32.addWidget(self.g_frequentbuyer_combobox)
        self.verticalLayout_4.addWidget(self.g_frequentbuyer_frame)
        self.g_deleteorders_frame = QtWidgets.QFrame(self.g_register_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_deleteorders_frame.sizePolicy().hasHeightForWidth())
        self.g_deleteorders_frame.setSizePolicy(sizePolicy)
        self.g_deleteorders_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_deleteorders_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_deleteorders_frame.setObjectName("g_deleteorders_frame")
        self.horizontalLayout_38 = QtWidgets.QHBoxLayout(self.g_deleteorders_frame)
        self.horizontalLayout_38.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_38.setObjectName("horizontalLayout_38")
        self.g_deleteorders_label = QtWidgets.QLabel(self.g_deleteorders_frame)
        self.g_deleteorders_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_deleteorders_label.setMaximumSize(QtCore.QSize(265, 16777215))
        self.g_deleteorders_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_deleteorders_label.setObjectName("g_deleteorders_label")
        self.horizontalLayout_38.addWidget(self.g_deleteorders_label)
        self.g_deleteorders_combobox = QtWidgets.QComboBox(self.g_deleteorders_frame)
        self.g_deleteorders_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_deleteorders_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_deleteorders_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_deleteorders_combobox.setObjectName("g_deleteorders_combobox")
        self.g_deleteorders_combobox.addItem("")
        self.g_deleteorders_combobox.addItem("")
        self.g_deleteorders_combobox.addItem("")
        self.horizontalLayout_38.addWidget(self.g_deleteorders_combobox)
        self.verticalLayout_4.addWidget(self.g_deleteorders_frame)
        spacerItem1 = QtWidgets.QSpacerItem(20, 92, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem1)
        self.g_main_tabWidget.addTab(self.g_register_tab, "")
        self.g_pettracker_tab = QtWidgets.QWidget()
        self.g_pettracker_tab.setObjectName("g_pettracker_tab")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.g_pettracker_tab)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.g_pet_cost_frame = QtWidgets.QFrame(self.g_pettracker_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_pet_cost_frame.sizePolicy().hasHeightForWidth())
        self.g_pet_cost_frame.setSizePolicy(sizePolicy)
        self.g_pet_cost_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pet_cost_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pet_cost_frame.setObjectName("g_pet_cost_frame")
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout(self.g_pet_cost_frame)
        self.horizontalLayout_14.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.g_pet_cost_label = QtWidgets.QLabel(self.g_pet_cost_frame)
        self.g_pet_cost_label.setMinimumSize(QtCore.QSize(85, 0))
        self.g_pet_cost_label.setMaximumSize(QtCore.QSize(75, 16777215))
        self.g_pet_cost_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_pet_cost_label.setObjectName("g_pet_cost_label")
        self.horizontalLayout_14.addWidget(self.g_pet_cost_label)
        self.g_pt_cost_combobox = QtWidgets.QComboBox(self.g_pet_cost_frame)
        self.g_pt_cost_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pt_cost_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pt_cost_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_pt_cost_combobox.setObjectName("g_pt_cost_combobox")
        self.g_pt_cost_combobox.addItem("")
        self.g_pt_cost_combobox.addItem("")
        self.g_pt_cost_combobox.addItem("")
        self.horizontalLayout_14.addWidget(self.g_pt_cost_combobox)
        self.verticalLayout_5.addWidget(self.g_pet_cost_frame)
        self.g_pet_export_frame = QtWidgets.QFrame(self.g_pettracker_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_pet_export_frame.sizePolicy().hasHeightForWidth())
        self.g_pet_export_frame.setSizePolicy(sizePolicy)
        self.g_pet_export_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pet_export_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pet_export_frame.setObjectName("g_pet_export_frame")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.g_pet_export_frame)
        self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.g_pet_export_label = QtWidgets.QLabel(self.g_pet_export_frame)
        self.g_pet_export_label.setMinimumSize(QtCore.QSize(85, 0))
        self.g_pet_export_label.setMaximumSize(QtCore.QSize(75, 16777215))
        self.g_pet_export_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_pet_export_label.setObjectName("g_pet_export_label")
        self.horizontalLayout_13.addWidget(self.g_pet_export_label)
        self.g_pt_petexport_combobox = QtWidgets.QComboBox(self.g_pet_export_frame)
        self.g_pt_petexport_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pt_petexport_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pt_petexport_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_pt_petexport_combobox.setObjectName("g_pt_petexport_combobox")
        self.g_pt_petexport_combobox.addItem("")
        self.g_pt_petexport_combobox.addItem("")
        self.horizontalLayout_13.addWidget(self.g_pt_petexport_combobox)
        self.verticalLayout_5.addWidget(self.g_pet_export_frame)
        self.g_pet_paperwork_frame = QtWidgets.QFrame(self.g_pettracker_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_pet_paperwork_frame.sizePolicy().hasHeightForWidth())
        self.g_pet_paperwork_frame.setSizePolicy(sizePolicy)
        self.g_pet_paperwork_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pet_paperwork_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pet_paperwork_frame.setObjectName("g_pet_paperwork_frame")
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout(self.g_pet_paperwork_frame)
        self.horizontalLayout_15.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.g_pet_paperwork_label = QtWidgets.QLabel(self.g_pet_paperwork_frame)
        self.g_pet_paperwork_label.setMinimumSize(QtCore.QSize(85, 0))
        self.g_pet_paperwork_label.setMaximumSize(QtCore.QSize(75, 16777215))
        self.g_pet_paperwork_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_pet_paperwork_label.setObjectName("g_pet_paperwork_label")
        self.horizontalLayout_15.addWidget(self.g_pet_paperwork_label)
        self.g_pt_petpaperwork_combobox = QtWidgets.QComboBox(self.g_pet_paperwork_frame)
        self.g_pt_petpaperwork_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pt_petpaperwork_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pt_petpaperwork_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_pt_petpaperwork_combobox.setObjectName("g_pt_petpaperwork_combobox")
        self.g_pt_petpaperwork_combobox.addItem("")
        self.g_pt_petpaperwork_combobox.addItem("")
        self.horizontalLayout_15.addWidget(self.g_pt_petpaperwork_combobox)
        self.verticalLayout_5.addWidget(self.g_pet_paperwork_frame)
        spacerItem2 = QtWidgets.QSpacerItem(20, 203, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_5.addItem(spacerItem2)
        self.g_main_tabWidget.addTab(self.g_pettracker_tab, "")
        self.g_products_tab = QtWidgets.QWidget()
        self.g_products_tab.setObjectName("g_products_tab")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.g_products_tab)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.g_prod_adjustinventoryt_frame = QtWidgets.QFrame(self.g_products_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_prod_adjustinventoryt_frame.sizePolicy().hasHeightForWidth())
        self.g_prod_adjustinventoryt_frame.setSizePolicy(sizePolicy)
        self.g_prod_adjustinventoryt_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_prod_adjustinventoryt_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_prod_adjustinventoryt_frame.setObjectName("g_prod_adjustinventoryt_frame")
        self.horizontalLayout_20 = QtWidgets.QHBoxLayout(self.g_prod_adjustinventoryt_frame)
        self.horizontalLayout_20.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_20.setObjectName("horizontalLayout_20")
        self.g_prod_adjustinventory_label = QtWidgets.QLabel(self.g_prod_adjustinventoryt_frame)
        self.g_prod_adjustinventory_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_prod_adjustinventory_label.setMaximumSize(QtCore.QSize(170, 16777215))
        self.g_prod_adjustinventory_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_prod_adjustinventory_label.setObjectName("g_prod_adjustinventory_label")
        self.horizontalLayout_20.addWidget(self.g_prod_adjustinventory_label)
        self.g_prod_adjustinventory_combobox = QtWidgets.QComboBox(self.g_prod_adjustinventoryt_frame)
        self.g_prod_adjustinventory_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_prod_adjustinventory_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_prod_adjustinventory_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_prod_adjustinventory_combobox.setObjectName("g_prod_adjustinventory_combobox")
        self.g_prod_adjustinventory_combobox.addItem("")
        self.g_prod_adjustinventory_combobox.addItem("")
        self.horizontalLayout_20.addWidget(self.g_prod_adjustinventory_combobox)
        self.verticalLayout_6.addWidget(self.g_prod_adjustinventoryt_frame)
        self.g_prod_category_frame = QtWidgets.QFrame(self.g_products_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_prod_category_frame.sizePolicy().hasHeightForWidth())
        self.g_prod_category_frame.setSizePolicy(sizePolicy)
        self.g_prod_category_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_prod_category_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_prod_category_frame.setObjectName("g_prod_category_frame")
        self.horizontalLayout_23 = QtWidgets.QHBoxLayout(self.g_prod_category_frame)
        self.horizontalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_23.setObjectName("horizontalLayout_23")
        self.g_prod_category_label = QtWidgets.QLabel(self.g_prod_category_frame)
        self.g_prod_category_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_prod_category_label.setMaximumSize(QtCore.QSize(170, 16777215))
        self.g_prod_category_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_prod_category_label.setObjectName("g_prod_category_label")
        self.horizontalLayout_23.addWidget(self.g_prod_category_label)
        self.g_prod_category_combobox = QtWidgets.QComboBox(self.g_prod_category_frame)
        self.g_prod_category_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_prod_category_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_prod_category_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_prod_category_combobox.setObjectName("g_prod_category_combobox")
        self.g_prod_category_combobox.addItem("")
        self.g_prod_category_combobox.addItem("")
        self.horizontalLayout_23.addWidget(self.g_prod_category_combobox)
        self.verticalLayout_6.addWidget(self.g_prod_category_frame)
        self.g_prod_export_frame = QtWidgets.QFrame(self.g_products_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_prod_export_frame.sizePolicy().hasHeightForWidth())
        self.g_prod_export_frame.setSizePolicy(sizePolicy)
        self.g_prod_export_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_prod_export_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_prod_export_frame.setObjectName("g_prod_export_frame")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout(self.g_prod_export_frame)
        self.horizontalLayout_16.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.g_prod_export_label = QtWidgets.QLabel(self.g_prod_export_frame)
        self.g_prod_export_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_prod_export_label.setMaximumSize(QtCore.QSize(170, 16777215))
        self.g_prod_export_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_prod_export_label.setObjectName("g_prod_export_label")
        self.horizontalLayout_16.addWidget(self.g_prod_export_label)
        self.g_prod_productexport_combobox = QtWidgets.QComboBox(self.g_prod_export_frame)
        self.g_prod_productexport_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_prod_productexport_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_prod_productexport_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_prod_productexport_combobox.setObjectName("g_prod_productexport_combobox")
        self.g_prod_productexport_combobox.addItem("")
        self.g_prod_productexport_combobox.addItem("")
        self.horizontalLayout_16.addWidget(self.g_prod_productexport_combobox)
        self.verticalLayout_6.addWidget(self.g_prod_export_frame)
        self.g_prod_bundleexport_frame = QtWidgets.QFrame(self.g_products_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_prod_bundleexport_frame.sizePolicy().hasHeightForWidth())
        self.g_prod_bundleexport_frame.setSizePolicy(sizePolicy)
        self.g_prod_bundleexport_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_prod_bundleexport_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_prod_bundleexport_frame.setObjectName("g_prod_bundleexport_frame")
        self.horizontalLayout_26 = QtWidgets.QHBoxLayout(self.g_prod_bundleexport_frame)
        self.horizontalLayout_26.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_26.setObjectName("horizontalLayout_26")
        self.g_prod_bundleexport_label = QtWidgets.QLabel(self.g_prod_bundleexport_frame)
        self.g_prod_bundleexport_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_prod_bundleexport_label.setMaximumSize(QtCore.QSize(170, 16777215))
        self.g_prod_bundleexport_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_prod_bundleexport_label.setObjectName("g_prod_bundleexport_label")
        self.horizontalLayout_26.addWidget(self.g_prod_bundleexport_label)
        self.g_prod_bundleexport_combobox = QtWidgets.QComboBox(self.g_prod_bundleexport_frame)
        self.g_prod_bundleexport_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_prod_bundleexport_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_prod_bundleexport_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_prod_bundleexport_combobox.setObjectName("g_prod_bundleexport_combobox")
        self.g_prod_bundleexport_combobox.addItem("")
        self.g_prod_bundleexport_combobox.addItem("")
        self.horizontalLayout_26.addWidget(self.g_prod_bundleexport_combobox)
        self.verticalLayout_6.addWidget(self.g_prod_bundleexport_frame)
        self.g_prod_transfer_frame = QtWidgets.QFrame(self.g_products_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_prod_transfer_frame.sizePolicy().hasHeightForWidth())
        self.g_prod_transfer_frame.setSizePolicy(sizePolicy)
        self.g_prod_transfer_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_prod_transfer_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_prod_transfer_frame.setObjectName("g_prod_transfer_frame")
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout(self.g_prod_transfer_frame)
        self.horizontalLayout_17.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        self.g_prod_transfer_label = QtWidgets.QLabel(self.g_prod_transfer_frame)
        self.g_prod_transfer_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_prod_transfer_label.setMaximumSize(QtCore.QSize(170, 16777215))
        self.g_prod_transfer_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_prod_transfer_label.setObjectName("g_prod_transfer_label")
        self.horizontalLayout_17.addWidget(self.g_prod_transfer_label)
        self.g_prod_transfer_combobox = QtWidgets.QComboBox(self.g_prod_transfer_frame)
        self.g_prod_transfer_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_prod_transfer_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_prod_transfer_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_prod_transfer_combobox.setObjectName("g_prod_transfer_combobox")
        self.g_prod_transfer_combobox.addItem("")
        self.g_prod_transfer_combobox.addItem("")
        self.horizontalLayout_17.addWidget(self.g_prod_transfer_combobox)
        self.verticalLayout_6.addWidget(self.g_prod_transfer_frame)
        self.g_prod_inventory_frame = QtWidgets.QFrame(self.g_products_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_prod_inventory_frame.sizePolicy().hasHeightForWidth())
        self.g_prod_inventory_frame.setSizePolicy(sizePolicy)
        self.g_prod_inventory_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_prod_inventory_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_prod_inventory_frame.setObjectName("g_prod_inventory_frame")
        self.horizontalLayout_24 = QtWidgets.QHBoxLayout(self.g_prod_inventory_frame)
        self.horizontalLayout_24.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_24.setObjectName("horizontalLayout_24")
        self.g_prod_inventory_label = QtWidgets.QLabel(self.g_prod_inventory_frame)
        self.g_prod_inventory_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_prod_inventory_label.setMaximumSize(QtCore.QSize(170, 16777215))
        self.g_prod_inventory_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_prod_inventory_label.setObjectName("g_prod_inventory_label")
        self.horizontalLayout_24.addWidget(self.g_prod_inventory_label)
        self.g_prod_inventory_combobox = QtWidgets.QComboBox(self.g_prod_inventory_frame)
        self.g_prod_inventory_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_prod_inventory_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_prod_inventory_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_prod_inventory_combobox.setObjectName("g_prod_inventory_combobox")
        self.g_prod_inventory_combobox.addItem("")
        self.g_prod_inventory_combobox.addItem("")
        self.horizontalLayout_24.addWidget(self.g_prod_inventory_combobox)
        self.verticalLayout_6.addWidget(self.g_prod_inventory_frame)
        self.g_prod_reset_inventory_frame = QtWidgets.QFrame(self.g_products_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_prod_reset_inventory_frame.sizePolicy().hasHeightForWidth())
        self.g_prod_reset_inventory_frame.setSizePolicy(sizePolicy)
        self.g_prod_reset_inventory_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_prod_reset_inventory_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_prod_reset_inventory_frame.setObjectName("g_prod_reset_inventory_frame")
        self.horizontalLayout_21 = QtWidgets.QHBoxLayout(self.g_prod_reset_inventory_frame)
        self.horizontalLayout_21.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_21.setObjectName("horizontalLayout_21")
        self.g_prod_reset_inventory_label = QtWidgets.QLabel(self.g_prod_reset_inventory_frame)
        self.g_prod_reset_inventory_label.setMinimumSize(QtCore.QSize(170, 0))
        self.g_prod_reset_inventory_label.setMaximumSize(QtCore.QSize(170, 16777215))
        self.g_prod_reset_inventory_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_prod_reset_inventory_label.setObjectName("g_prod_reset_inventory_label")
        self.horizontalLayout_21.addWidget(self.g_prod_reset_inventory_label)
        self.g_prod_reset_inventory_combobox = QtWidgets.QComboBox(self.g_prod_reset_inventory_frame)
        self.g_prod_reset_inventory_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_prod_reset_inventory_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_prod_reset_inventory_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_prod_reset_inventory_combobox.setObjectName("g_prod_reset_inventory_combobox")
        self.g_prod_reset_inventory_combobox.addItem("")
        self.g_prod_reset_inventory_combobox.addItem("")
        self.horizontalLayout_21.addWidget(self.g_prod_reset_inventory_combobox)
        self.verticalLayout_6.addWidget(self.g_prod_reset_inventory_frame)
        spacerItem3 = QtWidgets.QSpacerItem(20, 129, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_6.addItem(spacerItem3)
        self.g_main_tabWidget.addTab(self.g_products_tab, "")
        self.g_pr_tab = QtWidgets.QWidget()
        self.g_pr_tab.setObjectName("g_pr_tab")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.g_pr_tab)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.g_pr_po_frame = QtWidgets.QFrame(self.g_pr_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_pr_po_frame.sizePolicy().hasHeightForWidth())
        self.g_pr_po_frame.setSizePolicy(sizePolicy)
        self.g_pr_po_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pr_po_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pr_po_frame.setObjectName("g_pr_po_frame")
        self.horizontalLayout_18 = QtWidgets.QHBoxLayout(self.g_pr_po_frame)
        self.horizontalLayout_18.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_18.setObjectName("horizontalLayout_18")
        self.g_pr_po_label = QtWidgets.QLabel(self.g_pr_po_frame)
        self.g_pr_po_label.setMinimumSize(QtCore.QSize(133, 0))
        self.g_pr_po_label.setMaximumSize(QtCore.QSize(133, 16777215))
        self.g_pr_po_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_pr_po_label.setObjectName("g_pr_po_label")
        self.horizontalLayout_18.addWidget(self.g_pr_po_label)
        self.g_pr_po_combobox = QtWidgets.QComboBox(self.g_pr_po_frame)
        self.g_pr_po_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pr_po_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pr_po_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_pr_po_combobox.setObjectName("g_pr_po_combobox")
        self.g_pr_po_combobox.addItem("")
        self.g_pr_po_combobox.addItem("")
        self.horizontalLayout_18.addWidget(self.g_pr_po_combobox)
        self.verticalLayout_8.addWidget(self.g_pr_po_frame)
        self.g_pr_warehouse_frame_2 = QtWidgets.QFrame(self.g_pr_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_pr_warehouse_frame_2.sizePolicy().hasHeightForWidth())
        self.g_pr_warehouse_frame_2.setSizePolicy(sizePolicy)
        self.g_pr_warehouse_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pr_warehouse_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pr_warehouse_frame_2.setObjectName("g_pr_warehouse_frame_2")
        self.horizontalLayout_34 = QtWidgets.QHBoxLayout(self.g_pr_warehouse_frame_2)
        self.horizontalLayout_34.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_34.setObjectName("horizontalLayout_34")
        self.g_pr_modifysubmittedpo_label = QtWidgets.QLabel(self.g_pr_warehouse_frame_2)
        self.g_pr_modifysubmittedpo_label.setMinimumSize(QtCore.QSize(133, 0))
        self.g_pr_modifysubmittedpo_label.setMaximumSize(QtCore.QSize(133, 16777215))
        self.g_pr_modifysubmittedpo_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_pr_modifysubmittedpo_label.setObjectName("g_pr_modifysubmittedpo_label")
        self.horizontalLayout_34.addWidget(self.g_pr_modifysubmittedpo_label)
        self.g_pr_modifysubmittedpo_combobox = QtWidgets.QComboBox(self.g_pr_warehouse_frame_2)
        self.g_pr_modifysubmittedpo_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pr_modifysubmittedpo_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pr_modifysubmittedpo_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_pr_modifysubmittedpo_combobox.setObjectName("g_pr_modifysubmittedpo_combobox")
        self.g_pr_modifysubmittedpo_combobox.addItem("")
        self.g_pr_modifysubmittedpo_combobox.addItem("")
        self.horizontalLayout_34.addWidget(self.g_pr_modifysubmittedpo_combobox)
        self.verticalLayout_8.addWidget(self.g_pr_warehouse_frame_2)
        self.g_pr_schedulepo_frame = QtWidgets.QFrame(self.g_pr_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_pr_schedulepo_frame.sizePolicy().hasHeightForWidth())
        self.g_pr_schedulepo_frame.setSizePolicy(sizePolicy)
        self.g_pr_schedulepo_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pr_schedulepo_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pr_schedulepo_frame.setObjectName("g_pr_schedulepo_frame")
        self.horizontalLayout_35 = QtWidgets.QHBoxLayout(self.g_pr_schedulepo_frame)
        self.horizontalLayout_35.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_35.setObjectName("horizontalLayout_35")
        self.g_pr_chedulepo_label = QtWidgets.QLabel(self.g_pr_schedulepo_frame)
        self.g_pr_chedulepo_label.setMinimumSize(QtCore.QSize(133, 0))
        self.g_pr_chedulepo_label.setMaximumSize(QtCore.QSize(133, 16777215))
        self.g_pr_chedulepo_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_pr_chedulepo_label.setObjectName("g_pr_chedulepo_label")
        self.horizontalLayout_35.addWidget(self.g_pr_chedulepo_label)
        self.g_pr_schedulepo_combobox = QtWidgets.QComboBox(self.g_pr_schedulepo_frame)
        self.g_pr_schedulepo_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pr_schedulepo_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pr_schedulepo_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_pr_schedulepo_combobox.setObjectName("g_pr_schedulepo_combobox")
        self.g_pr_schedulepo_combobox.addItem("")
        self.g_pr_schedulepo_combobox.addItem("")
        self.horizontalLayout_35.addWidget(self.g_pr_schedulepo_combobox)
        self.verticalLayout_8.addWidget(self.g_pr_schedulepo_frame)
        self.g_pr_receivingpo_frame = QtWidgets.QFrame(self.g_pr_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_pr_receivingpo_frame.sizePolicy().hasHeightForWidth())
        self.g_pr_receivingpo_frame.setSizePolicy(sizePolicy)
        self.g_pr_receivingpo_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pr_receivingpo_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pr_receivingpo_frame.setObjectName("g_pr_receivingpo_frame")
        self.horizontalLayout_19 = QtWidgets.QHBoxLayout(self.g_pr_receivingpo_frame)
        self.horizontalLayout_19.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_19.setObjectName("horizontalLayout_19")
        self.g_pr_receivingpo_label = QtWidgets.QLabel(self.g_pr_receivingpo_frame)
        self.g_pr_receivingpo_label.setMinimumSize(QtCore.QSize(133, 0))
        self.g_pr_receivingpo_label.setMaximumSize(QtCore.QSize(133, 16777215))
        self.g_pr_receivingpo_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_pr_receivingpo_label.setObjectName("g_pr_receivingpo_label")
        self.horizontalLayout_19.addWidget(self.g_pr_receivingpo_label)
        self.g_pr_receivingpo_combobox = QtWidgets.QComboBox(self.g_pr_receivingpo_frame)
        self.g_pr_receivingpo_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pr_receivingpo_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pr_receivingpo_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_pr_receivingpo_combobox.setObjectName("g_pr_receivingpo_combobox")
        self.g_pr_receivingpo_combobox.addItem("")
        self.g_pr_receivingpo_combobox.addItem("")
        self.horizontalLayout_19.addWidget(self.g_pr_receivingpo_combobox)
        self.verticalLayout_8.addWidget(self.g_pr_receivingpo_frame)
        self.g_pr_warehouse_frame = QtWidgets.QFrame(self.g_pr_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_pr_warehouse_frame.sizePolicy().hasHeightForWidth())
        self.g_pr_warehouse_frame.setSizePolicy(sizePolicy)
        self.g_pr_warehouse_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pr_warehouse_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pr_warehouse_frame.setObjectName("g_pr_warehouse_frame")
        self.horizontalLayout_22 = QtWidgets.QHBoxLayout(self.g_pr_warehouse_frame)
        self.horizontalLayout_22.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_22.setObjectName("horizontalLayout_22")
        self.g_pr_warehouse_label = QtWidgets.QLabel(self.g_pr_warehouse_frame)
        self.g_pr_warehouse_label.setMinimumSize(QtCore.QSize(133, 0))
        self.g_pr_warehouse_label.setMaximumSize(QtCore.QSize(133, 16777215))
        self.g_pr_warehouse_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_pr_warehouse_label.setObjectName("g_pr_warehouse_label")
        self.horizontalLayout_22.addWidget(self.g_pr_warehouse_label)
        self.g_pr_warehouse_combobox = QtWidgets.QComboBox(self.g_pr_warehouse_frame)
        self.g_pr_warehouse_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pr_warehouse_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pr_warehouse_combobox.setProperty("qp_intstn_integration_id", "")
        self.g_pr_warehouse_combobox.setObjectName("g_pr_warehouse_combobox")
        self.g_pr_warehouse_combobox.addItem("")
        self.g_pr_warehouse_combobox.addItem("")
        self.horizontalLayout_22.addWidget(self.g_pr_warehouse_combobox)
        self.verticalLayout_8.addWidget(self.g_pr_warehouse_frame)
        spacerItem4 = QtWidgets.QSpacerItem(20, 166, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_8.addItem(spacerItem4)
        self.g_main_tabWidget.addTab(self.g_pr_tab, "")
        self.verticalLayout.addWidget(self.g_main_tabWidget)
        self.g_bottombar_frame = QtWidgets.QFrame(self.g_selectsupplier_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.g_bottombar_frame.setSizePolicy(sizePolicy)
        self.g_bottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_bottombar_frame.setStyleSheet("")
        self.g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottombar_frame.setObjectName("g_bottombar_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_bottombar_frame)
        self.horizontalLayout.setContentsMargins(0, 5, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_controlbuttons_frame = QtWidgets.QFrame(self.g_bottombar_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem5)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_cancel_button.setEnabled(True)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_11.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/fpe/check"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_11.addWidget(self.g_save_button)
        self.horizontalLayout.addWidget(self.g_controlbuttons_frame)
        self.verticalLayout.addWidget(self.g_bottombar_frame)
        self.verticalLayout_2.addWidget(self.g_selectsupplier_frame)

        self.retranslateUi(ManagePermissions)
        self.g_main_tabWidget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(ManagePermissions)

    def retranslateUi(self, ManagePermissions):
        _translate = QtCore.QCoreApplication.translate
        ManagePermissions.setWindowTitle(_translate("ManagePermissions", "Manage Permissions"))
        self.g_main_label.setText(_translate("ManagePermissions", "Manage Permissions"))
        self.g_custexport_label.setText(_translate("ManagePermissions", "Customer Export"))
        self.g_general_customerexport_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_general_customerexport_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_cost_label.setText(_translate("ManagePermissions", "Product Cost, Markup, Margin"))
        self.g_general_costmarkupmargin_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_general_costmarkupmargin_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_general_costmarkupmargin_combobox.setItemText(2, _translate("ManagePermissions", "Modify"))
        self.g_subscription_label.setText(_translate("ManagePermissions", "Subscriptions"))
        self.g_subscription_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_subscription_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_subscription_combobox.setItemText(2, _translate("ManagePermissions", "Modify"))
        self.g_deletepetesigndocuments_label.setText(_translate("ManagePermissions", "Can Delete E-Sign Documents"))
        self.g_deletepetesigndocuments_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_deletepetesigndocuments_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_deletepetesigndocuments_combobox.setItemText(2, _translate("ManagePermissions", "Modify"))
        self.g_main_tabWidget.setTabText(self.g_main_tabWidget.indexOf(self.g_general_tab), _translate("ManagePermissions", "General"))
        self.g_closetray_label.setText(_translate("ManagePermissions", "Close Tray"))
        self.g_cr_closetray_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_cr_closetray_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_opentray_label.setText(_translate("ManagePermissions", "Open Tray"))
        self.g_cr_opentray_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_cr_opentray_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_pettycash_label.setText(_translate("ManagePermissions", "Petty Cash"))
        self.g_cr_pettycash_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_cr_pettycash_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_void_label.setText(_translate("ManagePermissions", "Void Payment"))
        self.g_cr_voidpayment_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_cr_voidpayment_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_change_label.setText(_translate("ManagePermissions", "Make Change/ Wrong Change"))
        self.g_cr_makechangewrongchange_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_cr_makechangewrongchange_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_taxexemptions_label.setText(_translate("ManagePermissions", "Tax Exemptions"))
        self.g_taxexemptions_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_taxexemptions_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_placeinvoiceonhold_label.setText(_translate("ManagePermissions", "Place Invoice on Hold"))
        self.g_placeinvoiceonhold_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_placeinvoiceonhold_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_cr_canapprovediscount_label.setText(_translate("ManagePermissions", "Can Approve Discount"))
        self.g_cr_canapprovediscount_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_cr_canapprovediscount_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_removecustomerfrominvoice_label.setText(_translate("ManagePermissions", "Remove Customer from Invoice with Payment"))
        self.g_removecustomerfrominvoice_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_removecustomerfrominvoice_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_removeproductfrominvoice_label.setText(_translate("ManagePermissions", "Change Product on Invoice with Payment"))
        self.g_removeproductfrominvoice_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_removeproductfrominvoice_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_bonusbucksbalance_label.setText(_translate("ManagePermissions", "Manage Bonus Bucks Balance"))
        self.g_bonusbucksbalance_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_bonusbucksbalance_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_frequentbuyer_label.setText(_translate("ManagePermissions", "Manage Frequent Buyer"))
        self.g_frequentbuyer_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_frequentbuyer_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_deleteorders_label.setText(_translate("ManagePermissions", "Delete Orders "))
        self.g_deleteorders_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_deleteorders_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_deleteorders_combobox.setItemText(2, _translate("ManagePermissions", "Modify"))
        self.g_main_tabWidget.setTabText(self.g_main_tabWidget.indexOf(self.g_register_tab), _translate("ManagePermissions", "Cash Register"))
        self.g_pet_cost_label.setText(_translate("ManagePermissions", "Pet Cost"))
        self.g_pt_cost_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_pt_cost_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_pt_cost_combobox.setItemText(2, _translate("ManagePermissions", "Modify"))
        self.g_pet_export_label.setText(_translate("ManagePermissions", "Pet Export"))
        self.g_pt_petexport_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_pt_petexport_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_pet_paperwork_label.setText(_translate("ManagePermissions", "Pet Paperwork"))
        self.g_pt_petpaperwork_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_pt_petpaperwork_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_main_tabWidget.setTabText(self.g_main_tabWidget.indexOf(self.g_pettracker_tab), _translate("ManagePermissions", "Pet Tracker"))
        self.g_prod_adjustinventory_label.setText(_translate("ManagePermissions", "Adjust Inventory"))
        self.g_prod_adjustinventory_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_prod_adjustinventory_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_prod_category_label.setText(_translate("ManagePermissions", "Categories"))
        self.g_prod_category_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_prod_category_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_prod_export_label.setText(_translate("ManagePermissions", "Product Export"))
        self.g_prod_productexport_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_prod_productexport_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_prod_bundleexport_label.setText(_translate("ManagePermissions", "Bundle Export"))
        self.g_prod_bundleexport_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_prod_bundleexport_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_prod_transfer_label.setText(_translate("ManagePermissions", "Transfer Inventory"))
        self.g_prod_transfer_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_prod_transfer_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_prod_inventory_label.setText(_translate("ManagePermissions", "Product Inventory"))
        self.g_prod_inventory_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_prod_inventory_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_prod_reset_inventory_label.setText(_translate("ManagePermissions", "Inventory Counts and Resets"))
        self.g_prod_reset_inventory_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_prod_reset_inventory_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_main_tabWidget.setTabText(self.g_main_tabWidget.indexOf(self.g_products_tab), _translate("ManagePermissions", "Products"))
        self.g_pr_po_label.setText(_translate("ManagePermissions", "PO\'s"))
        self.g_pr_po_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_pr_po_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_pr_modifysubmittedpo_label.setText(_translate("ManagePermissions", "Modify Submitted PO\'s"))
        self.g_pr_modifysubmittedpo_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_pr_modifysubmittedpo_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_pr_chedulepo_label.setText(_translate("ManagePermissions", "Schedule PO\'s"))
        self.g_pr_schedulepo_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_pr_schedulepo_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_pr_receivingpo_label.setText(_translate("ManagePermissions", "Receiving PO\'s"))
        self.g_pr_receivingpo_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_pr_receivingpo_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_pr_warehouse_label.setText(_translate("ManagePermissions", "Warehouse"))
        self.g_pr_warehouse_combobox.setItemText(0, _translate("ManagePermissions", "Hidden"))
        self.g_pr_warehouse_combobox.setItemText(1, _translate("ManagePermissions", "Visible"))
        self.g_main_tabWidget.setTabText(self.g_main_tabWidget.indexOf(self.g_pr_tab), _translate("ManagePermissions", "Purchase && Receiving"))
        self.g_cancel_button.setText(_translate("ManagePermissions", "Cancel"))
        self.g_save_button.setText(_translate("ManagePermissions", "Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManagePermissions = QtWidgets.QDialog()
    ui = Ui_ManagePermissions()
    ui.setupUi(ManagePermissions)
    ManagePermissions.show()
    sys.exit(app.exec_())
