# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'createlayaway_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_CreateLayawayDialog(object):
    def setupUi(self, CreateLayawayDialog):
        CreateLayawayDialog.setObjectName("CreateLayawayDialog")
        CreateLayawayDialog.resize(500, 376)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(CreateLayawayDialog.sizePolicy().hasHeightForWidth())
        CreateLayawayDialog.setSizePolicy(sizePolicy)
        CreateLayawayDialog.setMinimumSize(QtCore.QSize(500, 376))
        CreateLayawayDialog.setMaximumSize(QtCore.QSize(16777215, 356))
        CreateLayawayDialog.setStyleSheet("\n"
"QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"#g_unifiedsearch_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_searchbox_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_clearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_searchtext_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_search_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QDateTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"#g_dialog_frame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
" QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QTabWidget {\n"
"    border:0px;\n"
"}\n"
"\n"
"#g_printingoptions_widget,\n"
"#g_verify_widget {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius:0px;\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"#g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-weight:bold;\n"
"    font-size:17px;\n"
"}\n"
"\n"
"#g_duedate_label_2, #g_inform_label {\n"
"    font-weight:bold;\n"
"    font-size:15px;\n"
"}\n"
"#g_duedate_label {\n"
"    font-weight:bold;\n"
"    font-size:11px;\n"
"}\n"
"\n"
"#g_origination_value_label, #g_cancellation_value_label {\n"
"    font-weight:bold;\n"
"    font-size:12px;\n"
"}\n"
"#g_duedate_timeedit {\n"
"    font-size:11px;\n"
"}")
        CreateLayawayDialog.setProperty("screen_toolbutton_tooltip_text", "")
        CreateLayawayDialog.setProperty("screen_toolbutton_title_text", "")
        CreateLayawayDialog.setProperty("screen_toolbutton_stylesheet_text", "")
        CreateLayawayDialog.setProperty("screen_indicator_stylesheet_text", "")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(CreateLayawayDialog)
        self.verticalLayout_2.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_progress_frame = QtWidgets.QFrame(CreateLayawayDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.verticalLayout_2.addWidget(self.g_progress_frame)
        self.g_titlepagenav_frame = QtWidgets.QFrame(CreateLayawayDialog)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_30 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_30.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_30.setObjectName("horizontalLayout_30")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout_23 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_23.setObjectName("verticalLayout_23")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_23.addWidget(self.g_main_label)
        self.horizontalLayout_30.addWidget(self.g_title_frame)
        self.verticalLayout_2.addWidget(self.g_titlepagenav_frame)
        self.g_duedate_frame_2 = QtWidgets.QFrame(CreateLayawayDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_duedate_frame_2.sizePolicy().hasHeightForWidth())
        self.g_duedate_frame_2.setSizePolicy(sizePolicy)
        self.g_duedate_frame_2.setMinimumSize(QtCore.QSize(0, 50))
        self.g_duedate_frame_2.setMaximumSize(QtCore.QSize(16777215, 50))
        self.g_duedate_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_duedate_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_duedate_frame_2.setObjectName("g_duedate_frame_2")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_duedate_frame_2)
        self.verticalLayout.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_duedate_label_2 = QtWidgets.QLabel(self.g_duedate_frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_duedate_label_2.sizePolicy().hasHeightForWidth())
        self.g_duedate_label_2.setSizePolicy(sizePolicy)
        self.g_duedate_label_2.setMinimumSize(QtCore.QSize(0, 0))
        self.g_duedate_label_2.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_duedate_label_2.setFont(font)
        self.g_duedate_label_2.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_duedate_label_2.setWordWrap(True)
        self.g_duedate_label_2.setObjectName("g_duedate_label_2")
        self.verticalLayout.addWidget(self.g_duedate_label_2)
        self.verticalLayout_2.addWidget(self.g_duedate_frame_2)
        self.g_duedate_frame = QtWidgets.QFrame(CreateLayawayDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_duedate_frame.sizePolicy().hasHeightForWidth())
        self.g_duedate_frame.setSizePolicy(sizePolicy)
        self.g_duedate_frame.setMinimumSize(QtCore.QSize(0, 60))
        self.g_duedate_frame.setMaximumSize(QtCore.QSize(16777215, 60))
        self.g_duedate_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_duedate_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_duedate_frame.setObjectName("g_duedate_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_duedate_frame)
        self.horizontalLayout_2.setContentsMargins(9, 9, 9, 9)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_duedate_label = QtWidgets.QLabel(self.g_duedate_frame)
        self.g_duedate_label.setMinimumSize(QtCore.QSize(100, 0))
        self.g_duedate_label.setMaximumSize(QtCore.QSize(90, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_duedate_label.setFont(font)
        self.g_duedate_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_duedate_label.setObjectName("g_duedate_label")
        self.horizontalLayout_2.addWidget(self.g_duedate_label)
        self.g_duedate_datetimeedit = QtWidgets.QDateTimeEdit(self.g_duedate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_duedate_datetimeedit.sizePolicy().hasHeightForWidth())
        self.g_duedate_datetimeedit.setSizePolicy(sizePolicy)
        self.g_duedate_datetimeedit.setMinimumSize(QtCore.QSize(120, 31))
        self.g_duedate_datetimeedit.setMaximumSize(QtCore.QSize(120, 31))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_duedate_datetimeedit.setFont(font)
        self.g_duedate_datetimeedit.setStyleSheet("")
        self.g_duedate_datetimeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_duedate_datetimeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2016, 12, 1), QtCore.QTime(0, 0, 0)))
        self.g_duedate_datetimeedit.setDate(QtCore.QDate(2016, 12, 1))
        self.g_duedate_datetimeedit.setCalendarPopup(True)
        self.g_duedate_datetimeedit.setTimeSpec(QtCore.Qt.LocalTime)
        self.g_duedate_datetimeedit.setObjectName("g_duedate_datetimeedit")
        self.horizontalLayout_2.addWidget(self.g_duedate_datetimeedit)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem)
        self.verticalLayout_2.addWidget(self.g_duedate_frame)
        self.g_info_frame = QtWidgets.QFrame(CreateLayawayDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_info_frame.sizePolicy().hasHeightForWidth())
        self.g_info_frame.setSizePolicy(sizePolicy)
        self.g_info_frame.setMinimumSize(QtCore.QSize(0, 110))
        self.g_info_frame.setMaximumSize(QtCore.QSize(16777215, 110))
        self.g_info_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_info_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_info_frame.setObjectName("g_info_frame")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_info_frame)
        self.verticalLayout_3.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_inform_label = QtWidgets.QLabel(self.g_info_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_inform_label.sizePolicy().hasHeightForWidth())
        self.g_inform_label.setSizePolicy(sizePolicy)
        self.g_inform_label.setMinimumSize(QtCore.QSize(0, 30))
        self.g_inform_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_inform_label.setFont(font)
        self.g_inform_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_inform_label.setWordWrap(True)
        self.g_inform_label.setObjectName("g_inform_label")
        self.verticalLayout_3.addWidget(self.g_inform_label)
        self.g_origination_frame = QtWidgets.QFrame(self.g_info_frame)
        self.g_origination_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_origination_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_origination_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_origination_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_origination_frame.setObjectName("g_origination_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_origination_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 9, 0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_origination_label = QtWidgets.QLabel(self.g_origination_frame)
        self.g_origination_label.setMinimumSize(QtCore.QSize(120, 0))
        self.g_origination_label.setMaximumSize(QtCore.QSize(90, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_origination_label.setFont(font)
        self.g_origination_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_origination_label.setObjectName("g_origination_label")
        self.horizontalLayout_3.addWidget(self.g_origination_label)
        self.g_origination_value_label = QtWidgets.QLabel(self.g_origination_frame)
        self.g_origination_value_label.setMinimumSize(QtCore.QSize(0, 0))
        self.g_origination_value_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_origination_value_label.setFont(font)
        self.g_origination_value_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_origination_value_label.setObjectName("g_origination_value_label")
        self.horizontalLayout_3.addWidget(self.g_origination_value_label)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem1)
        self.verticalLayout_3.addWidget(self.g_origination_frame)
        self.g_cancellation_frame = QtWidgets.QFrame(self.g_info_frame)
        self.g_cancellation_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_cancellation_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_cancellation_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cancellation_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cancellation_frame.setObjectName("g_cancellation_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_cancellation_frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 9, 0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_cancellation_label = QtWidgets.QLabel(self.g_cancellation_frame)
        self.g_cancellation_label.setMinimumSize(QtCore.QSize(120, 0))
        self.g_cancellation_label.setMaximumSize(QtCore.QSize(90, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_cancellation_label.setFont(font)
        self.g_cancellation_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_cancellation_label.setObjectName("g_cancellation_label")
        self.horizontalLayout_4.addWidget(self.g_cancellation_label)
        self.g_cancellation_value_label = QtWidgets.QLabel(self.g_cancellation_frame)
        self.g_cancellation_value_label.setMinimumSize(QtCore.QSize(0, 0))
        self.g_cancellation_value_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_cancellation_value_label.setFont(font)
        self.g_cancellation_value_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_cancellation_value_label.setObjectName("g_cancellation_value_label")
        self.horizontalLayout_4.addWidget(self.g_cancellation_value_label)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem2)
        self.verticalLayout_3.addWidget(self.g_cancellation_frame)
        self.verticalLayout_2.addWidget(self.g_info_frame)
        spacerItem3 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Minimum)
        self.verticalLayout_2.addItem(spacerItem3)
        self.g_controlbuttons_frame = QtWidgets.QFrame(CreateLayawayDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem4 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem4)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_11.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_save_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_11.addWidget(self.g_save_button)
        self.verticalLayout_2.addWidget(self.g_controlbuttons_frame)

        self.retranslateUi(CreateLayawayDialog)
        QtCore.QMetaObject.connectSlotsByName(CreateLayawayDialog)

    def retranslateUi(self, CreateLayawayDialog):
        _translate = QtCore.QCoreApplication.translate
        CreateLayawayDialog.setWindowTitle(_translate("CreateLayawayDialog", "Create Layaway"))
        self.g_main_label.setText(_translate("CreateLayawayDialog", "Create Layaway"))
        self.g_duedate_label_2.setText(_translate("CreateLayawayDialog", "The layaway must be paid off by the due date below else it will be considered abandoned."))
        self.g_duedate_label.setText(_translate("CreateLayawayDialog", "Due Date"))
        self.g_duedate_datetimeedit.setDisplayFormat(_translate("CreateLayawayDialog", "M/d/yyyy"))
        self.g_inform_label.setText(_translate("CreateLayawayDialog", "Inform the customer of the following fees before continuing:"))
        self.g_origination_label.setText(_translate("CreateLayawayDialog", "Origination Fee:"))
        self.g_origination_value_label.setText(_translate("CreateLayawayDialog", "$ 10.00"))
        self.g_cancellation_label.setText(_translate("CreateLayawayDialog", "Cancellation Fee"))
        self.g_cancellation_value_label.setText(_translate("CreateLayawayDialog", "$ 10.00"))
        self.g_cancel_button.setText(_translate("CreateLayawayDialog", "  Cancel"))
        self.g_save_button.setText(_translate("CreateLayawayDialog", "  Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    CreateLayawayDialog = QtWidgets.QDialog()
    ui = Ui_CreateLayawayDialog()
    ui.setupUi(CreateLayawayDialog)
    CreateLayawayDialog.show()
    sys.exit(app.exec_())
