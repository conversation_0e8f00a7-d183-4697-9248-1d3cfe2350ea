# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'manageentity_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_ManageEntity(object):
    def setupUi(self, ManageEntity):
        ManageEntity.setObjectName("ManageEntity")
        ManageEntity.resize(990, 848)
        ManageEntity.setMinimumSize(QtCore.QSize(990, 600))
        ManageEntity.setMaximumSize(QtCore.QSize(********, ********))
        ManageEntity.setStyleSheet("#ManageEntity {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"QLabel {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"#g_main_label {\n"
"    font: 17px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"QFrame {\n"
"    border:0px;\n"
"    margin-top: 0px;\n"
"    margin-right: 0px;\n"
"    margin-bottom: 0px;\n"
"    margin-left: 0px;\n"
"    spacing: 0px;\n"
"    padding: 0px;\n"
"}\n"
"\n"
"QCheckBox {\n"
"    padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px; \n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/entity_dialog/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/entity_dialog/checked\');\n"
"    border:0px;\n"
"} \n"
"\n"
"QDateTimeEdit, QTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
" \n"
"QDateTimeEdit::drop-down, QTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"\n"
".QListWidget {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    padding: 2px;\n"
"    background: transparent;\n"
"}\n"
"\n"
"#g_mfg_images_list,\n"
"#g_phone_listWidget,\n"
"#g_email_listWidget {\n"
"    border: 0px;\n"
"    background: transparent;\n"
"}\n"
"\n"
".QListWidget::item {\n"
"    padding-bottom:1px;\n"
"    padding-right:1px;\n"
"}\n"
"\n"
".QListWidget::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
".QListWidget::item::selected {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    color: #000000\n"
"}\n"
"\n"
".QListWidget::indicator:unchecked {\n"
"    image: url(\':/entity_dialog/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
".QListWidget::indicator:checked {\n"
"    image: url(\':/entity_dialog/checked\');\n"
"    border:0px;\n"
"} \n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"QFontComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QFontComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    /*background-color: #000;*/\n"
"}\n"
"\n"
"PyCheckComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"PyCheckComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    /*background-color: #000;*/\n"
"}\n"
"\n"
"PyCheckComboBox QAbstractItemView::item {\n"
"     margin-top: 5px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QSpinBox {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px;\n"
"}\n"
"\n"
".QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-size: 13px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QTabBar::scroller {width: 48px;}\n"
"\n"
"QTabWidget::tab-bar {\n"
"   left: 0;\n"
"}\n"
"\n"
"QTabWidget {\n"
"    border:0px;\n"
"}\n"
"\n"
"#g_settings_tabs QTabWidget::tab-bar {\n"
"     left: 10px; \n"
" }\n"
"\n"
"QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"QRadioButton {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
".QScrollArea {\n"
"    background-color: #f4f4f4;\n"
"    border-radius:5px;\n"
"    border:1px solid #073c83;\n"
"    padding:4px;\n"
"}\n"
"\n"
"QScrollBar:horizontal {\n"
"height:0px;\n"
"}\n"
"\n"
"#scrollAreaWidgetContents {\n"
"background-color: #f4f4f4;\n"
"}\n"
"\n"
"\n"
"#g_unified_search_frame, #g_unified_search_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_search_box_frame, #g_search_box_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_clear_search_button, #g_clear_search_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_search_text_lineedit, #g_search_text_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_search_combobox, #g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_search_combobox, #g_search_combobox::drop-down {\n"
"       subcontrol-origin: margin;\n"
"       background:;\n"
"       width:20px;\n"
"       }\n"
"        #g_deleteaddress_button,\n"
"        #g_deleteemergencycontact_button,\n"
"        #g_deletecustomerpet_button,\n"
"        #g_diablemarketingsubscription_button,\n"
"        #g_deleterepeatdelivery_button,\n"
"        #g_removesupplierinfo_button,\n"
"        #g_removecustomfield_button,\n"
"        #g_cancel_button,\n"
"        #g_removepaperwork_trans_button,\n"
"        #g_removepaperwork_breeder_button,\n"
"        #g_removepaperwork_broker_button,\n"
"        #g_deletebrand_button,\n"
"        #g_astrounlink_button,\n"
"        #g_dmunlink_button,\n"
"        #g_delete_card_button {\n"
"       background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"       border:2px solid #c80319;\n"
"       }\n"
"        #g_deleteaddress_button:pressed,\n"
"        #g_deleteemergencycontact_button:pressed,\n"
"        #g_deletecustomerpet_button:pressed,\n"
"        #g_diablemarketingsubscription_button:pressed,\n"
"        #g_deleterepeatdelivery_button:pressed,\n"
"        #g_removesupplierinfo_button:pressed,\n"
"        #g_removecustomfield_button:pressed,\n"
"        #g_cancel_button:pressed,\n"
"        #g_removepaperwork_trans_button:pressed,\n"
"        #g_removepaperwork_breeder_button:pressed,\n"
"        #g_removepaperwork_broker_button:pressed,\n"
"        #g_deletebrand_button:pressed,\n"
"        #g_astrounlink_button:pressed,\n"
"        #g_dmunlink_button:pressed,\n"
"        #g_delete_card_button:pressed {\n"
"       background: #c80319\n"
"       }\n"
"        #g_createusername_button,\n"
"        #g_primaryaddress_button,\n"
"        #g_addaddress_button,\n"
"        #g_managefrequentBuyer_button,\n"
"        #g_addemergencycontact_button,\n"
"        #g_addcustomerpet_button,\n"
"        #g_sendwelcome_button,\n"
"        #g_enablemarketingsubscription_button,\n"
"        #g_resetpassword_button,\n"
"        #g_repeatdelivery_massaction_button,\n"
"        #g_addrepeatdelivery_button,\n"
"          #g_newhouseaccount_button,\n"
"        #g_managehouseaccount_button,\n"
"          #g_quotes_select_button,\n"
"          #g_massactionsupplierinfo_button,\n"
"            #g_addsupplierinfo_button,\n"
"           #g_make_payment_button,\n"
"          #g_addcustom_button,\n"
"          #g_save_button,\n"
"          #g_addpaperwork_trans_button,\n"
"          #g_addpaperwork_breeder_button,\n"
"          #g_addpaperwork_broker_button,\n"
"          #g_addbrand_button,\n"
"          #g_astrolink_button,\n"
"          #g_dmlink_button,\n"
"          #g_savecard_button {\n"
"       background-color: #009c00;\n"
"       border:2px solid #007f00;\n"
"       }\n"
"    #g_createusername_button:pressed,\n"
"    #g_primaryaddress_button:pressed,\n"
"    #g_addaddress_button:pressed,\n"
"    #g_managefrequentBuyer_button:pressed,\n"
"    #g_addemergencycontact_button:pressed,\n"
"    #g_addcustomerpet_button:pressed,\n"
"    #g_sendwelcome_button:pressed,\n"
"    #g_enablemarketingsubscription_button:pressed,\n"
"    #g_resetpassword_button:pressed,\n"
"    #g_repeatdelivery_massaction_button:pressed,\n"
"    #g_addrepeatdelivery_button:pressed,\n"
"      #g_newhouseaccount_button:pressed,\n"
"       #g_managehouseaccount_button:pressed,\n"
"       #g_quotes_select_button:pressed,\n"
"       #g_massactionsupplierinfo_button:pressed,\n"
"       #g_addsupplierinfo_button:pressed,\n"
"       #g_addcustom_button:pressed,\n"
"       #g_make_payment_button:pressed,\n"
"       #g_addpaperwork_trans_button:pressed,\n"
"       #g_addpaperwork_breeder_button:pressed,\n"
"       #g_addpaperwork_broker_button:pressed,\n"
"       #g_save_button:pressed,\n"
"       #g_addbrand_button:pressed,\n"
"       #g_astrolink_button:pressed,\n"
"       #g_dmlink_button:pressed,\n"
"       #g_savecard_button:pressed {\n"
"       background-color: #007f00;\n"
"       }\n"
"\n"
"        #g_address_tab,\n"
"        #g_emergencycontacts_tab,\n"
"        #g_website_tab,\n"
"       #g_repeatdelivery_tab,\n"
"       #g_specialorders_tab,\n"
"       #g_history_tab,\n"
"       #g_recurring_tab,\n"
"       #g_layaways_tab,\n"
"       #g_openorders_tab,\n"
"       #g_specialorders_tab\n"
"       #g_mfgmarketing_tab,\n"
"       #g_mfgmedia_tab,\n"
"       #g_customfield_widget,\n"
"       #g_receipts_widget,\n"
"       #g_receipts_layout_widget,\n"
"       #g_receipts_disclaimers_widget,\n"
"       #g_receipts_logo_widget,\n"
"       #g_pricing_widget,\n"
"       #g_contactinfo_widget,\n"
"       #g_notes_widget,\n"
"       #g_pictures_widget,\n"
"       #g_sales_widget,\n"
"       #g_frequentbuyer_widget,\n"
"       #g_programs_widget,\n"
"       #g_rewards_widget,\n"
"       #g_purchasehistory_widget,\n"
"       #g_supplierterms_widget,\n"
"       #g_employeesettings_widget,\n"
"       #g_taxinfo_widget,\n"
"       #g_programstatus_widget,\n"
"       #g_rewardsearned_widget,\n"
"       #g_payments_widget,\n"
"       #g_litters_widget,\n"
"       #g_pets_widget,\n"
"       #g_breederinfo_widget,\n"
"       #g_brokerinfo_widget,\n"
"       #g_vetinfo_widget,\n"
"       #g_transporterinfo_widget,\n"
"       #g_purchaseorder_widget,\n"
"       #g_product_widget,\n"
"       #g_brands_widget,\n"
"       #g_astroinfo_widget,\n"
"       #g_dminfo_widget,\n"
"       #g_astrocards_tab,\n"
"       #g_astrooffers_tab,\n"
"       #g_savedcards_widget\n"
"       {\n"
"       border:1px solid #073c83;\n"
"       border-radius: 5px;\n"
"       border-top-left-radius:0px;\n"
"       background-color: #f4f4f4;\n"
"       }\n"
"\n"
"       #g_groups_list {\n"
"       background-color: #007f00;\n"
"       border:0px;\n"
"       }\n"
"\n"
"#g_pidbsupplier_combobox::drop-down,\n"
"#g_fontsize_combobox::drop-down,\n"
"#g_font_combobox::drop-down,\n"
"#g_customer_checkcombobox::drop-down,\n"
"#g_location_checkcombobox::drop-down,\n"
"#g_supplierinfosendvia_combobox::drop-down,\n"
"#g_search_combobox::drop-down,\n"
"#g_state_combobox::drop-down,\n"
"#g_emailtype_combobox::drop-down,\n"
"#g_defaultlocation_combobox::drop-down,\n"
"#g_country_combobox::drop-down,\n"
"#g_employeerole_combobox::drop-down,\n"
"#g_employeestatus_combobox::drop-down {    \n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"}\n"
"\n"
"#g_default_taxes_list {\n"
"    border:0px solid #073c83;\n"
"}\n"
"\n"
"#g_default_taxes_list::item {   \n"
"    padding-top: 5px; \n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_totalpages_label {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_selectgroupbox_label,\n"
"#g_entity_label,\n"
"#g_business_label,\n"
"#g_location_label,\n"
"#g_locationid_label,\n"
"#g_entity_label_7 {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_contactinfo_widget QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QTabWidget QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"/* Notes tab */\n"
"#g_warning_label, #g_info_label {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"/* Sales tab */\n"
"#g_sales_widget QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_sales_table {\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"/* History tab */\n"
"\n"
"#g_history_widget QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_history_table {\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"/* Custom Fields tab */\n"
"\n"
"#g_attributeshelp_label {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_customfield_table {\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"#g_attributes_groupBox {\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QListWidget {\n"
"    font-size: 12px;\n"
"\n"
"}\n"
"\n"
"/*Breeders/Broker Info tab */\n"
"#g_totalpages_breeder_label,\n"
"#g_totalpages_broker_label,\n"
"#g_totalpages_trans_label {\n"
"    font-weight: normal;\n"
"}\n"
"\n"
"/* Receipts tab */\n"
"#g_price_on_layaway_checkbox {\n"
"    font-size: 13px;\n"
"}\n"
"\n"
"/* PETZ tab*/\n"
"\n"
"#g_dmlink_toolbutton::menu-button {\n"
"    width: 16px;\n"
"    border:2px solid #e1e148;\n"
"}\n"
"\n"
"#g_dmlink_toolbutton {\n"
"    background: #ffff66;\n"
"    border:2px solid #e1e148;\n"
"    font-size: 15px;\n"
"    font-weight: bold; \n"
"    color: black;\n"
"}\n"
"\n"
"QToolButton[popupMode=\"1\"] {\n"
"    padding-right: 20px; /* make way for the popup button */\n"
"}\n"
"\n"
"#g_dmlink_toolbutton:pressed {\n"
"    background: #e1e148;\n"
"}\n"
"\n"
"#g_dmlink_toolbutton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}\n"
"\n"
"#g_dmlink_toolbutton > QMenu {\n"
"    background-color: #ffff66; \n"
"    font-size: 15px;\n"
"    font-weight: bold; \n"
"    color: black;\n"
"\n"
"}\n"
"\n"
"#g_dmlink_toolbutton > QMenu::item:selected { /* when user selects item using mouse or keyboard */\n"
"    background-color: #e1e148;\n"
"}\n"
"\n"
"")
        self.verticalLayout_22 = QtWidgets.QVBoxLayout(ManageEntity)
        self.verticalLayout_22.setContentsMargins(9, 0, 9, 9)
        self.verticalLayout_22.setSpacing(6)
        self.verticalLayout_22.setObjectName("verticalLayout_22")
        self.g_top_frame = QtWidgets.QFrame(ManageEntity)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setMaximumSize(QtCore.QSize(********, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_66 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_66.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_66.setSpacing(0)
        self.horizontalLayout_66.setObjectName("horizontalLayout_66")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_66.addWidget(self.g_progress_frame)
        self.verticalLayout_22.addWidget(self.g_top_frame)
        self.g_titlepagenav_frame = QtWidgets.QFrame(ManageEntity)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(********, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_30 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_30.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_30.setSpacing(6)
        self.horizontalLayout_30.setObjectName("horizontalLayout_30")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout_23 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_23.setObjectName("verticalLayout_23")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(********, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_23.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_23.addItem(spacerItem)
        self.horizontalLayout_30.addWidget(self.g_title_frame)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(********, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout.setSpacing(6)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout.addItem(spacerItem1)
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagenav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_slash_label.setObjectName("g_slash_label")
        self.g_pagenav_hlayout.addWidget(self.g_slash_label)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout.addWidget(self.g_totalpages_label)
        self.horizontalLayout_30.addWidget(self.g_pagenav_frame)
        self.verticalLayout_22.addWidget(self.g_titlepagenav_frame)
        self.g_selectgroupbox_label = QtWidgets.QLabel(ManageEntity)
        self.g_selectgroupbox_label.setMinimumSize(QtCore.QSize(0, 20))
        self.g_selectgroupbox_label.setMaximumSize(QtCore.QSize(********, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_selectgroupbox_label.setFont(font)
        self.g_selectgroupbox_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_selectgroupbox_label.setObjectName("g_selectgroupbox_label")
        self.verticalLayout_22.addWidget(self.g_selectgroupbox_label)
        self.frame_4 = QtWidgets.QFrame(ManageEntity)
        self.frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_4.setObjectName("frame_4")
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout(self.frame_4)
        self.horizontalLayout_15.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.frame_7 = QtWidgets.QFrame(self.frame_4)
        self.frame_7.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_7.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_7.setObjectName("frame_7")
        self.verticalLayout_50 = QtWidgets.QVBoxLayout(self.frame_7)
        self.verticalLayout_50.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_50.setObjectName("verticalLayout_50")
        self.g_entity_frame = QtWidgets.QFrame(self.frame_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_entity_frame.sizePolicy().hasHeightForWidth())
        self.g_entity_frame.setSizePolicy(sizePolicy)
        self.g_entity_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_entity_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_entity_frame.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.g_entity_frame.setObjectName("g_entity_frame")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout(self.g_entity_frame)
        self.horizontalLayout_16.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_16.setSpacing(6)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.g_entity_label = QtWidgets.QLabel(self.g_entity_frame)
        self.g_entity_label.setMinimumSize(QtCore.QSize(90, 31))
        self.g_entity_label.setMaximumSize(QtCore.QSize(90, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_entity_label.setFont(font)
        self.g_entity_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entity_label.setObjectName("g_entity_label")
        self.horizontalLayout_16.addWidget(self.g_entity_label)
        self.g_entityfirst_lineedit = QtWidgets.QLineEdit(self.g_entity_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_entityfirst_lineedit.sizePolicy().hasHeightForWidth())
        self.g_entityfirst_lineedit.setSizePolicy(sizePolicy)
        self.g_entityfirst_lineedit.setMinimumSize(QtCore.QSize(248, 31))
        self.g_entityfirst_lineedit.setMaximumSize(QtCore.QSize(248, 31))
        font = QtGui.QFont()
        self.g_entityfirst_lineedit.setFont(font)
        self.g_entityfirst_lineedit.setReadOnly(False)
        self.g_entityfirst_lineedit.setProperty("qp_epn_names_before_key_names", "")
        self.g_entityfirst_lineedit.setObjectName("g_entityfirst_lineedit")
        self.horizontalLayout_16.addWidget(self.g_entityfirst_lineedit)
        self.g_entitylast_lineedit = QtWidgets.QLineEdit(self.g_entity_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_entitylast_lineedit.sizePolicy().hasHeightForWidth())
        self.g_entitylast_lineedit.setSizePolicy(sizePolicy)
        self.g_entitylast_lineedit.setMinimumSize(QtCore.QSize(248, 31))
        self.g_entitylast_lineedit.setMaximumSize(QtCore.QSize(248, 31))
        font = QtGui.QFont()
        self.g_entitylast_lineedit.setFont(font)
        self.g_entitylast_lineedit.setReadOnly(False)
        self.g_entitylast_lineedit.setProperty("qp_epn_key_names", "")
        self.g_entitylast_lineedit.setObjectName("g_entitylast_lineedit")
        self.horizontalLayout_16.addWidget(self.g_entitylast_lineedit)
        spacerItem2 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_16.addItem(spacerItem2)
        self.verticalLayout_50.addWidget(self.g_entity_frame)
        self.g_business_frame = QtWidgets.QFrame(self.frame_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_business_frame.sizePolicy().hasHeightForWidth())
        self.g_business_frame.setSizePolicy(sizePolicy)
        self.g_business_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_business_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_business_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_business_frame.setObjectName("g_business_frame")
        self.horizontalLayout_28 = QtWidgets.QHBoxLayout(self.g_business_frame)
        self.horizontalLayout_28.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_28.setSpacing(6)
        self.horizontalLayout_28.setObjectName("horizontalLayout_28")
        self.g_business_label = QtWidgets.QLabel(self.g_business_frame)
        self.g_business_label.setMinimumSize(QtCore.QSize(90, 31))
        self.g_business_label.setMaximumSize(QtCore.QSize(90, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_business_label.setFont(font)
        self.g_business_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_business_label.setObjectName("g_business_label")
        self.horizontalLayout_28.addWidget(self.g_business_label)
        self.g_business_lineedit = QtWidgets.QLineEdit(self.g_business_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_business_lineedit.sizePolicy().hasHeightForWidth())
        self.g_business_lineedit.setSizePolicy(sizePolicy)
        self.g_business_lineedit.setMinimumSize(QtCore.QSize(500, 31))
        self.g_business_lineedit.setMaximumSize(QtCore.QSize(500, 31))
        font = QtGui.QFont()
        self.g_business_lineedit.setFont(font)
        self.g_business_lineedit.setText("")
        self.g_business_lineedit.setReadOnly(False)
        self.g_business_lineedit.setProperty("qp_ent_name", "")
        self.g_business_lineedit.setObjectName("g_business_lineedit")
        self.horizontalLayout_28.addWidget(self.g_business_lineedit)
        spacerItem3 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_28.addItem(spacerItem3)
        self.verticalLayout_50.addWidget(self.g_business_frame)
        self.g_entitygroups_frame = QtWidgets.QFrame(self.frame_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_entitygroups_frame.sizePolicy().hasHeightForWidth())
        self.g_entitygroups_frame.setSizePolicy(sizePolicy)
        self.g_entitygroups_frame.setMinimumSize(QtCore.QSize(0, 34))
        self.g_entitygroups_frame.setMaximumSize(QtCore.QSize(********, 34))
        self.g_entitygroups_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_entitygroups_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_entitygroups_frame.setObjectName("g_entitygroups_frame")
        self.horizontalLayout_60 = QtWidgets.QHBoxLayout(self.g_entitygroups_frame)
        self.horizontalLayout_60.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_60.setSpacing(6)
        self.horizontalLayout_60.setObjectName("horizontalLayout_60")
        self.g_entity_label_7 = QtWidgets.QLabel(self.g_entitygroups_frame)
        self.g_entity_label_7.setMinimumSize(QtCore.QSize(90, 31))
        self.g_entity_label_7.setMaximumSize(QtCore.QSize(90, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_entity_label_7.setFont(font)
        self.g_entity_label_7.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entity_label_7.setObjectName("g_entity_label_7")
        self.horizontalLayout_60.addWidget(self.g_entity_label_7, 0, QtCore.Qt.AlignHCenter|QtCore.Qt.AlignVCenter)
        self.g_group_checkcombobox = PyCheckCombobox(self.g_entitygroups_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_group_checkcombobox.sizePolicy().hasHeightForWidth())
        self.g_group_checkcombobox.setSizePolicy(sizePolicy)
        self.g_group_checkcombobox.setMinimumSize(QtCore.QSize(500, 31))
        self.g_group_checkcombobox.setMaximumSize(QtCore.QSize(500, 31))
        self.g_group_checkcombobox.setToolTip("")
        self.g_group_checkcombobox.setWhatsThis("")
        self.g_group_checkcombobox.setCurrentText("")
        self.g_group_checkcombobox.setObjectName("g_group_checkcombobox")
        self.horizontalLayout_60.addWidget(self.g_group_checkcombobox)
        self.g_managerole_button = QtWidgets.QPushButton(self.g_entitygroups_frame)
        self.g_managerole_button.setMinimumSize(QtCore.QSize(0, 0))
        self.g_managerole_button.setMaximumSize(QtCore.QSize(********, 31))
        self.g_managerole_button.setStyleSheet("border:none;\n"
"background-color:none;")
        self.g_managerole_button.setText("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/drop-down-dot"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_managerole_button.setIcon(icon)
        self.g_managerole_button.setIconSize(QtCore.QSize(22, 31))
        self.g_managerole_button.setObjectName("g_managerole_button")
        self.horizontalLayout_60.addWidget(self.g_managerole_button)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_60.addItem(spacerItem4)
        self.verticalLayout_50.addWidget(self.g_entitygroups_frame)
        self.horizontalLayout_15.addWidget(self.frame_7)
        self.frame_8 = QtWidgets.QFrame(self.frame_4)
        self.frame_8.setMinimumSize(QtCore.QSize(50, 0))
        self.frame_8.setMaximumSize(QtCore.QSize(300, ********))
        self.frame_8.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_8.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_8.setObjectName("frame_8")
        self.verticalLayout_52 = QtWidgets.QVBoxLayout(self.frame_8)
        self.verticalLayout_52.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_52.setObjectName("verticalLayout_52")
        self.horizontalLayout_15.addWidget(self.frame_8)
        self.verticalLayout_22.addWidget(self.frame_4)
        self.g_manageentity_tabwidget = QtWidgets.QTabWidget(ManageEntity)
        self.g_manageentity_tabwidget.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Maximum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_manageentity_tabwidget.sizePolicy().hasHeightForWidth())
        self.g_manageentity_tabwidget.setSizePolicy(sizePolicy)
        self.g_manageentity_tabwidget.setMinimumSize(QtCore.QSize(990, 328))
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        font.setKerning(True)
        self.g_manageentity_tabwidget.setFont(font)
        self.g_manageentity_tabwidget.setAutoFillBackground(False)
        self.g_manageentity_tabwidget.setTabPosition(QtWidgets.QTabWidget.North)
        self.g_manageentity_tabwidget.setTabShape(QtWidgets.QTabWidget.Rounded)
        self.g_manageentity_tabwidget.setIconSize(QtCore.QSize(50, 50))
        self.g_manageentity_tabwidget.setElideMode(QtCore.Qt.ElideNone)
        self.g_manageentity_tabwidget.setUsesScrollButtons(True)
        self.g_manageentity_tabwidget.setDocumentMode(False)
        self.g_manageentity_tabwidget.setTabsClosable(False)
        self.g_manageentity_tabwidget.setMovable(False)
        self.g_manageentity_tabwidget.setObjectName("g_manageentity_tabwidget")
        self.g_contactinfo_widget = QtWidgets.QWidget()
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_contactinfo_widget.setFont(font)
        self.g_contactinfo_widget.setStyleSheet("")
        self.g_contactinfo_widget.setObjectName("g_contactinfo_widget")
        self.horizontalLayout_47 = QtWidgets.QHBoxLayout(self.g_contactinfo_widget)
        self.horizontalLayout_47.setObjectName("horizontalLayout_47")
        self.frame_5 = QtWidgets.QFrame(self.g_contactinfo_widget)
        self.frame_5.setMinimumSize(QtCore.QSize(650, 0))
        self.frame_5.setMaximumSize(QtCore.QSize(650, ********))
        self.frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_5.setObjectName("frame_5")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frame_5)
        self.verticalLayout.setContentsMargins(0, 9, 0, 0)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_addresscontainer_frame = QtWidgets.QFrame(self.frame_5)
        self.g_addresscontainer_frame.setMinimumSize(QtCore.QSize(64, 68))
        self.g_addresscontainer_frame.setMaximumSize(QtCore.QSize(********, 68))
        self.g_addresscontainer_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_addresscontainer_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_addresscontainer_frame.setObjectName("g_addresscontainer_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_addresscontainer_frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setSpacing(6)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_address_label = QtWidgets.QLabel(self.g_addresscontainer_frame)
        self.g_address_label.setMinimumSize(QtCore.QSize(65, 28))
        self.g_address_label.setMaximumSize(QtCore.QSize(65, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_address_label.setFont(font)
        self.g_address_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_address_label.setObjectName("g_address_label")
        self.horizontalLayout_4.addWidget(self.g_address_label, 0, QtCore.Qt.AlignHCenter|QtCore.Qt.AlignVCenter)
        self.g_address_frame = QtWidgets.QFrame(self.g_addresscontainer_frame)
        self.g_address_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_address_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_address_frame.setObjectName("g_address_frame")
        self.verticalLayout_18 = QtWidgets.QVBoxLayout(self.g_address_frame)
        self.verticalLayout_18.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_18.setSpacing(6)
        self.verticalLayout_18.setObjectName("verticalLayout_18")
        self.g_addressline1_lineedit = QtWidgets.QLineEdit(self.g_address_frame)
        self.g_addressline1_lineedit.setMinimumSize(QtCore.QSize(443, 31))
        self.g_addressline1_lineedit.setMaximumSize(QtCore.QSize(443, 31))
        self.g_addressline1_lineedit.setReadOnly(False)
        self.g_addressline1_lineedit.setProperty("qp_addr_address_1", "")
        self.g_addressline1_lineedit.setProperty("qp_addr_address_type", "")
        self.g_addressline1_lineedit.setProperty("qp_addr_address_type_id", "")
        self.g_addressline1_lineedit.setProperty("qp_addr_rank", "")
        self.g_addressline1_lineedit.setObjectName("g_addressline1_lineedit")
        self.verticalLayout_18.addWidget(self.g_addressline1_lineedit)
        self.g_addressline2_lineedit = QtWidgets.QLineEdit(self.g_address_frame)
        self.g_addressline2_lineedit.setMinimumSize(QtCore.QSize(443, 31))
        self.g_addressline2_lineedit.setMaximumSize(QtCore.QSize(443, 31))
        self.g_addressline2_lineedit.setReadOnly(False)
        self.g_addressline2_lineedit.setProperty("qp_addr_address_2", "")
        self.g_addressline2_lineedit.setObjectName("g_addressline2_lineedit")
        self.verticalLayout_18.addWidget(self.g_addressline2_lineedit)
        self.horizontalLayout_4.addWidget(self.g_address_frame)
        spacerItem5 = QtWidgets.QSpacerItem(218, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem5)
        self.verticalLayout.addWidget(self.g_addresscontainer_frame)
        self.g_city_frame = QtWidgets.QFrame(self.frame_5)
        self.g_city_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_city_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_city_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_city_frame.setObjectName("g_city_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_city_frame)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setSpacing(6)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_city_label = QtWidgets.QLabel(self.g_city_frame)
        self.g_city_label.setMinimumSize(QtCore.QSize(65, 0))
        self.g_city_label.setMaximumSize(QtCore.QSize(65, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_city_label.setFont(font)
        self.g_city_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_city_label.setObjectName("g_city_label")
        self.horizontalLayout_5.addWidget(self.g_city_label)
        self.g_city_lineedit = QtWidgets.QLineEdit(self.g_city_frame)
        self.g_city_lineedit.setMinimumSize(QtCore.QSize(180, 31))
        self.g_city_lineedit.setMaximumSize(QtCore.QSize(180, 31))
        self.g_city_lineedit.setText("")
        self.g_city_lineedit.setReadOnly(False)
        self.g_city_lineedit.setProperty("qp_addr_city", "")
        self.g_city_lineedit.setObjectName("g_city_lineedit")
        self.horizontalLayout_5.addWidget(self.g_city_lineedit)
        self.g_state_label = QtWidgets.QLabel(self.g_city_frame)
        self.g_state_label.setMinimumSize(QtCore.QSize(60, 0))
        self.g_state_label.setMaximumSize(QtCore.QSize(60, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_state_label.setFont(font)
        self.g_state_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_state_label.setObjectName("g_state_label")
        self.horizontalLayout_5.addWidget(self.g_state_label)
        self.g_state_combobox = QtWidgets.QComboBox(self.g_city_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_state_combobox.sizePolicy().hasHeightForWidth())
        self.g_state_combobox.setSizePolicy(sizePolicy)
        self.g_state_combobox.setMinimumSize(QtCore.QSize(110, 31))
        self.g_state_combobox.setMaximumSize(QtCore.QSize(110, 31))
        self.g_state_combobox.setEditable(False)
        self.g_state_combobox.setProperty("qp_addr_region_id", "")
        self.g_state_combobox.setObjectName("g_state_combobox")
        self.horizontalLayout_5.addWidget(self.g_state_combobox)
        self.g_zip_label = QtWidgets.QLabel(self.g_city_frame)
        self.g_zip_label.setMinimumSize(QtCore.QSize(64, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_zip_label.setFont(font)
        self.g_zip_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_zip_label.setObjectName("g_zip_label")
        self.horizontalLayout_5.addWidget(self.g_zip_label)
        self.g_zip_lineedit = QtWidgets.QLineEdit(self.g_city_frame)
        self.g_zip_lineedit.setMinimumSize(QtCore.QSize(119, 31))
        self.g_zip_lineedit.setMaximumSize(QtCore.QSize(119, 31))
        self.g_zip_lineedit.setText("")
        self.g_zip_lineedit.setReadOnly(False)
        self.g_zip_lineedit.setProperty("qp_addr_postal_code", "")
        self.g_zip_lineedit.setObjectName("g_zip_lineedit")
        self.horizontalLayout_5.addWidget(self.g_zip_lineedit)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem6)
        self.verticalLayout.addWidget(self.g_city_frame)
        self.g_statezipcountry_frame = QtWidgets.QFrame(self.frame_5)
        self.g_statezipcountry_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_statezipcountry_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_statezipcountry_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_statezipcountry_frame.setObjectName("g_statezipcountry_frame")
        self.horizontalLayout_18 = QtWidgets.QHBoxLayout(self.g_statezipcountry_frame)
        self.horizontalLayout_18.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_18.setSpacing(6)
        self.horizontalLayout_18.setObjectName("horizontalLayout_18")
        self.g_country_label = QtWidgets.QLabel(self.g_statezipcountry_frame)
        self.g_country_label.setMinimumSize(QtCore.QSize(65, 0))
        self.g_country_label.setMaximumSize(QtCore.QSize(65, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_country_label.setFont(font)
        self.g_country_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_country_label.setObjectName("g_country_label")
        self.horizontalLayout_18.addWidget(self.g_country_label)
        self.g_country_combobox = QtWidgets.QComboBox(self.g_statezipcountry_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_country_combobox.sizePolicy().hasHeightForWidth())
        self.g_country_combobox.setSizePolicy(sizePolicy)
        self.g_country_combobox.setMinimumSize(QtCore.QSize(180, 31))
        self.g_country_combobox.setMaximumSize(QtCore.QSize(180, 31))
        self.g_country_combobox.setProperty("qp_addr_country_id", "")
        self.g_country_combobox.setObjectName("g_country_combobox")
        self.g_country_combobox.addItem("")
        self.g_country_combobox.addItem("")
        self.g_country_combobox.addItem("")
        self.g_country_combobox.addItem("")
        self.horizontalLayout_18.addWidget(self.g_country_combobox)
        self.g_birthday_frame = QtWidgets.QFrame(self.g_statezipcountry_frame)
        self.g_birthday_frame.setMaximumSize(QtCore.QSize(177, ********))
        self.g_birthday_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_birthday_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_birthday_frame.setObjectName("g_birthday_frame")
        self.horizontalLayout_41 = QtWidgets.QHBoxLayout(self.g_birthday_frame)
        self.horizontalLayout_41.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_41.setObjectName("horizontalLayout_41")
        self.g_city_label_3 = QtWidgets.QLabel(self.g_birthday_frame)
        self.g_city_label_3.setMinimumSize(QtCore.QSize(60, 0))
        self.g_city_label_3.setMaximumSize(QtCore.QSize(60, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_city_label_3.setFont(font)
        self.g_city_label_3.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_city_label_3.setObjectName("g_city_label_3")
        self.horizontalLayout_41.addWidget(self.g_city_label_3)
        self.g_ent_birthday_timeedit = QtWidgets.QDateTimeEdit(self.g_birthday_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_ent_birthday_timeedit.sizePolicy().hasHeightForWidth())
        self.g_ent_birthday_timeedit.setSizePolicy(sizePolicy)
        self.g_ent_birthday_timeedit.setMinimumSize(QtCore.QSize(110, 31))
        self.g_ent_birthday_timeedit.setMaximumSize(QtCore.QSize(110, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_ent_birthday_timeedit.setFont(font)
        self.g_ent_birthday_timeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_ent_birthday_timeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 6), QtCore.QTime(0, 0, 0)))
        self.g_ent_birthday_timeedit.setCalendarPopup(True)
        self.g_ent_birthday_timeedit.setObjectName("g_ent_birthday_timeedit")
        self.horizontalLayout_41.addWidget(self.g_ent_birthday_timeedit)
        self.horizontalLayout_18.addWidget(self.g_birthday_frame)
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_18.addItem(spacerItem7)
        self.verticalLayout.addWidget(self.g_statezipcountry_frame)
        self.g_phoneemial_frame = QtWidgets.QFrame(self.frame_5)
        self.g_phoneemial_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_phoneemial_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_phoneemial_frame.setObjectName("g_phoneemial_frame")
        self.horizontalLayout_20 = QtWidgets.QHBoxLayout(self.g_phoneemial_frame)
        self.horizontalLayout_20.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_20.setSpacing(0)
        self.horizontalLayout_20.setObjectName("horizontalLayout_20")
        self.g_phone_listWidget = QtWidgets.QListWidget(self.g_phoneemial_frame)
        self.g_phone_listWidget.setMinimumSize(QtCore.QSize(320, 0))
        self.g_phone_listWidget.setMaximumSize(QtCore.QSize(320, ********))
        self.g_phone_listWidget.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_phone_listWidget.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.g_phone_listWidget.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_phone_listWidget.setObjectName("g_phone_listWidget")
        self.horizontalLayout_20.addWidget(self.g_phone_listWidget)
        spacerItem8 = QtWidgets.QSpacerItem(15, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_20.addItem(spacerItem8)
        self.g_email_listWidget = QtWidgets.QListWidget(self.g_phoneemial_frame)
        self.g_email_listWidget.setMinimumSize(QtCore.QSize(320, 0))
        self.g_email_listWidget.setMaximumSize(QtCore.QSize(320, ********))
        self.g_email_listWidget.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_email_listWidget.setObjectName("g_email_listWidget")
        self.horizontalLayout_20.addWidget(self.g_email_listWidget)
        spacerItem9 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_20.addItem(spacerItem9)
        self.verticalLayout.addWidget(self.g_phoneemial_frame)
        self.horizontalLayout_47.addWidget(self.frame_5)
        self.g_contactinfoextra_scrollArea = QtWidgets.QScrollArea(self.g_contactinfo_widget)
        self.g_contactinfoextra_scrollArea.setStyleSheet(".QScrollArea {\n"
"    background-color: #f4f4f4;\n"
"    border-radius:1px;\n"
"    border: 1px solid transparent;\n"
"    padding 0px;\n"
"}\n"
"\n"
"QScrollBar:horizontal {\n"
"height:0px;\n"
"}\n"
"\n"
"#scrollAreaWidgetContents {\n"
"background-color: #f4f4f4;\n"
"}\n"
"")
        self.g_contactinfoextra_scrollArea.setWidgetResizable(True)
        self.g_contactinfoextra_scrollArea.setObjectName("g_contactinfoextra_scrollArea")
        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 287, 736))
        self.scrollAreaWidgetContents.setObjectName("scrollAreaWidgetContents")
        self.verticalLayout_39 = QtWidgets.QVBoxLayout(self.scrollAreaWidgetContents)
        self.verticalLayout_39.setObjectName("verticalLayout_39")
        self.g_label_group = QtWidgets.QGroupBox(self.scrollAreaWidgetContents)
        self.g_label_group.setMinimumSize(QtCore.QSize(235, 0))
        self.g_label_group.setMaximumSize(QtCore.QSize(********, 70))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_label_group.setFont(font)
        self.g_label_group.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_label_group.setAlignment(QtCore.Qt.AlignHCenter|QtCore.Qt.AlignTop)
        self.g_label_group.setObjectName("g_label_group")
        self.verticalLayout_53 = QtWidgets.QVBoxLayout(self.g_label_group)
        self.verticalLayout_53.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout_53.setSpacing(6)
        self.verticalLayout_53.setObjectName("verticalLayout_53")
        self.frame_9 = QtWidgets.QFrame(self.g_label_group)
        self.frame_9.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_9.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_9.setObjectName("frame_9")
        self.horizontalLayout_40 = QtWidgets.QHBoxLayout(self.frame_9)
        self.horizontalLayout_40.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_40.setSpacing(6)
        self.horizontalLayout_40.setObjectName("horizontalLayout_40")
        self.g_print_customercardlabel_button = QtWidgets.QPushButton(self.frame_9)
        self.g_print_customercardlabel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_print_customercardlabel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_print_customercardlabel_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/square_dot"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_print_customercardlabel_button.setIcon(icon1)
        self.g_print_customercardlabel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_print_customercardlabel_button.setObjectName("g_print_customercardlabel_button")
        self.horizontalLayout_40.addWidget(self.g_print_customercardlabel_button)
        self.g_print_addresslabel_button = QtWidgets.QPushButton(self.frame_9)
        self.g_print_addresslabel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_print_addresslabel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_print_addresslabel_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/entity_dialog/rectangle_dot"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_print_addresslabel_button.setIcon(icon2)
        self.g_print_addresslabel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_print_addresslabel_button.setObjectName("g_print_addresslabel_button")
        self.horizontalLayout_40.addWidget(self.g_print_addresslabel_button)
        self.verticalLayout_53.addWidget(self.frame_9)
        self.verticalLayout_39.addWidget(self.g_label_group)
        self.g_loyalty_group = QtWidgets.QGroupBox(self.scrollAreaWidgetContents)
        self.g_loyalty_group.setMinimumSize(QtCore.QSize(235, 48))
        self.g_loyalty_group.setMaximumSize(QtCore.QSize(********, 90))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_loyalty_group.setFont(font)
        self.g_loyalty_group.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_loyalty_group.setAlignment(QtCore.Qt.AlignHCenter|QtCore.Qt.AlignTop)
        self.g_loyalty_group.setObjectName("g_loyalty_group")
        self.verticalLayout_54 = QtWidgets.QVBoxLayout(self.g_loyalty_group)
        self.verticalLayout_54.setContentsMargins(9, 6, 9, 6)
        self.verticalLayout_54.setSpacing(6)
        self.verticalLayout_54.setObjectName("verticalLayout_54")
        self.g_loyaltycard_frame_2 = QtWidgets.QFrame(self.g_loyalty_group)
        self.g_loyaltycard_frame_2.setMinimumSize(QtCore.QSize(0, 35))
        self.g_loyaltycard_frame_2.setMaximumSize(QtCore.QSize(********, 35))
        self.g_loyaltycard_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_loyaltycard_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_loyaltycard_frame_2.setObjectName("g_loyaltycard_frame_2")
        self.horizontalLayout_67 = QtWidgets.QHBoxLayout(self.g_loyaltycard_frame_2)
        self.horizontalLayout_67.setContentsMargins(0, 0, 4, 0)
        self.horizontalLayout_67.setObjectName("horizontalLayout_67")
        self.g_loyaltycard_label_2 = QtWidgets.QLabel(self.g_loyaltycard_frame_2)
        self.g_loyaltycard_label_2.setMinimumSize(QtCore.QSize(80, 31))
        self.g_loyaltycard_label_2.setMaximumSize(QtCore.QSize(30, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_loyaltycard_label_2.setFont(font)
        self.g_loyaltycard_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_loyaltycard_label_2.setObjectName("g_loyaltycard_label_2")
        self.horizontalLayout_67.addWidget(self.g_loyaltycard_label_2)
        self.g_loyaltycard_lineedit = QtWidgets.QLineEdit(self.g_loyaltycard_frame_2)
        self.g_loyaltycard_lineedit.setEnabled(True)
        self.g_loyaltycard_lineedit.setMinimumSize(QtCore.QSize(100, 31))
        self.g_loyaltycard_lineedit.setMaximumSize(QtCore.QSize(100, 31))
        font = QtGui.QFont()
        self.g_loyaltycard_lineedit.setFont(font)
        self.g_loyaltycard_lineedit.setAutoFillBackground(False)
        self.g_loyaltycard_lineedit.setStyleSheet("")
        self.g_loyaltycard_lineedit.setText("")
        self.g_loyaltycard_lineedit.setFrame(True)
        self.g_loyaltycard_lineedit.setReadOnly(False)
        self.g_loyaltycard_lineedit.setClearButtonEnabled(False)
        self.g_loyaltycard_lineedit.setProperty("qp_cust_loyalty_acct_code", "")
        self.g_loyaltycard_lineedit.setObjectName("g_loyaltycard_lineedit")
        self.horizontalLayout_67.addWidget(self.g_loyaltycard_lineedit)
        spacerItem10 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_67.addItem(spacerItem10)
        self.verticalLayout_54.addWidget(self.g_loyaltycard_frame_2)
        self.g_loyaltycard_frame = QtWidgets.QFrame(self.g_loyalty_group)
        self.g_loyaltycard_frame.setMinimumSize(QtCore.QSize(0, 35))
        self.g_loyaltycard_frame.setMaximumSize(QtCore.QSize(********, 35))
        self.g_loyaltycard_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_loyaltycard_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_loyaltycard_frame.setObjectName("g_loyaltycard_frame")
        self.horizontalLayout_73 = QtWidgets.QHBoxLayout(self.g_loyaltycard_frame)
        self.horizontalLayout_73.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_73.setObjectName("horizontalLayout_73")
        self.g_loyaltybalance_label = QtWidgets.QLabel(self.g_loyaltycard_frame)
        self.g_loyaltybalance_label.setMinimumSize(QtCore.QSize(80, 31))
        self.g_loyaltybalance_label.setMaximumSize(QtCore.QSize(52, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_loyaltybalance_label.setFont(font)
        self.g_loyaltybalance_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_loyaltybalance_label.setObjectName("g_loyaltybalance_label")
        self.horizontalLayout_73.addWidget(self.g_loyaltybalance_label)
        self.g_loyaltybalance_lineedit = QtWidgets.QLineEdit(self.g_loyaltycard_frame)
        self.g_loyaltybalance_lineedit.setEnabled(True)
        self.g_loyaltybalance_lineedit.setMinimumSize(QtCore.QSize(100, 31))
        self.g_loyaltybalance_lineedit.setMaximumSize(QtCore.QSize(100, 31))
        font = QtGui.QFont()
        self.g_loyaltybalance_lineedit.setFont(font)
        self.g_loyaltybalance_lineedit.setAutoFillBackground(False)
        self.g_loyaltybalance_lineedit.setStyleSheet("")
        self.g_loyaltybalance_lineedit.setText("")
        self.g_loyaltybalance_lineedit.setFrame(True)
        self.g_loyaltybalance_lineedit.setReadOnly(False)
        self.g_loyaltybalance_lineedit.setClearButtonEnabled(False)
        self.g_loyaltybalance_lineedit.setProperty("qp_cust_loyalty_acct_code", "")
        self.g_loyaltybalance_lineedit.setObjectName("g_loyaltybalance_lineedit")
        self.horizontalLayout_73.addWidget(self.g_loyaltybalance_lineedit)
        self.g_updatebonusbucks_label = QtWidgets.QLabel(self.g_loyaltycard_frame)
        self.g_updatebonusbucks_label.setEnabled(True)
        self.g_updatebonusbucks_label.setMaximumSize(QtCore.QSize(30, ********))
        self.g_updatebonusbucks_label.setText("")
        self.g_updatebonusbucks_label.setPixmap(QtGui.QPixmap(":/customers_screen_dialog/pencil"))
        self.g_updatebonusbucks_label.setObjectName("g_updatebonusbucks_label")
        self.horizontalLayout_73.addWidget(self.g_updatebonusbucks_label)
        spacerItem11 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_73.addItem(spacerItem11)
        self.verticalLayout_54.addWidget(self.g_loyaltycard_frame)
        self.verticalLayout_39.addWidget(self.g_loyalty_group)
        self.g_subscription_group = QtWidgets.QGroupBox(self.scrollAreaWidgetContents)
        self.g_subscription_group.setMinimumSize(QtCore.QSize(0, 0))
        self.g_subscription_group.setMaximumSize(QtCore.QSize(********, 52))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_subscription_group.setFont(font)
        self.g_subscription_group.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_subscription_group.setAlignment(QtCore.Qt.AlignHCenter|QtCore.Qt.AlignTop)
        self.g_subscription_group.setObjectName("g_subscription_group")
        self.horizontalLayout_72 = QtWidgets.QHBoxLayout(self.g_subscription_group)
        self.horizontalLayout_72.setContentsMargins(9, 9, 9, 9)
        self.horizontalLayout_72.setSpacing(6)
        self.horizontalLayout_72.setObjectName("horizontalLayout_72")
        self.g_subscription_label = QtWidgets.QLabel(self.g_subscription_group)
        self.g_subscription_label.setObjectName("g_subscription_label")
        self.horizontalLayout_72.addWidget(self.g_subscription_label)
        self.verticalLayout_39.addWidget(self.g_subscription_group)
        self.g_taxes_groupbox = QtWidgets.QGroupBox(self.scrollAreaWidgetContents)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_taxes_groupbox.sizePolicy().hasHeightForWidth())
        self.g_taxes_groupbox.setSizePolicy(sizePolicy)
        self.g_taxes_groupbox.setMinimumSize(QtCore.QSize(0, 52))
        self.g_taxes_groupbox.setMaximumSize(QtCore.QSize(********, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_taxes_groupbox.setFont(font)
        self.g_taxes_groupbox.setObjectName("g_taxes_groupbox")
        self.verticalLayout_37 = QtWidgets.QVBoxLayout(self.g_taxes_groupbox)
        self.verticalLayout_37.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout_37.setSpacing(6)
        self.verticalLayout_37.setObjectName("verticalLayout_37")
        self.frame = QtWidgets.QFrame(self.g_taxes_groupbox)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout_17.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        self.g_tax_exemptions_label = QtWidgets.QLabel(self.frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_tax_exemptions_label.setFont(font)
        self.g_tax_exemptions_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_tax_exemptions_label.setWordWrap(True)
        self.g_tax_exemptions_label.setObjectName("g_tax_exemptions_label")
        self.horizontalLayout_17.addWidget(self.g_tax_exemptions_label)
        self.g_updatetaxcertificates_label = QtWidgets.QLabel(self.frame)
        self.g_updatetaxcertificates_label.setEnabled(True)
        self.g_updatetaxcertificates_label.setMaximumSize(QtCore.QSize(30, ********))
        self.g_updatetaxcertificates_label.setText("")
        self.g_updatetaxcertificates_label.setPixmap(QtGui.QPixmap(":/customers_screen_dialog/pencil"))
        self.g_updatetaxcertificates_label.setObjectName("g_updatetaxcertificates_label")
        self.horizontalLayout_17.addWidget(self.g_updatetaxcertificates_label)
        self.verticalLayout_37.addWidget(self.frame)
        self.g_tax_exemption_table = QtWidgets.QTableWidget(self.g_taxes_groupbox)
        self.g_tax_exemption_table.setMaximumSize(QtCore.QSize(********, 120))
        self.g_tax_exemption_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_tax_exemption_table.setAlternatingRowColors(True)
        self.g_tax_exemption_table.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.g_tax_exemption_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_tax_exemption_table.setObjectName("g_tax_exemption_table")
        self.g_tax_exemption_table.setColumnCount(3)
        self.g_tax_exemption_table.setRowCount(4)
        item = QtWidgets.QTableWidgetItem()
        self.g_tax_exemption_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_tax_exemption_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_tax_exemption_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_tax_exemption_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tax_exemption_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tax_exemption_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tax_exemption_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tax_exemption_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tax_exemption_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_tax_exemption_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_tax_exemption_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_tax_exemption_table.setItem(2, 2, item)
        self.g_tax_exemption_table.horizontalHeader().setCascadingSectionResizes(True)
        self.g_tax_exemption_table.horizontalHeader().setDefaultSectionSize(200)
        self.g_tax_exemption_table.horizontalHeader().setStretchLastSection(True)
        self.g_tax_exemption_table.verticalHeader().setVisible(False)
        self.verticalLayout_37.addWidget(self.g_tax_exemption_table)
        self.verticalLayout_39.addWidget(self.g_taxes_groupbox)
        self.g_custompricing_group = QtWidgets.QGroupBox(self.scrollAreaWidgetContents)
        self.g_custompricing_group.setMinimumSize(QtCore.QSize(0, 52))
        self.g_custompricing_group.setMaximumSize(QtCore.QSize(********, 52))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_custompricing_group.setFont(font)
        self.g_custompricing_group.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_custompricing_group.setAlignment(QtCore.Qt.AlignHCenter|QtCore.Qt.AlignTop)
        self.g_custompricing_group.setObjectName("g_custompricing_group")
        self.horizontalLayout_74 = QtWidgets.QHBoxLayout(self.g_custompricing_group)
        self.horizontalLayout_74.setContentsMargins(9, 9, 9, 9)
        self.horizontalLayout_74.setSpacing(6)
        self.horizontalLayout_74.setObjectName("horizontalLayout_74")
        self.g_custompricing_label = QtWidgets.QLabel(self.g_custompricing_group)
        self.g_custompricing_label.setObjectName("g_custompricing_label")
        self.horizontalLayout_74.addWidget(self.g_custompricing_label)
        self.g_custompricing_label_2 = QtWidgets.QLabel(self.g_custompricing_group)
        self.g_custompricing_label_2.setEnabled(True)
        self.g_custompricing_label_2.setMaximumSize(QtCore.QSize(30, 30))
        self.g_custompricing_label_2.setStyleSheet("QScrollArea {\n"
"    border: none;\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"QScrollArea > QWidget {\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"QScrollArea QWidget#scrollAreaWidgetContents {\n"
"    background-color: #f4f4f4;\n"
"    border: none;\n"
"}\n"
"\n"
"QScrollBar:horizontal {\n"
"    height: 0px;\n"
"}\n"
"\n"
"QScrollBar:vertical {\n"
"    width: 0px;\n"
"}")
        self.g_custompricing_label_2.setText("")
        self.g_custompricing_label_2.setPixmap(QtGui.QPixmap(":/customers_screen_dialog/pencil"))
        self.g_custompricing_label_2.setObjectName("g_custompricing_label_2")
        self.horizontalLayout_74.addWidget(self.g_custompricing_label_2)
        self.verticalLayout_39.addWidget(self.g_custompricing_group)
        self.g_houseaccount_group = QtWidgets.QGroupBox(self.scrollAreaWidgetContents)
        self.g_houseaccount_group.setMinimumSize(QtCore.QSize(0, 0))
        self.g_houseaccount_group.setMaximumSize(QtCore.QSize(********, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_houseaccount_group.setFont(font)
        self.g_houseaccount_group.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_houseaccount_group.setAlignment(QtCore.Qt.AlignHCenter|QtCore.Qt.AlignTop)
        self.g_houseaccount_group.setObjectName("g_houseaccount_group")
        self.verticalLayout_59 = QtWidgets.QVBoxLayout(self.g_houseaccount_group)
        self.verticalLayout_59.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout_59.setSpacing(6)
        self.verticalLayout_59.setObjectName("verticalLayout_59")
        self.g_houseaccount_label = QtWidgets.QLabel(self.g_houseaccount_group)
        self.g_houseaccount_label.setObjectName("g_houseaccount_label")
        self.verticalLayout_59.addWidget(self.g_houseaccount_label)
        self.g_houseaccount_table = QtWidgets.QTableWidget(self.g_houseaccount_group)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_houseaccount_table.sizePolicy().hasHeightForWidth())
        self.g_houseaccount_table.setSizePolicy(sizePolicy)
        self.g_houseaccount_table.setMinimumSize(QtCore.QSize(0, 0))
        self.g_houseaccount_table.setMaximumSize(QtCore.QSize(********, 120))
        self.g_houseaccount_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_houseaccount_table.setAlternatingRowColors(True)
        self.g_houseaccount_table.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.g_houseaccount_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_houseaccount_table.setObjectName("g_houseaccount_table")
        self.g_houseaccount_table.setColumnCount(3)
        self.g_houseaccount_table.setRowCount(4)
        item = QtWidgets.QTableWidgetItem()
        self.g_houseaccount_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_houseaccount_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_houseaccount_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_houseaccount_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_houseaccount_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_houseaccount_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_houseaccount_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_houseaccount_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_houseaccount_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_houseaccount_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_houseaccount_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_houseaccount_table.setItem(2, 1, item)
        self.g_houseaccount_table.horizontalHeader().setCascadingSectionResizes(True)
        self.g_houseaccount_table.horizontalHeader().setDefaultSectionSize(200)
        self.g_houseaccount_table.horizontalHeader().setStretchLastSection(True)
        self.g_houseaccount_table.verticalHeader().setVisible(False)
        self.verticalLayout_59.addWidget(self.g_houseaccount_table)
        self.g_houseaccountcontrols_frame = QtWidgets.QFrame(self.g_houseaccount_group)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_houseaccountcontrols_frame.sizePolicy().hasHeightForWidth())
        self.g_houseaccountcontrols_frame.setSizePolicy(sizePolicy)
        self.g_houseaccountcontrols_frame.setMinimumSize(QtCore.QSize(247, 39))
        self.g_houseaccountcontrols_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.g_houseaccountcontrols_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_houseaccountcontrols_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_houseaccountcontrols_frame.setObjectName("g_houseaccountcontrols_frame")
        self.horizontalLayout_25 = QtWidgets.QHBoxLayout(self.g_houseaccountcontrols_frame)
        self.horizontalLayout_25.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_25.setSpacing(6)
        self.horizontalLayout_25.setObjectName("horizontalLayout_25")
        spacerItem12 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_25.addItem(spacerItem12)
        self.g_payhouseaccount_button = QtWidgets.QPushButton(self.g_houseaccountcontrols_frame)
        self.g_payhouseaccount_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_payhouseaccount_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_payhouseaccount_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/entity_dialog/dollar"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_payhouseaccount_button.setIcon(icon3)
        self.g_payhouseaccount_button.setIconSize(QtCore.QSize(24, 24))
        self.g_payhouseaccount_button.setObjectName("g_payhouseaccount_button")
        self.horizontalLayout_25.addWidget(self.g_payhouseaccount_button)
        self.g_newhouseaccount_button = QtWidgets.QPushButton(self.g_houseaccountcontrols_frame)
        self.g_newhouseaccount_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_newhouseaccount_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_newhouseaccount_button.setStyleSheet("")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/entity_dialog/icons/flat_add_white_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_newhouseaccount_button.setIcon(icon4)
        self.g_newhouseaccount_button.setIconSize(QtCore.QSize(24, 24))
        self.g_newhouseaccount_button.setObjectName("g_newhouseaccount_button")
        self.horizontalLayout_25.addWidget(self.g_newhouseaccount_button)
        self.g_managehouseaccount_button = QtWidgets.QPushButton(self.g_houseaccountcontrols_frame)
        self.g_managehouseaccount_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_managehouseaccount_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_managehouseaccount_button.setStyleSheet("")
        icon5 = QtGui.QIcon()
        icon5.addPixmap(QtGui.QPixmap(":/entity_dialog/edit"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_managehouseaccount_button.setIcon(icon5)
        self.g_managehouseaccount_button.setIconSize(QtCore.QSize(24, 24))
        self.g_managehouseaccount_button.setObjectName("g_managehouseaccount_button")
        self.horizontalLayout_25.addWidget(self.g_managehouseaccount_button)
        self.verticalLayout_59.addWidget(self.g_houseaccountcontrols_frame)
        self.verticalLayout_39.addWidget(self.g_houseaccount_group)
        spacerItem13 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_39.addItem(spacerItem13)
        self.g_contactinfoextra_scrollArea.setWidget(self.scrollAreaWidgetContents)
        self.horizontalLayout_47.addWidget(self.g_contactinfoextra_scrollArea)
        self.g_manageentity_tabwidget.addTab(self.g_contactinfo_widget, "")
        self.g_address_tab = QtWidgets.QWidget()
        self.g_address_tab.setObjectName("g_address_tab")
        self.verticalLayout_40 = QtWidgets.QVBoxLayout(self.g_address_tab)
        self.verticalLayout_40.setObjectName("verticalLayout_40")
        self.g_address_not_available_frame = QtWidgets.QFrame(self.g_address_tab)
        self.g_address_not_available_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_address_not_available_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_address_not_available_frame.setObjectName("g_address_not_available_frame")
        self.verticalLayout_107 = QtWidgets.QVBoxLayout(self.g_address_not_available_frame)
        self.verticalLayout_107.setObjectName("verticalLayout_107")
        spacerItem14 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_107.addItem(spacerItem14)
        self.label = QtWidgets.QLabel(self.g_address_not_available_frame)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.verticalLayout_107.addWidget(self.label)
        spacerItem15 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_107.addItem(spacerItem15)
        self.verticalLayout_40.addWidget(self.g_address_not_available_frame)
        self.g_address_table = QtWidgets.QTableWidget(self.g_address_tab)
        self.g_address_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_address_table.setAlternatingRowColors(True)
        self.g_address_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_address_table.setObjectName("g_address_table")
        self.g_address_table.setColumnCount(10)
        self.g_address_table.setRowCount(5)
        item = QtWidgets.QTableWidgetItem()
        self.g_address_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_address_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_address_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_address_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_address_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_address_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_address_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_address_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_address_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_address_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_address_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_address_table.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_address_table.setHorizontalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_address_table.setHorizontalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_address_table.setHorizontalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_address_table.setItem(0, 5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_address_table.setItem(0, 6, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_address_table.setItem(0, 8, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_address_table.setItem(2, 7, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_address_table.setItem(3, 9, item)
        self.g_address_table.horizontalHeader().setCascadingSectionResizes(True)
        self.g_address_table.horizontalHeader().setDefaultSectionSize(100)
        self.g_address_table.horizontalHeader().setStretchLastSection(True)
        self.g_address_table.verticalHeader().setVisible(False)
        self.verticalLayout_40.addWidget(self.g_address_table)
        self.g_address_buttons_frame = QtWidgets.QFrame(self.g_address_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_address_buttons_frame.sizePolicy().hasHeightForWidth())
        self.g_address_buttons_frame.setSizePolicy(sizePolicy)
        self.g_address_buttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_address_buttons_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.g_address_buttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_address_buttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_address_buttons_frame.setObjectName("g_address_buttons_frame")
        self.horizontalLayout_85 = QtWidgets.QHBoxLayout(self.g_address_buttons_frame)
        self.horizontalLayout_85.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_85.setSpacing(6)
        self.horizontalLayout_85.setObjectName("horizontalLayout_85")
        spacerItem16 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_85.addItem(spacerItem16)
        self.g_deleteaddress_button = QtWidgets.QPushButton(self.g_address_buttons_frame)
        self.g_deleteaddress_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_deleteaddress_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_deleteaddress_button.setStyleSheet("")
        icon6 = QtGui.QIcon()
        icon6.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_deleteaddress_button.setIcon(icon6)
        self.g_deleteaddress_button.setIconSize(QtCore.QSize(24, 24))
        self.g_deleteaddress_button.setObjectName("g_deleteaddress_button")
        self.horizontalLayout_85.addWidget(self.g_deleteaddress_button)
        self.g_manageaddress_button = QtWidgets.QPushButton(self.g_address_buttons_frame)
        self.g_manageaddress_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manageaddress_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_manageaddress_button.setStyleSheet("")
        icon7 = QtGui.QIcon()
        icon7.addPixmap(QtGui.QPixmap(":/entity_dialog/manage"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_manageaddress_button.setIcon(icon7)
        self.g_manageaddress_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manageaddress_button.setObjectName("g_manageaddress_button")
        self.horizontalLayout_85.addWidget(self.g_manageaddress_button)
        self.g_primaryaddress_button = QtWidgets.QPushButton(self.g_address_buttons_frame)
        self.g_primaryaddress_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_primaryaddress_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_primaryaddress_button.setStyleSheet("")
        icon8 = QtGui.QIcon()
        icon8.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_primaryaddress_button.setIcon(icon8)
        self.g_primaryaddress_button.setIconSize(QtCore.QSize(24, 24))
        self.g_primaryaddress_button.setObjectName("g_primaryaddress_button")
        self.horizontalLayout_85.addWidget(self.g_primaryaddress_button)
        self.g_addaddress_button = QtWidgets.QPushButton(self.g_address_buttons_frame)
        self.g_addaddress_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_addaddress_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_addaddress_button.setStyleSheet("")
        self.g_addaddress_button.setIcon(icon8)
        self.g_addaddress_button.setIconSize(QtCore.QSize(24, 24))
        self.g_addaddress_button.setObjectName("g_addaddress_button")
        self.horizontalLayout_85.addWidget(self.g_addaddress_button)
        self.verticalLayout_40.addWidget(self.g_address_buttons_frame)
        self.g_manageentity_tabwidget.addTab(self.g_address_tab, "")
        self.g_website_tab = QtWidgets.QWidget()
        self.g_website_tab.setObjectName("g_website_tab")
        self.verticalLayout_60 = QtWidgets.QVBoxLayout(self.g_website_tab)
        self.verticalLayout_60.setObjectName("verticalLayout_60")
        self.g_websites_not_available_frame = QtWidgets.QFrame(self.g_website_tab)
        self.g_websites_not_available_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_websites_not_available_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_websites_not_available_frame.setObjectName("g_websites_not_available_frame")
        self.verticalLayout_108 = QtWidgets.QVBoxLayout(self.g_websites_not_available_frame)
        self.verticalLayout_108.setObjectName("verticalLayout_108")
        spacerItem17 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_108.addItem(spacerItem17)
        self.label_2 = QtWidgets.QLabel(self.g_websites_not_available_frame)
        self.label_2.setAlignment(QtCore.Qt.AlignCenter)
        self.label_2.setObjectName("label_2")
        self.verticalLayout_108.addWidget(self.label_2)
        spacerItem18 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_108.addItem(spacerItem18)
        self.verticalLayout_60.addWidget(self.g_websites_not_available_frame)
        self.g_website_username_frame = QtWidgets.QFrame(self.g_website_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_website_username_frame.sizePolicy().hasHeightForWidth())
        self.g_website_username_frame.setSizePolicy(sizePolicy)
        self.g_website_username_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_website_username_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_website_username_frame.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.g_website_username_frame.setObjectName("g_website_username_frame")
        self.horizontalLayout_71 = QtWidgets.QHBoxLayout(self.g_website_username_frame)
        self.horizontalLayout_71.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_71.setSpacing(6)
        self.horizontalLayout_71.setObjectName("horizontalLayout_71")
        self.g_resetpassword_frame = QtWidgets.QFrame(self.g_website_username_frame)
        self.g_resetpassword_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_resetpassword_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_resetpassword_frame.setObjectName("g_resetpassword_frame")
        self.horizontalLayout_78 = QtWidgets.QHBoxLayout(self.g_resetpassword_frame)
        self.horizontalLayout_78.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_78.setObjectName("horizontalLayout_78")
        self.g_entity_label_2 = QtWidgets.QLabel(self.g_resetpassword_frame)
        self.g_entity_label_2.setMinimumSize(QtCore.QSize(65, 31))
        self.g_entity_label_2.setMaximumSize(QtCore.QSize(65, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_entity_label_2.setFont(font)
        self.g_entity_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entity_label_2.setObjectName("g_entity_label_2")
        self.horizontalLayout_78.addWidget(self.g_entity_label_2)
        self.g_webusername_lineedit = QtWidgets.QLineEdit(self.g_resetpassword_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_webusername_lineedit.sizePolicy().hasHeightForWidth())
        self.g_webusername_lineedit.setSizePolicy(sizePolicy)
        self.g_webusername_lineedit.setMinimumSize(QtCore.QSize(248, 31))
        self.g_webusername_lineedit.setMaximumSize(QtCore.QSize(248, 31))
        font = QtGui.QFont()
        self.g_webusername_lineedit.setFont(font)
        self.g_webusername_lineedit.setReadOnly(True)
        self.g_webusername_lineedit.setPlaceholderText("")
        self.g_webusername_lineedit.setProperty("qp_epn_names_before_key_names", "")
        self.g_webusername_lineedit.setObjectName("g_webusername_lineedit")
        self.horizontalLayout_78.addWidget(self.g_webusername_lineedit)
        self.g_resetpassword_button = QtWidgets.QPushButton(self.g_resetpassword_frame)
        self.g_resetpassword_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_resetpassword_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_resetpassword_button.setStyleSheet("")
        self.g_resetpassword_button.setIcon(icon8)
        self.g_resetpassword_button.setIconSize(QtCore.QSize(24, 24))
        self.g_resetpassword_button.setObjectName("g_resetpassword_button")
        self.horizontalLayout_78.addWidget(self.g_resetpassword_button)
        self.horizontalLayout_71.addWidget(self.g_resetpassword_frame)
        self.g_createusername_button = QtWidgets.QPushButton(self.g_website_username_frame)
        self.g_createusername_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_createusername_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_createusername_button.setStyleSheet("")
        self.g_createusername_button.setIcon(icon8)
        self.g_createusername_button.setIconSize(QtCore.QSize(24, 24))
        self.g_createusername_button.setObjectName("g_createusername_button")
        self.horizontalLayout_71.addWidget(self.g_createusername_button)
        self.g_welcomeemail_button = QtWidgets.QPushButton(self.g_website_username_frame)
        self.g_welcomeemail_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_welcomeemail_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_welcomeemail_button.setStyleSheet("")
        self.g_welcomeemail_button.setIcon(icon8)
        self.g_welcomeemail_button.setIconSize(QtCore.QSize(24, 24))
        self.g_welcomeemail_button.setObjectName("g_welcomeemail_button")
        self.horizontalLayout_71.addWidget(self.g_welcomeemail_button)
        spacerItem19 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_71.addItem(spacerItem19)
        self.verticalLayout_60.addWidget(self.g_website_username_frame)
        self.g_website_v_spacer_frame = QtWidgets.QFrame(self.g_website_tab)
        self.g_website_v_spacer_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_website_v_spacer_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_website_v_spacer_frame.setObjectName("g_website_v_spacer_frame")
        self.verticalLayout_63 = QtWidgets.QVBoxLayout(self.g_website_v_spacer_frame)
        self.verticalLayout_63.setObjectName("verticalLayout_63")
        spacerItem20 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_63.addItem(spacerItem20)
        self.verticalLayout_60.addWidget(self.g_website_v_spacer_frame)
        self.g_marketingsubscription_frame = QtWidgets.QFrame(self.g_website_tab)
        self.g_marketingsubscription_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_marketingsubscription_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_marketingsubscription_frame.setObjectName("g_marketingsubscription_frame")
        self.verticalLayout_61 = QtWidgets.QVBoxLayout(self.g_marketingsubscription_frame)
        self.verticalLayout_61.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_61.setObjectName("verticalLayout_61")
        self.g_marketingsubsciptions_table = QtWidgets.QTableWidget(self.g_marketingsubscription_frame)
        self.g_marketingsubsciptions_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_marketingsubsciptions_table.setAlternatingRowColors(True)
        self.g_marketingsubsciptions_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_marketingsubsciptions_table.setObjectName("g_marketingsubsciptions_table")
        self.g_marketingsubsciptions_table.setColumnCount(2)
        self.g_marketingsubsciptions_table.setRowCount(5)
        item = QtWidgets.QTableWidgetItem()
        self.g_marketingsubsciptions_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_marketingsubsciptions_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_marketingsubsciptions_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_marketingsubsciptions_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_marketingsubsciptions_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_marketingsubsciptions_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_marketingsubsciptions_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_marketingsubsciptions_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_marketingsubsciptions_table.setItem(0, 1, item)
        self.g_marketingsubsciptions_table.horizontalHeader().setCascadingSectionResizes(True)
        self.g_marketingsubsciptions_table.horizontalHeader().setDefaultSectionSize(100)
        self.g_marketingsubsciptions_table.horizontalHeader().setStretchLastSection(True)
        self.g_marketingsubsciptions_table.verticalHeader().setVisible(False)
        self.verticalLayout_61.addWidget(self.g_marketingsubsciptions_table)
        self.g_website_ontrol_frame = QtWidgets.QFrame(self.g_marketingsubscription_frame)
        self.g_website_ontrol_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_website_ontrol_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_website_ontrol_frame.setObjectName("g_website_ontrol_frame")
        self.horizontalLayout_82 = QtWidgets.QHBoxLayout(self.g_website_ontrol_frame)
        self.horizontalLayout_82.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_82.setSpacing(6)
        self.horizontalLayout_82.setObjectName("horizontalLayout_82")
        spacerItem21 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_82.addItem(spacerItem21)
        self.g_diablemarketingsubscription_button = QtWidgets.QPushButton(self.g_website_ontrol_frame)
        self.g_diablemarketingsubscription_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_diablemarketingsubscription_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_diablemarketingsubscription_button.setStyleSheet("")
        self.g_diablemarketingsubscription_button.setIcon(icon6)
        self.g_diablemarketingsubscription_button.setIconSize(QtCore.QSize(24, 24))
        self.g_diablemarketingsubscription_button.setObjectName("g_diablemarketingsubscription_button")
        self.horizontalLayout_82.addWidget(self.g_diablemarketingsubscription_button)
        self.g_enablemarketingsubscription_button = QtWidgets.QPushButton(self.g_website_ontrol_frame)
        self.g_enablemarketingsubscription_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_enablemarketingsubscription_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_enablemarketingsubscription_button.setStyleSheet("")
        self.g_enablemarketingsubscription_button.setIcon(icon6)
        self.g_enablemarketingsubscription_button.setIconSize(QtCore.QSize(24, 24))
        self.g_enablemarketingsubscription_button.setObjectName("g_enablemarketingsubscription_button")
        self.horizontalLayout_82.addWidget(self.g_enablemarketingsubscription_button)
        self.g_emailmarketingsubscription_button = QtWidgets.QPushButton(self.g_website_ontrol_frame)
        self.g_emailmarketingsubscription_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_emailmarketingsubscription_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_emailmarketingsubscription_button.setStyleSheet("")
        self.g_emailmarketingsubscription_button.setIcon(icon8)
        self.g_emailmarketingsubscription_button.setIconSize(QtCore.QSize(24, 24))
        self.g_emailmarketingsubscription_button.setObjectName("g_emailmarketingsubscription_button")
        self.horizontalLayout_82.addWidget(self.g_emailmarketingsubscription_button)
        self.verticalLayout_61.addWidget(self.g_website_ontrol_frame)
        self.verticalLayout_60.addWidget(self.g_marketingsubscription_frame)
        self.g_manageentity_tabwidget.addTab(self.g_website_tab, "")
        self.g_notes_widget = QtWidgets.QWidget()
        self.g_notes_widget.setStyleSheet("")
        self.g_notes_widget.setObjectName("g_notes_widget")
        self.verticalLayout_12 = QtWidgets.QVBoxLayout(self.g_notes_widget)
        self.verticalLayout_12.setObjectName("verticalLayout_12")
        self.g_publicnotes_groupbox = QtWidgets.QGroupBox(self.g_notes_widget)
        self.g_publicnotes_groupbox.setObjectName("g_publicnotes_groupbox")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_publicnotes_groupbox)
        self.horizontalLayout_6.setContentsMargins(9, 9, 9, 9)
        self.horizontalLayout_6.setSpacing(6)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_warning_label = QtWidgets.QLabel(self.g_publicnotes_groupbox)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_warning_label.setFont(font)
        self.g_warning_label.setObjectName("g_warning_label")
        self.horizontalLayout_6.addWidget(self.g_warning_label)
        self.g_warning_lineedit = QtWidgets.QLineEdit(self.g_publicnotes_groupbox)
        self.g_warning_lineedit.setText("")
        self.g_warning_lineedit.setProperty("qp_cust_warning", "")
        self.g_warning_lineedit.setObjectName("g_warning_lineedit")
        self.horizontalLayout_6.addWidget(self.g_warning_lineedit)
        self.g_info_label = QtWidgets.QLabel(self.g_publicnotes_groupbox)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_info_label.setFont(font)
        self.g_info_label.setObjectName("g_info_label")
        self.horizontalLayout_6.addWidget(self.g_info_label)
        self.g_info_lineedit = QtWidgets.QLineEdit(self.g_publicnotes_groupbox)
        self.g_info_lineedit.setText("")
        self.g_info_lineedit.setProperty("qp_cust_info", "")
        self.g_info_lineedit.setObjectName("g_info_lineedit")
        self.horizontalLayout_6.addWidget(self.g_info_lineedit)
        self.g_info_lineedit.raise_()
        self.g_warning_lineedit.raise_()
        self.g_info_label.raise_()
        self.g_warning_label.raise_()
        self.verticalLayout_12.addWidget(self.g_publicnotes_groupbox)
        self.g_privatenotes_groupbox = QtWidgets.QGroupBox(self.g_notes_widget)
        self.g_privatenotes_groupbox.setObjectName("g_privatenotes_groupbox")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_privatenotes_groupbox)
        self.horizontalLayout_7.setContentsMargins(9, 9, 9, 9)
        self.horizontalLayout_7.setSpacing(6)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_privatenotes_plaintextedit = QtWidgets.QPlainTextEdit(self.g_privatenotes_groupbox)
        self.g_privatenotes_plaintextedit.setProperty("qp_ent_notes", "")
        self.g_privatenotes_plaintextedit.setObjectName("g_privatenotes_plaintextedit")
        self.horizontalLayout_7.addWidget(self.g_privatenotes_plaintextedit)
        self.verticalLayout_12.addWidget(self.g_privatenotes_groupbox)
        self.g_employeenotes_groupbox = QtWidgets.QGroupBox(self.g_notes_widget)
        self.g_employeenotes_groupbox.setObjectName("g_employeenotes_groupbox")
        self.horizontalLayout_52 = QtWidgets.QHBoxLayout(self.g_employeenotes_groupbox)
        self.horizontalLayout_52.setContentsMargins(9, 9, 9, 9)
        self.horizontalLayout_52.setSpacing(6)
        self.horizontalLayout_52.setObjectName("horizontalLayout_52")
        self.g_privateemployee_plaintextedit = QtWidgets.QPlainTextEdit(self.g_employeenotes_groupbox)
        self.g_privateemployee_plaintextedit.setProperty("qp_ent_notes", "")
        self.g_privateemployee_plaintextedit.setObjectName("g_privateemployee_plaintextedit")
        self.horizontalLayout_52.addWidget(self.g_privateemployee_plaintextedit)
        self.verticalLayout_12.addWidget(self.g_employeenotes_groupbox)
        self.g_manageentity_tabwidget.addTab(self.g_notes_widget, "")
        self.g_pictures_widget = QtWidgets.QWidget()
        self.g_pictures_widget.setObjectName("g_pictures_widget")
        self.verticalLayout_14 = QtWidgets.QVBoxLayout(self.g_pictures_widget)
        self.verticalLayout_14.setObjectName("verticalLayout_14")
        self.g_cropper_area = QtWidgets.QFrame(self.g_pictures_widget)
        self.g_cropper_area.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cropper_area.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cropper_area.setObjectName("g_cropper_area")
        self.verticalLayout_29 = QtWidgets.QVBoxLayout(self.g_cropper_area)
        self.verticalLayout_29.setObjectName("verticalLayout_29")
        self.g_description_frame = QtWidgets.QFrame(self.g_cropper_area)
        self.g_description_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_description_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_description_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_description_frame.setObjectName("g_description_frame")
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout(self.g_description_frame)
        self.horizontalLayout_14.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_14.setSpacing(6)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.g_desc_label = QtWidgets.QLabel(self.g_description_frame)
        self.g_desc_label.setMinimumSize(QtCore.QSize(140, 0))
        self.g_desc_label.setMaximumSize(QtCore.QSize(140, ********))
        self.g_desc_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_desc_label.setObjectName("g_desc_label")
        self.horizontalLayout_14.addWidget(self.g_desc_label)
        self.g_description_lineedit = QtWidgets.QLineEdit(self.g_description_frame)
        self.g_description_lineedit.setObjectName("g_description_lineedit")
        self.horizontalLayout_14.addWidget(self.g_description_lineedit)
        self.verticalLayout_29.addWidget(self.g_description_frame)
        self.g_mediatitle_frame = QtWidgets.QFrame(self.g_cropper_area)
        self.g_mediatitle_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_mediatitle_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_mediatitle_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_mediatitle_frame.setObjectName("g_mediatitle_frame")
        self.horizontalLayout_21 = QtWidgets.QHBoxLayout(self.g_mediatitle_frame)
        self.horizontalLayout_21.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_21.setSpacing(6)
        self.horizontalLayout_21.setObjectName("horizontalLayout_21")
        self.g_title_label = QtWidgets.QLabel(self.g_mediatitle_frame)
        self.g_title_label.setMinimumSize(QtCore.QSize(140, 0))
        self.g_title_label.setMaximumSize(QtCore.QSize(140, ********))
        self.g_title_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_title_label.setObjectName("g_title_label")
        self.horizontalLayout_21.addWidget(self.g_title_label)
        self.g_title_lineedit = QtWidgets.QLineEdit(self.g_mediatitle_frame)
        self.g_title_lineedit.setObjectName("g_title_lineedit")
        self.horizontalLayout_21.addWidget(self.g_title_lineedit)
        self.g_tags_label = QtWidgets.QLabel(self.g_mediatitle_frame)
        self.g_tags_label.setMinimumSize(QtCore.QSize(50, 0))
        self.g_tags_label.setMaximumSize(QtCore.QSize(50, ********))
        self.g_tags_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_tags_label.setObjectName("g_tags_label")
        self.horizontalLayout_21.addWidget(self.g_tags_label)
        self.g_tags_lineedit = QtWidgets.QLineEdit(self.g_mediatitle_frame)
        self.g_tags_lineedit.setObjectName("g_tags_lineedit")
        self.horizontalLayout_21.addWidget(self.g_tags_lineedit)
        self.verticalLayout_29.addWidget(self.g_mediatitle_frame)
        self.g_cropper_frame = QtWidgets.QFrame(self.g_cropper_area)
        self.g_cropper_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_cropper_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cropper_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cropper_frame.setObjectName("g_cropper_frame")
        self.horizontalLayout_62 = QtWidgets.QHBoxLayout(self.g_cropper_frame)
        self.horizontalLayout_62.setSpacing(6)
        self.horizontalLayout_62.setObjectName("horizontalLayout_62")
        self.verticalLayout_29.addWidget(self.g_cropper_frame)
        self.g_size_label = QtWidgets.QLabel(self.g_cropper_area)
        self.g_size_label.setMinimumSize(QtCore.QSize(90, 0))
        self.g_size_label.setMaximumSize(QtCore.QSize(********, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_size_label.setFont(font)
        self.g_size_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_size_label.setObjectName("g_size_label")
        self.verticalLayout_29.addWidget(self.g_size_label)
        spacerItem22 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_29.addItem(spacerItem22)
        spacerItem23 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_29.addItem(spacerItem23)
        self.g_controlbuttons_frame_2 = QtWidgets.QFrame(self.g_cropper_area)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame_2.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame_2.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame_2.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame_2.setMaximumSize(QtCore.QSize(********, 40))
        self.g_controlbuttons_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame_2.setObjectName("g_controlbuttons_frame_2")
        self.horizontalLayout_58 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame_2)
        self.horizontalLayout_58.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_58.setSpacing(6)
        self.horizontalLayout_58.setObjectName("horizontalLayout_58")
        spacerItem24 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_58.addItem(spacerItem24)
        self.g_cropper_cancel_btn = QtWidgets.QPushButton(self.g_controlbuttons_frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_cropper_cancel_btn.sizePolicy().hasHeightForWidth())
        self.g_cropper_cancel_btn.setSizePolicy(sizePolicy)
        self.g_cropper_cancel_btn.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cropper_cancel_btn.setMaximumSize(QtCore.QSize(120, ********))
        icon9 = QtGui.QIcon()
        icon9.addPixmap(QtGui.QPixmap(":/pet_tracker/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cropper_cancel_btn.setIcon(icon9)
        self.g_cropper_cancel_btn.setIconSize(QtCore.QSize(24, 24))
        self.g_cropper_cancel_btn.setObjectName("g_cropper_cancel_btn")
        self.horizontalLayout_58.addWidget(self.g_cropper_cancel_btn)
        self.g_crop_and_save_btn = QtWidgets.QPushButton(self.g_controlbuttons_frame_2)
        self.g_crop_and_save_btn.setMinimumSize(QtCore.QSize(120, 40))
        self.g_crop_and_save_btn.setMaximumSize(QtCore.QSize(120, ********))
        icon10 = QtGui.QIcon()
        icon10.addPixmap(QtGui.QPixmap(":/pet_tracker/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_crop_and_save_btn.setIcon(icon10)
        self.g_crop_and_save_btn.setIconSize(QtCore.QSize(24, 24))
        self.g_crop_and_save_btn.setObjectName("g_crop_and_save_btn")
        self.horizontalLayout_58.addWidget(self.g_crop_and_save_btn)
        self.verticalLayout_29.addWidget(self.g_controlbuttons_frame_2)
        self.verticalLayout_14.addWidget(self.g_cropper_area)
        self.g_addedit_groupBox = QtWidgets.QGroupBox(self.g_pictures_widget)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_addedit_groupBox.setFont(font)
        self.g_addedit_groupBox.setObjectName("g_addedit_groupBox")
        self.verticalLayout_38 = QtWidgets.QVBoxLayout(self.g_addedit_groupBox)
        self.verticalLayout_38.setSpacing(6)
        self.verticalLayout_38.setObjectName("verticalLayout_38")
        self.g_entity_images_list = QtWidgets.QListWidget(self.g_addedit_groupBox)
        self.g_entity_images_list.setMinimumSize(QtCore.QSize(0, 0))
        self.g_entity_images_list.setWordWrap(False)
        self.g_entity_images_list.setObjectName("g_entity_images_list")
        self.verticalLayout_38.addWidget(self.g_entity_images_list)
        self.g_upload_frame = QtWidgets.QFrame(self.g_addedit_groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_upload_frame.sizePolicy().hasHeightForWidth())
        self.g_upload_frame.setSizePolicy(sizePolicy)
        self.g_upload_frame.setMinimumSize(QtCore.QSize(0, 50))
        self.g_upload_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.g_upload_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_upload_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_upload_frame.setObjectName("g_upload_frame")
        self.horizontalLayout_45 = QtWidgets.QHBoxLayout(self.g_upload_frame)
        self.horizontalLayout_45.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_45.setSpacing(6)
        self.horizontalLayout_45.setObjectName("horizontalLayout_45")
        self.g_uploadnew_label = QtWidgets.QLabel(self.g_upload_frame)
        self.g_uploadnew_label.setMinimumSize(QtCore.QSize(80, 31))
        self.g_uploadnew_label.setMaximumSize(QtCore.QSize(0, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_uploadnew_label.setFont(font)
        self.g_uploadnew_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_uploadnew_label.setObjectName("g_uploadnew_label")
        self.horizontalLayout_45.addWidget(self.g_uploadnew_label)
        self.g_uploadfilename_lineedit = QtWidgets.QLineEdit(self.g_upload_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_uploadfilename_lineedit.sizePolicy().hasHeightForWidth())
        self.g_uploadfilename_lineedit.setSizePolicy(sizePolicy)
        self.g_uploadfilename_lineedit.setMinimumSize(QtCore.QSize(100, 31))
        self.g_uploadfilename_lineedit.setMaximumSize(QtCore.QSize(********, 31))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_uploadfilename_lineedit.setFont(font)
        self.g_uploadfilename_lineedit.setReadOnly(False)
        self.g_uploadfilename_lineedit.setObjectName("g_uploadfilename_lineedit")
        self.horizontalLayout_45.addWidget(self.g_uploadfilename_lineedit)
        self.g_selectfile_button = QtWidgets.QPushButton(self.g_upload_frame)
        self.g_selectfile_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_selectfile_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_selectfile_button.setStyleSheet("")
        self.g_selectfile_button.setIconSize(QtCore.QSize(24, 24))
        self.g_selectfile_button.setObjectName("g_selectfile_button")
        self.horizontalLayout_45.addWidget(self.g_selectfile_button)
        self.verticalLayout_38.addWidget(self.g_upload_frame)
        self.verticalLayout_14.addWidget(self.g_addedit_groupBox)
        self.g_manageentity_tabwidget.addTab(self.g_pictures_widget, "")
        self.g_sales_widget = QtWidgets.QWidget()
        self.g_sales_widget.setStyleSheet("")
        self.g_sales_widget.setObjectName("g_sales_widget")
        self.verticalLayout_9 = QtWidgets.QVBoxLayout(self.g_sales_widget)
        self.verticalLayout_9.setObjectName("verticalLayout_9")
        self.g_salestab_tabWidget = QtWidgets.QTabWidget(self.g_sales_widget)
        self.g_salestab_tabWidget.setObjectName("g_salestab_tabWidget")
        self.g_history_tab = QtWidgets.QWidget()
        self.g_history_tab.setObjectName("g_history_tab")
        self.verticalLayout_55 = QtWidgets.QVBoxLayout(self.g_history_tab)
        self.verticalLayout_55.setObjectName("verticalLayout_55")
        self.g_startenddate_frame_3 = QtWidgets.QFrame(self.g_history_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_startenddate_frame_3.sizePolicy().hasHeightForWidth())
        self.g_startenddate_frame_3.setSizePolicy(sizePolicy)
        self.g_startenddate_frame_3.setMinimumSize(QtCore.QSize(0, 0))
        self.g_startenddate_frame_3.setMaximumSize(QtCore.QSize(********, 31))
        self.g_startenddate_frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_startenddate_frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_startenddate_frame_3.setObjectName("g_startenddate_frame_3")
        self.horizontalLayout_29 = QtWidgets.QHBoxLayout(self.g_startenddate_frame_3)
        self.horizontalLayout_29.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_29.setSpacing(6)
        self.horizontalLayout_29.setObjectName("horizontalLayout_29")
        self.g_startdate_label_3 = QtWidgets.QLabel(self.g_startenddate_frame_3)
        self.g_startdate_label_3.setMinimumSize(QtCore.QSize(70, 31))
        self.g_startdate_label_3.setMaximumSize(QtCore.QSize(60, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_startdate_label_3.setFont(font)
        self.g_startdate_label_3.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_startdate_label_3.setObjectName("g_startdate_label_3")
        self.horizontalLayout_29.addWidget(self.g_startdate_label_3)
        self.g_sales_startdate_timeedit = QtWidgets.QDateTimeEdit(self.g_startenddate_frame_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sales_startdate_timeedit.sizePolicy().hasHeightForWidth())
        self.g_sales_startdate_timeedit.setSizePolicy(sizePolicy)
        self.g_sales_startdate_timeedit.setMinimumSize(QtCore.QSize(120, 31))
        self.g_sales_startdate_timeedit.setMaximumSize(QtCore.QSize(120, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_sales_startdate_timeedit.setFont(font)
        self.g_sales_startdate_timeedit.setStyleSheet("")
        self.g_sales_startdate_timeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_sales_startdate_timeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2013, 12, 6), QtCore.QTime(0, 0, 0)))
        self.g_sales_startdate_timeedit.setCalendarPopup(True)
        self.g_sales_startdate_timeedit.setTimeSpec(QtCore.Qt.LocalTime)
        self.g_sales_startdate_timeedit.setObjectName("g_sales_startdate_timeedit")
        self.horizontalLayout_29.addWidget(self.g_sales_startdate_timeedit)
        self.g_enddate_label_3 = QtWidgets.QLabel(self.g_startenddate_frame_3)
        self.g_enddate_label_3.setMinimumSize(QtCore.QSize(70, 31))
        self.g_enddate_label_3.setMaximumSize(QtCore.QSize(60, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_enddate_label_3.setFont(font)
        self.g_enddate_label_3.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_enddate_label_3.setObjectName("g_enddate_label_3")
        self.horizontalLayout_29.addWidget(self.g_enddate_label_3)
        self.g_sales_enddate_timeedit = QtWidgets.QDateTimeEdit(self.g_startenddate_frame_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sales_enddate_timeedit.sizePolicy().hasHeightForWidth())
        self.g_sales_enddate_timeedit.setSizePolicy(sizePolicy)
        self.g_sales_enddate_timeedit.setMinimumSize(QtCore.QSize(120, 31))
        self.g_sales_enddate_timeedit.setMaximumSize(QtCore.QSize(120, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_sales_enddate_timeedit.setFont(font)
        self.g_sales_enddate_timeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_sales_enddate_timeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 6), QtCore.QTime(0, 0, 0)))
        self.g_sales_enddate_timeedit.setCalendarPopup(True)
        self.g_sales_enddate_timeedit.setObjectName("g_sales_enddate_timeedit")
        self.horizontalLayout_29.addWidget(self.g_sales_enddate_timeedit)
        self.g_sales_type_frame = QtWidgets.QFrame(self.g_startenddate_frame_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sales_type_frame.sizePolicy().hasHeightForWidth())
        self.g_sales_type_frame.setSizePolicy(sizePolicy)
        self.g_sales_type_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_sales_type_frame.setMaximumSize(QtCore.QSize(********, 31))
        self.g_sales_type_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_sales_type_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_sales_type_frame.setObjectName("g_sales_type_frame")
        self.horizontalLayout_37 = QtWidgets.QHBoxLayout(self.g_sales_type_frame)
        self.horizontalLayout_37.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_37.setObjectName("horizontalLayout_37")
        self.g_sales_type_label = QtWidgets.QLabel(self.g_sales_type_frame)
        self.g_sales_type_label.setMinimumSize(QtCore.QSize(40, 0))
        self.g_sales_type_label.setMaximumSize(QtCore.QSize(40, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_sales_type_label.setFont(font)
        self.g_sales_type_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_sales_type_label.setObjectName("g_sales_type_label")
        self.horizontalLayout_37.addWidget(self.g_sales_type_label)
        self.g_sales_type_combbox = QtWidgets.QComboBox(self.g_sales_type_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sales_type_combbox.sizePolicy().hasHeightForWidth())
        self.g_sales_type_combbox.setSizePolicy(sizePolicy)
        self.g_sales_type_combbox.setMinimumSize(QtCore.QSize(175, 31))
        self.g_sales_type_combbox.setMaximumSize(QtCore.QSize(175, 31))
        self.g_sales_type_combbox.setEditable(True)
        self.g_sales_type_combbox.setObjectName("g_sales_type_combbox")
        self.g_sales_type_combbox.addItem("")
        self.g_sales_type_combbox.addItem("")
        self.g_sales_type_combbox.addItem("")
        self.horizontalLayout_37.addWidget(self.g_sales_type_combbox)
        self.horizontalLayout_29.addWidget(self.g_sales_type_frame)
        self.g_sales_sumbyperiod_frame = QtWidgets.QFrame(self.g_startenddate_frame_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sales_sumbyperiod_frame.sizePolicy().hasHeightForWidth())
        self.g_sales_sumbyperiod_frame.setSizePolicy(sizePolicy)
        self.g_sales_sumbyperiod_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_sales_sumbyperiod_frame.setMaximumSize(QtCore.QSize(********, 31))
        self.g_sales_sumbyperiod_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_sales_sumbyperiod_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_sales_sumbyperiod_frame.setObjectName("g_sales_sumbyperiod_frame")
        self.horizontalLayout_39 = QtWidgets.QHBoxLayout(self.g_sales_sumbyperiod_frame)
        self.horizontalLayout_39.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_39.setObjectName("horizontalLayout_39")
        self.g_sales_sumbyperiod_label = QtWidgets.QLabel(self.g_sales_sumbyperiod_frame)
        self.g_sales_sumbyperiod_label.setMinimumSize(QtCore.QSize(90, 0))
        self.g_sales_sumbyperiod_label.setMaximumSize(QtCore.QSize(90, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_sales_sumbyperiod_label.setFont(font)
        self.g_sales_sumbyperiod_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_sales_sumbyperiod_label.setObjectName("g_sales_sumbyperiod_label")
        self.horizontalLayout_39.addWidget(self.g_sales_sumbyperiod_label)
        self.g_sales_sumbyperiod_combbox = QtWidgets.QComboBox(self.g_sales_sumbyperiod_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sales_sumbyperiod_combbox.sizePolicy().hasHeightForWidth())
        self.g_sales_sumbyperiod_combbox.setSizePolicy(sizePolicy)
        self.g_sales_sumbyperiod_combbox.setMinimumSize(QtCore.QSize(100, 31))
        self.g_sales_sumbyperiod_combbox.setMaximumSize(QtCore.QSize(100, 31))
        self.g_sales_sumbyperiod_combbox.setEditable(True)
        self.g_sales_sumbyperiod_combbox.setObjectName("g_sales_sumbyperiod_combbox")
        self.g_sales_sumbyperiod_combbox.addItem("")
        self.g_sales_sumbyperiod_combbox.addItem("")
        self.g_sales_sumbyperiod_combbox.addItem("")
        self.horizontalLayout_39.addWidget(self.g_sales_sumbyperiod_combbox)
        spacerItem25 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_39.addItem(spacerItem25)
        self.g_exportsales_label = QtWidgets.QLabel(self.g_sales_sumbyperiod_frame)
        self.g_exportsales_label.setEnabled(True)
        self.g_exportsales_label.setText("")
        self.g_exportsales_label.setPixmap(QtGui.QPixmap(":/entity_dialog/export"))
        self.g_exportsales_label.setObjectName("g_exportsales_label")
        self.horizontalLayout_39.addWidget(self.g_exportsales_label)
        spacerItem26 = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_39.addItem(spacerItem26)
        self.horizontalLayout_29.addWidget(self.g_sales_sumbyperiod_frame)
        self.verticalLayout_55.addWidget(self.g_startenddate_frame_3)
        self.g_sales_table = QtWidgets.QTableWidget(self.g_history_tab)
        self.g_sales_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_sales_table.setAlternatingRowColors(True)
        self.g_sales_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_sales_table.setObjectName("g_sales_table")
        self.g_sales_table.setColumnCount(7)
        self.g_sales_table.setRowCount(5)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_sales_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_sales_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_sales_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_sales_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_sales_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_sales_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_sales_table.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_sales_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_sales_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(1, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_sales_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(2, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(2, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_sales_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(3, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(3, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_sales_table.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(4, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(4, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_sales_table.setItem(4, 3, item)
        self.g_sales_table.horizontalHeader().setCascadingSectionResizes(True)
        self.g_sales_table.horizontalHeader().setDefaultSectionSize(100)
        self.g_sales_table.horizontalHeader().setStretchLastSection(True)
        self.g_sales_table.verticalHeader().setVisible(False)
        self.verticalLayout_55.addWidget(self.g_sales_table)
        self.g_salestab_tabWidget.addTab(self.g_history_tab, "")
        self.g_repeatdelivery_tab = QtWidgets.QWidget()
        self.g_repeatdelivery_tab.setObjectName("g_repeatdelivery_tab")
        self.verticalLayout_58 = QtWidgets.QVBoxLayout(self.g_repeatdelivery_tab)
        self.verticalLayout_58.setObjectName("verticalLayout_58")
        self.g_startenddate_repeatdelivery_frame = QtWidgets.QFrame(self.g_repeatdelivery_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_startenddate_repeatdelivery_frame.sizePolicy().hasHeightForWidth())
        self.g_startenddate_repeatdelivery_frame.setSizePolicy(sizePolicy)
        self.g_startenddate_repeatdelivery_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_startenddate_repeatdelivery_frame.setMaximumSize(QtCore.QSize(********, 31))
        self.g_startenddate_repeatdelivery_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_startenddate_repeatdelivery_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_startenddate_repeatdelivery_frame.setObjectName("g_startenddate_repeatdelivery_frame")
        self.horizontalLayout_42 = QtWidgets.QHBoxLayout(self.g_startenddate_repeatdelivery_frame)
        self.horizontalLayout_42.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_42.setSpacing(6)
        self.horizontalLayout_42.setObjectName("horizontalLayout_42")
        self.g_repeat_order_startdate_label = QtWidgets.QLabel(self.g_startenddate_repeatdelivery_frame)
        self.g_repeat_order_startdate_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_repeat_order_startdate_label.setMaximumSize(QtCore.QSize(60, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_repeat_order_startdate_label.setFont(font)
        self.g_repeat_order_startdate_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_repeat_order_startdate_label.setObjectName("g_repeat_order_startdate_label")
        self.horizontalLayout_42.addWidget(self.g_repeat_order_startdate_label)
        self.g_start_repeatdelivery_datetimeedit = QtWidgets.QDateTimeEdit(self.g_startenddate_repeatdelivery_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_start_repeatdelivery_datetimeedit.sizePolicy().hasHeightForWidth())
        self.g_start_repeatdelivery_datetimeedit.setSizePolicy(sizePolicy)
        self.g_start_repeatdelivery_datetimeedit.setMinimumSize(QtCore.QSize(120, 31))
        self.g_start_repeatdelivery_datetimeedit.setMaximumSize(QtCore.QSize(120, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_start_repeatdelivery_datetimeedit.setFont(font)
        self.g_start_repeatdelivery_datetimeedit.setStyleSheet("")
        self.g_start_repeatdelivery_datetimeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_start_repeatdelivery_datetimeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2013, 12, 6), QtCore.QTime(0, 0, 0)))
        self.g_start_repeatdelivery_datetimeedit.setCalendarPopup(True)
        self.g_start_repeatdelivery_datetimeedit.setTimeSpec(QtCore.Qt.LocalTime)
        self.g_start_repeatdelivery_datetimeedit.setObjectName("g_start_repeatdelivery_datetimeedit")
        self.horizontalLayout_42.addWidget(self.g_start_repeatdelivery_datetimeedit)
        self.g_repeat_orders_enddate_label = QtWidgets.QLabel(self.g_startenddate_repeatdelivery_frame)
        self.g_repeat_orders_enddate_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_repeat_orders_enddate_label.setMaximumSize(QtCore.QSize(60, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_repeat_orders_enddate_label.setFont(font)
        self.g_repeat_orders_enddate_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_repeat_orders_enddate_label.setObjectName("g_repeat_orders_enddate_label")
        self.horizontalLayout_42.addWidget(self.g_repeat_orders_enddate_label)
        self.g_end_start_repeatdelivery_datetimeedit = QtWidgets.QDateTimeEdit(self.g_startenddate_repeatdelivery_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_end_start_repeatdelivery_datetimeedit.sizePolicy().hasHeightForWidth())
        self.g_end_start_repeatdelivery_datetimeedit.setSizePolicy(sizePolicy)
        self.g_end_start_repeatdelivery_datetimeedit.setMinimumSize(QtCore.QSize(120, 31))
        self.g_end_start_repeatdelivery_datetimeedit.setMaximumSize(QtCore.QSize(120, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_end_start_repeatdelivery_datetimeedit.setFont(font)
        self.g_end_start_repeatdelivery_datetimeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_end_start_repeatdelivery_datetimeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 6), QtCore.QTime(0, 0, 0)))
        self.g_end_start_repeatdelivery_datetimeedit.setCalendarPopup(True)
        self.g_end_start_repeatdelivery_datetimeedit.setObjectName("g_end_start_repeatdelivery_datetimeedit")
        self.horizontalLayout_42.addWidget(self.g_end_start_repeatdelivery_datetimeedit)
        self.g_sales_type_frame_2 = QtWidgets.QFrame(self.g_startenddate_repeatdelivery_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sales_type_frame_2.sizePolicy().hasHeightForWidth())
        self.g_sales_type_frame_2.setSizePolicy(sizePolicy)
        self.g_sales_type_frame_2.setMinimumSize(QtCore.QSize(0, 0))
        self.g_sales_type_frame_2.setMaximumSize(QtCore.QSize(********, 31))
        self.g_sales_type_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_sales_type_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_sales_type_frame_2.setObjectName("g_sales_type_frame_2")
        self.horizontalLayout_68 = QtWidgets.QHBoxLayout(self.g_sales_type_frame_2)
        self.horizontalLayout_68.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_68.setObjectName("horizontalLayout_68")
        self.horizontalLayout_42.addWidget(self.g_sales_type_frame_2)
        self.g_sales_sumbyperiod_frame_2 = QtWidgets.QFrame(self.g_startenddate_repeatdelivery_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sales_sumbyperiod_frame_2.sizePolicy().hasHeightForWidth())
        self.g_sales_sumbyperiod_frame_2.setSizePolicy(sizePolicy)
        self.g_sales_sumbyperiod_frame_2.setMinimumSize(QtCore.QSize(0, 0))
        self.g_sales_sumbyperiod_frame_2.setMaximumSize(QtCore.QSize(********, 31))
        self.g_sales_sumbyperiod_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_sales_sumbyperiod_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_sales_sumbyperiod_frame_2.setObjectName("g_sales_sumbyperiod_frame_2")
        self.horizontalLayout_69 = QtWidgets.QHBoxLayout(self.g_sales_sumbyperiod_frame_2)
        self.horizontalLayout_69.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_69.setObjectName("horizontalLayout_69")
        self.g_locaiton_repeat_orders_frame = QtWidgets.QFrame(self.g_sales_sumbyperiod_frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_locaiton_repeat_orders_frame.sizePolicy().hasHeightForWidth())
        self.g_locaiton_repeat_orders_frame.setSizePolicy(sizePolicy)
        self.g_locaiton_repeat_orders_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_locaiton_repeat_orders_frame.setMaximumSize(QtCore.QSize(********, 31))
        self.g_locaiton_repeat_orders_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_locaiton_repeat_orders_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_locaiton_repeat_orders_frame.setObjectName("g_locaiton_repeat_orders_frame")
        self.horizontalLayout_48 = QtWidgets.QHBoxLayout(self.g_locaiton_repeat_orders_frame)
        self.horizontalLayout_48.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_48.setObjectName("horizontalLayout_48")
        self.g_location_label = QtWidgets.QLabel(self.g_locaiton_repeat_orders_frame)
        self.g_location_label.setMinimumSize(QtCore.QSize(40, 0))
        self.g_location_label.setMaximumSize(QtCore.QSize(********, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_location_label.setFont(font)
        self.g_location_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_location_label.setObjectName("g_location_label")
        self.horizontalLayout_48.addWidget(self.g_location_label)
        self.g_location_repeat_orders_combobox = QtWidgets.QComboBox(self.g_locaiton_repeat_orders_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_location_repeat_orders_combobox.sizePolicy().hasHeightForWidth())
        self.g_location_repeat_orders_combobox.setSizePolicy(sizePolicy)
        self.g_location_repeat_orders_combobox.setMinimumSize(QtCore.QSize(175, 31))
        self.g_location_repeat_orders_combobox.setMaximumSize(QtCore.QSize(175, 31))
        self.g_location_repeat_orders_combobox.setEditable(True)
        self.g_location_repeat_orders_combobox.setCurrentText("")
        self.g_location_repeat_orders_combobox.setObjectName("g_location_repeat_orders_combobox")
        self.horizontalLayout_48.addWidget(self.g_location_repeat_orders_combobox)
        self.horizontalLayout_69.addWidget(self.g_locaiton_repeat_orders_frame)
        self.horizontalLayout_42.addWidget(self.g_sales_sumbyperiod_frame_2)
        self.verticalLayout_58.addWidget(self.g_startenddate_repeatdelivery_frame)
        self.g_repeatdelivery_table = QtWidgets.QTableWidget(self.g_repeatdelivery_tab)
        self.g_repeatdelivery_table.setEnabled(True)
        self.g_repeatdelivery_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_repeatdelivery_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_repeatdelivery_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_repeatdelivery_table.setAlternatingRowColors(True)
        self.g_repeatdelivery_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_repeatdelivery_table.setShowGrid(False)
        self.g_repeatdelivery_table.setObjectName("g_repeatdelivery_table")
        self.g_repeatdelivery_table.setColumnCount(9)
        self.g_repeatdelivery_table.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_repeatdelivery_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_repeatdelivery_table.setHorizontalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setHorizontalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setItem(0, 4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setItem(0, 5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_repeatdelivery_table.setItem(0, 6, item)
        self.g_repeatdelivery_table.horizontalHeader().setDefaultSectionSize(150)
        self.g_repeatdelivery_table.horizontalHeader().setStretchLastSection(True)
        self.g_repeatdelivery_table.verticalHeader().setVisible(False)
        self.verticalLayout_58.addWidget(self.g_repeatdelivery_table)
        self.g_paymentscontrols_frame_5 = QtWidgets.QFrame(self.g_repeatdelivery_tab)
        self.g_paymentscontrols_frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_paymentscontrols_frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_paymentscontrols_frame_5.setObjectName("g_paymentscontrols_frame_5")
        self.horizontalLayout_80 = QtWidgets.QHBoxLayout(self.g_paymentscontrols_frame_5)
        self.horizontalLayout_80.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_80.setSpacing(6)
        self.horizontalLayout_80.setObjectName("horizontalLayout_80")
        self.g_repeat_order_status_label = QtWidgets.QLabel(self.g_paymentscontrols_frame_5)
        self.g_repeat_order_status_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_repeat_order_status_label.setMaximumSize(QtCore.QSize(********, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_repeat_order_status_label.setFont(font)
        self.g_repeat_order_status_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_repeat_order_status_label.setObjectName("g_repeat_order_status_label")
        self.horizontalLayout_80.addWidget(self.g_repeat_order_status_label)
        spacerItem27 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_80.addItem(spacerItem27)
        self.g_deleterepeatdelivery_button = QtWidgets.QPushButton(self.g_paymentscontrols_frame_5)
        self.g_deleterepeatdelivery_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_deleterepeatdelivery_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_deleterepeatdelivery_button.setStyleSheet("")
        self.g_deleterepeatdelivery_button.setIcon(icon6)
        self.g_deleterepeatdelivery_button.setIconSize(QtCore.QSize(24, 24))
        self.g_deleterepeatdelivery_button.setObjectName("g_deleterepeatdelivery_button")
        self.horizontalLayout_80.addWidget(self.g_deleterepeatdelivery_button)
        self.g_managerepeatdelivery_button = QtWidgets.QPushButton(self.g_paymentscontrols_frame_5)
        self.g_managerepeatdelivery_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_managerepeatdelivery_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_managerepeatdelivery_button.setStyleSheet("")
        self.g_managerepeatdelivery_button.setIcon(icon5)
        self.g_managerepeatdelivery_button.setIconSize(QtCore.QSize(24, 24))
        self.g_managerepeatdelivery_button.setObjectName("g_managerepeatdelivery_button")
        self.horizontalLayout_80.addWidget(self.g_managerepeatdelivery_button)
        self.g_repeatdelivery_massaction_button = QtWidgets.QPushButton(self.g_paymentscontrols_frame_5)
        self.g_repeatdelivery_massaction_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_repeatdelivery_massaction_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_repeatdelivery_massaction_button.setStyleSheet("")
        icon11 = QtGui.QIcon()
        icon11.addPixmap(QtGui.QPixmap(":/entity_dialog/mass_action"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_repeatdelivery_massaction_button.setIcon(icon11)
        self.g_repeatdelivery_massaction_button.setIconSize(QtCore.QSize(24, 24))
        self.g_repeatdelivery_massaction_button.setObjectName("g_repeatdelivery_massaction_button")
        self.horizontalLayout_80.addWidget(self.g_repeatdelivery_massaction_button)
        self.g_addrepeatdelivery_button = QtWidgets.QPushButton(self.g_paymentscontrols_frame_5)
        self.g_addrepeatdelivery_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_addrepeatdelivery_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_addrepeatdelivery_button.setStyleSheet("")
        self.g_addrepeatdelivery_button.setIcon(icon4)
        self.g_addrepeatdelivery_button.setIconSize(QtCore.QSize(24, 24))
        self.g_addrepeatdelivery_button.setObjectName("g_addrepeatdelivery_button")
        self.horizontalLayout_80.addWidget(self.g_addrepeatdelivery_button)
        self.verticalLayout_58.addWidget(self.g_paymentscontrols_frame_5)
        self.g_salestab_tabWidget.addTab(self.g_repeatdelivery_tab, "")
        self.g_layaways_tab = QtWidgets.QWidget()
        self.g_layaways_tab.setObjectName("g_layaways_tab")
        self.verticalLayout_56 = QtWidgets.QVBoxLayout(self.g_layaways_tab)
        self.verticalLayout_56.setObjectName("verticalLayout_56")
        self.g_layaways_table = QtWidgets.QTableWidget(self.g_layaways_tab)
        self.g_layaways_table.setEnabled(True)
        self.g_layaways_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_layaways_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_layaways_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_layaways_table.setAlternatingRowColors(True)
        self.g_layaways_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_layaways_table.setShowGrid(False)
        self.g_layaways_table.setObjectName("g_layaways_table")
        self.g_layaways_table.setColumnCount(6)
        self.g_layaways_table.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_layaways_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_layaways_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_layaways_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_layaways_table.setItem(0, 5, item)
        self.g_layaways_table.horizontalHeader().setDefaultSectionSize(150)
        self.g_layaways_table.horizontalHeader().setStretchLastSection(True)
        self.g_layaways_table.verticalHeader().setVisible(False)
        self.verticalLayout_56.addWidget(self.g_layaways_table)
        self.g_paymentscontrols_frame_2 = QtWidgets.QFrame(self.g_layaways_tab)
        self.g_paymentscontrols_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_paymentscontrols_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_paymentscontrols_frame_2.setObjectName("g_paymentscontrols_frame_2")
        self.horizontalLayout_33 = QtWidgets.QHBoxLayout(self.g_paymentscontrols_frame_2)
        self.horizontalLayout_33.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_33.setSpacing(6)
        self.horizontalLayout_33.setObjectName("horizontalLayout_33")
        spacerItem28 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_33.addItem(spacerItem28)
        self.g_display_layaway_button = QtWidgets.QPushButton(self.g_paymentscontrols_frame_2)
        self.g_display_layaway_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_display_layaway_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_display_layaway_button.setStyleSheet("")
        icon12 = QtGui.QIcon()
        icon12.addPixmap(QtGui.QPixmap(":/entity_dialog/find"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_display_layaway_button.setIcon(icon12)
        self.g_display_layaway_button.setIconSize(QtCore.QSize(24, 24))
        self.g_display_layaway_button.setObjectName("g_display_layaway_button")
        self.horizontalLayout_33.addWidget(self.g_display_layaway_button)
        self.g_make_payment_button = QtWidgets.QPushButton(self.g_paymentscontrols_frame_2)
        self.g_make_payment_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_make_payment_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_make_payment_button.setStyleSheet("")
        self.g_make_payment_button.setIcon(icon3)
        self.g_make_payment_button.setIconSize(QtCore.QSize(24, 24))
        self.g_make_payment_button.setObjectName("g_make_payment_button")
        self.horizontalLayout_33.addWidget(self.g_make_payment_button)
        self.verticalLayout_56.addWidget(self.g_paymentscontrols_frame_2)
        self.g_salestab_tabWidget.addTab(self.g_layaways_tab, "")
        self.g_openorders_tab = QtWidgets.QWidget()
        self.g_openorders_tab.setObjectName("g_openorders_tab")
        self.verticalLayout_31 = QtWidgets.QVBoxLayout(self.g_openorders_tab)
        self.verticalLayout_31.setObjectName("verticalLayout_31")
        self.g_quotes_table = QtWidgets.QTableWidget(self.g_openorders_tab)
        self.g_quotes_table.setEnabled(True)
        self.g_quotes_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_quotes_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_quotes_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_quotes_table.setAlternatingRowColors(True)
        self.g_quotes_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_quotes_table.setShowGrid(False)
        self.g_quotes_table.setObjectName("g_quotes_table")
        self.g_quotes_table.setColumnCount(6)
        self.g_quotes_table.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_quotes_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_quotes_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_quotes_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_quotes_table.setItem(0, 5, item)
        self.g_quotes_table.horizontalHeader().setDefaultSectionSize(150)
        self.g_quotes_table.horizontalHeader().setStretchLastSection(True)
        self.g_quotes_table.verticalHeader().setVisible(False)
        self.verticalLayout_31.addWidget(self.g_quotes_table)
        self.__g_quotesbottombar_frame = QtWidgets.QFrame(self.g_openorders_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_quotesbottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_quotesbottombar_frame.setSizePolicy(sizePolicy)
        self.__g_quotesbottombar_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_quotesbottombar_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.__g_quotesbottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_quotesbottombar_frame.setStyleSheet("")
        self.__g_quotesbottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_quotesbottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_quotesbottombar_frame.setObjectName("__g_quotesbottombar_frame")
        self.horizontalLayout_70 = QtWidgets.QHBoxLayout(self.__g_quotesbottombar_frame)
        self.horizontalLayout_70.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_70.setSpacing(0)
        self.horizontalLayout_70.setObjectName("horizontalLayout_70")
        self.g_quotestotalresults_label = QtWidgets.QLabel(self.__g_quotesbottombar_frame)
        self.g_quotestotalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_quotestotalresults_label.setFont(font)
        self.g_quotestotalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_quotestotalresults_label.setObjectName("g_quotestotalresults_label")
        self.horizontalLayout_70.addWidget(self.g_quotestotalresults_label)
        spacerItem29 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_70.addItem(spacerItem29)
        self.__g_quotesbuttons_frame = QtWidgets.QFrame(self.__g_quotesbottombar_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_quotesbuttons_frame.sizePolicy().hasHeightForWidth())
        self.__g_quotesbuttons_frame.setSizePolicy(sizePolicy)
        self.__g_quotesbuttons_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_quotesbuttons_frame.setMaximumSize(QtCore.QSize(495, 40))
        self.__g_quotesbuttons_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_quotesbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_quotesbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_quotesbuttons_frame.setObjectName("__g_quotesbuttons_frame")
        self.horizontalLayout_81 = QtWidgets.QHBoxLayout(self.__g_quotesbuttons_frame)
        self.horizontalLayout_81.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_81.setSpacing(5)
        self.horizontalLayout_81.setObjectName("horizontalLayout_81")
        self.g_quotes_select_button = QtWidgets.QPushButton(self.__g_quotesbuttons_frame)
        self.g_quotes_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_quotes_select_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_quotes_select_button.setStyleSheet("")
        self.g_quotes_select_button.setIcon(icon8)
        self.g_quotes_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_quotes_select_button.setObjectName("g_quotes_select_button")
        self.horizontalLayout_81.addWidget(self.g_quotes_select_button)
        self.horizontalLayout_70.addWidget(self.__g_quotesbuttons_frame)
        self.verticalLayout_31.addWidget(self.__g_quotesbottombar_frame)
        self.g_salestab_tabWidget.addTab(self.g_openorders_tab, "")
        self.g_specialorders_tab = QtWidgets.QWidget()
        self.g_specialorders_tab.setObjectName("g_specialorders_tab")
        self.verticalLayout_57 = QtWidgets.QVBoxLayout(self.g_specialorders_tab)
        self.verticalLayout_57.setObjectName("verticalLayout_57")
        self.g_layaways_table_3 = QtWidgets.QTableWidget(self.g_specialorders_tab)
        self.g_layaways_table_3.setEnabled(True)
        self.g_layaways_table_3.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_layaways_table_3.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_layaways_table_3.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_layaways_table_3.setAlternatingRowColors(True)
        self.g_layaways_table_3.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_layaways_table_3.setShowGrid(False)
        self.g_layaways_table_3.setObjectName("g_layaways_table_3")
        self.g_layaways_table_3.setColumnCount(6)
        self.g_layaways_table_3.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_layaways_table_3.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table_3.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table_3.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_layaways_table_3.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table_3.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_layaways_table_3.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_layaways_table_3.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_layaways_table_3.setItem(0, 5, item)
        self.g_layaways_table_3.horizontalHeader().setDefaultSectionSize(150)
        self.g_layaways_table_3.horizontalHeader().setStretchLastSection(True)
        self.g_layaways_table_3.verticalHeader().setVisible(False)
        self.verticalLayout_57.addWidget(self.g_layaways_table_3)
        self.g_paymentscontrols_frame_4 = QtWidgets.QFrame(self.g_specialorders_tab)
        self.g_paymentscontrols_frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_paymentscontrols_frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_paymentscontrols_frame_4.setObjectName("g_paymentscontrols_frame_4")
        self.horizontalLayout_79 = QtWidgets.QHBoxLayout(self.g_paymentscontrols_frame_4)
        self.horizontalLayout_79.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_79.setSpacing(6)
        self.horizontalLayout_79.setObjectName("horizontalLayout_79")
        spacerItem30 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_79.addItem(spacerItem30)
        self.g_display_layaway_button_3 = QtWidgets.QPushButton(self.g_paymentscontrols_frame_4)
        self.g_display_layaway_button_3.setMinimumSize(QtCore.QSize(120, 40))
        self.g_display_layaway_button_3.setMaximumSize(QtCore.QSize(120, 40))
        self.g_display_layaway_button_3.setStyleSheet("")
        self.g_display_layaway_button_3.setIcon(icon12)
        self.g_display_layaway_button_3.setIconSize(QtCore.QSize(24, 24))
        self.g_display_layaway_button_3.setObjectName("g_display_layaway_button_3")
        self.horizontalLayout_79.addWidget(self.g_display_layaway_button_3)
        self.g_make_payment_button_3 = QtWidgets.QPushButton(self.g_paymentscontrols_frame_4)
        self.g_make_payment_button_3.setMinimumSize(QtCore.QSize(120, 40))
        self.g_make_payment_button_3.setMaximumSize(QtCore.QSize(120, 40))
        self.g_make_payment_button_3.setStyleSheet("")
        self.g_make_payment_button_3.setIcon(icon3)
        self.g_make_payment_button_3.setIconSize(QtCore.QSize(24, 24))
        self.g_make_payment_button_3.setObjectName("g_make_payment_button_3")
        self.horizontalLayout_79.addWidget(self.g_make_payment_button_3)
        self.verticalLayout_57.addWidget(self.g_paymentscontrols_frame_4)
        self.g_salestab_tabWidget.addTab(self.g_specialorders_tab, "")
        self.verticalLayout_9.addWidget(self.g_salestab_tabWidget)
        self.g_manageentity_tabwidget.addTab(self.g_sales_widget, "")
        self.g_emergencycontacts_tab = QtWidgets.QWidget()
        self.g_emergencycontacts_tab.setObjectName("g_emergencycontacts_tab")
        self.verticalLayout_62 = QtWidgets.QVBoxLayout(self.g_emergencycontacts_tab)
        self.verticalLayout_62.setObjectName("verticalLayout_62")
        self.g_emer_contacts_not_available_frame = QtWidgets.QFrame(self.g_emergencycontacts_tab)
        self.g_emer_contacts_not_available_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_emer_contacts_not_available_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_emer_contacts_not_available_frame.setObjectName("g_emer_contacts_not_available_frame")
        self.verticalLayout_109 = QtWidgets.QVBoxLayout(self.g_emer_contacts_not_available_frame)
        self.verticalLayout_109.setObjectName("verticalLayout_109")
        spacerItem31 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_109.addItem(spacerItem31)
        self.label_3 = QtWidgets.QLabel(self.g_emer_contacts_not_available_frame)
        self.label_3.setAlignment(QtCore.Qt.AlignCenter)
        self.label_3.setObjectName("label_3")
        self.verticalLayout_109.addWidget(self.label_3)
        spacerItem32 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_109.addItem(spacerItem32)
        self.verticalLayout_62.addWidget(self.g_emer_contacts_not_available_frame)
        self.g_emergencycontact_table = QtWidgets.QTableWidget(self.g_emergencycontacts_tab)
        self.g_emergencycontact_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_emergencycontact_table.setAlternatingRowColors(True)
        self.g_emergencycontact_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_emergencycontact_table.setObjectName("g_emergencycontact_table")
        self.g_emergencycontact_table.setColumnCount(5)
        self.g_emergencycontact_table.setRowCount(5)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_emergencycontact_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_emergencycontact_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_emergencycontact_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_emergencycontact_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_emergencycontact_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_emergencycontact_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_emergencycontact_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(0, 4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_emergencycontact_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(1, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(1, 4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_emergencycontact_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(2, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(2, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(2, 4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_emergencycontact_table.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(3, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(3, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(3, 4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_emergencycontact_table.setItem(4, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(4, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(4, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_emergencycontact_table.setItem(4, 4, item)
        self.g_emergencycontact_table.horizontalHeader().setCascadingSectionResizes(True)
        self.g_emergencycontact_table.horizontalHeader().setDefaultSectionSize(100)
        self.g_emergencycontact_table.horizontalHeader().setStretchLastSection(True)
        self.g_emergencycontact_table.verticalHeader().setVisible(False)
        self.verticalLayout_62.addWidget(self.g_emergencycontact_table)
        self.g_emergencycontact_buttons_frame = QtWidgets.QFrame(self.g_emergencycontacts_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_emergencycontact_buttons_frame.sizePolicy().hasHeightForWidth())
        self.g_emergencycontact_buttons_frame.setSizePolicy(sizePolicy)
        self.g_emergencycontact_buttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_emergencycontact_buttons_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.g_emergencycontact_buttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_emergencycontact_buttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_emergencycontact_buttons_frame.setObjectName("g_emergencycontact_buttons_frame")
        self.horizontalLayout_83 = QtWidgets.QHBoxLayout(self.g_emergencycontact_buttons_frame)
        self.horizontalLayout_83.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_83.setSpacing(6)
        self.horizontalLayout_83.setObjectName("horizontalLayout_83")
        spacerItem33 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_83.addItem(spacerItem33)
        self.g_deleteemergencycontact_button = QtWidgets.QPushButton(self.g_emergencycontact_buttons_frame)
        self.g_deleteemergencycontact_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_deleteemergencycontact_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_deleteemergencycontact_button.setStyleSheet("")
        self.g_deleteemergencycontact_button.setIcon(icon6)
        self.g_deleteemergencycontact_button.setIconSize(QtCore.QSize(24, 24))
        self.g_deleteemergencycontact_button.setObjectName("g_deleteemergencycontact_button")
        self.horizontalLayout_83.addWidget(self.g_deleteemergencycontact_button)
        self.g_manageemergencycontact_button = QtWidgets.QPushButton(self.g_emergencycontact_buttons_frame)
        self.g_manageemergencycontact_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manageemergencycontact_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_manageemergencycontact_button.setStyleSheet("")
        icon13 = QtGui.QIcon()
        icon13.addPixmap(QtGui.QPixmap(":/pet_tracker/edit"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_manageemergencycontact_button.setIcon(icon13)
        self.g_manageemergencycontact_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manageemergencycontact_button.setObjectName("g_manageemergencycontact_button")
        self.horizontalLayout_83.addWidget(self.g_manageemergencycontact_button)
        self.g_addemergencycontact_button = QtWidgets.QPushButton(self.g_emergencycontact_buttons_frame)
        self.g_addemergencycontact_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_addemergencycontact_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_addemergencycontact_button.setStyleSheet("")
        self.g_addemergencycontact_button.setIcon(icon10)
        self.g_addemergencycontact_button.setIconSize(QtCore.QSize(24, 24))
        self.g_addemergencycontact_button.setObjectName("g_addemergencycontact_button")
        self.horizontalLayout_83.addWidget(self.g_addemergencycontact_button)
        self.verticalLayout_62.addWidget(self.g_emergencycontact_buttons_frame)
        self.g_manageentity_tabwidget.addTab(self.g_emergencycontacts_tab, "")
        self.g_customfield_widget = QtWidgets.QWidget()
        self.g_customfield_widget.setObjectName("g_customfield_widget")
        self.verticalLayout_49 = QtWidgets.QVBoxLayout(self.g_customfield_widget)
        self.verticalLayout_49.setObjectName("verticalLayout_49")
        self.g_attributes_groupBox = QtWidgets.QGroupBox(self.g_customfield_widget)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_attributes_groupBox.setFont(font)
        self.g_attributes_groupBox.setObjectName("g_attributes_groupBox")
        self.verticalLayout_28 = QtWidgets.QVBoxLayout(self.g_attributes_groupBox)
        self.verticalLayout_28.setObjectName("verticalLayout_28")
        self.g_attributeshelp_label = QtWidgets.QLabel(self.g_attributes_groupBox)
        self.g_attributeshelp_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_attributeshelp_label.setObjectName("g_attributeshelp_label")
        self.verticalLayout_28.addWidget(self.g_attributeshelp_label)
        self.g_customfield_table = QtWidgets.QTableWidget(self.g_attributes_groupBox)
        self.g_customfield_table.setMinimumSize(QtCore.QSize(575, 0))
        self.g_customfield_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_customfield_table.setAlternatingRowColors(True)
        self.g_customfield_table.setShowGrid(False)
        self.g_customfield_table.setObjectName("g_customfield_table")
        self.g_customfield_table.setColumnCount(2)
        self.g_customfield_table.setRowCount(5)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_customfield_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_customfield_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_customfield_table.setItem(4, 1, item)
        self.g_customfield_table.horizontalHeader().setDefaultSectionSize(270)
        self.g_customfield_table.horizontalHeader().setStretchLastSection(True)
        self.g_customfield_table.verticalHeader().setVisible(False)
        self.verticalLayout_28.addWidget(self.g_customfield_table)
        self.g_attributesbuttons_frame = QtWidgets.QFrame(self.g_attributes_groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_attributesbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_attributesbuttons_frame.setSizePolicy(sizePolicy)
        self.g_attributesbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_attributesbuttons_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.g_attributesbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_attributesbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_attributesbuttons_frame.setObjectName("g_attributesbuttons_frame")
        self.horizontalLayout_61 = QtWidgets.QHBoxLayout(self.g_attributesbuttons_frame)
        self.horizontalLayout_61.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_61.setSpacing(6)
        self.horizontalLayout_61.setObjectName("horizontalLayout_61")
        spacerItem34 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_61.addItem(spacerItem34)
        self.g_removecustomfield_button = QtWidgets.QPushButton(self.g_attributesbuttons_frame)
        self.g_removecustomfield_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_removecustomfield_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_removecustomfield_button.setStyleSheet("")
        self.g_removecustomfield_button.setIcon(icon6)
        self.g_removecustomfield_button.setIconSize(QtCore.QSize(24, 24))
        self.g_removecustomfield_button.setObjectName("g_removecustomfield_button")
        self.horizontalLayout_61.addWidget(self.g_removecustomfield_button)
        self.g_managecustomfield_button = QtWidgets.QPushButton(self.g_attributesbuttons_frame)
        self.g_managecustomfield_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_managecustomfield_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_managecustomfield_button.setStyleSheet("")
        self.g_managecustomfield_button.setIcon(icon13)
        self.g_managecustomfield_button.setIconSize(QtCore.QSize(24, 24))
        self.g_managecustomfield_button.setObjectName("g_managecustomfield_button")
        self.horizontalLayout_61.addWidget(self.g_managecustomfield_button)
        self.g_addcustom_button = QtWidgets.QPushButton(self.g_attributesbuttons_frame)
        self.g_addcustom_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_addcustom_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_addcustom_button.setStyleSheet("")
        self.g_addcustom_button.setIcon(icon10)
        self.g_addcustom_button.setIconSize(QtCore.QSize(24, 24))
        self.g_addcustom_button.setObjectName("g_addcustom_button")
        self.horizontalLayout_61.addWidget(self.g_addcustom_button)
        self.verticalLayout_28.addWidget(self.g_attributesbuttons_frame)
        self.verticalLayout_49.addWidget(self.g_attributes_groupBox)
        self.g_manageentity_tabwidget.addTab(self.g_customfield_widget, "")
        self.g_frequentbuyer_widget = QtWidgets.QWidget()
        self.g_frequentbuyer_widget.setObjectName("g_frequentbuyer_widget")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.g_frequentbuyer_widget)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.g_frequentbuyer_not_available_frame = QtWidgets.QFrame(self.g_frequentbuyer_widget)
        self.g_frequentbuyer_not_available_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_frequentbuyer_not_available_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_frequentbuyer_not_available_frame.setObjectName("g_frequentbuyer_not_available_frame")
        self.verticalLayout_110 = QtWidgets.QVBoxLayout(self.g_frequentbuyer_not_available_frame)
        self.verticalLayout_110.setObjectName("verticalLayout_110")
        spacerItem35 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_110.addItem(spacerItem35)
        self.label_4 = QtWidgets.QLabel(self.g_frequentbuyer_not_available_frame)
        self.label_4.setAlignment(QtCore.Qt.AlignCenter)
        self.label_4.setObjectName("label_4")
        self.verticalLayout_110.addWidget(self.label_4)
        spacerItem36 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_110.addItem(spacerItem36)
        self.verticalLayout_5.addWidget(self.g_frequentbuyer_not_available_frame)
        self.g_frequentbuyer_tabwidget = QtWidgets.QTabWidget(self.g_frequentbuyer_widget)
        font = QtGui.QFont()
        font.setPointSize(10)
        self.g_frequentbuyer_tabwidget.setFont(font)
        self.g_frequentbuyer_tabwidget.setAutoFillBackground(False)
        self.g_frequentbuyer_tabwidget.setStyleSheet("")
        self.g_frequentbuyer_tabwidget.setTabPosition(QtWidgets.QTabWidget.North)
        self.g_frequentbuyer_tabwidget.setTabShape(QtWidgets.QTabWidget.Rounded)
        self.g_frequentbuyer_tabwidget.setUsesScrollButtons(True)
        self.g_frequentbuyer_tabwidget.setDocumentMode(False)
        self.g_frequentbuyer_tabwidget.setMovable(False)
        self.g_frequentbuyer_tabwidget.setObjectName("g_frequentbuyer_tabwidget")
        self.g_programstatus_widget = QtWidgets.QWidget()
        self.g_programstatus_widget.setStyleSheet("")
        self.g_programstatus_widget.setObjectName("g_programstatus_widget")
        self.verticalLayout_13 = QtWidgets.QVBoxLayout(self.g_programstatus_widget)
        self.verticalLayout_13.setObjectName("verticalLayout_13")
        self.g_programstatus_table = QtWidgets.QTableWidget(self.g_programstatus_widget)
        self.g_programstatus_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_programstatus_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_programstatus_table.setAlternatingRowColors(True)
        self.g_programstatus_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_programstatus_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_programstatus_table.setObjectName("g_programstatus_table")
        self.g_programstatus_table.setColumnCount(4)
        self.g_programstatus_table.setRowCount(5)
        item = QtWidgets.QTableWidgetItem()
        self.g_programstatus_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_programstatus_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_programstatus_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_programstatus_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_programstatus_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_programstatus_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_programstatus_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_programstatus_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_programstatus_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_programstatus_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_programstatus_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_programstatus_table.setItem(0, 3, item)
        self.g_programstatus_table.horizontalHeader().setStretchLastSection(True)
        self.verticalLayout_13.addWidget(self.g_programstatus_table)
        self.g_frequentbuyerscontrols_frame = QtWidgets.QFrame(self.g_programstatus_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_frequentbuyerscontrols_frame.sizePolicy().hasHeightForWidth())
        self.g_frequentbuyerscontrols_frame.setSizePolicy(sizePolicy)
        self.g_frequentbuyerscontrols_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_frequentbuyerscontrols_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.g_frequentbuyerscontrols_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_frequentbuyerscontrols_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_frequentbuyerscontrols_frame.setObjectName("g_frequentbuyerscontrols_frame")
        self.horizontalLayout_84 = QtWidgets.QHBoxLayout(self.g_frequentbuyerscontrols_frame)
        self.horizontalLayout_84.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_84.setSpacing(6)
        self.horizontalLayout_84.setObjectName("horizontalLayout_84")
        spacerItem37 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_84.addItem(spacerItem37)
        self.g_addfrequentBuyer_button = QtWidgets.QPushButton(self.g_frequentbuyerscontrols_frame)
        self.g_addfrequentBuyer_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_addfrequentBuyer_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_addfrequentBuyer_button.setStyleSheet("")
        icon14 = QtGui.QIcon()
        icon14.addPixmap(QtGui.QPixmap(":/pet_tracker/add"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_addfrequentBuyer_button.setIcon(icon14)
        self.g_addfrequentBuyer_button.setIconSize(QtCore.QSize(24, 24))
        self.g_addfrequentBuyer_button.setObjectName("g_addfrequentBuyer_button")
        self.horizontalLayout_84.addWidget(self.g_addfrequentBuyer_button)
        self.g_managefrequentBuyer_button = QtWidgets.QPushButton(self.g_frequentbuyerscontrols_frame)
        self.g_managefrequentBuyer_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_managefrequentBuyer_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_managefrequentBuyer_button.setStyleSheet("")
        self.g_managefrequentBuyer_button.setIcon(icon13)
        self.g_managefrequentBuyer_button.setIconSize(QtCore.QSize(24, 24))
        self.g_managefrequentBuyer_button.setObjectName("g_managefrequentBuyer_button")
        self.horizontalLayout_84.addWidget(self.g_managefrequentBuyer_button)
        self.verticalLayout_13.addWidget(self.g_frequentbuyerscontrols_frame)
        self.g_frequentbuyer_tabwidget.addTab(self.g_programstatus_widget, "")
        self.g_rewardsearned_widget = QtWidgets.QWidget()
        self.g_rewardsearned_widget.setObjectName("g_rewardsearned_widget")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.g_rewardsearned_widget)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.g_rewardsearned_table = QtWidgets.QTableWidget(self.g_rewardsearned_widget)
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_rewardsearned_table.setFont(font)
        self.g_rewardsearned_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_rewardsearned_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_rewardsearned_table.setAlternatingRowColors(True)
        self.g_rewardsearned_table.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.g_rewardsearned_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_rewardsearned_table.setObjectName("g_rewardsearned_table")
        self.g_rewardsearned_table.setColumnCount(5)
        self.g_rewardsearned_table.setRowCount(3)
        item = QtWidgets.QTableWidgetItem()
        self.g_rewardsearned_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_rewardsearned_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_rewardsearned_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_rewardsearned_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_rewardsearned_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_rewardsearned_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_rewardsearned_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_rewardsearned_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_rewardsearned_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_rewardsearned_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_rewardsearned_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_rewardsearned_table.setItem(0, 4, item)
        self.g_rewardsearned_table.horizontalHeader().setStretchLastSection(True)
        self.verticalLayout_8.addWidget(self.g_rewardsearned_table)
        self.g_frequentbuyer_tabwidget.addTab(self.g_rewardsearned_widget, "")
        self.verticalLayout_5.addWidget(self.g_frequentbuyer_tabwidget)
        self.g_manageentity_tabwidget.addTab(self.g_frequentbuyer_widget, "")
        self.g_supplierterms_widget = QtWidgets.QWidget()
        self.g_supplierterms_widget.setObjectName("g_supplierterms_widget")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.g_supplierterms_widget)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.g_corp_supplier_frame = QtWidgets.QFrame(self.g_supplierterms_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_corp_supplier_frame.sizePolicy().hasHeightForWidth())
        self.g_corp_supplier_frame.setSizePolicy(sizePolicy)
        self.g_corp_supplier_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_corp_supplier_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_corp_supplier_frame.setObjectName("g_corp_supplier_frame")
        self.verticalLayout_47 = QtWidgets.QVBoxLayout(self.g_corp_supplier_frame)
        self.verticalLayout_47.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_47.setObjectName("verticalLayout_47")
        self.g_supplierinfo_table = QtWidgets.QTableWidget(self.g_corp_supplier_frame)
        self.g_supplierinfo_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_supplierinfo_table.setAlternatingRowColors(True)
        self.g_supplierinfo_table.setGridStyle(QtCore.Qt.NoPen)
        self.g_supplierinfo_table.setObjectName("g_supplierinfo_table")
        self.g_supplierinfo_table.setColumnCount(10)
        self.g_supplierinfo_table.setRowCount(4)
        item = QtWidgets.QTableWidgetItem()
        self.g_supplierinfo_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_supplierinfo_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_supplierinfo_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_supplierinfo_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_supplierinfo_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_supplierinfo_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_supplierinfo_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_supplierinfo_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_supplierinfo_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_supplierinfo_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_supplierinfo_table.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_supplierinfo_table.setHorizontalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_supplierinfo_table.setHorizontalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_supplierinfo_table.setHorizontalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_supplierinfo_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_supplierinfo_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_supplierinfo_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_supplierinfo_table.setItem(0, 7, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_supplierinfo_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_supplierinfo_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_supplierinfo_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_supplierinfo_table.setItem(1, 8, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_supplierinfo_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_supplierinfo_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_supplierinfo_table.setItem(2, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_supplierinfo_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_supplierinfo_table.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_supplierinfo_table.setItem(3, 2, item)
        self.g_supplierinfo_table.horizontalHeader().setCascadingSectionResizes(True)
        self.g_supplierinfo_table.horizontalHeader().setDefaultSectionSize(150)
        self.g_supplierinfo_table.horizontalHeader().setStretchLastSection(True)
        self.g_supplierinfo_table.verticalHeader().setVisible(False)
        self.verticalLayout_47.addWidget(self.g_supplierinfo_table)
        self.g_supplierinfobuttons_frame = QtWidgets.QFrame(self.g_corp_supplier_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_supplierinfobuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_supplierinfobuttons_frame.setSizePolicy(sizePolicy)
        self.g_supplierinfobuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_supplierinfobuttons_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.g_supplierinfobuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_supplierinfobuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_supplierinfobuttons_frame.setObjectName("g_supplierinfobuttons_frame")
        self.horizontalLayout_57 = QtWidgets.QHBoxLayout(self.g_supplierinfobuttons_frame)
        self.horizontalLayout_57.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_57.setSpacing(6)
        self.horizontalLayout_57.setObjectName("horizontalLayout_57")
        spacerItem38 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_57.addItem(spacerItem38)
        self.g_removesupplierinfo_button = QtWidgets.QPushButton(self.g_supplierinfobuttons_frame)
        self.g_removesupplierinfo_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_removesupplierinfo_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_removesupplierinfo_button.setStyleSheet("")
        icon15 = QtGui.QIcon()
        icon15.addPixmap(QtGui.QPixmap(":/purchasing_and_receiving/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_removesupplierinfo_button.setIcon(icon15)
        self.g_removesupplierinfo_button.setIconSize(QtCore.QSize(24, 24))
        self.g_removesupplierinfo_button.setObjectName("g_removesupplierinfo_button")
        self.horizontalLayout_57.addWidget(self.g_removesupplierinfo_button)
        self.g_managesupplierinfo_button = QtWidgets.QPushButton(self.g_supplierinfobuttons_frame)
        self.g_managesupplierinfo_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_managesupplierinfo_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_managesupplierinfo_button.setStyleSheet("")
        icon16 = QtGui.QIcon()
        icon16.addPixmap(QtGui.QPixmap(":/purchasing_and_receiving/edit_white"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_managesupplierinfo_button.setIcon(icon16)
        self.g_managesupplierinfo_button.setIconSize(QtCore.QSize(24, 24))
        self.g_managesupplierinfo_button.setObjectName("g_managesupplierinfo_button")
        self.horizontalLayout_57.addWidget(self.g_managesupplierinfo_button)
        self.g_massactionsupplierinfo_button = QtWidgets.QPushButton(self.g_supplierinfobuttons_frame)
        self.g_massactionsupplierinfo_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_massactionsupplierinfo_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_massactionsupplierinfo_button.setStyleSheet("")
        icon17 = QtGui.QIcon()
        icon17.addPixmap(QtGui.QPixmap(":/purchasing_and_receiving/pen_bucket"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_massactionsupplierinfo_button.setIcon(icon17)
        self.g_massactionsupplierinfo_button.setIconSize(QtCore.QSize(24, 24))
        self.g_massactionsupplierinfo_button.setObjectName("g_massactionsupplierinfo_button")
        self.horizontalLayout_57.addWidget(self.g_massactionsupplierinfo_button)
        self.g_addsupplierinfo_button = QtWidgets.QPushButton(self.g_supplierinfobuttons_frame)
        self.g_addsupplierinfo_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_addsupplierinfo_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_addsupplierinfo_button.setStyleSheet("")
        icon18 = QtGui.QIcon()
        icon18.addPixmap(QtGui.QPixmap(":/purchasing_and_receiving/add_white"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_addsupplierinfo_button.setIcon(icon18)
        self.g_addsupplierinfo_button.setIconSize(QtCore.QSize(24, 24))
        self.g_addsupplierinfo_button.setObjectName("g_addsupplierinfo_button")
        self.horizontalLayout_57.addWidget(self.g_addsupplierinfo_button)
        self.verticalLayout_47.addWidget(self.g_supplierinfobuttons_frame)
        self.verticalLayout_6.addWidget(self.g_corp_supplier_frame)
        self.g_supplier_frame = QtWidgets.QFrame(self.g_supplierterms_widget)
        self.g_supplier_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_supplier_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_supplier_frame.setObjectName("g_supplier_frame")
        self.verticalLayout_17 = QtWidgets.QVBoxLayout(self.g_supplier_frame)
        self.verticalLayout_17.setObjectName("verticalLayout_17")
        self.g_sub_supplier_frame = QtWidgets.QFrame(self.g_supplier_frame)
        self.g_sub_supplier_frame.setMinimumSize(QtCore.QSize(0, 283))
        self.g_sub_supplier_frame.setMaximumSize(QtCore.QSize(********, ********))
        self.g_sub_supplier_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_sub_supplier_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_sub_supplier_frame.setObjectName("g_sub_supplier_frame")
        self.horizontalLayout_32 = QtWidgets.QHBoxLayout(self.g_sub_supplier_frame)
        self.horizontalLayout_32.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_32.setObjectName("horizontalLayout_32")
        self.g_supplier_orderinfo_frame = QtWidgets.QFrame(self.g_sub_supplier_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_supplier_orderinfo_frame.sizePolicy().hasHeightForWidth())
        self.g_supplier_orderinfo_frame.setSizePolicy(sizePolicy)
        self.g_supplier_orderinfo_frame.setMinimumSize(QtCore.QSize(380, 500))
        self.g_supplier_orderinfo_frame.setMaximumSize(QtCore.QSize(500, ********))
        self.g_supplier_orderinfo_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_supplier_orderinfo_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_supplier_orderinfo_frame.setObjectName("g_supplier_orderinfo_frame")
        self.verticalLayout_24 = QtWidgets.QVBoxLayout(self.g_supplier_orderinfo_frame)
        self.verticalLayout_24.setContentsMargins(0, 0, 9, 0)
        self.verticalLayout_24.setSpacing(6)
        self.verticalLayout_24.setObjectName("verticalLayout_24")
        self.g_supplierinfosuppliersettings_groupbox = QtWidgets.QGroupBox(self.g_supplier_orderinfo_frame)
        self.g_supplierinfosuppliersettings_groupbox.setMinimumSize(QtCore.QSize(0, 137))
        self.g_supplierinfosuppliersettings_groupbox.setMaximumSize(QtCore.QSize(********, 170))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_supplierinfosuppliersettings_groupbox.setFont(font)
        self.g_supplierinfosuppliersettings_groupbox.setObjectName("g_supplierinfosuppliersettings_groupbox")
        self.verticalLayout_25 = QtWidgets.QVBoxLayout(self.g_supplierinfosuppliersettings_groupbox)
        self.verticalLayout_25.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout_25.setSpacing(6)
        self.verticalLayout_25.setObjectName("verticalLayout_25")
        self.g_suppliersetting_frame = QtWidgets.QFrame(self.g_supplierinfosuppliersettings_groupbox)
        self.g_suppliersetting_frame.setMinimumSize(QtCore.QSize(0, 35))
        self.g_suppliersetting_frame.setMaximumSize(QtCore.QSize(********, 31))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_suppliersetting_frame.setFont(font)
        self.g_suppliersetting_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_suppliersetting_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_suppliersetting_frame.setObjectName("g_suppliersetting_frame")
        self.horizontalLayout_34 = QtWidgets.QHBoxLayout(self.g_suppliersetting_frame)
        self.horizontalLayout_34.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_34.setSpacing(6)
        self.horizontalLayout_34.setObjectName("horizontalLayout_34")
        self.g_accountnumber_label = QtWidgets.QLabel(self.g_suppliersetting_frame)
        self.g_accountnumber_label.setMinimumSize(QtCore.QSize(101, 0))
        self.g_accountnumber_label.setMaximumSize(QtCore.QSize(101, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_accountnumber_label.setFont(font)
        self.g_accountnumber_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_accountnumber_label.setObjectName("g_accountnumber_label")
        self.horizontalLayout_34.addWidget(self.g_accountnumber_label)
        self.g_accountnumber_lineedit = QtWidgets.QLineEdit(self.g_suppliersetting_frame)
        self.g_accountnumber_lineedit.setMinimumSize(QtCore.QSize(200, 31))
        self.g_accountnumber_lineedit.setMaximumSize(QtCore.QSize(70, 31))
        self.g_accountnumber_lineedit.setText("")
        self.g_accountnumber_lineedit.setProperty("qp_suploc_account_number", "")
        self.g_accountnumber_lineedit.setObjectName("g_accountnumber_lineedit")
        self.horizontalLayout_34.addWidget(self.g_accountnumber_lineedit)
        self.g_ispetsupplier_checkbox = QtWidgets.QCheckBox(self.g_suppliersetting_frame)
        self.g_ispetsupplier_checkbox.setMinimumSize(QtCore.QSize(0, 31))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_ispetsupplier_checkbox.setFont(font)
        self.g_ispetsupplier_checkbox.setObjectName("g_ispetsupplier_checkbox")
        self.horizontalLayout_34.addWidget(self.g_ispetsupplier_checkbox)
        spacerItem39 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_34.addItem(spacerItem39)
        self.verticalLayout_25.addWidget(self.g_suppliersetting_frame)
        self.g_leadtime_frame = QtWidgets.QFrame(self.g_supplierinfosuppliersettings_groupbox)
        self.g_leadtime_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_leadtime_frame.setMaximumSize(QtCore.QSize(********, 31))
        self.g_leadtime_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_leadtime_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_leadtime_frame.setObjectName("g_leadtime_frame")
        self.horizontalLayout_38 = QtWidgets.QHBoxLayout(self.g_leadtime_frame)
        self.horizontalLayout_38.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_38.setSpacing(6)
        self.horizontalLayout_38.setObjectName("horizontalLayout_38")
        self.g_leadtime_label = QtWidgets.QLabel(self.g_leadtime_frame)
        self.g_leadtime_label.setMinimumSize(QtCore.QSize(101, 0))
        self.g_leadtime_label.setMaximumSize(QtCore.QSize(101, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_leadtime_label.setFont(font)
        self.g_leadtime_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_leadtime_label.setObjectName("g_leadtime_label")
        self.horizontalLayout_38.addWidget(self.g_leadtime_label)
        self.g_leadtime_lineedit = QtWidgets.QLineEdit(self.g_leadtime_frame)
        self.g_leadtime_lineedit.setMinimumSize(QtCore.QSize(100, 0))
        self.g_leadtime_lineedit.setMaximumSize(QtCore.QSize(100, ********))
        self.g_leadtime_lineedit.setObjectName("g_leadtime_lineedit")
        self.horizontalLayout_38.addWidget(self.g_leadtime_lineedit)
        spacerItem40 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_38.addItem(spacerItem40)
        self.verticalLayout_25.addWidget(self.g_leadtime_frame)
        self.g_orderfrequency_frame = QtWidgets.QFrame(self.g_supplierinfosuppliersettings_groupbox)
        self.g_orderfrequency_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_orderfrequency_frame.setMaximumSize(QtCore.QSize(********, 31))
        self.g_orderfrequency_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_orderfrequency_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_orderfrequency_frame.setObjectName("g_orderfrequency_frame")
        self.horizontalLayout_44 = QtWidgets.QHBoxLayout(self.g_orderfrequency_frame)
        self.horizontalLayout_44.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_44.setSpacing(6)
        self.horizontalLayout_44.setObjectName("horizontalLayout_44")
        self.g_leadtime_label_2 = QtWidgets.QLabel(self.g_orderfrequency_frame)
        self.g_leadtime_label_2.setMinimumSize(QtCore.QSize(101, 0))
        self.g_leadtime_label_2.setMaximumSize(QtCore.QSize(101, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_leadtime_label_2.setFont(font)
        self.g_leadtime_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_leadtime_label_2.setObjectName("g_leadtime_label_2")
        self.horizontalLayout_44.addWidget(self.g_leadtime_label_2)
        self.g_orderfrequency_lineedit = QtWidgets.QLineEdit(self.g_orderfrequency_frame)
        self.g_orderfrequency_lineedit.setMinimumSize(QtCore.QSize(100, 0))
        self.g_orderfrequency_lineedit.setMaximumSize(QtCore.QSize(100, ********))
        self.g_orderfrequency_lineedit.setObjectName("g_orderfrequency_lineedit")
        self.horizontalLayout_44.addWidget(self.g_orderfrequency_lineedit)
        spacerItem41 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_44.addItem(spacerItem41)
        self.verticalLayout_25.addWidget(self.g_orderfrequency_frame)
        self.verticalLayout_24.addWidget(self.g_supplierinfosuppliersettings_groupbox)
        self.g_supplierinfoordersettings_groupbox = QtWidgets.QGroupBox(self.g_supplier_orderinfo_frame)
        self.g_supplierinfoordersettings_groupbox.setMinimumSize(QtCore.QSize(0, 95))
        self.g_supplierinfoordersettings_groupbox.setMaximumSize(QtCore.QSize(********, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_supplierinfoordersettings_groupbox.setFont(font)
        self.g_supplierinfoordersettings_groupbox.setObjectName("g_supplierinfoordersettings_groupbox")
        self.verticalLayout_32 = QtWidgets.QVBoxLayout(self.g_supplierinfoordersettings_groupbox)
        self.verticalLayout_32.setContentsMargins(4, 4, 4, 4)
        self.verticalLayout_32.setSpacing(6)
        self.verticalLayout_32.setObjectName("verticalLayout_32")
        self.g_supplierinfoordersettings_frame_4 = QtWidgets.QFrame(self.g_supplierinfoordersettings_groupbox)
        self.g_supplierinfoordersettings_frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_supplierinfoordersettings_frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_supplierinfoordersettings_frame_4.setObjectName("g_supplierinfoordersettings_frame_4")
        self.horizontalLayout_35 = QtWidgets.QHBoxLayout(self.g_supplierinfoordersettings_frame_4)
        self.horizontalLayout_35.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_35.setSpacing(6)
        self.horizontalLayout_35.setObjectName("horizontalLayout_35")
        self.g_minorderamount_label_4 = QtWidgets.QLabel(self.g_supplierinfoordersettings_frame_4)
        self.g_minorderamount_label_4.setMinimumSize(QtCore.QSize(101, 0))
        self.g_minorderamount_label_4.setMaximumSize(QtCore.QSize(101, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_minorderamount_label_4.setFont(font)
        self.g_minorderamount_label_4.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_minorderamount_label_4.setObjectName("g_minorderamount_label_4")
        self.horizontalLayout_35.addWidget(self.g_minorderamount_label_4)
        self.g_minorderamount_lineedit = QtWidgets.QLineEdit(self.g_supplierinfoordersettings_frame_4)
        self.g_minorderamount_lineedit.setMinimumSize(QtCore.QSize(80, 0))
        self.g_minorderamount_lineedit.setMaximumSize(QtCore.QSize(80, 31))
        self.g_minorderamount_lineedit.setText("")
        self.g_minorderamount_lineedit.setProperty("qp_suploc_min_order_amount", "")
        self.g_minorderamount_lineedit.setObjectName("g_minorderamount_lineedit")
        self.horizontalLayout_35.addWidget(self.g_minorderamount_lineedit)
        self.g_supplierinfopoformat_label_4 = QtWidgets.QLabel(self.g_supplierinfoordersettings_frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_supplierinfopoformat_label_4.sizePolicy().hasHeightForWidth())
        self.g_supplierinfopoformat_label_4.setSizePolicy(sizePolicy)
        self.g_supplierinfopoformat_label_4.setMinimumSize(QtCore.QSize(70, 0))
        self.g_supplierinfopoformat_label_4.setMaximumSize(QtCore.QSize(70, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_supplierinfopoformat_label_4.setFont(font)
        self.g_supplierinfopoformat_label_4.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_supplierinfopoformat_label_4.setObjectName("g_supplierinfopoformat_label_4")
        self.horizontalLayout_35.addWidget(self.g_supplierinfopoformat_label_4)
        self.g_supplierinfosendvia_combobox = QtWidgets.QComboBox(self.g_supplierinfoordersettings_frame_4)
        self.g_supplierinfosendvia_combobox.setMinimumSize(QtCore.QSize(0, 0))
        self.g_supplierinfosendvia_combobox.setMaximumSize(QtCore.QSize(********, 31))
        self.g_supplierinfosendvia_combobox.setProperty("qp_suploc_send_po_via", "")
        self.g_supplierinfosendvia_combobox.setObjectName("g_supplierinfosendvia_combobox")
        self.g_supplierinfosendvia_combobox.addItem("")
        self.g_supplierinfosendvia_combobox.addItem("")
        self.g_supplierinfosendvia_combobox.addItem("")
        self.horizontalLayout_35.addWidget(self.g_supplierinfosendvia_combobox)
        spacerItem42 = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_35.addItem(spacerItem42)
        self.verticalLayout_32.addWidget(self.g_supplierinfoordersettings_frame_4)
        self.g_wieght_frame = QtWidgets.QFrame(self.g_supplierinfoordersettings_groupbox)
        self.g_wieght_frame.setMaximumSize(QtCore.QSize(********, 35))
        self.g_wieght_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_wieght_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_wieght_frame.setObjectName("g_wieght_frame")
        self.horizontalLayout_106 = QtWidgets.QHBoxLayout(self.g_wieght_frame)
        self.horizontalLayout_106.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_106.setSpacing(6)
        self.horizontalLayout_106.setObjectName("horizontalLayout_106")
        self.g_min_weight_label = QtWidgets.QLabel(self.g_wieght_frame)
        self.g_min_weight_label.setMinimumSize(QtCore.QSize(101, 0))
        self.g_min_weight_label.setMaximumSize(QtCore.QSize(101, ********))
        self.g_min_weight_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_min_weight_label.setObjectName("g_min_weight_label")
        self.horizontalLayout_106.addWidget(self.g_min_weight_label)
        self.g_min_weight_lineedit = QtWidgets.QLineEdit(self.g_wieght_frame)
        self.g_min_weight_lineedit.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_min_weight_lineedit.sizePolicy().hasHeightForWidth())
        self.g_min_weight_lineedit.setSizePolicy(sizePolicy)
        self.g_min_weight_lineedit.setMinimumSize(QtCore.QSize(80, 31))
        self.g_min_weight_lineedit.setMaximumSize(QtCore.QSize(80, 31))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_min_weight_lineedit.setFont(font)
        self.g_min_weight_lineedit.setStyleSheet("")
        self.g_min_weight_lineedit.setText("")
        self.g_min_weight_lineedit.setReadOnly(False)
        self.g_min_weight_lineedit.setProperty("qp_pwt_weight", "")
        self.g_min_weight_lineedit.setObjectName("g_min_weight_lineedit")
        self.horizontalLayout_106.addWidget(self.g_min_weight_lineedit)
        self.g_max_weight_label = QtWidgets.QLabel(self.g_wieght_frame)
        self.g_max_weight_label.setMinimumSize(QtCore.QSize(70, 0))
        self.g_max_weight_label.setMaximumSize(QtCore.QSize(70, ********))
        self.g_max_weight_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_max_weight_label.setObjectName("g_max_weight_label")
        self.horizontalLayout_106.addWidget(self.g_max_weight_label)
        self.g_max_weight_lineedit = QtWidgets.QLineEdit(self.g_wieght_frame)
        self.g_max_weight_lineedit.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_max_weight_lineedit.sizePolicy().hasHeightForWidth())
        self.g_max_weight_lineedit.setSizePolicy(sizePolicy)
        self.g_max_weight_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_max_weight_lineedit.setMaximumSize(QtCore.QSize(200, 31))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_max_weight_lineedit.setFont(font)
        self.g_max_weight_lineedit.setStyleSheet("")
        self.g_max_weight_lineedit.setText("")
        self.g_max_weight_lineedit.setReadOnly(False)
        self.g_max_weight_lineedit.setProperty("qp_pwt_weight", "")
        self.g_max_weight_lineedit.setObjectName("g_max_weight_lineedit")
        self.horizontalLayout_106.addWidget(self.g_max_weight_lineedit)
        self.g_weightmeasure_combobox = QtWidgets.QComboBox(self.g_wieght_frame)
        self.g_weightmeasure_combobox.setMinimumSize(QtCore.QSize(60, 31))
        self.g_weightmeasure_combobox.setMaximumSize(QtCore.QSize(60, 31))
        self.g_weightmeasure_combobox.setProperty("qp_pwt_weight", "")
        self.g_weightmeasure_combobox.setObjectName("g_weightmeasure_combobox")
        self.g_weightmeasure_combobox.addItem("")
        self.g_weightmeasure_combobox.addItem("")
        self.g_weightmeasure_combobox.addItem("")
        self.horizontalLayout_106.addWidget(self.g_weightmeasure_combobox)
        spacerItem43 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_106.addItem(spacerItem43)
        self.verticalLayout_32.addWidget(self.g_wieght_frame)
        self.verticalLayout_24.addWidget(self.g_supplierinfoordersettings_groupbox)
        self.g_verification_groupbox = QtWidgets.QGroupBox(self.g_supplier_orderinfo_frame)
        self.g_verification_groupbox.setMinimumSize(QtCore.QSize(0, 64))
        self.g_verification_groupbox.setMaximumSize(QtCore.QSize(********, 64))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_verification_groupbox.setFont(font)
        self.g_verification_groupbox.setObjectName("g_verification_groupbox")
        self.verticalLayout_34 = QtWidgets.QVBoxLayout(self.g_verification_groupbox)
        self.verticalLayout_34.setContentsMargins(4, 4, 4, 4)
        self.verticalLayout_34.setSpacing(6)
        self.verticalLayout_34.setObjectName("verticalLayout_34")
        self.g_supplierinfoordersettings_frame_6 = QtWidgets.QFrame(self.g_verification_groupbox)
        self.g_supplierinfoordersettings_frame_6.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_supplierinfoordersettings_frame_6.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_supplierinfoordersettings_frame_6.setObjectName("g_supplierinfoordersettings_frame_6")
        self.horizontalLayout_43 = QtWidgets.QHBoxLayout(self.g_supplierinfoordersettings_frame_6)
        self.horizontalLayout_43.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_43.setSpacing(6)
        self.horizontalLayout_43.setObjectName("horizontalLayout_43")
        self.g_supplierinfopoformat_label_5 = QtWidgets.QLabel(self.g_supplierinfoordersettings_frame_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_supplierinfopoformat_label_5.sizePolicy().hasHeightForWidth())
        self.g_supplierinfopoformat_label_5.setSizePolicy(sizePolicy)
        self.g_supplierinfopoformat_label_5.setMinimumSize(QtCore.QSize(101, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_supplierinfopoformat_label_5.setFont(font)
        self.g_supplierinfopoformat_label_5.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_supplierinfopoformat_label_5.setObjectName("g_supplierinfopoformat_label_5")
        self.horizontalLayout_43.addWidget(self.g_supplierinfopoformat_label_5)
        self.g_verification_combobox = QtWidgets.QComboBox(self.g_supplierinfoordersettings_frame_6)
        self.g_verification_combobox.setMinimumSize(QtCore.QSize(120, 0))
        self.g_verification_combobox.setMaximumSize(QtCore.QSize(********, 31))
        self.g_verification_combobox.setProperty("qp_suploc_send_po_via", "")
        self.g_verification_combobox.setObjectName("g_verification_combobox")
        self.g_verification_combobox.addItem("")
        self.g_verification_combobox.addItem("")
        self.g_verification_combobox.addItem("")
        self.horizontalLayout_43.addWidget(self.g_verification_combobox)
        spacerItem44 = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_43.addItem(spacerItem44)
        self.verticalLayout_34.addWidget(self.g_supplierinfoordersettings_frame_6)
        self.verticalLayout_24.addWidget(self.g_verification_groupbox)
        spacerItem45 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_24.addItem(spacerItem45)
        self.horizontalLayout_32.addWidget(self.g_supplier_orderinfo_frame)
        self.g_supplierinfo_frame = QtWidgets.QFrame(self.g_sub_supplier_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_supplierinfo_frame.sizePolicy().hasHeightForWidth())
        self.g_supplierinfo_frame.setSizePolicy(sizePolicy)
        self.g_supplierinfo_frame.setMaximumSize(QtCore.QSize(567, ********))
        self.g_supplierinfo_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_supplierinfo_frame.setObjectName("g_supplierinfo_frame")
        self.verticalLayout_27 = QtWidgets.QVBoxLayout(self.g_supplierinfo_frame)
        self.verticalLayout_27.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_27.setSpacing(6)
        self.verticalLayout_27.setObjectName("verticalLayout_27")
        self.g_pidb_groupbox = QtWidgets.QGroupBox(self.g_supplierinfo_frame)
        self.g_pidb_groupbox.setMinimumSize(QtCore.QSize(0, 100))
        self.g_pidb_groupbox.setMaximumSize(QtCore.QSize(********, 100))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_pidb_groupbox.setFont(font)
        self.g_pidb_groupbox.setObjectName("g_pidb_groupbox")
        self.verticalLayout_48 = QtWidgets.QVBoxLayout(self.g_pidb_groupbox)
        self.verticalLayout_48.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout_48.setSpacing(6)
        self.verticalLayout_48.setObjectName("verticalLayout_48")
        self.g_supplierinfoordersettings_frame_5 = QtWidgets.QFrame(self.g_pidb_groupbox)
        self.g_supplierinfoordersettings_frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_supplierinfoordersettings_frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_supplierinfoordersettings_frame_5.setObjectName("g_supplierinfoordersettings_frame_5")
        self.horizontalLayout_54 = QtWidgets.QHBoxLayout(self.g_supplierinfoordersettings_frame_5)
        self.horizontalLayout_54.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_54.setSpacing(6)
        self.horizontalLayout_54.setObjectName("horizontalLayout_54")
        self.g_minorderamount_label_5 = QtWidgets.QLabel(self.g_supplierinfoordersettings_frame_5)
        self.g_minorderamount_label_5.setMinimumSize(QtCore.QSize(65, 0))
        self.g_minorderamount_label_5.setMaximumSize(QtCore.QSize(65, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_minorderamount_label_5.setFont(font)
        self.g_minorderamount_label_5.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_minorderamount_label_5.setObjectName("g_minorderamount_label_5")
        self.horizontalLayout_54.addWidget(self.g_minorderamount_label_5)
        self.g_pidbmappedto_lineedit = QtWidgets.QLineEdit(self.g_supplierinfoordersettings_frame_5)
        self.g_pidbmappedto_lineedit.setMinimumSize(QtCore.QSize(200, 31))
        self.g_pidbmappedto_lineedit.setMaximumSize(QtCore.QSize(********, 31))
        self.g_pidbmappedto_lineedit.setText("")
        self.g_pidbmappedto_lineedit.setProperty("qp_suploc_min_order_amount", "")
        self.g_pidbmappedto_lineedit.setObjectName("g_pidbmappedto_lineedit")
        self.horizontalLayout_54.addWidget(self.g_pidbmappedto_lineedit)
        self.verticalLayout_48.addWidget(self.g_supplierinfoordersettings_frame_5)
        self.g_supplierinfoordersettings_frame_7 = QtWidgets.QFrame(self.g_pidb_groupbox)
        self.g_supplierinfoordersettings_frame_7.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_supplierinfoordersettings_frame_7.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_supplierinfoordersettings_frame_7.setObjectName("g_supplierinfoordersettings_frame_7")
        self.horizontalLayout_65 = QtWidgets.QHBoxLayout(self.g_supplierinfoordersettings_frame_7)
        self.horizontalLayout_65.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_65.setSpacing(6)
        self.horizontalLayout_65.setObjectName("horizontalLayout_65")
        self.g_lastsync_label = QtWidgets.QLabel(self.g_supplierinfoordersettings_frame_7)
        self.g_lastsync_label.setMinimumSize(QtCore.QSize(65, 0))
        self.g_lastsync_label.setMaximumSize(QtCore.QSize(65, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_lastsync_label.setFont(font)
        self.g_lastsync_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_lastsync_label.setObjectName("g_lastsync_label")
        self.horizontalLayout_65.addWidget(self.g_lastsync_label)
        self.g_lastsync_lineedit = QtWidgets.QLineEdit(self.g_supplierinfoordersettings_frame_7)
        self.g_lastsync_lineedit.setMinimumSize(QtCore.QSize(200, 31))
        self.g_lastsync_lineedit.setMaximumSize(QtCore.QSize(200, 31))
        self.g_lastsync_lineedit.setText("")
        self.g_lastsync_lineedit.setProperty("qp_suploc_min_order_amount", "")
        self.g_lastsync_lineedit.setObjectName("g_lastsync_lineedit")
        self.horizontalLayout_65.addWidget(self.g_lastsync_lineedit)
        self.g_enablepidb_checkBox = QtWidgets.QCheckBox(self.g_supplierinfoordersettings_frame_7)
        self.g_enablepidb_checkBox.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_enablepidb_checkBox.setFont(font)
        self.g_enablepidb_checkBox.setChecked(False)
        self.g_enablepidb_checkBox.setObjectName("g_enablepidb_checkBox")
        self.horizontalLayout_65.addWidget(self.g_enablepidb_checkBox)
        spacerItem46 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_65.addItem(spacerItem46)
        self.verticalLayout_48.addWidget(self.g_supplierinfoordersettings_frame_7)
        self.g_supplierpidbstatus_frame = QtWidgets.QFrame(self.g_pidb_groupbox)
        self.g_supplierpidbstatus_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_supplierpidbstatus_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_supplierpidbstatus_frame.setObjectName("g_supplierpidbstatus_frame")
        self.horizontalLayout_59 = QtWidgets.QHBoxLayout(self.g_supplierpidbstatus_frame)
        self.horizontalLayout_59.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_59.setSpacing(6)
        self.horizontalLayout_59.setObjectName("horizontalLayout_59")
        self.g_minorderamount_label_6 = QtWidgets.QLabel(self.g_supplierpidbstatus_frame)
        self.g_minorderamount_label_6.setMinimumSize(QtCore.QSize(90, 0))
        self.g_minorderamount_label_6.setMaximumSize(QtCore.QSize(90, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_minorderamount_label_6.setFont(font)
        self.g_minorderamount_label_6.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_minorderamount_label_6.setObjectName("g_minorderamount_label_6")
        self.horizontalLayout_59.addWidget(self.g_minorderamount_label_6)
        self.g_pidbstatus_lineedit = QtWidgets.QLineEdit(self.g_supplierpidbstatus_frame)
        self.g_pidbstatus_lineedit.setMinimumSize(QtCore.QSize(100, 31))
        self.g_pidbstatus_lineedit.setMaximumSize(QtCore.QSize(100, 31))
        self.g_pidbstatus_lineedit.setText("")
        self.g_pidbstatus_lineedit.setProperty("qp_suploc_min_order_amount", "")
        self.g_pidbstatus_lineedit.setObjectName("g_pidbstatus_lineedit")
        self.horizontalLayout_59.addWidget(self.g_pidbstatus_lineedit)
        self.g_minorderamount_label_7 = QtWidgets.QLabel(self.g_supplierpidbstatus_frame)
        self.g_minorderamount_label_7.setMinimumSize(QtCore.QSize(45, 0))
        self.g_minorderamount_label_7.setMaximumSize(QtCore.QSize(45, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_minorderamount_label_7.setFont(font)
        self.g_minorderamount_label_7.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_minorderamount_label_7.setObjectName("g_minorderamount_label_7")
        self.horizontalLayout_59.addWidget(self.g_minorderamount_label_7)
        self.g_pidbnote_lineedit = QtWidgets.QLineEdit(self.g_supplierpidbstatus_frame)
        self.g_pidbnote_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pidbnote_lineedit.setMaximumSize(QtCore.QSize(********, 31))
        self.g_pidbnote_lineedit.setText("")
        self.g_pidbnote_lineedit.setProperty("qp_suploc_min_order_amount", "")
        self.g_pidbnote_lineedit.setObjectName("g_pidbnote_lineedit")
        self.horizontalLayout_59.addWidget(self.g_pidbnote_lineedit)
        self.verticalLayout_48.addWidget(self.g_supplierpidbstatus_frame)
        self.verticalLayout_27.addWidget(self.g_pidb_groupbox)
        self.g_supplierinfonotes_groupbox = QtWidgets.QGroupBox(self.g_supplierinfo_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_supplierinfonotes_groupbox.setFont(font)
        self.g_supplierinfonotes_groupbox.setObjectName("g_supplierinfonotes_groupbox")
        self.horizontalLayout_51 = QtWidgets.QHBoxLayout(self.g_supplierinfonotes_groupbox)
        self.horizontalLayout_51.setContentsMargins(9, 9, 9, 9)
        self.horizontalLayout_51.setSpacing(6)
        self.horizontalLayout_51.setObjectName("horizontalLayout_51")
        self.g_suppliernotes_plaintextedit = QtWidgets.QPlainTextEdit(self.g_supplierinfonotes_groupbox)
        self.g_suppliernotes_plaintextedit.setProperty("qp_suploc_notes", "")
        self.g_suppliernotes_plaintextedit.setObjectName("g_suppliernotes_plaintextedit")
        self.horizontalLayout_51.addWidget(self.g_suppliernotes_plaintextedit)
        self.verticalLayout_27.addWidget(self.g_supplierinfonotes_groupbox)
        self.g_supplierinforight_frame = QtWidgets.QFrame(self.g_supplierinfo_frame)
        self.g_supplierinforight_frame.setObjectName("g_supplierinforight_frame")
        self.verticalLayout_33 = QtWidgets.QVBoxLayout(self.g_supplierinforight_frame)
        self.verticalLayout_33.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_33.setObjectName("verticalLayout_33")
        self.verticalLayout_27.addWidget(self.g_supplierinforight_frame)
        self.horizontalLayout_32.addWidget(self.g_supplierinfo_frame)
        self.verticalLayout_17.addWidget(self.g_sub_supplier_frame)
        self.verticalLayout_6.addWidget(self.g_supplier_frame)
        self.g_manageentity_tabwidget.addTab(self.g_supplierterms_widget, "")
        self.g_purchasehistory_widget = QtWidgets.QWidget()
        self.g_purchasehistory_widget.setObjectName("g_purchasehistory_widget")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.g_purchasehistory_widget)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.g_startenddate_frame = QtWidgets.QFrame(self.g_purchasehistory_widget)
        self.g_startenddate_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_startenddate_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_startenddate_frame.setObjectName("g_startenddate_frame")
        self.horizontalLayout_23 = QtWidgets.QHBoxLayout(self.g_startenddate_frame)
        self.horizontalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_23.setSpacing(6)
        self.horizontalLayout_23.setObjectName("horizontalLayout_23")
        self.g_startdate_label = QtWidgets.QLabel(self.g_startenddate_frame)
        self.g_startdate_label.setMinimumSize(QtCore.QSize(75, 0))
        self.g_startdate_label.setMaximumSize(QtCore.QSize(60, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_startdate_label.setFont(font)
        self.g_startdate_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_startdate_label.setObjectName("g_startdate_label")
        self.horizontalLayout_23.addWidget(self.g_startdate_label)
        self.g_startdate_timeedit = QtWidgets.QDateTimeEdit(self.g_startenddate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_startdate_timeedit.sizePolicy().hasHeightForWidth())
        self.g_startdate_timeedit.setSizePolicy(sizePolicy)
        self.g_startdate_timeedit.setMinimumSize(QtCore.QSize(200, 31))
        self.g_startdate_timeedit.setMaximumSize(QtCore.QSize(200, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_startdate_timeedit.setFont(font)
        self.g_startdate_timeedit.setStyleSheet("")
        self.g_startdate_timeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_startdate_timeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2013, 12, 6), QtCore.QTime(0, 0, 0)))
        self.g_startdate_timeedit.setCalendarPopup(True)
        self.g_startdate_timeedit.setTimeSpec(QtCore.Qt.LocalTime)
        self.g_startdate_timeedit.setObjectName("g_startdate_timeedit")
        self.horizontalLayout_23.addWidget(self.g_startdate_timeedit)
        self.g_enddate_label = QtWidgets.QLabel(self.g_startenddate_frame)
        self.g_enddate_label.setMinimumSize(QtCore.QSize(75, 0))
        self.g_enddate_label.setMaximumSize(QtCore.QSize(60, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_enddate_label.setFont(font)
        self.g_enddate_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_enddate_label.setObjectName("g_enddate_label")
        self.horizontalLayout_23.addWidget(self.g_enddate_label)
        self.g_enddate_timeedit = QtWidgets.QDateTimeEdit(self.g_startenddate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_enddate_timeedit.sizePolicy().hasHeightForWidth())
        self.g_enddate_timeedit.setSizePolicy(sizePolicy)
        self.g_enddate_timeedit.setMinimumSize(QtCore.QSize(200, 31))
        self.g_enddate_timeedit.setMaximumSize(QtCore.QSize(200, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_enddate_timeedit.setFont(font)
        self.g_enddate_timeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_enddate_timeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 6), QtCore.QTime(0, 0, 0)))
        self.g_enddate_timeedit.setCalendarPopup(True)
        self.g_enddate_timeedit.setObjectName("g_enddate_timeedit")
        self.horizontalLayout_23.addWidget(self.g_enddate_timeedit)
        spacerItem47 = QtWidgets.QSpacerItem(346, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_23.addItem(spacerItem47)
        self.verticalLayout_7.addWidget(self.g_startenddate_frame)
        self.g_purchaseorder_tabwidget = QtWidgets.QTabWidget(self.g_purchasehistory_widget)
        font = QtGui.QFont()
        font.setPointSize(10)
        self.g_purchaseorder_tabwidget.setFont(font)
        self.g_purchaseorder_tabwidget.setAutoFillBackground(False)
        self.g_purchaseorder_tabwidget.setStyleSheet("")
        self.g_purchaseorder_tabwidget.setTabPosition(QtWidgets.QTabWidget.North)
        self.g_purchaseorder_tabwidget.setTabShape(QtWidgets.QTabWidget.Rounded)
        self.g_purchaseorder_tabwidget.setUsesScrollButtons(True)
        self.g_purchaseorder_tabwidget.setDocumentMode(False)
        self.g_purchaseorder_tabwidget.setMovable(False)
        self.g_purchaseorder_tabwidget.setObjectName("g_purchaseorder_tabwidget")
        self.g_purchaseorder_widget = QtWidgets.QWidget()
        self.g_purchaseorder_widget.setStyleSheet("")
        self.g_purchaseorder_widget.setObjectName("g_purchaseorder_widget")
        self.verticalLayout_19 = QtWidgets.QVBoxLayout(self.g_purchaseorder_widget)
        self.verticalLayout_19.setObjectName("verticalLayout_19")
        self.g_purchaseorder_table = QtWidgets.QTableWidget(self.g_purchaseorder_widget)
        self.g_purchaseorder_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_purchaseorder_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_purchaseorder_table.setAlternatingRowColors(True)
        self.g_purchaseorder_table.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_purchaseorder_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_purchaseorder_table.setShowGrid(False)
        self.g_purchaseorder_table.setObjectName("g_purchaseorder_table")
        self.g_purchaseorder_table.setColumnCount(6)
        self.g_purchaseorder_table.setRowCount(6)
        item = QtWidgets.QTableWidgetItem()
        self.g_purchaseorder_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_purchaseorder_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_purchaseorder_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_purchaseorder_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_purchaseorder_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_purchaseorder_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_purchaseorder_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_purchaseorder_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_purchaseorder_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_purchaseorder_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_purchaseorder_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_purchaseorder_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_purchaseorder_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_purchaseorder_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_purchaseorder_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_purchaseorder_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_purchaseorder_table.setItem(0, 4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_purchaseorder_table.setItem(0, 5, item)
        self.g_purchaseorder_table.horizontalHeader().setDefaultSectionSize(135)
        self.g_purchaseorder_table.horizontalHeader().setStretchLastSection(True)
        self.g_purchaseorder_table.verticalHeader().setVisible(False)
        self.verticalLayout_19.addWidget(self.g_purchaseorder_table)
        self.g_purchaseorder_tabwidget.addTab(self.g_purchaseorder_widget, "")
        self.g_product_widget = QtWidgets.QWidget()
        self.g_product_widget.setObjectName("g_product_widget")
        self.verticalLayout_10 = QtWidgets.QVBoxLayout(self.g_product_widget)
        self.verticalLayout_10.setObjectName("verticalLayout_10")
        self.g_product_table = QtWidgets.QTableWidget(self.g_product_widget)
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_product_table.setFont(font)
        self.g_product_table.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)
        self.g_product_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_product_table.setAlternatingRowColors(True)
        self.g_product_table.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.g_product_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_product_table.setShowGrid(False)
        self.g_product_table.setObjectName("g_product_table")
        self.g_product_table.setColumnCount(9)
        self.g_product_table.setRowCount(5)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_product_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_product_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_product_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_product_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_product_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_product_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_product_table.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_product_table.setHorizontalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_product_table.setHorizontalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setItem(0, 4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setItem(0, 5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setItem(0, 6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setItem(0, 7, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setItem(0, 8, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_product_table.setItem(1, 0, item)
        self.g_product_table.horizontalHeader().setDefaultSectionSize(87)
        self.g_product_table.horizontalHeader().setStretchLastSection(True)
        self.g_product_table.verticalHeader().setVisible(False)
        self.verticalLayout_10.addWidget(self.g_product_table)
        self.g_purchaseorder_tabwidget.addTab(self.g_product_widget, "")
        self.verticalLayout_7.addWidget(self.g_purchaseorder_tabwidget)
        self.g_manageentity_tabwidget.addTab(self.g_purchasehistory_widget, "")
        self.g_manurfacturers_tab = QtWidgets.QWidget()
        self.g_manurfacturers_tab.setObjectName("g_manurfacturers_tab")
        self.verticalLayout_41 = QtWidgets.QVBoxLayout(self.g_manurfacturers_tab)
        self.verticalLayout_41.setObjectName("verticalLayout_41")
        self.g_manufacturers_table = QtWidgets.QTableWidget(self.g_manurfacturers_tab)
        self.g_manufacturers_table.setAlternatingRowColors(True)
        self.g_manufacturers_table.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.g_manufacturers_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_manufacturers_table.setShowGrid(False)
        self.g_manufacturers_table.setObjectName("g_manufacturers_table")
        self.g_manufacturers_table.setColumnCount(0)
        self.g_manufacturers_table.setRowCount(14)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_manufacturers_table.setVerticalHeaderItem(13, item)
        self.g_manufacturers_table.horizontalHeader().setVisible(False)
        self.g_manufacturers_table.horizontalHeader().setDefaultSectionSize(200)
        self.g_manufacturers_table.horizontalHeader().setStretchLastSection(True)
        self.g_manufacturers_table.verticalHeader().setVisible(False)
        self.verticalLayout_41.addWidget(self.g_manufacturers_table)
        self.g_manufactuer_controlbuttons_frame = QtWidgets.QFrame(self.g_manurfacturers_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_manufactuer_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_manufactuer_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_manufactuer_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 46))
        self.g_manufactuer_controlbuttons_frame.setMaximumSize(QtCore.QSize(********, 46))
        self.g_manufactuer_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_manufactuer_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_manufactuer_controlbuttons_frame.setObjectName("g_manufactuer_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_manufactuer_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 6, 0, 0)
        self.horizontalLayout_11.setSpacing(6)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.g_manfactuer_totalresults_label = QtWidgets.QLabel(self.g_manufactuer_controlbuttons_frame)
        self.g_manfactuer_totalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_manfactuer_totalresults_label.setFont(font)
        self.g_manfactuer_totalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_manfactuer_totalresults_label.setObjectName("g_manfactuer_totalresults_label")
        self.horizontalLayout_11.addWidget(self.g_manfactuer_totalresults_label)
        spacerItem48 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem48)
        self.g_manufactuer_massaction_button = QtWidgets.QPushButton(self.g_manufactuer_controlbuttons_frame)
        self.g_manufactuer_massaction_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manufactuer_massaction_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_manufactuer_massaction_button.setStyleSheet("")
        self.g_manufactuer_massaction_button.setIcon(icon18)
        self.g_manufactuer_massaction_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manufactuer_massaction_button.setObjectName("g_manufactuer_massaction_button")
        self.horizontalLayout_11.addWidget(self.g_manufactuer_massaction_button)
        self.verticalLayout_41.addWidget(self.g_manufactuer_controlbuttons_frame)
        self.g_manageentity_tabwidget.addTab(self.g_manurfacturers_tab, "")
        self.g_brands_widget = QtWidgets.QWidget()
        self.g_brands_widget.setObjectName("g_brands_widget")
        self.verticalLayout_26 = QtWidgets.QVBoxLayout(self.g_brands_widget)
        self.verticalLayout_26.setObjectName("verticalLayout_26")
        self.g_brands_table = QtWidgets.QTableWidget(self.g_brands_widget)
        self.g_brands_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_brands_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_brands_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_brands_table.setAlternatingRowColors(True)
        self.g_brands_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_brands_table.setShowGrid(False)
        self.g_brands_table.setObjectName("g_brands_table")
        self.g_brands_table.setColumnCount(1)
        self.g_brands_table.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_brands_table.setHorizontalHeaderItem(0, item)
        self.g_brands_table.horizontalHeader().setDefaultSectionSize(199)
        self.g_brands_table.horizontalHeader().setMinimumSectionSize(37)
        self.g_brands_table.horizontalHeader().setStretchLastSection(True)
        self.g_brands_table.verticalHeader().setVisible(False)
        self.verticalLayout_26.addWidget(self.g_brands_table)
        self.g_managebrand_frame = QtWidgets.QFrame(self.g_brands_widget)
        self.g_managebrand_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_managebrand_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_managebrand_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_managebrand_frame.setObjectName("g_managebrand_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_managebrand_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setSpacing(6)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        spacerItem49 = QtWidgets.QSpacerItem(653, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem49)
        self.g_deletebrand_button = QtWidgets.QPushButton(self.g_managebrand_frame)
        self.g_deletebrand_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_deletebrand_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_deletebrand_button.setStyleSheet("")
        self.g_deletebrand_button.setIcon(icon6)
        self.g_deletebrand_button.setIconSize(QtCore.QSize(24, 24))
        self.g_deletebrand_button.setObjectName("g_deletebrand_button")
        self.horizontalLayout_3.addWidget(self.g_deletebrand_button)
        self.g_managebrand_button = QtWidgets.QPushButton(self.g_managebrand_frame)
        self.g_managebrand_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_managebrand_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_managebrand_button.setStyleSheet("")
        self.g_managebrand_button.setIcon(icon5)
        self.g_managebrand_button.setIconSize(QtCore.QSize(24, 24))
        self.g_managebrand_button.setObjectName("g_managebrand_button")
        self.horizontalLayout_3.addWidget(self.g_managebrand_button)
        self.g_addbrand_button = QtWidgets.QPushButton(self.g_managebrand_frame)
        self.g_addbrand_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_addbrand_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_addbrand_button.setStyleSheet("")
        self.g_addbrand_button.setIcon(icon4)
        self.g_addbrand_button.setIconSize(QtCore.QSize(24, 24))
        self.g_addbrand_button.setObjectName("g_addbrand_button")
        self.horizontalLayout_3.addWidget(self.g_addbrand_button)
        self.verticalLayout_26.addWidget(self.g_managebrand_frame)
        self.g_manageentity_tabwidget.addTab(self.g_brands_widget, "")
        self.g_mfgmedia_tab = QtWidgets.QWidget()
        self.g_mfgmedia_tab.setObjectName("g_mfgmedia_tab")
        self.verticalLayout_45 = QtWidgets.QVBoxLayout(self.g_mfgmedia_tab)
        self.verticalLayout_45.setObjectName("verticalLayout_45")
        self.g_cropper_mfg_area = QtWidgets.QFrame(self.g_mfgmedia_tab)
        self.g_cropper_mfg_area.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cropper_mfg_area.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cropper_mfg_area.setObjectName("g_cropper_mfg_area")
        self.verticalLayout_43 = QtWidgets.QVBoxLayout(self.g_cropper_mfg_area)
        self.verticalLayout_43.setObjectName("verticalLayout_43")
        self.g_description_mfg_frame = QtWidgets.QFrame(self.g_cropper_mfg_area)
        self.g_description_mfg_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_description_mfg_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_description_mfg_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_description_mfg_frame.setObjectName("g_description_mfg_frame")
        self.horizontalLayout_53 = QtWidgets.QHBoxLayout(self.g_description_mfg_frame)
        self.horizontalLayout_53.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_53.setSpacing(6)
        self.horizontalLayout_53.setObjectName("horizontalLayout_53")
        self.g_description_mfg_label = QtWidgets.QLabel(self.g_description_mfg_frame)
        self.g_description_mfg_label.setMinimumSize(QtCore.QSize(140, 0))
        self.g_description_mfg_label.setMaximumSize(QtCore.QSize(140, ********))
        self.g_description_mfg_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_description_mfg_label.setObjectName("g_description_mfg_label")
        self.horizontalLayout_53.addWidget(self.g_description_mfg_label)
        self.g_image_description_mfg_lineedit = QtWidgets.QLineEdit(self.g_description_mfg_frame)
        self.g_image_description_mfg_lineedit.setObjectName("g_image_description_mfg_lineedit")
        self.horizontalLayout_53.addWidget(self.g_image_description_mfg_lineedit)
        self.verticalLayout_43.addWidget(self.g_description_mfg_frame)
        self.g_mediatitle_mfg_frame = QtWidgets.QFrame(self.g_cropper_mfg_area)
        self.g_mediatitle_mfg_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_mediatitle_mfg_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_mediatitle_mfg_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_mediatitle_mfg_frame.setObjectName("g_mediatitle_mfg_frame")
        self.horizontalLayout_55 = QtWidgets.QHBoxLayout(self.g_mediatitle_mfg_frame)
        self.horizontalLayout_55.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_55.setSpacing(6)
        self.horizontalLayout_55.setObjectName("horizontalLayout_55")
        self.g_title_mfg_label = QtWidgets.QLabel(self.g_mediatitle_mfg_frame)
        self.g_title_mfg_label.setMinimumSize(QtCore.QSize(140, 0))
        self.g_title_mfg_label.setMaximumSize(QtCore.QSize(140, ********))
        self.g_title_mfg_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_title_mfg_label.setObjectName("g_title_mfg_label")
        self.horizontalLayout_55.addWidget(self.g_title_mfg_label)
        self.g_title_mfg_lineedit = QtWidgets.QLineEdit(self.g_mediatitle_mfg_frame)
        self.g_title_mfg_lineedit.setObjectName("g_title_mfg_lineedit")
        self.horizontalLayout_55.addWidget(self.g_title_mfg_lineedit)
        self.g_tags_mfg_label = QtWidgets.QLabel(self.g_mediatitle_mfg_frame)
        self.g_tags_mfg_label.setMinimumSize(QtCore.QSize(50, 0))
        self.g_tags_mfg_label.setMaximumSize(QtCore.QSize(50, ********))
        self.g_tags_mfg_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_tags_mfg_label.setObjectName("g_tags_mfg_label")
        self.horizontalLayout_55.addWidget(self.g_tags_mfg_label)
        self.g_tags_mfg_lineedit = QtWidgets.QLineEdit(self.g_mediatitle_mfg_frame)
        self.g_tags_mfg_lineedit.setObjectName("g_tags_mfg_lineedit")
        self.horizontalLayout_55.addWidget(self.g_tags_mfg_lineedit)
        self.verticalLayout_43.addWidget(self.g_mediatitle_mfg_frame)
        self.g_cropper_mfg_frame = QtWidgets.QFrame(self.g_cropper_mfg_area)
        self.g_cropper_mfg_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_cropper_mfg_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cropper_mfg_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cropper_mfg_frame.setObjectName("g_cropper_mfg_frame")
        self.horizontalLayout_63 = QtWidgets.QHBoxLayout(self.g_cropper_mfg_frame)
        self.horizontalLayout_63.setSpacing(6)
        self.horizontalLayout_63.setObjectName("horizontalLayout_63")
        self.verticalLayout_43.addWidget(self.g_cropper_mfg_frame)
        self.g_size_mfg_label = QtWidgets.QLabel(self.g_cropper_mfg_area)
        self.g_size_mfg_label.setMinimumSize(QtCore.QSize(90, 0))
        self.g_size_mfg_label.setMaximumSize(QtCore.QSize(********, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_size_mfg_label.setFont(font)
        self.g_size_mfg_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_size_mfg_label.setObjectName("g_size_mfg_label")
        self.verticalLayout_43.addWidget(self.g_size_mfg_label)
        spacerItem50 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_43.addItem(spacerItem50)
        spacerItem51 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_43.addItem(spacerItem51)
        self.g_controlbuttons_mfg_frame = QtWidgets.QFrame(self.g_cropper_mfg_area)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_mfg_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_mfg_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_mfg_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_mfg_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.g_controlbuttons_mfg_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_mfg_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_mfg_frame.setObjectName("g_controlbuttons_mfg_frame")
        self.horizontalLayout_64 = QtWidgets.QHBoxLayout(self.g_controlbuttons_mfg_frame)
        self.horizontalLayout_64.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_64.setSpacing(6)
        self.horizontalLayout_64.setObjectName("horizontalLayout_64")
        spacerItem52 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_64.addItem(spacerItem52)
        self.g_cropper_cancel_mfg_btn = QtWidgets.QPushButton(self.g_controlbuttons_mfg_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_cropper_cancel_mfg_btn.sizePolicy().hasHeightForWidth())
        self.g_cropper_cancel_mfg_btn.setSizePolicy(sizePolicy)
        self.g_cropper_cancel_mfg_btn.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cropper_cancel_mfg_btn.setMaximumSize(QtCore.QSize(120, ********))
        self.g_cropper_cancel_mfg_btn.setIcon(icon9)
        self.g_cropper_cancel_mfg_btn.setIconSize(QtCore.QSize(24, 24))
        self.g_cropper_cancel_mfg_btn.setObjectName("g_cropper_cancel_mfg_btn")
        self.horizontalLayout_64.addWidget(self.g_cropper_cancel_mfg_btn)
        self.g_crop_and_save_mfg_btn = QtWidgets.QPushButton(self.g_controlbuttons_mfg_frame)
        self.g_crop_and_save_mfg_btn.setMinimumSize(QtCore.QSize(120, 40))
        self.g_crop_and_save_mfg_btn.setMaximumSize(QtCore.QSize(120, ********))
        self.g_crop_and_save_mfg_btn.setIcon(icon10)
        self.g_crop_and_save_mfg_btn.setIconSize(QtCore.QSize(24, 24))
        self.g_crop_and_save_mfg_btn.setObjectName("g_crop_and_save_mfg_btn")
        self.horizontalLayout_64.addWidget(self.g_crop_and_save_mfg_btn)
        self.verticalLayout_43.addWidget(self.g_controlbuttons_mfg_frame)
        self.verticalLayout_45.addWidget(self.g_cropper_mfg_area)
        self.g_addedit_mfg_groupBox = QtWidgets.QGroupBox(self.g_mfgmedia_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_addedit_mfg_groupBox.sizePolicy().hasHeightForWidth())
        self.g_addedit_mfg_groupBox.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_addedit_mfg_groupBox.setFont(font)
        self.g_addedit_mfg_groupBox.setObjectName("g_addedit_mfg_groupBox")
        self.verticalLayout_44 = QtWidgets.QVBoxLayout(self.g_addedit_mfg_groupBox)
        self.verticalLayout_44.setSpacing(6)
        self.verticalLayout_44.setObjectName("verticalLayout_44")
        spacerItem53 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_44.addItem(spacerItem53)
        self.g_mfg_images_list = QtWidgets.QListWidget(self.g_addedit_mfg_groupBox)
        self.g_mfg_images_list.setMinimumSize(QtCore.QSize(0, 0))
        self.g_mfg_images_list.setWordWrap(False)
        self.g_mfg_images_list.setObjectName("g_mfg_images_list")
        self.verticalLayout_44.addWidget(self.g_mfg_images_list)
        spacerItem54 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_44.addItem(spacerItem54)
        self.g_upload_mfg_frame = QtWidgets.QFrame(self.g_addedit_mfg_groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_upload_mfg_frame.sizePolicy().hasHeightForWidth())
        self.g_upload_mfg_frame.setSizePolicy(sizePolicy)
        self.g_upload_mfg_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_upload_mfg_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.g_upload_mfg_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_upload_mfg_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_upload_mfg_frame.setObjectName("g_upload_mfg_frame")
        self.horizontalLayout_56 = QtWidgets.QHBoxLayout(self.g_upload_mfg_frame)
        self.horizontalLayout_56.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_56.setSpacing(6)
        self.horizontalLayout_56.setObjectName("horizontalLayout_56")
        self.g_uploadnew_mfg_label = QtWidgets.QLabel(self.g_upload_mfg_frame)
        self.g_uploadnew_mfg_label.setMinimumSize(QtCore.QSize(80, 31))
        self.g_uploadnew_mfg_label.setMaximumSize(QtCore.QSize(0, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_uploadnew_mfg_label.setFont(font)
        self.g_uploadnew_mfg_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_uploadnew_mfg_label.setObjectName("g_uploadnew_mfg_label")
        self.horizontalLayout_56.addWidget(self.g_uploadnew_mfg_label)
        self.g_uploadfilename_mfg_lineedit = QtWidgets.QLineEdit(self.g_upload_mfg_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_uploadfilename_mfg_lineedit.sizePolicy().hasHeightForWidth())
        self.g_uploadfilename_mfg_lineedit.setSizePolicy(sizePolicy)
        self.g_uploadfilename_mfg_lineedit.setMinimumSize(QtCore.QSize(100, 31))
        self.g_uploadfilename_mfg_lineedit.setMaximumSize(QtCore.QSize(********, 31))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_uploadfilename_mfg_lineedit.setFont(font)
        self.g_uploadfilename_mfg_lineedit.setReadOnly(False)
        self.g_uploadfilename_mfg_lineedit.setObjectName("g_uploadfilename_mfg_lineedit")
        self.horizontalLayout_56.addWidget(self.g_uploadfilename_mfg_lineedit)
        self.g_selectfile_mfg_button = QtWidgets.QPushButton(self.g_upload_mfg_frame)
        self.g_selectfile_mfg_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_selectfile_mfg_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_selectfile_mfg_button.setStyleSheet("")
        icon19 = QtGui.QIcon()
        icon19.addPixmap(QtGui.QPixmap(":/products/white magnifier"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_selectfile_mfg_button.setIcon(icon19)
        self.g_selectfile_mfg_button.setIconSize(QtCore.QSize(24, 24))
        self.g_selectfile_mfg_button.setObjectName("g_selectfile_mfg_button")
        self.horizontalLayout_56.addWidget(self.g_selectfile_mfg_button)
        self.verticalLayout_44.addWidget(self.g_upload_mfg_frame)
        self.verticalLayout_45.addWidget(self.g_addedit_mfg_groupBox)
        self.g_manageentity_tabwidget.addTab(self.g_mfgmedia_tab, "")
        self.g_mfgmarketing_tab = QtWidgets.QWidget()
        self.g_mfgmarketing_tab.setObjectName("g_mfgmarketing_tab")
        self.verticalLayout_46 = QtWidgets.QVBoxLayout(self.g_mfgmarketing_tab)
        self.verticalLayout_46.setObjectName("verticalLayout_46")
        self.g_featuredbrand_frame = QtWidgets.QFrame(self.g_mfgmarketing_tab)
        self.g_featuredbrand_frame.setMinimumSize(QtCore.QSize(0, 30))
        self.g_featuredbrand_frame.setMaximumSize(QtCore.QSize(********, 30))
        self.g_featuredbrand_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_featuredbrand_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_featuredbrand_frame.setObjectName("g_featuredbrand_frame")
        self.horizontalLayout_36 = QtWidgets.QHBoxLayout(self.g_featuredbrand_frame)
        self.horizontalLayout_36.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_36.setObjectName("horizontalLayout_36")
        self.g_featured_checkbox = QtWidgets.QCheckBox(self.g_featuredbrand_frame)
        self.g_featured_checkbox.setMinimumSize(QtCore.QSize(200, 0))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_featured_checkbox.setFont(font)
        self.g_featured_checkbox.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_featured_checkbox.setChecked(False)
        self.g_featured_checkbox.setProperty("qp_stn_value", "")
        self.g_featured_checkbox.setObjectName("g_featured_checkbox")
        self.horizontalLayout_36.addWidget(self.g_featured_checkbox)
        self.g_public_checkbox = QtWidgets.QCheckBox(self.g_featuredbrand_frame)
        self.g_public_checkbox.setMinimumSize(QtCore.QSize(200, 0))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_public_checkbox.setFont(font)
        self.g_public_checkbox.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_public_checkbox.setChecked(False)
        self.g_public_checkbox.setProperty("qp_stn_value", "")
        self.g_public_checkbox.setObjectName("g_public_checkbox")
        self.horizontalLayout_36.addWidget(self.g_public_checkbox)
        spacerItem55 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_36.addItem(spacerItem55)
        self.verticalLayout_46.addWidget(self.g_featuredbrand_frame)
        self.g_marketing_label = QtWidgets.QLabel(self.g_mfgmarketing_tab)
        self.g_marketing_label.setMinimumSize(QtCore.QSize(70, 0))
        self.g_marketing_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_marketing_label.setObjectName("g_marketing_label")
        self.verticalLayout_46.addWidget(self.g_marketing_label)
        self.g_mfg_marketing_emessage_plaintextedit = QtWidgets.QPlainTextEdit(self.g_mfgmarketing_tab)
        self.g_mfg_marketing_emessage_plaintextedit.setMinimumSize(QtCore.QSize(550, 0))
        self.g_mfg_marketing_emessage_plaintextedit.setMaximumSize(QtCore.QSize(********, 130))
        self.g_mfg_marketing_emessage_plaintextedit.setProperty("qp_pet_notes", "")
        self.g_mfg_marketing_emessage_plaintextedit.setObjectName("g_mfg_marketing_emessage_plaintextedit")
        self.verticalLayout_46.addWidget(self.g_mfg_marketing_emessage_plaintextedit)
        self.g_marketing_description_label = QtWidgets.QLabel(self.g_mfgmarketing_tab)
        self.g_marketing_description_label.setMinimumSize(QtCore.QSize(0, 0))
        self.g_marketing_description_label.setMaximumSize(QtCore.QSize(********, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_marketing_description_label.setFont(font)
        self.g_marketing_description_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_marketing_description_label.setObjectName("g_marketing_description_label")
        self.verticalLayout_46.addWidget(self.g_marketing_description_label)
        self.g_mfg_marketing_description_lineedit = QtWidgets.QPlainTextEdit(self.g_mfgmarketing_tab)
        self.g_mfg_marketing_description_lineedit.setMinimumSize(QtCore.QSize(550, 0))
        self.g_mfg_marketing_description_lineedit.setMaximumSize(QtCore.QSize(********, ********))
        self.g_mfg_marketing_description_lineedit.setProperty("qp_pet_notes", "")
        self.g_mfg_marketing_description_lineedit.setObjectName("g_mfg_marketing_description_lineedit")
        self.verticalLayout_46.addWidget(self.g_mfg_marketing_description_lineedit)
        self.g_marketinghelp_label = QtWidgets.QLabel(self.g_mfgmarketing_tab)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_marketinghelp_label.sizePolicy().hasHeightForWidth())
        self.g_marketinghelp_label.setSizePolicy(sizePolicy)
        self.g_marketinghelp_label.setMinimumSize(QtCore.QSize(0, 25))
        self.g_marketinghelp_label.setMaximumSize(QtCore.QSize(********, 25))
        self.g_marketinghelp_label.setWordWrap(True)
        self.g_marketinghelp_label.setObjectName("g_marketinghelp_label")
        self.verticalLayout_46.addWidget(self.g_marketinghelp_label)
        self.g_manageentity_tabwidget.addTab(self.g_mfgmarketing_tab, "")
        self.g_employeesettings_widget = QtWidgets.QWidget()
        self.g_employeesettings_widget.setEnabled(True)
        self.g_employeesettings_widget.setStyleSheet(".QTabBar::tab {\n"
"    background-color: black;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}")
        self.g_employeesettings_widget.setObjectName("g_employeesettings_widget")
        self.horizontalLayout_87 = QtWidgets.QHBoxLayout(self.g_employeesettings_widget)
        self.horizontalLayout_87.setObjectName("horizontalLayout_87")
        self.frame_11 = QtWidgets.QFrame(self.g_employeesettings_widget)
        self.frame_11.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_11.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_11.setObjectName("frame_11")
        self.verticalLayout_65 = QtWidgets.QVBoxLayout(self.frame_11)
        self.verticalLayout_65.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_65.setObjectName("verticalLayout_65")
        self.frame_12 = QtWidgets.QFrame(self.frame_11)
        self.frame_12.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_12.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_12.setObjectName("frame_12")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.frame_12)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.g_displayname_label = QtWidgets.QLabel(self.frame_12)
        self.g_displayname_label.setMinimumSize(QtCore.QSize(100, 0))
        self.g_displayname_label.setMaximumSize(QtCore.QSize(********, 100))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_displayname_label.setFont(font)
        self.g_displayname_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_displayname_label.setObjectName("g_displayname_label")
        self.horizontalLayout_10.addWidget(self.g_displayname_label)
        self.g_displayname_lineedit = QtWidgets.QLineEdit(self.frame_12)
        self.g_displayname_lineedit.setMinimumSize(QtCore.QSize(100, 0))
        self.g_displayname_lineedit.setProperty("qp_user_display_name", "")
        self.g_displayname_lineedit.setProperty("qp_user_username", "")
        self.g_displayname_lineedit.setObjectName("g_displayname_lineedit")
        self.horizontalLayout_10.addWidget(self.g_displayname_lineedit)
        self.verticalLayout_65.addWidget(self.frame_12)
        self.frame_13 = QtWidgets.QFrame(self.frame_11)
        self.frame_13.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_13.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_13.setObjectName("frame_13")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.frame_13)
        self.horizontalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.g_employeerole_label = QtWidgets.QLabel(self.frame_13)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_employeerole_label.sizePolicy().hasHeightForWidth())
        self.g_employeerole_label.setSizePolicy(sizePolicy)
        self.g_employeerole_label.setMinimumSize(QtCore.QSize(100, 0))
        self.g_employeerole_label.setMaximumSize(QtCore.QSize(100, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_employeerole_label.setFont(font)
        self.g_employeerole_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_employeerole_label.setObjectName("g_employeerole_label")
        self.horizontalLayout_12.addWidget(self.g_employeerole_label)
        self.g_employee_id_lineedit = QtWidgets.QLineEdit(self.frame_13)
        self.g_employee_id_lineedit.setMinimumSize(QtCore.QSize(100, 0))
        self.g_employee_id_lineedit.setProperty("qp_user_display_name", "")
        self.g_employee_id_lineedit.setProperty("qp_user_username", "")
        self.g_employee_id_lineedit.setObjectName("g_employee_id_lineedit")
        self.horizontalLayout_12.addWidget(self.g_employee_id_lineedit)
        self.g_employeerole_label_2 = QtWidgets.QLabel(self.frame_13)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_employeerole_label_2.sizePolicy().hasHeightForWidth())
        self.g_employeerole_label_2.setSizePolicy(sizePolicy)
        self.g_employeerole_label_2.setMinimumSize(QtCore.QSize(90, 0))
        self.g_employeerole_label_2.setMaximumSize(QtCore.QSize(90, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_employeerole_label_2.setFont(font)
        self.g_employeerole_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_employeerole_label_2.setObjectName("g_employeerole_label_2")
        self.horizontalLayout_12.addWidget(self.g_employeerole_label_2)
        self.g_employeerole_combobox = QtWidgets.QComboBox(self.frame_13)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_employeerole_combobox.sizePolicy().hasHeightForWidth())
        self.g_employeerole_combobox.setSizePolicy(sizePolicy)
        self.g_employeerole_combobox.setMinimumSize(QtCore.QSize(0, 0))
        self.g_employeerole_combobox.setMaximumSize(QtCore.QSize(********, 31))
        self.g_employeerole_combobox.setProperty("qp_erole_role_id", "")
        self.g_employeerole_combobox.setObjectName("g_employeerole_combobox")
        self.g_employeerole_combobox.addItem("")
        self.g_employeerole_combobox.addItem("")
        self.g_employeerole_combobox.addItem("")
        self.horizontalLayout_12.addWidget(self.g_employeerole_combobox)
        self.g_managepermission_button = QtWidgets.QPushButton(self.frame_13)
        self.g_managepermission_button.setMinimumSize(QtCore.QSize(25, 0))
        self.g_managepermission_button.setMaximumSize(QtCore.QSize(25, 31))
        self.g_managepermission_button.setStyleSheet("border:none;\n"
"background-color:none;")
        self.g_managepermission_button.setText("")
        self.g_managepermission_button.setIcon(icon)
        self.g_managepermission_button.setIconSize(QtCore.QSize(22, 31))
        self.g_managepermission_button.setObjectName("g_managepermission_button")
        self.horizontalLayout_12.addWidget(self.g_managepermission_button)
        self.verticalLayout_65.addWidget(self.frame_13)
        self.frame_14 = QtWidgets.QFrame(self.frame_11)
        self.frame_14.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_14.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_14.setObjectName("frame_14")
        self.horizontalLayout_86 = QtWidgets.QHBoxLayout(self.frame_14)
        self.horizontalLayout_86.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_86.setObjectName("horizontalLayout_86")
        self.g_moduleaccess_label = QtWidgets.QLabel(self.frame_14)
        self.g_moduleaccess_label.setMinimumSize(QtCore.QSize(100, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_moduleaccess_label.setFont(font)
        self.g_moduleaccess_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_moduleaccess_label.setObjectName("g_moduleaccess_label")
        self.horizontalLayout_86.addWidget(self.g_moduleaccess_label)
        self.g_moduleaccess_list = QtWidgets.QListWidget(self.frame_14)
        self.g_moduleaccess_list.setMinimumSize(QtCore.QSize(0, 0))
        self.g_moduleaccess_list.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_moduleaccess_list.setAlternatingRowColors(False)
        self.g_moduleaccess_list.setSelectionMode(QtWidgets.QAbstractItemView.MultiSelection)
        self.g_moduleaccess_list.setProperty("qp_ent_accessible_apps", "")
        self.g_moduleaccess_list.setObjectName("g_moduleaccess_list")
        item = QtWidgets.QListWidgetItem()
        self.g_moduleaccess_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_moduleaccess_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_moduleaccess_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_moduleaccess_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_moduleaccess_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        self.g_moduleaccess_list.addItem(item)
        self.horizontalLayout_86.addWidget(self.g_moduleaccess_list)
        self.verticalLayout_65.addWidget(self.frame_14)
        self.horizontalLayout_87.addWidget(self.frame_11)
        self.frame_2 = QtWidgets.QFrame(self.g_employeesettings_widget)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout_64 = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout_64.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_64.setObjectName("verticalLayout_64")
        self.frame_3 = QtWidgets.QFrame(self.frame_2)
        self.frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.frame_3)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_password_label = QtWidgets.QLabel(self.frame_3)
        self.g_password_label.setMinimumSize(QtCore.QSize(100, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_password_label.setFont(font)
        self.g_password_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_password_label.setObjectName("g_password_label")
        self.horizontalLayout.addWidget(self.g_password_label)
        self.g_password_lineedit = QtWidgets.QLineEdit(self.frame_3)
        self.g_password_lineedit.setEnabled(True)
        self.g_password_lineedit.setInputMask("")
        self.g_password_lineedit.setText("")
        self.g_password_lineedit.setEchoMode(QtWidgets.QLineEdit.Password)
        self.g_password_lineedit.setProperty("qp_user_password", "")
        self.g_password_lineedit.setObjectName("g_password_lineedit")
        self.horizontalLayout.addWidget(self.g_password_lineedit)
        self.verticalLayout_64.addWidget(self.frame_3)
        self.frame_6 = QtWidgets.QFrame(self.frame_2)
        self.frame_6.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_6.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_6.setObjectName("frame_6")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.frame_6)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_employeestatus_label = QtWidgets.QLabel(self.frame_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_employeestatus_label.sizePolicy().hasHeightForWidth())
        self.g_employeestatus_label.setSizePolicy(sizePolicy)
        self.g_employeestatus_label.setMinimumSize(QtCore.QSize(100, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_employeestatus_label.setFont(font)
        self.g_employeestatus_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_employeestatus_label.setObjectName("g_employeestatus_label")
        self.horizontalLayout_8.addWidget(self.g_employeestatus_label)
        self.g_employeestatus_combobox = QtWidgets.QComboBox(self.frame_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_employeestatus_combobox.sizePolicy().hasHeightForWidth())
        self.g_employeestatus_combobox.setSizePolicy(sizePolicy)
        self.g_employeestatus_combobox.setMinimumSize(QtCore.QSize(0, 0))
        self.g_employeestatus_combobox.setMaximumSize(QtCore.QSize(********, 31))
        self.g_employeestatus_combobox.setProperty("qp_user_is_active", "")
        self.g_employeestatus_combobox.setObjectName("g_employeestatus_combobox")
        self.g_employeestatus_combobox.addItem("")
        self.g_employeestatus_combobox.addItem("")
        self.horizontalLayout_8.addWidget(self.g_employeestatus_combobox)
        self.g_emplocations_label = QtWidgets.QLabel(self.frame_6)
        self.g_emplocations_label.setMinimumSize(QtCore.QSize(90, 0))
        self.g_emplocations_label.setMaximumSize(QtCore.QSize(55, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_emplocations_label.setFont(font)
        self.g_emplocations_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_emplocations_label.setObjectName("g_emplocations_label")
        self.horizontalLayout_8.addWidget(self.g_emplocations_label)
        self.g_emplocations_combobox = QtWidgets.QComboBox(self.frame_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_emplocations_combobox.sizePolicy().hasHeightForWidth())
        self.g_emplocations_combobox.setSizePolicy(sizePolicy)
        self.g_emplocations_combobox.setMinimumSize(QtCore.QSize(0, 0))
        self.g_emplocations_combobox.setMaximumSize(QtCore.QSize(********, 31))
        self.g_emplocations_combobox.setProperty("qp_user_is_active", "")
        self.g_emplocations_combobox.setObjectName("g_emplocations_combobox")
        self.g_emplocations_combobox.addItem("")
        self.g_emplocations_combobox.addItem("")
        self.horizontalLayout_8.addWidget(self.g_emplocations_combobox)
        self.verticalLayout_64.addWidget(self.frame_6)
        self.frame_10 = QtWidgets.QFrame(self.frame_2)
        self.frame_10.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_10.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_10.setObjectName("frame_10")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.frame_10)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.g_locationaccess_label = QtWidgets.QLabel(self.frame_10)
        self.g_locationaccess_label.setMinimumSize(QtCore.QSize(100, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_locationaccess_label.setFont(font)
        self.g_locationaccess_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_locationaccess_label.setObjectName("g_locationaccess_label")
        self.horizontalLayout_9.addWidget(self.g_locationaccess_label)
        self.g_locationaccess_list = QtWidgets.QListWidget(self.frame_10)
        self.g_locationaccess_list.setStyleSheet("")
        self.g_locationaccess_list.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_locationaccess_list.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_locationaccess_list.setAlternatingRowColors(False)
        self.g_locationaccess_list.setSelectionMode(QtWidgets.QAbstractItemView.MultiSelection)
        self.g_locationaccess_list.setModelColumn(0)
        self.g_locationaccess_list.setSelectionRectVisible(False)
        self.g_locationaccess_list.setProperty("qp_ent_accessible_locations", "")
        self.g_locationaccess_list.setObjectName("g_locationaccess_list")
        item = QtWidgets.QListWidgetItem()
        item.setFlags(QtCore.Qt.ItemIsSelectable|QtCore.Qt.ItemIsDragEnabled|QtCore.Qt.ItemIsEnabled)
        self.g_locationaccess_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        item.setFlags(QtCore.Qt.ItemIsSelectable|QtCore.Qt.ItemIsDragEnabled|QtCore.Qt.ItemIsEnabled)
        self.g_locationaccess_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        item.setFlags(QtCore.Qt.ItemIsSelectable|QtCore.Qt.ItemIsDragEnabled|QtCore.Qt.ItemIsEnabled)
        self.g_locationaccess_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        item.setFlags(QtCore.Qt.ItemIsSelectable|QtCore.Qt.ItemIsDragEnabled|QtCore.Qt.ItemIsEnabled)
        self.g_locationaccess_list.addItem(item)
        self.horizontalLayout_9.addWidget(self.g_locationaccess_list)
        self.verticalLayout_64.addWidget(self.frame_10)
        self.horizontalLayout_87.addWidget(self.frame_2)
        self.g_manageentity_tabwidget.addTab(self.g_employeesettings_widget, "")
        self.g_pets_widget = QtWidgets.QWidget()
        self.g_pets_widget.setObjectName("g_pets_widget")
        self.verticalLayout_20 = QtWidgets.QVBoxLayout(self.g_pets_widget)
        self.verticalLayout_20.setObjectName("verticalLayout_20")
        self.g_pets_not_available_frame = QtWidgets.QFrame(self.g_pets_widget)
        self.g_pets_not_available_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pets_not_available_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pets_not_available_frame.setObjectName("g_pets_not_available_frame")
        self.verticalLayout_111 = QtWidgets.QVBoxLayout(self.g_pets_not_available_frame)
        self.verticalLayout_111.setObjectName("verticalLayout_111")
        spacerItem56 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_111.addItem(spacerItem56)
        self.label_5 = QtWidgets.QLabel(self.g_pets_not_available_frame)
        self.label_5.setAlignment(QtCore.Qt.AlignCenter)
        self.label_5.setObjectName("label_5")
        self.verticalLayout_111.addWidget(self.label_5)
        spacerItem57 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_111.addItem(spacerItem57)
        self.verticalLayout_20.addWidget(self.g_pets_not_available_frame)
        self.g_pets_table = QtWidgets.QTableWidget(self.g_pets_widget)
        self.g_pets_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_pets_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_pets_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_pets_table.setAlternatingRowColors(True)
        self.g_pets_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_pets_table.setShowGrid(False)
        self.g_pets_table.setObjectName("g_pets_table")
        self.g_pets_table.setColumnCount(9)
        self.g_pets_table.setRowCount(15)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setVerticalHeaderItem(14, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_pets_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_pets_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_pets_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setHorizontalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_pets_table.setHorizontalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_pets_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_pets_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_pets_table.setItem(0, 8, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_pets_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_pets_table.setItem(1, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(False)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_pets_table.setItem(1, 8, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_pets_table.setItem(2, 1, item)
        self.g_pets_table.horizontalHeader().setDefaultSectionSize(200)
        self.g_pets_table.horizontalHeader().setStretchLastSection(True)
        self.g_pets_table.verticalHeader().setVisible(False)
        self.verticalLayout_20.addWidget(self.g_pets_table)
        self.g_pets_buttons_frame = QtWidgets.QFrame(self.g_pets_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_pets_buttons_frame.sizePolicy().hasHeightForWidth())
        self.g_pets_buttons_frame.setSizePolicy(sizePolicy)
        self.g_pets_buttons_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_pets_buttons_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.g_pets_buttons_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_pets_buttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pets_buttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pets_buttons_frame.setObjectName("g_pets_buttons_frame")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.g_pets_buttons_frame)
        self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        spacerItem58 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_13.addItem(spacerItem58)
        self.g_deletecustomerpet_button = QtWidgets.QPushButton(self.g_pets_buttons_frame)
        self.g_deletecustomerpet_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_deletecustomerpet_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_deletecustomerpet_button.setStyleSheet("")
        self.g_deletecustomerpet_button.setIcon(icon6)
        self.g_deletecustomerpet_button.setIconSize(QtCore.QSize(24, 24))
        self.g_deletecustomerpet_button.setObjectName("g_deletecustomerpet_button")
        self.horizontalLayout_13.addWidget(self.g_deletecustomerpet_button)
        self.g_manage_pet_button = QtWidgets.QPushButton(self.g_pets_buttons_frame)
        self.g_manage_pet_button.setEnabled(True)
        self.g_manage_pet_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manage_pet_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_manage_pet_button.setStyleSheet("")
        icon20 = QtGui.QIcon()
        icon20.addPixmap(QtGui.QPixmap(":/customers_screen_dialog/manage"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_manage_pet_button.setIcon(icon20)
        self.g_manage_pet_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manage_pet_button.setObjectName("g_manage_pet_button")
        self.horizontalLayout_13.addWidget(self.g_manage_pet_button)
        self.g_addcustomerpet_button = QtWidgets.QPushButton(self.g_pets_buttons_frame)
        self.g_addcustomerpet_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_addcustomerpet_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_addcustomerpet_button.setStyleSheet("")
        self.g_addcustomerpet_button.setIcon(icon14)
        self.g_addcustomerpet_button.setIconSize(QtCore.QSize(24, 24))
        self.g_addcustomerpet_button.setObjectName("g_addcustomerpet_button")
        self.horizontalLayout_13.addWidget(self.g_addcustomerpet_button)
        self.verticalLayout_20.addWidget(self.g_pets_buttons_frame)
        self.g_manageentity_tabwidget.addTab(self.g_pets_widget, "")
        self.g_breederinfo_widget = QtWidgets.QWidget()
        self.g_breederinfo_widget.setObjectName("g_breederinfo_widget")
        self.verticalLayout_51 = QtWidgets.QVBoxLayout(self.g_breederinfo_widget)
        self.verticalLayout_51.setObjectName("verticalLayout_51")
        self.g_breederslug_frame = QtWidgets.QFrame(self.g_breederinfo_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_breederslug_frame.sizePolicy().hasHeightForWidth())
        self.g_breederslug_frame.setSizePolicy(sizePolicy)
        self.g_breederslug_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_breederslug_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_breederslug_frame.setObjectName("g_breederslug_frame")
        self.horizontalLayout_19 = QtWidgets.QHBoxLayout(self.g_breederslug_frame)
        self.horizontalLayout_19.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_19.setObjectName("horizontalLayout_19")
        self.g_breederslug_label = QtWidgets.QLabel(self.g_breederslug_frame)
        self.g_breederslug_label.setMinimumSize(QtCore.QSize(80, 0))
        self.g_breederslug_label.setMaximumSize(QtCore.QSize(80, ********))
        self.g_breederslug_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_breederslug_label.setObjectName("g_breederslug_label")
        self.horizontalLayout_19.addWidget(self.g_breederslug_label)
        self.g_breederslug_lineedit = QtWidgets.QLineEdit(self.g_breederslug_frame)
        self.g_breederslug_lineedit.setMinimumSize(QtCore.QSize(400, 31))
        self.g_breederslug_lineedit.setMaximumSize(QtCore.QSize(158, 31))
        self.g_breederslug_lineedit.setProperty("qp_user_display_name", "")
        self.g_breederslug_lineedit.setProperty("qp_user_username", "")
        self.g_breederslug_lineedit.setObjectName("g_breederslug_lineedit")
        self.horizontalLayout_19.addWidget(self.g_breederslug_lineedit)
        spacerItem59 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_19.addItem(spacerItem59)
        self.verticalLayout_51.addWidget(self.g_breederslug_frame)
        self.g_usdastate_frame = QtWidgets.QFrame(self.g_breederinfo_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_usdastate_frame.sizePolicy().hasHeightForWidth())
        self.g_usdastate_frame.setSizePolicy(sizePolicy)
        self.g_usdastate_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_usdastate_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_usdastate_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_usdastate_frame.setObjectName("g_usdastate_frame")
        self.horizontalLayout_26 = QtWidgets.QHBoxLayout(self.g_usdastate_frame)
        self.horizontalLayout_26.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_26.setSpacing(6)
        self.horizontalLayout_26.setObjectName("horizontalLayout_26")
        self.g_usda_label = QtWidgets.QLabel(self.g_usdastate_frame)
        self.g_usda_label.setMinimumSize(QtCore.QSize(80, 31))
        self.g_usda_label.setMaximumSize(QtCore.QSize(80, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_usda_label.setFont(font)
        self.g_usda_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_usda_label.setObjectName("g_usda_label")
        self.horizontalLayout_26.addWidget(self.g_usda_label)
        self.g_usda_lineedit = QtWidgets.QLineEdit(self.g_usdastate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_usda_lineedit.sizePolicy().hasHeightForWidth())
        self.g_usda_lineedit.setSizePolicy(sizePolicy)
        self.g_usda_lineedit.setMinimumSize(QtCore.QSize(300, 31))
        self.g_usda_lineedit.setMaximumSize(QtCore.QSize(********, 31))
        font = QtGui.QFont()
        self.g_usda_lineedit.setFont(font)
        self.g_usda_lineedit.setMaxLength(10)
        self.g_usda_lineedit.setReadOnly(False)
        self.g_usda_lineedit.setPlaceholderText("")
        self.g_usda_lineedit.setProperty("qp_pbreeder_usda_num", "")
        self.g_usda_lineedit.setObjectName("g_usda_lineedit")
        self.horizontalLayout_26.addWidget(self.g_usda_lineedit)
        self.g_state_label1 = QtWidgets.QLabel(self.g_usdastate_frame)
        self.g_state_label1.setMinimumSize(QtCore.QSize(60, 31))
        self.g_state_label1.setMaximumSize(QtCore.QSize(60, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_state_label1.setFont(font)
        self.g_state_label1.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_state_label1.setObjectName("g_state_label1")
        self.horizontalLayout_26.addWidget(self.g_state_label1)
        self.g_state_lineedit = QtWidgets.QLineEdit(self.g_usdastate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_state_lineedit.sizePolicy().hasHeightForWidth())
        self.g_state_lineedit.setSizePolicy(sizePolicy)
        self.g_state_lineedit.setMinimumSize(QtCore.QSize(300, 31))
        self.g_state_lineedit.setMaximumSize(QtCore.QSize(********, 31))
        font = QtGui.QFont()
        self.g_state_lineedit.setFont(font)
        self.g_state_lineedit.setMaxLength(10)
        self.g_state_lineedit.setReadOnly(False)
        self.g_state_lineedit.setPlaceholderText("")
        self.g_state_lineedit.setProperty("qp_pbreeder_state_num", "")
        self.g_state_lineedit.setObjectName("g_state_lineedit")
        self.horizontalLayout_26.addWidget(self.g_state_lineedit)
        spacerItem60 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_26.addItem(spacerItem60)
        self.verticalLayout_51.addWidget(self.g_usdastate_frame)
        self.g_searchresults_hlayout_7 = QtWidgets.QHBoxLayout()
        self.g_searchresults_hlayout_7.setSpacing(4)
        self.g_searchresults_hlayout_7.setObjectName("g_searchresults_hlayout_7")
        spacerItem61 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_searchresults_hlayout_7.addItem(spacerItem61)
        self.g_pagenav_hlayout_7 = QtWidgets.QHBoxLayout()
        self.g_pagenav_hlayout_7.setSpacing(8)
        self.g_pagenav_hlayout_7.setObjectName("g_pagenav_hlayout_7")
        spacerItem62 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout_7.addItem(spacerItem62)
        self.g_previouspage_breeder_label = QtWidgets.QLabel(self.g_breederinfo_widget)
        self.g_previouspage_breeder_label.setEnabled(False)
        self.g_previouspage_breeder_label.setText("")
        self.g_previouspage_breeder_label.setPixmap(QtGui.QPixmap(":/pet_tracker/previous"))
        self.g_previouspage_breeder_label.setScaledContents(False)
        self.g_previouspage_breeder_label.setObjectName("g_previouspage_breeder_label")
        self.g_pagenav_hlayout_7.addWidget(self.g_previouspage_breeder_label)
        self.g_nextpage_breeder_label = QtWidgets.QLabel(self.g_breederinfo_widget)
        self.g_nextpage_breeder_label.setEnabled(True)
        self.g_nextpage_breeder_label.setText("")
        self.g_nextpage_breeder_label.setPixmap(QtGui.QPixmap(":/pet_tracker/next"))
        self.g_nextpage_breeder_label.setObjectName("g_nextpage_breeder_label")
        self.g_pagenav_hlayout_7.addWidget(self.g_nextpage_breeder_label)
        self.g_currentpage_breeder_lineedit = QtWidgets.QLineEdit(self.g_breederinfo_widget)
        self.g_currentpage_breeder_lineedit.setEnabled(True)
        self.g_currentpage_breeder_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_currentpage_breeder_lineedit.setMaximumSize(QtCore.QSize(30, ********))
        self.g_currentpage_breeder_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_breeder_lineedit.setObjectName("g_currentpage_breeder_lineedit")
        self.g_pagenav_hlayout_7.addWidget(self.g_currentpage_breeder_lineedit)
        self.g_slash_label_6 = QtWidgets.QLabel(self.g_breederinfo_widget)
        self.g_slash_label_6.setObjectName("g_slash_label_6")
        self.g_pagenav_hlayout_7.addWidget(self.g_slash_label_6)
        self.g_totalpages_breeder_label = QtWidgets.QLabel(self.g_breederinfo_widget)
        self.g_totalpages_breeder_label.setObjectName("g_totalpages_breeder_label")
        self.g_pagenav_hlayout_7.addWidget(self.g_totalpages_breeder_label)
        self.g_searchresults_hlayout_7.addLayout(self.g_pagenav_hlayout_7)
        self.verticalLayout_51.addLayout(self.g_searchresults_hlayout_7)
        self.g_paperwork_breeder_table = QtWidgets.QTableWidget(self.g_breederinfo_widget)
        self.g_paperwork_breeder_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_paperwork_breeder_table.setDragEnabled(False)
        self.g_paperwork_breeder_table.setDragDropOverwriteMode(False)
        self.g_paperwork_breeder_table.setDragDropMode(QtWidgets.QAbstractItemView.NoDragDrop)
        self.g_paperwork_breeder_table.setAlternatingRowColors(True)
        self.g_paperwork_breeder_table.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.g_paperwork_breeder_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_paperwork_breeder_table.setShowGrid(False)
        self.g_paperwork_breeder_table.setObjectName("g_paperwork_breeder_table")
        self.g_paperwork_breeder_table.setColumnCount(4)
        self.g_paperwork_breeder_table.setRowCount(6)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_paperwork_breeder_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_paperwork_breeder_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_paperwork_breeder_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_paperwork_breeder_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setItem(1, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_breeder_table.setItem(3, 0, item)
        self.g_paperwork_breeder_table.horizontalHeader().setDefaultSectionSize(200)
        self.g_paperwork_breeder_table.horizontalHeader().setStretchLastSection(True)
        self.g_paperwork_breeder_table.verticalHeader().setVisible(False)
        self.verticalLayout_51.addWidget(self.g_paperwork_breeder_table)
        self.g_managepaperworkcontrols_frame_2 = QtWidgets.QFrame(self.g_breederinfo_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_managepaperworkcontrols_frame_2.sizePolicy().hasHeightForWidth())
        self.g_managepaperworkcontrols_frame_2.setSizePolicy(sizePolicy)
        self.g_managepaperworkcontrols_frame_2.setMinimumSize(QtCore.QSize(247, 40))
        self.g_managepaperworkcontrols_frame_2.setMaximumSize(QtCore.QSize(********, 40))
        self.g_managepaperworkcontrols_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_managepaperworkcontrols_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_managepaperworkcontrols_frame_2.setObjectName("g_managepaperworkcontrols_frame_2")
        self.horizontalLayout_75 = QtWidgets.QHBoxLayout(self.g_managepaperworkcontrols_frame_2)
        self.horizontalLayout_75.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_75.setSpacing(6)
        self.horizontalLayout_75.setObjectName("horizontalLayout_75")
        self.g_totalresults_breeder_label = QtWidgets.QLabel(self.g_managepaperworkcontrols_frame_2)
        self.g_totalresults_breeder_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_totalresults_breeder_label.setFont(font)
        self.g_totalresults_breeder_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_totalresults_breeder_label.setObjectName("g_totalresults_breeder_label")
        self.horizontalLayout_75.addWidget(self.g_totalresults_breeder_label)
        spacerItem63 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_75.addItem(spacerItem63)
        self.g_removepaperwork_breeder_button = QtWidgets.QPushButton(self.g_managepaperworkcontrols_frame_2)
        self.g_removepaperwork_breeder_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_removepaperwork_breeder_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_removepaperwork_breeder_button.setStyleSheet("")
        self.g_removepaperwork_breeder_button.setIcon(icon6)
        self.g_removepaperwork_breeder_button.setIconSize(QtCore.QSize(24, 24))
        self.g_removepaperwork_breeder_button.setObjectName("g_removepaperwork_breeder_button")
        self.horizontalLayout_75.addWidget(self.g_removepaperwork_breeder_button)
        self.g_managepaperwork_breeder_button = QtWidgets.QPushButton(self.g_managepaperworkcontrols_frame_2)
        self.g_managepaperwork_breeder_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_managepaperwork_breeder_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_managepaperwork_breeder_button.setStyleSheet("")
        self.g_managepaperwork_breeder_button.setIcon(icon5)
        self.g_managepaperwork_breeder_button.setIconSize(QtCore.QSize(24, 24))
        self.g_managepaperwork_breeder_button.setObjectName("g_managepaperwork_breeder_button")
        self.horizontalLayout_75.addWidget(self.g_managepaperwork_breeder_button)
        self.g_print_paperwork_breeder_button = QtWidgets.QPushButton(self.g_managepaperworkcontrols_frame_2)
        self.g_print_paperwork_breeder_button.setEnabled(True)
        self.g_print_paperwork_breeder_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_print_paperwork_breeder_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_print_paperwork_breeder_button.setStyleSheet("")
        icon21 = QtGui.QIcon()
        icon21.addPixmap(QtGui.QPixmap(":/entity_dialog/printer"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_print_paperwork_breeder_button.setIcon(icon21)
        self.g_print_paperwork_breeder_button.setIconSize(QtCore.QSize(24, 24))
        self.g_print_paperwork_breeder_button.setObjectName("g_print_paperwork_breeder_button")
        self.horizontalLayout_75.addWidget(self.g_print_paperwork_breeder_button)
        self.g_addpaperwork_breeder_button = QtWidgets.QPushButton(self.g_managepaperworkcontrols_frame_2)
        self.g_addpaperwork_breeder_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_addpaperwork_breeder_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_addpaperwork_breeder_button.setStyleSheet("")
        self.g_addpaperwork_breeder_button.setIcon(icon4)
        self.g_addpaperwork_breeder_button.setIconSize(QtCore.QSize(24, 24))
        self.g_addpaperwork_breeder_button.setObjectName("g_addpaperwork_breeder_button")
        self.horizontalLayout_75.addWidget(self.g_addpaperwork_breeder_button)
        self.verticalLayout_51.addWidget(self.g_managepaperworkcontrols_frame_2)
        self.g_manageentity_tabwidget.addTab(self.g_breederinfo_widget, "")
        self.g_brokerinfo_widget = QtWidgets.QWidget()
        self.g_brokerinfo_widget.setObjectName("g_brokerinfo_widget")
        self.verticalLayout_35 = QtWidgets.QVBoxLayout(self.g_brokerinfo_widget)
        self.verticalLayout_35.setObjectName("verticalLayout_35")
        self.g_broker_usdastate_frame = QtWidgets.QFrame(self.g_brokerinfo_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_broker_usdastate_frame.sizePolicy().hasHeightForWidth())
        self.g_broker_usdastate_frame.setSizePolicy(sizePolicy)
        self.g_broker_usdastate_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_broker_usdastate_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_broker_usdastate_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_broker_usdastate_frame.setObjectName("g_broker_usdastate_frame")
        self.horizontalLayout_261 = QtWidgets.QHBoxLayout(self.g_broker_usdastate_frame)
        self.horizontalLayout_261.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_261.setSpacing(6)
        self.horizontalLayout_261.setObjectName("horizontalLayout_261")
        self.g_broker_usda_label = QtWidgets.QLabel(self.g_broker_usdastate_frame)
        self.g_broker_usda_label.setMinimumSize(QtCore.QSize(80, 31))
        self.g_broker_usda_label.setMaximumSize(QtCore.QSize(80, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_broker_usda_label.setFont(font)
        self.g_broker_usda_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_broker_usda_label.setObjectName("g_broker_usda_label")
        self.horizontalLayout_261.addWidget(self.g_broker_usda_label)
        self.g_broker_usda_lineedit = QtWidgets.QLineEdit(self.g_broker_usdastate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_broker_usda_lineedit.sizePolicy().hasHeightForWidth())
        self.g_broker_usda_lineedit.setSizePolicy(sizePolicy)
        self.g_broker_usda_lineedit.setMinimumSize(QtCore.QSize(300, 31))
        self.g_broker_usda_lineedit.setMaximumSize(QtCore.QSize(********, 31))
        font = QtGui.QFont()
        self.g_broker_usda_lineedit.setFont(font)
        self.g_broker_usda_lineedit.setMaxLength(10)
        self.g_broker_usda_lineedit.setReadOnly(False)
        self.g_broker_usda_lineedit.setPlaceholderText("")
        self.g_broker_usda_lineedit.setProperty("qp_pbroker_usda_num", "")
        self.g_broker_usda_lineedit.setObjectName("g_broker_usda_lineedit")
        self.horizontalLayout_261.addWidget(self.g_broker_usda_lineedit)
        self.g_broker_state_label = QtWidgets.QLabel(self.g_broker_usdastate_frame)
        self.g_broker_state_label.setMinimumSize(QtCore.QSize(60, 31))
        self.g_broker_state_label.setMaximumSize(QtCore.QSize(60, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_broker_state_label.setFont(font)
        self.g_broker_state_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_broker_state_label.setObjectName("g_broker_state_label")
        self.horizontalLayout_261.addWidget(self.g_broker_state_label)
        self.g_broker_state_lineedit = QtWidgets.QLineEdit(self.g_broker_usdastate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_broker_state_lineedit.sizePolicy().hasHeightForWidth())
        self.g_broker_state_lineedit.setSizePolicy(sizePolicy)
        self.g_broker_state_lineedit.setMinimumSize(QtCore.QSize(300, 31))
        self.g_broker_state_lineedit.setMaximumSize(QtCore.QSize(********, 31))
        font = QtGui.QFont()
        self.g_broker_state_lineedit.setFont(font)
        self.g_broker_state_lineedit.setMaxLength(10)
        self.g_broker_state_lineedit.setReadOnly(False)
        self.g_broker_state_lineedit.setPlaceholderText("")
        self.g_broker_state_lineedit.setProperty("qp_pbroker_state_num", "")
        self.g_broker_state_lineedit.setObjectName("g_broker_state_lineedit")
        self.horizontalLayout_261.addWidget(self.g_broker_state_lineedit)
        spacerItem64 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_261.addItem(spacerItem64)
        self.verticalLayout_35.addWidget(self.g_broker_usdastate_frame)
        self.g_searchresults_hlayout_8 = QtWidgets.QHBoxLayout()
        self.g_searchresults_hlayout_8.setSpacing(4)
        self.g_searchresults_hlayout_8.setObjectName("g_searchresults_hlayout_8")
        self.g_totalresults_broker_label = QtWidgets.QLabel(self.g_brokerinfo_widget)
        self.g_totalresults_broker_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_totalresults_broker_label.setFont(font)
        self.g_totalresults_broker_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_totalresults_broker_label.setObjectName("g_totalresults_broker_label")
        self.g_searchresults_hlayout_8.addWidget(self.g_totalresults_broker_label)
        spacerItem65 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_searchresults_hlayout_8.addItem(spacerItem65)
        self.g_pagenav_hlayout_8 = QtWidgets.QHBoxLayout()
        self.g_pagenav_hlayout_8.setSpacing(8)
        self.g_pagenav_hlayout_8.setObjectName("g_pagenav_hlayout_8")
        spacerItem66 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout_8.addItem(spacerItem66)
        self.g_previouspage_broker_label = QtWidgets.QLabel(self.g_brokerinfo_widget)
        self.g_previouspage_broker_label.setEnabled(False)
        self.g_previouspage_broker_label.setText("")
        self.g_previouspage_broker_label.setPixmap(QtGui.QPixmap(":/pet_tracker/previous"))
        self.g_previouspage_broker_label.setScaledContents(False)
        self.g_previouspage_broker_label.setObjectName("g_previouspage_broker_label")
        self.g_pagenav_hlayout_8.addWidget(self.g_previouspage_broker_label)
        self.g_nextpage_broker_label = QtWidgets.QLabel(self.g_brokerinfo_widget)
        self.g_nextpage_broker_label.setEnabled(True)
        self.g_nextpage_broker_label.setText("")
        self.g_nextpage_broker_label.setPixmap(QtGui.QPixmap(":/pet_tracker/next"))
        self.g_nextpage_broker_label.setObjectName("g_nextpage_broker_label")
        self.g_pagenav_hlayout_8.addWidget(self.g_nextpage_broker_label)
        self.g_currentpage_broker_lineedit = QtWidgets.QLineEdit(self.g_brokerinfo_widget)
        self.g_currentpage_broker_lineedit.setEnabled(True)
        self.g_currentpage_broker_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_currentpage_broker_lineedit.setMaximumSize(QtCore.QSize(30, ********))
        self.g_currentpage_broker_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_broker_lineedit.setObjectName("g_currentpage_broker_lineedit")
        self.g_pagenav_hlayout_8.addWidget(self.g_currentpage_broker_lineedit)
        self.g_slash_label_7 = QtWidgets.QLabel(self.g_brokerinfo_widget)
        self.g_slash_label_7.setObjectName("g_slash_label_7")
        self.g_pagenav_hlayout_8.addWidget(self.g_slash_label_7)
        self.g_totalpages_broker_label = QtWidgets.QLabel(self.g_brokerinfo_widget)
        self.g_totalpages_broker_label.setObjectName("g_totalpages_broker_label")
        self.g_pagenav_hlayout_8.addWidget(self.g_totalpages_broker_label)
        self.g_searchresults_hlayout_8.addLayout(self.g_pagenav_hlayout_8)
        self.verticalLayout_35.addLayout(self.g_searchresults_hlayout_8)
        self.g_paperwork_broker_table = QtWidgets.QTableWidget(self.g_brokerinfo_widget)
        self.g_paperwork_broker_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_paperwork_broker_table.setDragEnabled(False)
        self.g_paperwork_broker_table.setDragDropOverwriteMode(False)
        self.g_paperwork_broker_table.setDragDropMode(QtWidgets.QAbstractItemView.NoDragDrop)
        self.g_paperwork_broker_table.setAlternatingRowColors(True)
        self.g_paperwork_broker_table.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.g_paperwork_broker_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_paperwork_broker_table.setShowGrid(False)
        self.g_paperwork_broker_table.setObjectName("g_paperwork_broker_table")
        self.g_paperwork_broker_table.setColumnCount(4)
        self.g_paperwork_broker_table.setRowCount(6)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_paperwork_broker_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_paperwork_broker_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_paperwork_broker_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_paperwork_broker_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setItem(1, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_broker_table.setItem(3, 0, item)
        self.g_paperwork_broker_table.horizontalHeader().setDefaultSectionSize(200)
        self.g_paperwork_broker_table.horizontalHeader().setStretchLastSection(True)
        self.g_paperwork_broker_table.verticalHeader().setVisible(False)
        self.verticalLayout_35.addWidget(self.g_paperwork_broker_table)
        self.g_managepaperworkcontrols_frame_3 = QtWidgets.QFrame(self.g_brokerinfo_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_managepaperworkcontrols_frame_3.sizePolicy().hasHeightForWidth())
        self.g_managepaperworkcontrols_frame_3.setSizePolicy(sizePolicy)
        self.g_managepaperworkcontrols_frame_3.setMinimumSize(QtCore.QSize(247, 40))
        self.g_managepaperworkcontrols_frame_3.setMaximumSize(QtCore.QSize(********, 40))
        self.g_managepaperworkcontrols_frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_managepaperworkcontrols_frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_managepaperworkcontrols_frame_3.setObjectName("g_managepaperworkcontrols_frame_3")
        self.horizontalLayout_76 = QtWidgets.QHBoxLayout(self.g_managepaperworkcontrols_frame_3)
        self.horizontalLayout_76.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_76.setSpacing(6)
        self.horizontalLayout_76.setObjectName("horizontalLayout_76")
        spacerItem67 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_76.addItem(spacerItem67)
        self.g_removepaperwork_broker_button = QtWidgets.QPushButton(self.g_managepaperworkcontrols_frame_3)
        self.g_removepaperwork_broker_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_removepaperwork_broker_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_removepaperwork_broker_button.setStyleSheet("")
        self.g_removepaperwork_broker_button.setIcon(icon6)
        self.g_removepaperwork_broker_button.setIconSize(QtCore.QSize(24, 24))
        self.g_removepaperwork_broker_button.setObjectName("g_removepaperwork_broker_button")
        self.horizontalLayout_76.addWidget(self.g_removepaperwork_broker_button)
        self.g_managepaperwork_broker_button = QtWidgets.QPushButton(self.g_managepaperworkcontrols_frame_3)
        self.g_managepaperwork_broker_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_managepaperwork_broker_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_managepaperwork_broker_button.setStyleSheet("")
        self.g_managepaperwork_broker_button.setIcon(icon5)
        self.g_managepaperwork_broker_button.setIconSize(QtCore.QSize(24, 24))
        self.g_managepaperwork_broker_button.setObjectName("g_managepaperwork_broker_button")
        self.horizontalLayout_76.addWidget(self.g_managepaperwork_broker_button)
        self.g_print_paperwork_broker_button = QtWidgets.QPushButton(self.g_managepaperworkcontrols_frame_3)
        self.g_print_paperwork_broker_button.setEnabled(True)
        self.g_print_paperwork_broker_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_print_paperwork_broker_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_print_paperwork_broker_button.setStyleSheet("")
        self.g_print_paperwork_broker_button.setIcon(icon21)
        self.g_print_paperwork_broker_button.setIconSize(QtCore.QSize(24, 24))
        self.g_print_paperwork_broker_button.setObjectName("g_print_paperwork_broker_button")
        self.horizontalLayout_76.addWidget(self.g_print_paperwork_broker_button)
        self.g_addpaperwork_broker_button = QtWidgets.QPushButton(self.g_managepaperworkcontrols_frame_3)
        self.g_addpaperwork_broker_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_addpaperwork_broker_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_addpaperwork_broker_button.setStyleSheet("")
        self.g_addpaperwork_broker_button.setIcon(icon4)
        self.g_addpaperwork_broker_button.setIconSize(QtCore.QSize(24, 24))
        self.g_addpaperwork_broker_button.setObjectName("g_addpaperwork_broker_button")
        self.horizontalLayout_76.addWidget(self.g_addpaperwork_broker_button)
        self.verticalLayout_35.addWidget(self.g_managepaperworkcontrols_frame_3)
        self.g_manageentity_tabwidget.addTab(self.g_brokerinfo_widget, "")
        self.g_vetinfo_widget = QtWidgets.QWidget()
        self.g_vetinfo_widget.setObjectName("g_vetinfo_widget")
        self.verticalLayout_21 = QtWidgets.QVBoxLayout(self.g_vetinfo_widget)
        self.verticalLayout_21.setObjectName("verticalLayout_21")
        self.g_vet_licence_frame = QtWidgets.QFrame(self.g_vetinfo_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_vet_licence_frame.sizePolicy().hasHeightForWidth())
        self.g_vet_licence_frame.setSizePolicy(sizePolicy)
        self.g_vet_licence_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_vet_licence_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_vet_licence_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_vet_licence_frame.setObjectName("g_vet_licence_frame")
        self.horizontalLayout_262 = QtWidgets.QHBoxLayout(self.g_vet_licence_frame)
        self.horizontalLayout_262.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_262.setObjectName("horizontalLayout_262")
        self.g_vet_licence_label = QtWidgets.QLabel(self.g_vet_licence_frame)
        self.g_vet_licence_label.setMinimumSize(QtCore.QSize(80, 31))
        self.g_vet_licence_label.setMaximumSize(QtCore.QSize(80, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_vet_licence_label.setFont(font)
        self.g_vet_licence_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_vet_licence_label.setObjectName("g_vet_licence_label")
        self.horizontalLayout_262.addWidget(self.g_vet_licence_label)
        self.g_vet_licence_lineedit = QtWidgets.QLineEdit(self.g_vet_licence_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_vet_licence_lineedit.sizePolicy().hasHeightForWidth())
        self.g_vet_licence_lineedit.setSizePolicy(sizePolicy)
        self.g_vet_licence_lineedit.setMinimumSize(QtCore.QSize(300, 31))
        self.g_vet_licence_lineedit.setMaximumSize(QtCore.QSize(********, 31))
        font = QtGui.QFont()
        self.g_vet_licence_lineedit.setFont(font)
        self.g_vet_licence_lineedit.setMaxLength(20)
        self.g_vet_licence_lineedit.setReadOnly(False)
        self.g_vet_licence_lineedit.setPlaceholderText("")
        self.g_vet_licence_lineedit.setProperty("qp_pvet_licence", "")
        self.g_vet_licence_lineedit.setObjectName("g_vet_licence_lineedit")
        self.horizontalLayout_262.addWidget(self.g_vet_licence_lineedit)
        spacerItem68 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_262.addItem(spacerItem68)
        self.verticalLayout_21.addWidget(self.g_vet_licence_frame)
        spacerItem69 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_21.addItem(spacerItem69)
        self.g_manageentity_tabwidget.addTab(self.g_vetinfo_widget, "")
        self.g_transporterinfo_widget = QtWidgets.QWidget()
        self.g_transporterinfo_widget.setObjectName("g_transporterinfo_widget")
        self.verticalLayout_36 = QtWidgets.QVBoxLayout(self.g_transporterinfo_widget)
        self.verticalLayout_36.setObjectName("verticalLayout_36")
        self.g_transporter_usdastate_frame = QtWidgets.QFrame(self.g_transporterinfo_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_transporter_usdastate_frame.sizePolicy().hasHeightForWidth())
        self.g_transporter_usdastate_frame.setSizePolicy(sizePolicy)
        self.g_transporter_usdastate_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_transporter_usdastate_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_transporter_usdastate_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_transporter_usdastate_frame.setObjectName("g_transporter_usdastate_frame")
        self.horizontalLayout_263 = QtWidgets.QHBoxLayout(self.g_transporter_usdastate_frame)
        self.horizontalLayout_263.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_263.setSpacing(6)
        self.horizontalLayout_263.setObjectName("horizontalLayout_263")
        self.g_transporter_usda_label = QtWidgets.QLabel(self.g_transporter_usdastate_frame)
        self.g_transporter_usda_label.setMinimumSize(QtCore.QSize(80, 31))
        self.g_transporter_usda_label.setMaximumSize(QtCore.QSize(80, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_transporter_usda_label.setFont(font)
        self.g_transporter_usda_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_transporter_usda_label.setObjectName("g_transporter_usda_label")
        self.horizontalLayout_263.addWidget(self.g_transporter_usda_label)
        self.g_transporter_usda_lineedit = QtWidgets.QLineEdit(self.g_transporter_usdastate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_transporter_usda_lineedit.sizePolicy().hasHeightForWidth())
        self.g_transporter_usda_lineedit.setSizePolicy(sizePolicy)
        self.g_transporter_usda_lineedit.setMinimumSize(QtCore.QSize(300, 31))
        self.g_transporter_usda_lineedit.setMaximumSize(QtCore.QSize(********, 31))
        font = QtGui.QFont()
        self.g_transporter_usda_lineedit.setFont(font)
        self.g_transporter_usda_lineedit.setMaxLength(10)
        self.g_transporter_usda_lineedit.setReadOnly(False)
        self.g_transporter_usda_lineedit.setProperty("qp_ptrnsp_usda_num", "")
        self.g_transporter_usda_lineedit.setObjectName("g_transporter_usda_lineedit")
        self.horizontalLayout_263.addWidget(self.g_transporter_usda_lineedit)
        self.g_transporter_state_label = QtWidgets.QLabel(self.g_transporter_usdastate_frame)
        self.g_transporter_state_label.setMinimumSize(QtCore.QSize(80, 31))
        self.g_transporter_state_label.setMaximumSize(QtCore.QSize(80, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_transporter_state_label.setFont(font)
        self.g_transporter_state_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_transporter_state_label.setObjectName("g_transporter_state_label")
        self.horizontalLayout_263.addWidget(self.g_transporter_state_label)
        self.g_transporter_state_lineedit = QtWidgets.QLineEdit(self.g_transporter_usdastate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_transporter_state_lineedit.sizePolicy().hasHeightForWidth())
        self.g_transporter_state_lineedit.setSizePolicy(sizePolicy)
        self.g_transporter_state_lineedit.setMinimumSize(QtCore.QSize(300, 31))
        self.g_transporter_state_lineedit.setMaximumSize(QtCore.QSize(********, 31))
        font = QtGui.QFont()
        self.g_transporter_state_lineedit.setFont(font)
        self.g_transporter_state_lineedit.setMaxLength(10)
        self.g_transporter_state_lineedit.setReadOnly(False)
        self.g_transporter_state_lineedit.setProperty("qp_ptrnsp_state_num", "")
        self.g_transporter_state_lineedit.setObjectName("g_transporter_state_lineedit")
        self.horizontalLayout_263.addWidget(self.g_transporter_state_lineedit)
        spacerItem70 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_263.addItem(spacerItem70)
        self.verticalLayout_36.addWidget(self.g_transporter_usdastate_frame)
        self.g_searchresults_hlayout_9 = QtWidgets.QHBoxLayout()
        self.g_searchresults_hlayout_9.setSpacing(4)
        self.g_searchresults_hlayout_9.setObjectName("g_searchresults_hlayout_9")
        self.g_totalresults_trans_label = QtWidgets.QLabel(self.g_transporterinfo_widget)
        self.g_totalresults_trans_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_totalresults_trans_label.setFont(font)
        self.g_totalresults_trans_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_totalresults_trans_label.setObjectName("g_totalresults_trans_label")
        self.g_searchresults_hlayout_9.addWidget(self.g_totalresults_trans_label)
        spacerItem71 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_searchresults_hlayout_9.addItem(spacerItem71)
        self.g_pagenav_hlayout_9 = QtWidgets.QHBoxLayout()
        self.g_pagenav_hlayout_9.setSpacing(8)
        self.g_pagenav_hlayout_9.setObjectName("g_pagenav_hlayout_9")
        spacerItem72 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout_9.addItem(spacerItem72)
        self.g_previouspage_trans_label = QtWidgets.QLabel(self.g_transporterinfo_widget)
        self.g_previouspage_trans_label.setEnabled(False)
        self.g_previouspage_trans_label.setText("")
        self.g_previouspage_trans_label.setPixmap(QtGui.QPixmap(":/pet_tracker/previous"))
        self.g_previouspage_trans_label.setScaledContents(False)
        self.g_previouspage_trans_label.setObjectName("g_previouspage_trans_label")
        self.g_pagenav_hlayout_9.addWidget(self.g_previouspage_trans_label)
        self.g_nextpage_trans_label = QtWidgets.QLabel(self.g_transporterinfo_widget)
        self.g_nextpage_trans_label.setEnabled(True)
        self.g_nextpage_trans_label.setText("")
        self.g_nextpage_trans_label.setPixmap(QtGui.QPixmap(":/pet_tracker/next"))
        self.g_nextpage_trans_label.setObjectName("g_nextpage_trans_label")
        self.g_pagenav_hlayout_9.addWidget(self.g_nextpage_trans_label)
        self.g_currentpage_trans_lineedit = QtWidgets.QLineEdit(self.g_transporterinfo_widget)
        self.g_currentpage_trans_lineedit.setEnabled(True)
        self.g_currentpage_trans_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_currentpage_trans_lineedit.setMaximumSize(QtCore.QSize(30, ********))
        self.g_currentpage_trans_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_trans_lineedit.setObjectName("g_currentpage_trans_lineedit")
        self.g_pagenav_hlayout_9.addWidget(self.g_currentpage_trans_lineedit)
        self.g_slash_label_8 = QtWidgets.QLabel(self.g_transporterinfo_widget)
        self.g_slash_label_8.setObjectName("g_slash_label_8")
        self.g_pagenav_hlayout_9.addWidget(self.g_slash_label_8)
        self.g_totalpages_trans_label = QtWidgets.QLabel(self.g_transporterinfo_widget)
        self.g_totalpages_trans_label.setObjectName("g_totalpages_trans_label")
        self.g_pagenav_hlayout_9.addWidget(self.g_totalpages_trans_label)
        self.g_searchresults_hlayout_9.addLayout(self.g_pagenav_hlayout_9)
        self.verticalLayout_36.addLayout(self.g_searchresults_hlayout_9)
        self.g_paperwork_trans_table = QtWidgets.QTableWidget(self.g_transporterinfo_widget)
        self.g_paperwork_trans_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_paperwork_trans_table.setDragEnabled(False)
        self.g_paperwork_trans_table.setDragDropOverwriteMode(False)
        self.g_paperwork_trans_table.setDragDropMode(QtWidgets.QAbstractItemView.NoDragDrop)
        self.g_paperwork_trans_table.setAlternatingRowColors(True)
        self.g_paperwork_trans_table.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.g_paperwork_trans_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_paperwork_trans_table.setShowGrid(False)
        self.g_paperwork_trans_table.setObjectName("g_paperwork_trans_table")
        self.g_paperwork_trans_table.setColumnCount(4)
        self.g_paperwork_trans_table.setRowCount(6)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_paperwork_trans_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_paperwork_trans_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_paperwork_trans_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_paperwork_trans_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setItem(1, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setItem(1, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_paperwork_trans_table.setItem(3, 0, item)
        self.g_paperwork_trans_table.horizontalHeader().setDefaultSectionSize(200)
        self.g_paperwork_trans_table.horizontalHeader().setStretchLastSection(True)
        self.g_paperwork_trans_table.verticalHeader().setVisible(False)
        self.verticalLayout_36.addWidget(self.g_paperwork_trans_table)
        self.g_managepaperworkcontrols_frame_4 = QtWidgets.QFrame(self.g_transporterinfo_widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_managepaperworkcontrols_frame_4.sizePolicy().hasHeightForWidth())
        self.g_managepaperworkcontrols_frame_4.setSizePolicy(sizePolicy)
        self.g_managepaperworkcontrols_frame_4.setMinimumSize(QtCore.QSize(247, 40))
        self.g_managepaperworkcontrols_frame_4.setMaximumSize(QtCore.QSize(********, 40))
        self.g_managepaperworkcontrols_frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_managepaperworkcontrols_frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_managepaperworkcontrols_frame_4.setObjectName("g_managepaperworkcontrols_frame_4")
        self.horizontalLayout_77 = QtWidgets.QHBoxLayout(self.g_managepaperworkcontrols_frame_4)
        self.horizontalLayout_77.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_77.setSpacing(6)
        self.horizontalLayout_77.setObjectName("horizontalLayout_77")
        spacerItem73 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_77.addItem(spacerItem73)
        self.g_removepaperwork_trans_button = QtWidgets.QPushButton(self.g_managepaperworkcontrols_frame_4)
        self.g_removepaperwork_trans_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_removepaperwork_trans_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_removepaperwork_trans_button.setStyleSheet("")
        self.g_removepaperwork_trans_button.setIcon(icon6)
        self.g_removepaperwork_trans_button.setIconSize(QtCore.QSize(24, 24))
        self.g_removepaperwork_trans_button.setObjectName("g_removepaperwork_trans_button")
        self.horizontalLayout_77.addWidget(self.g_removepaperwork_trans_button)
        self.g_managepaperwork_trans_button = QtWidgets.QPushButton(self.g_managepaperworkcontrols_frame_4)
        self.g_managepaperwork_trans_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_managepaperwork_trans_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_managepaperwork_trans_button.setStyleSheet("")
        self.g_managepaperwork_trans_button.setIcon(icon5)
        self.g_managepaperwork_trans_button.setIconSize(QtCore.QSize(24, 24))
        self.g_managepaperwork_trans_button.setObjectName("g_managepaperwork_trans_button")
        self.horizontalLayout_77.addWidget(self.g_managepaperwork_trans_button)
        self.g_print_paperwork_trans_button = QtWidgets.QPushButton(self.g_managepaperworkcontrols_frame_4)
        self.g_print_paperwork_trans_button.setEnabled(True)
        self.g_print_paperwork_trans_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_print_paperwork_trans_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_print_paperwork_trans_button.setStyleSheet("")
        self.g_print_paperwork_trans_button.setIcon(icon21)
        self.g_print_paperwork_trans_button.setIconSize(QtCore.QSize(24, 24))
        self.g_print_paperwork_trans_button.setObjectName("g_print_paperwork_trans_button")
        self.horizontalLayout_77.addWidget(self.g_print_paperwork_trans_button)
        self.g_addpaperwork_trans_button = QtWidgets.QPushButton(self.g_managepaperworkcontrols_frame_4)
        self.g_addpaperwork_trans_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_addpaperwork_trans_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_addpaperwork_trans_button.setStyleSheet("")
        self.g_addpaperwork_trans_button.setIcon(icon4)
        self.g_addpaperwork_trans_button.setIconSize(QtCore.QSize(24, 24))
        self.g_addpaperwork_trans_button.setObjectName("g_addpaperwork_trans_button")
        self.horizontalLayout_77.addWidget(self.g_addpaperwork_trans_button)
        self.verticalLayout_36.addWidget(self.g_managepaperworkcontrols_frame_4)
        self.g_manageentity_tabwidget.addTab(self.g_transporterinfo_widget, "")
        self.g_litters_widget = QtWidgets.QWidget()
        self.g_litters_widget.setObjectName("g_litters_widget")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_litters_widget)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_litters_table = QtWidgets.QTableWidget(self.g_litters_widget)
        self.g_litters_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_litters_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_litters_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_litters_table.setAlternatingRowColors(True)
        self.g_litters_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_litters_table.setShowGrid(False)
        self.g_litters_table.setObjectName("g_litters_table")
        self.g_litters_table.setColumnCount(5)
        self.g_litters_table.setRowCount(17)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(14, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(15, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setVerticalHeaderItem(16, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_litters_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_litters_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_litters_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        self.g_litters_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(False)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_litters_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignCenter)
        font = QtGui.QFont()
        font.setUnderline(False)
        item.setFont(font)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.NoBrush)
        item.setForeground(brush)
        self.g_litters_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_litters_table.setItem(3, 1, item)
        self.g_litters_table.horizontalHeader().setDefaultSectionSize(199)
        self.g_litters_table.horizontalHeader().setStretchLastSection(True)
        self.g_litters_table.verticalHeader().setVisible(False)
        self.verticalLayout_3.addWidget(self.g_litters_table)
        self.g_managelitter_frame = QtWidgets.QFrame(self.g_litters_widget)
        self.g_managelitter_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_managelitter_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_managelitter_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_managelitter_frame.setObjectName("g_managelitter_frame")
        self.horizontalLayout_24 = QtWidgets.QHBoxLayout(self.g_managelitter_frame)
        self.horizontalLayout_24.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_24.setSpacing(6)
        self.horizontalLayout_24.setObjectName("horizontalLayout_24")
        spacerItem74 = QtWidgets.QSpacerItem(653, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_24.addItem(spacerItem74)
        self.g_managelitter_button = QtWidgets.QPushButton(self.g_managelitter_frame)
        self.g_managelitter_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_managelitter_button.sizePolicy().hasHeightForWidth())
        self.g_managelitter_button.setSizePolicy(sizePolicy)
        self.g_managelitter_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_managelitter_button.setMaximumSize(QtCore.QSize(120, 40))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_managelitter_button.setFont(font)
        self.g_managelitter_button.setIcon(icon13)
        self.g_managelitter_button.setIconSize(QtCore.QSize(24, 24))
        self.g_managelitter_button.setObjectName("g_managelitter_button")
        self.horizontalLayout_24.addWidget(self.g_managelitter_button)
        self.verticalLayout_3.addWidget(self.g_managelitter_frame)
        self.g_manageentity_tabwidget.addTab(self.g_litters_widget, "")
        self.g_astroinfo_widget = QtWidgets.QWidget()
        self.g_astroinfo_widget.setObjectName("g_astroinfo_widget")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_astroinfo_widget)
        self.verticalLayout_4.setContentsMargins(12, 12, 12, 12)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.g_astromsg_frame = QtWidgets.QFrame(self.g_astroinfo_widget)
        self.g_astromsg_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_astromsg_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_astromsg_frame.setObjectName("g_astromsg_frame")
        self.verticalLayout_42 = QtWidgets.QVBoxLayout(self.g_astromsg_frame)
        self.verticalLayout_42.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_42.setSpacing(6)
        self.verticalLayout_42.setObjectName("verticalLayout_42")
        self.g_astromsg_label = QtWidgets.QLabel(self.g_astromsg_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_astromsg_label.sizePolicy().hasHeightForWidth())
        self.g_astromsg_label.setSizePolicy(sizePolicy)
        self.g_astromsg_label.setMinimumSize(QtCore.QSize(0, 30))
        self.g_astromsg_label.setMaximumSize(QtCore.QSize(********, 30))
        self.g_astromsg_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_astromsg_label.setObjectName("g_astromsg_label")
        self.verticalLayout_42.addWidget(self.g_astromsg_label)
        spacerItem75 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_42.addItem(spacerItem75)
        self.g_astro_link_frame = QtWidgets.QFrame(self.g_astromsg_frame)
        self.g_astro_link_frame.setMinimumSize(QtCore.QSize(0, 41))
        self.g_astro_link_frame.setMaximumSize(QtCore.QSize(********, 41))
        self.g_astro_link_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_astro_link_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_astro_link_frame.setObjectName("g_astro_link_frame")
        self.horizontalLayout_50 = QtWidgets.QHBoxLayout(self.g_astro_link_frame)
        self.horizontalLayout_50.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_50.setObjectName("horizontalLayout_50")
        spacerItem76 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_50.addItem(spacerItem76)
        self.g_astrolink_button = QtWidgets.QPushButton(self.g_astro_link_frame)
        self.g_astrolink_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_astrolink_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_astrolink_button.setIcon(icon4)
        self.g_astrolink_button.setIconSize(QtCore.QSize(24, 24))
        self.g_astrolink_button.setObjectName("g_astrolink_button")
        self.horizontalLayout_50.addWidget(self.g_astrolink_button)
        self.verticalLayout_42.addWidget(self.g_astro_link_frame)
        self.verticalLayout_4.addWidget(self.g_astromsg_frame)
        self.g_astroid_frame = QtWidgets.QFrame(self.g_astroinfo_widget)
        self.g_astroid_frame.setMinimumSize(QtCore.QSize(0, 41))
        self.g_astroid_frame.setMaximumSize(QtCore.QSize(********, 41))
        self.g_astroid_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_astroid_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_astroid_frame.setObjectName("g_astroid_frame")
        self.horizontalLayout_27 = QtWidgets.QHBoxLayout(self.g_astroid_frame)
        self.horizontalLayout_27.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_27.setObjectName("horizontalLayout_27")
        self.g_astroid_label = QtWidgets.QLabel(self.g_astroid_frame)
        self.g_astroid_label.setMaximumSize(QtCore.QSize(50, ********))
        self.g_astroid_label.setObjectName("g_astroid_label")
        self.horizontalLayout_27.addWidget(self.g_astroid_label)
        self.g_astroid_lineedit = QtWidgets.QLineEdit(self.g_astroid_frame)
        self.g_astroid_lineedit.setMaximumSize(QtCore.QSize(100, ********))
        self.g_astroid_lineedit.setReadOnly(True)
        self.g_astroid_lineedit.setObjectName("g_astroid_lineedit")
        self.horizontalLayout_27.addWidget(self.g_astroid_lineedit)
        self.g_astroname_label = QtWidgets.QLabel(self.g_astroid_frame)
        self.g_astroname_label.setMaximumSize(QtCore.QSize(40, ********))
        self.g_astroname_label.setObjectName("g_astroname_label")
        self.horizontalLayout_27.addWidget(self.g_astroname_label)
        self.g_astroname_lineedit = QtWidgets.QLineEdit(self.g_astroid_frame)
        self.g_astroname_lineedit.setMaximumSize(QtCore.QSize(400, ********))
        self.g_astroname_lineedit.setReadOnly(True)
        self.g_astroname_lineedit.setObjectName("g_astroname_lineedit")
        self.horizontalLayout_27.addWidget(self.g_astroname_lineedit)
        spacerItem77 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_27.addItem(spacerItem77)
        self.g_astrounlink_button = QtWidgets.QPushButton(self.g_astroid_frame)
        self.g_astrounlink_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_astrounlink_button.setMaximumSize(QtCore.QSize(120, ********))
        self.g_astrounlink_button.setIcon(icon6)
        self.g_astrounlink_button.setIconSize(QtCore.QSize(24, 24))
        self.g_astrounlink_button.setObjectName("g_astrounlink_button")
        self.horizontalLayout_27.addWidget(self.g_astrounlink_button)
        self.verticalLayout_4.addWidget(self.g_astroid_frame)
        self.g_astroinfo_frame = QtWidgets.QFrame(self.g_astroinfo_widget)
        self.g_astroinfo_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_astroinfo_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_astroinfo_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_astroinfo_frame.setObjectName("g_astroinfo_frame")
        self.verticalLayout_30 = QtWidgets.QVBoxLayout(self.g_astroinfo_frame)
        self.verticalLayout_30.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_30.setObjectName("verticalLayout_30")
        self.g_astro_tabwidget = QtWidgets.QTabWidget(self.g_astroinfo_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_astro_tabwidget.sizePolicy().hasHeightForWidth())
        self.g_astro_tabwidget.setSizePolicy(sizePolicy)
        self.g_astro_tabwidget.setDocumentMode(False)
        self.g_astro_tabwidget.setMovable(False)
        self.g_astro_tabwidget.setObjectName("g_astro_tabwidget")
        self.g_astrocards_tab = QtWidgets.QWidget()
        self.g_astrocards_tab.setObjectName("g_astrocards_tab")
        self.horizontalLayout_22 = QtWidgets.QHBoxLayout(self.g_astrocards_tab)
        self.horizontalLayout_22.setObjectName("horizontalLayout_22")
        self.g_astrocards_table = QtWidgets.QTableWidget(self.g_astrocards_tab)
        self.g_astrocards_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_astrocards_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_astrocards_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_astrocards_table.setAlternatingRowColors(True)
        self.g_astrocards_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_astrocards_table.setShowGrid(False)
        self.g_astrocards_table.setObjectName("g_astrocards_table")
        self.g_astrocards_table.setColumnCount(3)
        self.g_astrocards_table.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        self.g_astrocards_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_astrocards_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_astrocards_table.setHorizontalHeaderItem(2, item)
        self.g_astrocards_table.horizontalHeader().setStretchLastSection(True)
        self.g_astrocards_table.verticalHeader().setVisible(False)
        self.g_astrocards_table.verticalHeader().setStretchLastSection(False)
        self.horizontalLayout_22.addWidget(self.g_astrocards_table)
        self.g_astro_tabwidget.addTab(self.g_astrocards_tab, "")
        self.g_astrooffers_tab = QtWidgets.QWidget()
        self.g_astrooffers_tab.setObjectName("g_astrooffers_tab")
        self.horizontalLayout_49 = QtWidgets.QHBoxLayout(self.g_astrooffers_tab)
        self.horizontalLayout_49.setObjectName("horizontalLayout_49")
        self.g_astrooffers_table = QtWidgets.QTableWidget(self.g_astrooffers_tab)
        self.g_astrooffers_table.setObjectName("g_astrooffers_table")
        self.g_astrooffers_table.setColumnCount(4)
        self.g_astrooffers_table.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        self.g_astrooffers_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_astrooffers_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_astrooffers_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_astrooffers_table.setHorizontalHeaderItem(3, item)
        self.g_astrooffers_table.horizontalHeader().setStretchLastSection(True)
        self.g_astrooffers_table.verticalHeader().setStretchLastSection(False)
        self.horizontalLayout_49.addWidget(self.g_astrooffers_table)
        self.g_astro_tabwidget.addTab(self.g_astrooffers_tab, "")
        self.verticalLayout_30.addWidget(self.g_astro_tabwidget)
        self.verticalLayout_4.addWidget(self.g_astroinfo_frame)
        self.g_manageentity_tabwidget.addTab(self.g_astroinfo_widget, "")
        self.g_dminfo_widget = QtWidgets.QWidget()
        self.g_dminfo_widget.setObjectName("g_dminfo_widget")
        self.verticalLayout_16 = QtWidgets.QVBoxLayout(self.g_dminfo_widget)
        self.verticalLayout_16.setObjectName("verticalLayout_16")
        self.g_dmmsg_frame = QtWidgets.QFrame(self.g_dminfo_widget)
        self.g_dmmsg_frame.setMaximumSize(QtCore.QSize(********, 30))
        self.g_dmmsg_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_dmmsg_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_dmmsg_frame.setObjectName("g_dmmsg_frame")
        self.verticalLayout_15 = QtWidgets.QVBoxLayout(self.g_dmmsg_frame)
        self.verticalLayout_15.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_15.setSpacing(6)
        self.verticalLayout_15.setObjectName("verticalLayout_15")
        self.g_dmmsg_label = QtWidgets.QLabel(self.g_dmmsg_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dmmsg_label.sizePolicy().hasHeightForWidth())
        self.g_dmmsg_label.setSizePolicy(sizePolicy)
        self.g_dmmsg_label.setMaximumSize(QtCore.QSize(********, 30))
        self.g_dmmsg_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_dmmsg_label.setObjectName("g_dmmsg_label")
        self.verticalLayout_15.addWidget(self.g_dmmsg_label)
        self.verticalLayout_16.addWidget(self.g_dmmsg_frame)
        spacerItem78 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_16.addItem(spacerItem78)
        self.g_dmcontrols_frame = QtWidgets.QFrame(self.g_dminfo_widget)
        self.g_dmcontrols_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_dmcontrols_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_dmcontrols_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_dmcontrols_frame.setObjectName("g_dmcontrols_frame")
        self.horizontalLayout_46 = QtWidgets.QHBoxLayout(self.g_dmcontrols_frame)
        self.horizontalLayout_46.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_46.setObjectName("horizontalLayout_46")
        spacerItem79 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_46.addItem(spacerItem79)
        self.g_dmunlink_button = QtWidgets.QPushButton(self.g_dmcontrols_frame)
        self.g_dmunlink_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_dmunlink_button.setIcon(icon6)
        self.g_dmunlink_button.setIconSize(QtCore.QSize(24, 24))
        self.g_dmunlink_button.setObjectName("g_dmunlink_button")
        self.horizontalLayout_46.addWidget(self.g_dmunlink_button)
        self.g_dmlink_toolbutton = QtWidgets.QToolButton(self.g_dmcontrols_frame)
        self.g_dmlink_toolbutton.setMinimumSize(QtCore.QSize(120, 40))
        self.g_dmlink_toolbutton.setIconSize(QtCore.QSize(24, 24))
        self.g_dmlink_toolbutton.setObjectName("g_dmlink_toolbutton")
        self.horizontalLayout_46.addWidget(self.g_dmlink_toolbutton)
        self.verticalLayout_16.addWidget(self.g_dmcontrols_frame)
        self.g_manageentity_tabwidget.addTab(self.g_dminfo_widget, "")
        self.g_savedcards_widget = QtWidgets.QWidget()
        self.g_savedcards_widget.setObjectName("g_savedcards_widget")
        self.verticalLayout_11 = QtWidgets.QVBoxLayout(self.g_savedcards_widget)
        self.verticalLayout_11.setObjectName("verticalLayout_11")
        self.g_savedpayments_not_available_frame = QtWidgets.QFrame(self.g_savedcards_widget)
        self.g_savedpayments_not_available_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_savedpayments_not_available_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_savedpayments_not_available_frame.setObjectName("g_savedpayments_not_available_frame")
        self.verticalLayout_112 = QtWidgets.QVBoxLayout(self.g_savedpayments_not_available_frame)
        self.verticalLayout_112.setObjectName("verticalLayout_112")
        spacerItem80 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_112.addItem(spacerItem80)
        self.label_6 = QtWidgets.QLabel(self.g_savedpayments_not_available_frame)
        self.label_6.setAlignment(QtCore.Qt.AlignCenter)
        self.label_6.setObjectName("label_6")
        self.verticalLayout_112.addWidget(self.label_6)
        spacerItem81 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_112.addItem(spacerItem81)
        self.verticalLayout_11.addWidget(self.g_savedpayments_not_available_frame)
        self.g_savedcards_table = QtWidgets.QTableWidget(self.g_savedcards_widget)
        self.g_savedcards_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_savedcards_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_savedcards_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_savedcards_table.setAlternatingRowColors(True)
        self.g_savedcards_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_savedcards_table.setShowGrid(False)
        self.g_savedcards_table.setObjectName("g_savedcards_table")
        self.g_savedcards_table.setColumnCount(4)
        self.g_savedcards_table.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_savedcards_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_savedcards_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_savedcards_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_savedcards_table.setHorizontalHeaderItem(3, item)
        self.g_savedcards_table.horizontalHeader().setDefaultSectionSize(199)
        self.g_savedcards_table.horizontalHeader().setStretchLastSection(True)
        self.g_savedcards_table.verticalHeader().setVisible(False)
        self.verticalLayout_11.addWidget(self.g_savedcards_table)
        self.g_savedpayments_controls_frame = QtWidgets.QFrame(self.g_savedcards_widget)
        self.g_savedpayments_controls_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_savedpayments_controls_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_savedpayments_controls_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_savedpayments_controls_frame.setObjectName("g_savedpayments_controls_frame")
        self.horizontalLayout_31 = QtWidgets.QHBoxLayout(self.g_savedpayments_controls_frame)
        self.horizontalLayout_31.setObjectName("horizontalLayout_31")
        spacerItem82 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_31.addItem(spacerItem82)
        self.g_delete_card_button = QtWidgets.QPushButton(self.g_savedpayments_controls_frame)
        self.g_delete_card_button.setMinimumSize(QtCore.QSize(140, 40))
        self.g_delete_card_button.setIcon(icon6)
        self.g_delete_card_button.setIconSize(QtCore.QSize(24, 24))
        self.g_delete_card_button.setObjectName("g_delete_card_button")
        self.horizontalLayout_31.addWidget(self.g_delete_card_button)
        self.g_savecard_button = QtWidgets.QPushButton(self.g_savedpayments_controls_frame)
        self.g_savecard_button.setMinimumSize(QtCore.QSize(140, 40))
        self.g_savecard_button.setIcon(icon4)
        self.g_savecard_button.setIconSize(QtCore.QSize(24, 24))
        self.g_savecard_button.setObjectName("g_savecard_button")
        self.horizontalLayout_31.addWidget(self.g_savecard_button)
        self.verticalLayout_11.addWidget(self.g_savedpayments_controls_frame)
        self.g_manageentity_tabwidget.addTab(self.g_savedcards_widget, "")
        self.verticalLayout_22.addWidget(self.g_manageentity_tabwidget)
        self.g_bottombar_frame = QtWidgets.QFrame(ManageEntity)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.g_bottombar_frame.setSizePolicy(sizePolicy)
        self.g_bottombar_frame.setMinimumSize(QtCore.QSize(247, 39))
        self.g_bottombar_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottombar_frame.setObjectName("g_bottombar_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_bottombar_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setSpacing(6)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        spacerItem83 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem83)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_bottombar_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setStyleSheet("")
        self.g_cancel_button.setIcon(icon6)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_2.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_bottombar_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_save_button.setStyleSheet("")
        self.g_save_button.setIcon(icon8)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_2.addWidget(self.g_save_button)
        self.verticalLayout_22.addWidget(self.g_bottombar_frame)
        self.g_entity_label.setBuddy(self.g_entityfirst_lineedit)
        self.g_business_label.setBuddy(self.g_business_lineedit)
        self.g_entity_label_2.setBuddy(self.g_entityfirst_lineedit)
        self.g_sales_type_label.setBuddy(self.g_sales_sumbyperiod_combbox)
        self.g_sales_sumbyperiod_label.setBuddy(self.g_sales_sumbyperiod_combbox)
        self.g_location_label.setBuddy(self.g_sales_sumbyperiod_combbox)

        self.retranslateUi(ManageEntity)
        self.g_manageentity_tabwidget.setCurrentIndex(0)
        self.g_salestab_tabWidget.setCurrentIndex(0)
        self.g_frequentbuyer_tabwidget.setCurrentIndex(0)
        self.g_purchaseorder_tabwidget.setCurrentIndex(0)
        self.g_moduleaccess_list.setCurrentRow(-1)
        self.g_locationaccess_list.setCurrentRow(-1)
        self.g_astro_tabwidget.setCurrentIndex(1)
        QtCore.QMetaObject.connectSlotsByName(ManageEntity)
        ManageEntity.setTabOrder(self.g_manageentity_tabwidget, self.g_addressline1_lineedit)
        ManageEntity.setTabOrder(self.g_addressline1_lineedit, self.g_addressline2_lineedit)
        ManageEntity.setTabOrder(self.g_addressline2_lineedit, self.g_city_lineedit)
        ManageEntity.setTabOrder(self.g_city_lineedit, self.g_state_combobox)
        ManageEntity.setTabOrder(self.g_state_combobox, self.g_zip_lineedit)
        ManageEntity.setTabOrder(self.g_zip_lineedit, self.g_country_combobox)
        ManageEntity.setTabOrder(self.g_country_combobox, self.g_phone_listWidget)
        ManageEntity.setTabOrder(self.g_phone_listWidget, self.g_email_listWidget)
        ManageEntity.setTabOrder(self.g_email_listWidget, self.g_customfield_table)
        ManageEntity.setTabOrder(self.g_customfield_table, self.g_removecustomfield_button)
        ManageEntity.setTabOrder(self.g_removecustomfield_button, self.g_managecustomfield_button)
        ManageEntity.setTabOrder(self.g_managecustomfield_button, self.g_addcustom_button)
        ManageEntity.setTabOrder(self.g_addcustom_button, self.g_frequentbuyer_tabwidget)
        ManageEntity.setTabOrder(self.g_frequentbuyer_tabwidget, self.g_programstatus_table)
        ManageEntity.setTabOrder(self.g_programstatus_table, self.g_rewardsearned_table)
        ManageEntity.setTabOrder(self.g_rewardsearned_table, self.g_startdate_timeedit)
        ManageEntity.setTabOrder(self.g_startdate_timeedit, self.g_enddate_timeedit)
        ManageEntity.setTabOrder(self.g_enddate_timeedit, self.g_purchaseorder_tabwidget)
        ManageEntity.setTabOrder(self.g_purchaseorder_tabwidget, self.g_purchaseorder_table)
        ManageEntity.setTabOrder(self.g_purchaseorder_table, self.g_product_table)
        ManageEntity.setTabOrder(self.g_product_table, self.g_pets_table)
        ManageEntity.setTabOrder(self.g_pets_table, self.g_usda_lineedit)
        ManageEntity.setTabOrder(self.g_usda_lineedit, self.g_state_lineedit)
        ManageEntity.setTabOrder(self.g_state_lineedit, self.g_currentpage_breeder_lineedit)
        ManageEntity.setTabOrder(self.g_currentpage_breeder_lineedit, self.g_paperwork_breeder_table)
        ManageEntity.setTabOrder(self.g_paperwork_breeder_table, self.g_removepaperwork_breeder_button)
        ManageEntity.setTabOrder(self.g_removepaperwork_breeder_button, self.g_managepaperwork_breeder_button)
        ManageEntity.setTabOrder(self.g_managepaperwork_breeder_button, self.g_print_paperwork_breeder_button)
        ManageEntity.setTabOrder(self.g_print_paperwork_breeder_button, self.g_addpaperwork_breeder_button)
        ManageEntity.setTabOrder(self.g_addpaperwork_breeder_button, self.g_broker_usda_lineedit)
        ManageEntity.setTabOrder(self.g_broker_usda_lineedit, self.g_broker_state_lineedit)
        ManageEntity.setTabOrder(self.g_broker_state_lineedit, self.g_currentpage_broker_lineedit)
        ManageEntity.setTabOrder(self.g_currentpage_broker_lineedit, self.g_paperwork_broker_table)
        ManageEntity.setTabOrder(self.g_paperwork_broker_table, self.g_removepaperwork_broker_button)
        ManageEntity.setTabOrder(self.g_removepaperwork_broker_button, self.g_managepaperwork_broker_button)
        ManageEntity.setTabOrder(self.g_managepaperwork_broker_button, self.g_print_paperwork_broker_button)
        ManageEntity.setTabOrder(self.g_print_paperwork_broker_button, self.g_addpaperwork_broker_button)
        ManageEntity.setTabOrder(self.g_addpaperwork_broker_button, self.g_vet_licence_lineedit)
        ManageEntity.setTabOrder(self.g_vet_licence_lineedit, self.g_transporter_usda_lineedit)
        ManageEntity.setTabOrder(self.g_transporter_usda_lineedit, self.g_transporter_state_lineedit)
        ManageEntity.setTabOrder(self.g_transporter_state_lineedit, self.g_currentpage_trans_lineedit)
        ManageEntity.setTabOrder(self.g_currentpage_trans_lineedit, self.g_paperwork_trans_table)
        ManageEntity.setTabOrder(self.g_paperwork_trans_table, self.g_removepaperwork_trans_button)
        ManageEntity.setTabOrder(self.g_removepaperwork_trans_button, self.g_managepaperwork_trans_button)
        ManageEntity.setTabOrder(self.g_managepaperwork_trans_button, self.g_print_paperwork_trans_button)
        ManageEntity.setTabOrder(self.g_print_paperwork_trans_button, self.g_addpaperwork_trans_button)
        ManageEntity.setTabOrder(self.g_addpaperwork_trans_button, self.g_litters_table)
        ManageEntity.setTabOrder(self.g_litters_table, self.g_managelitter_button)
        ManageEntity.setTabOrder(self.g_managelitter_button, self.g_brands_table)
        ManageEntity.setTabOrder(self.g_brands_table, self.g_deletebrand_button)
        ManageEntity.setTabOrder(self.g_deletebrand_button, self.g_managebrand_button)
        ManageEntity.setTabOrder(self.g_managebrand_button, self.g_addbrand_button)
        ManageEntity.setTabOrder(self.g_addbrand_button, self.g_privatenotes_plaintextedit)
        ManageEntity.setTabOrder(self.g_privatenotes_plaintextedit, self.g_currentpage_lineedit)
        ManageEntity.setTabOrder(self.g_currentpage_lineedit, self.g_warning_lineedit)
        ManageEntity.setTabOrder(self.g_warning_lineedit, self.g_info_lineedit)
        ManageEntity.setTabOrder(self.g_info_lineedit, self.g_description_lineedit)
        ManageEntity.setTabOrder(self.g_description_lineedit, self.g_title_lineedit)
        ManageEntity.setTabOrder(self.g_title_lineedit, self.g_tags_lineedit)
        ManageEntity.setTabOrder(self.g_tags_lineedit, self.g_cropper_cancel_btn)
        ManageEntity.setTabOrder(self.g_cropper_cancel_btn, self.g_crop_and_save_btn)
        ManageEntity.setTabOrder(self.g_crop_and_save_btn, self.g_entity_images_list)
        ManageEntity.setTabOrder(self.g_entity_images_list, self.g_uploadfilename_lineedit)
        ManageEntity.setTabOrder(self.g_uploadfilename_lineedit, self.g_selectfile_button)
        ManageEntity.setTabOrder(self.g_selectfile_button, self.g_manufacturers_table)
        ManageEntity.setTabOrder(self.g_manufacturers_table, self.g_manufactuer_massaction_button)
        ManageEntity.setTabOrder(self.g_manufactuer_massaction_button, self.g_breederslug_lineedit)
        ManageEntity.setTabOrder(self.g_breederslug_lineedit, self.g_astroid_lineedit)
        ManageEntity.setTabOrder(self.g_astroid_lineedit, self.g_astroname_lineedit)
        ManageEntity.setTabOrder(self.g_astroname_lineedit, self.g_astrounlink_button)

    def retranslateUi(self, ManageEntity):
        _translate = QtCore.QCoreApplication.translate
        ManageEntity.setWindowTitle(_translate("ManageEntity", "Manage Entity Dialog"))
        self.g_main_label.setText(_translate("ManageEntity", "Manage People & Businesses"))
        self.g_previouspage_label.setToolTip(_translate("ManageEntity", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("ManageEntity", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("ManageEntity", "999"))
        self.g_slash_label.setText(_translate("ManageEntity", "/"))
        self.g_totalpages_label.setToolTip(_translate("ManageEntity", "Total Pages"))
        self.g_totalpages_label.setText(_translate("ManageEntity", "999"))
        self.g_selectgroupbox_label.setText(_translate("ManageEntity", "Select a Group from the List Below"))
        self.g_entity_label.setText(_translate("ManageEntity", "Name"))
        self.g_entityfirst_lineedit.setPlaceholderText(_translate("ManageEntity", "First"))
        self.g_entitylast_lineedit.setPlaceholderText(_translate("ManageEntity", "Last"))
        self.g_business_label.setText(_translate("ManageEntity", "Business"))
        self.g_business_lineedit.setPlaceholderText(_translate("ManageEntity", "Business Name"))
        self.g_entity_label_7.setText(_translate("ManageEntity", "Group(s)"))
        self.g_address_label.setText(_translate("ManageEntity", "Address"))
        self.g_city_label.setText(_translate("ManageEntity", "City"))
        self.g_state_label.setText(_translate("ManageEntity", "State"))
        self.g_zip_label.setText(_translate("ManageEntity", "Zip Code"))
        self.g_country_label.setText(_translate("ManageEntity", "Country"))
        self.g_country_combobox.setItemText(0, _translate("ManageEntity", "US"))
        self.g_country_combobox.setItemText(1, _translate("ManageEntity", "UK"))
        self.g_country_combobox.setItemText(2, _translate("ManageEntity", "RU"))
        self.g_country_combobox.setItemText(3, _translate("ManageEntity", "MX"))
        self.g_city_label_3.setText(_translate("ManageEntity", "Birthday"))
        self.g_ent_birthday_timeedit.setDisplayFormat(_translate("ManageEntity", "MM/dd/yyyy"))
        self.g_label_group.setTitle(_translate("ManageEntity", "Print Label"))
        self.g_print_customercardlabel_button.setText(_translate("ManageEntity", " Folder\n"
" Label"))
        self.g_print_addresslabel_button.setText(_translate("ManageEntity", " Address\n"
" Label"))
        self.g_loyalty_group.setTitle(_translate("ManageEntity", "Loyalty"))
        self.g_loyaltycard_label_2.setText(_translate("ManageEntity", "Card"))
        self.g_loyaltybalance_label.setText(_translate("ManageEntity", " Bonus Bucks"))
        self.g_updatebonusbucks_label.setToolTip(_translate("ManageEntity", "Next Page"))
        self.g_subscription_group.setTitle(_translate("ManageEntity", "Subscriptions"))
        self.g_subscription_label.setText(_translate("ManageEntity", "Customer has no active subscriptions."))
        self.g_taxes_groupbox.setTitle(_translate("ManageEntity", "Tax Exemptions"))
        self.g_tax_exemptions_label.setText(_translate("ManageEntity", "Customer has no tax exemptions."))
        self.g_updatetaxcertificates_label.setToolTip(_translate("ManageEntity", "Next Page"))
        item = self.g_tax_exemption_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_tax_exemption_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_tax_exemption_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_tax_exemption_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_tax_exemption_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Tax"))
        item = self.g_tax_exemption_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Cert #"))
        item = self.g_tax_exemption_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "End Date"))
        __sortingEnabled = self.g_tax_exemption_table.isSortingEnabled()
        self.g_tax_exemption_table.setSortingEnabled(False)
        item = self.g_tax_exemption_table.item(0, 0)
        item.setText(_translate("ManageEntity", "sss"))
        item = self.g_tax_exemption_table.item(0, 2)
        item.setText(_translate("ManageEntity", "************"))
        item = self.g_tax_exemption_table.item(1, 2)
        item.setText(_translate("ManageEntity", "************34"))
        item = self.g_tax_exemption_table.item(2, 2)
        item.setText(_translate("ManageEntity", "555666"))
        self.g_tax_exemption_table.setSortingEnabled(__sortingEnabled)
        self.g_custompricing_group.setTitle(_translate("ManageEntity", "Custom Pricing"))
        self.g_custompricing_label.setText(_translate("ManageEntity", "Customer has no custom pricing."))
        self.g_custompricing_label_2.setToolTip(_translate("ManageEntity", "Next Page"))
        self.g_houseaccount_group.setTitle(_translate("ManageEntity", "House Account / Store Credit"))
        self.g_houseaccount_label.setText(_translate("ManageEntity", "Customer has no House Accounts."))
        item = self.g_houseaccount_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_houseaccount_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_houseaccount_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_houseaccount_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_houseaccount_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Account"))
        item = self.g_houseaccount_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Balance"))
        item = self.g_houseaccount_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Credit"))
        __sortingEnabled = self.g_houseaccount_table.isSortingEnabled()
        self.g_houseaccount_table.setSortingEnabled(False)
        item = self.g_houseaccount_table.item(0, 1)
        item.setText(_translate("ManageEntity", "************"))
        item = self.g_houseaccount_table.item(1, 1)
        item.setText(_translate("ManageEntity", "************34"))
        item = self.g_houseaccount_table.item(2, 1)
        item.setText(_translate("ManageEntity", "555666"))
        self.g_houseaccount_table.setSortingEnabled(__sortingEnabled)
        self.g_payhouseaccount_button.setText(_translate("ManageEntity", "Pay"))
        self.g_newhouseaccount_button.setText(_translate("ManageEntity", "New"))
        self.g_managehouseaccount_button.setText(_translate("ManageEntity", "Manage"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_contactinfo_widget), _translate("ManageEntity", "Contact Info"))
        self.label.setText(_translate("ManageEntity", "Address are only available with full version of the POS."))
        item = self.g_address_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_address_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_address_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_address_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_address_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_address_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Name"))
        item = self.g_address_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Type"))
        item = self.g_address_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "First Name"))
        item = self.g_address_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Last Name"))
        item = self.g_address_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Business Name"))
        item = self.g_address_table.horizontalHeaderItem(5)
        item.setText(_translate("ManageEntity", "Address"))
        item = self.g_address_table.horizontalHeaderItem(6)
        item.setText(_translate("ManageEntity", "City"))
        item = self.g_address_table.horizontalHeaderItem(7)
        item.setText(_translate("ManageEntity", "State"))
        item = self.g_address_table.horizontalHeaderItem(8)
        item.setText(_translate("ManageEntity", "Zip"))
        item = self.g_address_table.horizontalHeaderItem(9)
        item.setText(_translate("ManageEntity", "Country"))
        __sortingEnabled = self.g_address_table.isSortingEnabled()
        self.g_address_table.setSortingEnabled(False)
        self.g_address_table.setSortingEnabled(__sortingEnabled)
        self.g_deleteaddress_button.setText(_translate("ManageEntity", " Delete"))
        self.g_manageaddress_button.setText(_translate("ManageEntity", " Manage"))
        self.g_primaryaddress_button.setText(_translate("ManageEntity", "Make\n"
"Primary"))
        self.g_addaddress_button.setText(_translate("ManageEntity", "Add"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_address_tab), _translate("ManageEntity", "Addresses"))
        self.label_2.setText(_translate("ManageEntity", "Website are only available with full version of the POS."))
        self.g_entity_label_2.setText(_translate("ManageEntity", "Username"))
        self.g_resetpassword_button.setText(_translate("ManageEntity", "Password\n"
"Reset"))
        self.g_createusername_button.setText(_translate("ManageEntity", "Create\n"
"Username"))
        self.g_welcomeemail_button.setText(_translate("ManageEntity", "Welcome\n"
"Email"))
        item = self.g_marketingsubsciptions_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_marketingsubsciptions_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_marketingsubsciptions_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_marketingsubsciptions_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_marketingsubsciptions_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_marketingsubsciptions_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Subscription Name"))
        item = self.g_marketingsubsciptions_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Enabled"))
        __sortingEnabled = self.g_marketingsubsciptions_table.isSortingEnabled()
        self.g_marketingsubsciptions_table.setSortingEnabled(False)
        self.g_marketingsubsciptions_table.setSortingEnabled(__sortingEnabled)
        self.g_diablemarketingsubscription_button.setText(_translate("ManageEntity", "Disable"))
        self.g_enablemarketingsubscription_button.setText(_translate("ManageEntity", "Enable"))
        self.g_emailmarketingsubscription_button.setText(_translate("ManageEntity", "Send Email"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_website_tab), _translate("ManageEntity", "Website"))
        self.g_publicnotes_groupbox.setTitle(_translate("ManageEntity", "Public/Shows in Cash Register"))
        self.g_warning_label.setText(_translate("ManageEntity", "Warning"))
        self.g_info_label.setText(_translate("ManageEntity", "Info"))
        self.g_privatenotes_groupbox.setTitle(_translate("ManageEntity", "Private/Only shows here"))
        self.g_employeenotes_groupbox.setTitle(_translate("ManageEntity", "Private/ Employee Notes"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_notes_widget), _translate("ManageEntity", "Notes"))
        self.g_desc_label.setText(_translate("ManageEntity", "Alternative Description"))
        self.g_title_label.setText(_translate("ManageEntity", "Title"))
        self.g_tags_label.setText(_translate("ManageEntity", "Tags"))
        self.g_tags_lineedit.setPlaceholderText(_translate("ManageEntity", "Tags must be seperated by commas"))
        self.g_size_label.setText(_translate("ManageEntity", "0x0"))
        self.g_cropper_cancel_btn.setText(_translate("ManageEntity", "Cancel"))
        self.g_crop_and_save_btn.setText(_translate("ManageEntity", "Crop and\n"
"   Save"))
        self.g_addedit_groupBox.setTitle(_translate("ManageEntity", "Add / Edit Existing Media"))
        self.g_uploadnew_label.setText(_translate("ManageEntity", "Upload New"))
        self.g_uploadfilename_lineedit.setPlaceholderText(_translate("ManageEntity", "No file chosen."))
        self.g_selectfile_button.setText(_translate("ManageEntity", "Select File"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_pictures_widget), _translate("ManageEntity", "Media"))
        self.g_startdate_label_3.setText(_translate("ManageEntity", "Start Date"))
        self.g_sales_startdate_timeedit.setDisplayFormat(_translate("ManageEntity", "MM/dd/yyyy"))
        self.g_enddate_label_3.setText(_translate("ManageEntity", "End Date"))
        self.g_sales_enddate_timeedit.setDisplayFormat(_translate("ManageEntity", "MM/dd/yyyy"))
        self.g_sales_type_label.setText(_translate("ManageEntity", "Type"))
        self.g_sales_type_combbox.setCurrentText(_translate("ManageEntity", "Sales"))
        self.g_sales_type_combbox.setItemText(0, _translate("ManageEntity", "Sales"))
        self.g_sales_type_combbox.setItemText(1, _translate("ManageEntity", "Return from Customers"))
        self.g_sales_type_combbox.setItemText(2, _translate("ManageEntity", "Layaways"))
        self.g_sales_sumbyperiod_label.setText(_translate("ManageEntity", "Sum by Period"))
        self.g_sales_sumbyperiod_combbox.setCurrentText(_translate("ManageEntity", "Event"))
        self.g_sales_sumbyperiod_combbox.setItemText(0, _translate("ManageEntity", "Event"))
        self.g_sales_sumbyperiod_combbox.setItemText(1, _translate("ManageEntity", "Week"))
        self.g_sales_sumbyperiod_combbox.setItemText(2, _translate("ManageEntity", "Month"))
        self.g_exportsales_label.setToolTip(_translate("ManageEntity", "Export Table"))
        item = self.g_sales_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_sales_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_sales_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_sales_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_sales_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_sales_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Date"))
        item = self.g_sales_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Type"))
        item = self.g_sales_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Number"))
        item = self.g_sales_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Employee"))
        item = self.g_sales_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Qty"))
        item = self.g_sales_table.horizontalHeaderItem(5)
        item.setText(_translate("ManageEntity", "Price"))
        item = self.g_sales_table.horizontalHeaderItem(6)
        item.setText(_translate("ManageEntity", "Discount"))
        __sortingEnabled = self.g_sales_table.isSortingEnabled()
        self.g_sales_table.setSortingEnabled(False)
        item = self.g_sales_table.item(0, 0)
        item.setText(_translate("ManageEntity", "10/15/2014"))
        item = self.g_sales_table.item(0, 1)
        item.setText(_translate("ManageEntity", "Sold"))
        item = self.g_sales_table.item(0, 2)
        item.setText(_translate("ManageEntity", "25514"))
        item = self.g_sales_table.item(0, 3)
        item.setText(_translate("ManageEntity", "Lisa H"))
        item = self.g_sales_table.item(1, 0)
        item.setText(_translate("ManageEntity", "10/15/2014"))
        item = self.g_sales_table.item(1, 1)
        item.setText(_translate("ManageEntity", "Returned"))
        item = self.g_sales_table.item(1, 2)
        item.setText(_translate("ManageEntity", "123"))
        item = self.g_sales_table.item(1, 3)
        item.setText(_translate("ManageEntity", "Erick A"))
        item = self.g_sales_table.item(2, 0)
        item.setText(_translate("ManageEntity", "10/14/2014"))
        item = self.g_sales_table.item(2, 1)
        item.setText(_translate("ManageEntity", "Sold"))
        item = self.g_sales_table.item(2, 2)
        item.setText(_translate("ManageEntity", "55633"))
        item = self.g_sales_table.item(2, 3)
        item.setText(_translate("ManageEntity", "Lisa H"))
        item = self.g_sales_table.item(3, 0)
        item.setText(_translate("ManageEntity", "10/14/2014"))
        item = self.g_sales_table.item(3, 1)
        item.setText(_translate("ManageEntity", "Sold"))
        item = self.g_sales_table.item(3, 2)
        item.setText(_translate("ManageEntity", "3422"))
        item = self.g_sales_table.item(3, 3)
        item.setText(_translate("ManageEntity", "Lisa H"))
        item = self.g_sales_table.item(4, 0)
        item.setText(_translate("ManageEntity", "10/14/2014"))
        item = self.g_sales_table.item(4, 1)
        item.setText(_translate("ManageEntity", "Sold"))
        item = self.g_sales_table.item(4, 2)
        item.setText(_translate("ManageEntity", "3222"))
        item = self.g_sales_table.item(4, 3)
        item.setText(_translate("ManageEntity", "Erick A"))
        self.g_sales_table.setSortingEnabled(__sortingEnabled)
        self.g_salestab_tabWidget.setTabText(self.g_salestab_tabWidget.indexOf(self.g_history_tab), _translate("ManageEntity", "History"))
        self.g_repeat_order_startdate_label.setText(_translate("ManageEntity", "Start Date"))
        self.g_start_repeatdelivery_datetimeedit.setDisplayFormat(_translate("ManageEntity", "MM/dd/yyyy"))
        self.g_repeat_orders_enddate_label.setText(_translate("ManageEntity", "End Date"))
        self.g_end_start_repeatdelivery_datetimeedit.setDisplayFormat(_translate("ManageEntity", "MM/dd/yyyy"))
        self.g_location_label.setText(_translate("ManageEntity", "Location"))
        self.g_repeatdelivery_table.setSortingEnabled(True)
        item = self.g_repeatdelivery_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_repeatdelivery_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "SKU"))
        item = self.g_repeatdelivery_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Barcode"))
        item = self.g_repeatdelivery_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Description"))
        item = self.g_repeatdelivery_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Price"))
        item = self.g_repeatdelivery_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Frequency"))
        item = self.g_repeatdelivery_table.horizontalHeaderItem(5)
        item.setText(_translate("ManageEntity", "Next Delivery"))
        item = self.g_repeatdelivery_table.horizontalHeaderItem(6)
        item.setText(_translate("ManageEntity", "Qty"))
        item = self.g_repeatdelivery_table.horizontalHeaderItem(7)
        item.setText(_translate("ManageEntity", "Enabled"))
        item = self.g_repeatdelivery_table.horizontalHeaderItem(8)
        item.setText(_translate("ManageEntity", "Location"))
        __sortingEnabled = self.g_repeatdelivery_table.isSortingEnabled()
        self.g_repeatdelivery_table.setSortingEnabled(False)
        self.g_repeatdelivery_table.setSortingEnabled(__sortingEnabled)
        self.g_repeat_order_status_label.setText(_translate("ManageEntity", "Screen will refresh when the Repeat Order has been created"))
        self.g_deleterepeatdelivery_button.setText(_translate("ManageEntity", "Remove"))
        self.g_managerepeatdelivery_button.setText(_translate("ManageEntity", "Manage"))
        self.g_repeatdelivery_massaction_button.setText(_translate("ManageEntity", "Mass Action"))
        self.g_addrepeatdelivery_button.setText(_translate("ManageEntity", "Add"))
        self.g_salestab_tabWidget.setTabText(self.g_salestab_tabWidget.indexOf(self.g_repeatdelivery_tab), _translate("ManageEntity", "Repeat Orders"))
        self.g_layaways_table.setSortingEnabled(True)
        item = self.g_layaways_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_layaways_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Layaway #"))
        item = self.g_layaways_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Open Date/Time"))
        item = self.g_layaways_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Due Date"))
        item = self.g_layaways_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Subtotal"))
        item = self.g_layaways_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Total"))
        item = self.g_layaways_table.horizontalHeaderItem(5)
        item.setText(_translate("ManageEntity", "Balance"))
        __sortingEnabled = self.g_layaways_table.isSortingEnabled()
        self.g_layaways_table.setSortingEnabled(False)
        self.g_layaways_table.setSortingEnabled(__sortingEnabled)
        self.g_display_layaway_button.setText(_translate("ManageEntity", "Display"))
        self.g_make_payment_button.setText(_translate("ManageEntity", "Make \n"
"Payment"))
        self.g_salestab_tabWidget.setTabText(self.g_salestab_tabWidget.indexOf(self.g_layaways_tab), _translate("ManageEntity", "Open Layaways"))
        self.g_quotes_table.setSortingEnabled(True)
        item = self.g_quotes_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_quotes_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Layaway #"))
        item = self.g_quotes_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Open Date/Time"))
        item = self.g_quotes_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Due Date"))
        item = self.g_quotes_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Subtotal"))
        item = self.g_quotes_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Total"))
        item = self.g_quotes_table.horizontalHeaderItem(5)
        item.setText(_translate("ManageEntity", "Balance"))
        __sortingEnabled = self.g_quotes_table.isSortingEnabled()
        self.g_quotes_table.setSortingEnabled(False)
        self.g_quotes_table.setSortingEnabled(__sortingEnabled)
        self.g_quotestotalresults_label.setText(_translate("ManageEntity", "Loading results."))
        self.g_quotes_select_button.setText(_translate("ManageEntity", "Select"))
        self.g_salestab_tabWidget.setTabText(self.g_salestab_tabWidget.indexOf(self.g_openorders_tab), _translate("ManageEntity", "Open Quotes"))
        self.g_layaways_table_3.setSortingEnabled(True)
        item = self.g_layaways_table_3.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_layaways_table_3.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Layaway #"))
        item = self.g_layaways_table_3.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Open Date/Time"))
        item = self.g_layaways_table_3.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Due Date"))
        item = self.g_layaways_table_3.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Subtotal"))
        item = self.g_layaways_table_3.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Total"))
        item = self.g_layaways_table_3.horizontalHeaderItem(5)
        item.setText(_translate("ManageEntity", "Balance"))
        __sortingEnabled = self.g_layaways_table_3.isSortingEnabled()
        self.g_layaways_table_3.setSortingEnabled(False)
        self.g_layaways_table_3.setSortingEnabled(__sortingEnabled)
        self.g_display_layaway_button_3.setText(_translate("ManageEntity", "Display"))
        self.g_make_payment_button_3.setText(_translate("ManageEntity", "Make \n"
"Payment"))
        self.g_salestab_tabWidget.setTabText(self.g_salestab_tabWidget.indexOf(self.g_specialorders_tab), _translate("ManageEntity", "Special Orders"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_sales_widget), _translate("ManageEntity", "Sales"))
        self.label_3.setText(_translate("ManageEntity", "Emergency Contacts are only available with full version of the POS."))
        item = self.g_emergencycontact_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_emergencycontact_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_emergencycontact_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_emergencycontact_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_emergencycontact_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_emergencycontact_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Label"))
        item = self.g_emergencycontact_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "First Name"))
        item = self.g_emergencycontact_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Last Name"))
        item = self.g_emergencycontact_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Phone"))
        item = self.g_emergencycontact_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Email"))
        __sortingEnabled = self.g_emergencycontact_table.isSortingEnabled()
        self.g_emergencycontact_table.setSortingEnabled(False)
        self.g_emergencycontact_table.setSortingEnabled(__sortingEnabled)
        self.g_deleteemergencycontact_button.setText(_translate("ManageEntity", " Delete"))
        self.g_manageemergencycontact_button.setText(_translate("ManageEntity", " Manage"))
        self.g_addemergencycontact_button.setText(_translate("ManageEntity", "Add"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_emergencycontacts_tab), _translate("ManageEntity", "Emergency Contacts"))
        self.g_attributes_groupBox.setTitle(_translate("ManageEntity", "Custom Fields"))
        self.g_attributeshelp_label.setText(_translate("ManageEntity", "Custom Fields are used to add properties onto a Pet. They can be used to aid in searching for a pet and for storing additional information on a pet."))
        item = self.g_customfield_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_customfield_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_customfield_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_customfield_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_customfield_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_customfield_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Field Name"))
        item = self.g_customfield_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Value"))
        __sortingEnabled = self.g_customfield_table.isSortingEnabled()
        self.g_customfield_table.setSortingEnabled(False)
        item = self.g_customfield_table.item(0, 0)
        item.setText(_translate("ManageEntity", "Color"))
        item = self.g_customfield_table.item(0, 1)
        item.setText(_translate("ManageEntity", "Purple"))
        item = self.g_customfield_table.item(1, 0)
        item.setText(_translate("ManageEntity", "Material"))
        item = self.g_customfield_table.item(1, 1)
        item.setText(_translate("ManageEntity", "Nylon"))
        item = self.g_customfield_table.item(2, 0)
        item.setText(_translate("ManageEntity", "Color"))
        item = self.g_customfield_table.item(2, 1)
        item.setText(_translate("ManageEntity", "Yellow"))
        item = self.g_customfield_table.item(3, 0)
        item.setText(_translate("ManageEntity", "Size"))
        item = self.g_customfield_table.item(3, 1)
        item.setText(_translate("ManageEntity", "1\""))
        item = self.g_customfield_table.item(4, 0)
        item.setText(_translate("ManageEntity", "None"))
        item = self.g_customfield_table.item(4, 1)
        item.setText(_translate("ManageEntity", "Coastal"))
        self.g_customfield_table.setSortingEnabled(__sortingEnabled)
        self.g_removecustomfield_button.setText(_translate("ManageEntity", " Delete"))
        self.g_managecustomfield_button.setText(_translate("ManageEntity", " Manage"))
        self.g_addcustom_button.setText(_translate("ManageEntity", "Add"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_customfield_widget), _translate("ManageEntity", "Custom Fields"))
        self.label_4.setText(_translate("ManageEntity", "Frequent Buyer are only available with full version of the POS."))
        self.g_programstatus_table.setSortingEnabled(True)
        item = self.g_programstatus_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_programstatus_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_programstatus_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_programstatus_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_programstatus_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_programstatus_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Program Name"))
        item = self.g_programstatus_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Purchased Qty"))
        item = self.g_programstatus_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Needed Qty"))
        item = self.g_programstatus_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Remaining Qty"))
        __sortingEnabled = self.g_programstatus_table.isSortingEnabled()
        self.g_programstatus_table.setSortingEnabled(False)
        self.g_programstatus_table.setSortingEnabled(__sortingEnabled)
        self.g_addfrequentBuyer_button.setText(_translate("ManageEntity", " Add"))
        self.g_managefrequentBuyer_button.setText(_translate("ManageEntity", " Manage"))
        self.g_frequentbuyer_tabwidget.setTabText(self.g_frequentbuyer_tabwidget.indexOf(self.g_programstatus_widget), _translate("ManageEntity", "Program Status"))
        self.g_rewardsearned_table.setSortingEnabled(True)
        item = self.g_rewardsearned_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_rewardsearned_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_rewardsearned_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_rewardsearned_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Program Name"))
        item = self.g_rewardsearned_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Reward Value"))
        item = self.g_rewardsearned_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Earned"))
        item = self.g_rewardsearned_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Redeemed"))
        item = self.g_rewardsearned_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Redeemed Invoice"))
        __sortingEnabled = self.g_rewardsearned_table.isSortingEnabled()
        self.g_rewardsearned_table.setSortingEnabled(False)
        self.g_rewardsearned_table.setSortingEnabled(__sortingEnabled)
        self.g_frequentbuyer_tabwidget.setTabText(self.g_frequentbuyer_tabwidget.indexOf(self.g_rewardsearned_widget), _translate("ManageEntity", "Rewards Earned"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_frequentbuyer_widget), _translate("ManageEntity", "Frequent Buyer"))
        item = self.g_supplierinfo_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_supplierinfo_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_supplierinfo_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_supplierinfo_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_supplierinfo_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Account #"))
        item = self.g_supplierinfo_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Lead Time"))
        item = self.g_supplierinfo_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Order Freq"))
        item = self.g_supplierinfo_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Min Order Amt"))
        item = self.g_supplierinfo_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Send Via"))
        item = self.g_supplierinfo_table.horizontalHeaderItem(5)
        item.setText(_translate("ManageEntity", "Scan Verify"))
        item = self.g_supplierinfo_table.horizontalHeaderItem(6)
        item.setText(_translate("ManageEntity", "Location"))
        item = self.g_supplierinfo_table.horizontalHeaderItem(7)
        item.setText(_translate("ManageEntity", "Last PIDB Sync"))
        item = self.g_supplierinfo_table.horizontalHeaderItem(8)
        item.setText(_translate("ManageEntity", "PIDB Enabled"))
        item = self.g_supplierinfo_table.horizontalHeaderItem(9)
        item.setText(_translate("ManageEntity", "Notes"))
        __sortingEnabled = self.g_supplierinfo_table.isSortingEnabled()
        self.g_supplierinfo_table.setSortingEnabled(False)
        self.g_supplierinfo_table.setSortingEnabled(__sortingEnabled)
        self.g_removesupplierinfo_button.setText(_translate("ManageEntity", "Delete"))
        self.g_managesupplierinfo_button.setText(_translate("ManageEntity", " Manage"))
        self.g_massactionsupplierinfo_button.setText(_translate("ManageEntity", "Mass\n"
"Action"))
        self.g_addsupplierinfo_button.setText(_translate("ManageEntity", " Add"))
        self.g_supplierinfosuppliersettings_groupbox.setTitle(_translate("ManageEntity", "Supplier Settings"))
        self.g_accountnumber_label.setText(_translate("ManageEntity", "Account Number"))
        self.g_ispetsupplier_checkbox.setText(_translate("ManageEntity", "Supplies Pets"))
        self.g_leadtime_label.setText(_translate("ManageEntity", "Lead Time"))
        self.g_leadtime_label_2.setText(_translate("ManageEntity", "Order Frequency"))
        self.g_supplierinfoordersettings_groupbox.setTitle(_translate("ManageEntity", "Order Settings"))
        self.g_minorderamount_label_4.setText(_translate("ManageEntity", "Minimum Order\n"
"Amount"))
        self.g_supplierinfopoformat_label_4.setText(_translate("ManageEntity", "Send Via"))
        self.g_supplierinfosendvia_combobox.setItemText(0, _translate("ManageEntity", "Save as PDF"))
        self.g_supplierinfosendvia_combobox.setItemText(1, _translate("ManageEntity", "Save as Text File"))
        self.g_supplierinfosendvia_combobox.setItemText(2, _translate("ManageEntity", "Send to Printer"))
        self.g_min_weight_label.setText(_translate("ManageEntity", "Min Weight"))
        self.g_max_weight_label.setText(_translate("ManageEntity", "Max Weight"))
        self.g_weightmeasure_combobox.setItemText(0, _translate("ManageEntity", "lb"))
        self.g_weightmeasure_combobox.setItemText(1, _translate("ManageEntity", "g"))
        self.g_weightmeasure_combobox.setItemText(2, _translate("ManageEntity", "kg"))
        self.g_verification_groupbox.setTitle(_translate("ManageEntity", "Verification Scan"))
        self.g_supplierinfopoformat_label_5.setText(_translate("ManageEntity", "Scan Verify"))
        self.g_verification_combobox.setItemText(0, _translate("ManageEntity", "None"))
        self.g_verification_combobox.setItemText(1, _translate("ManageEntity", "Optional"))
        self.g_verification_combobox.setItemText(2, _translate("ManageEntity", "Required"))
        self.g_pidb_groupbox.setTitle(_translate("ManageEntity", "PIDB"))
        self.g_minorderamount_label_5.setText(_translate("ManageEntity", "Mapped To"))
        self.g_lastsync_label.setText(_translate("ManageEntity", "Last Sync"))
        self.g_enablepidb_checkBox.setText(_translate("ManageEntity", "Enable Catalog Sync"))
        self.g_minorderamount_label_6.setText(_translate("ManageEntity", "Current Status"))
        self.g_minorderamount_label_7.setText(_translate("ManageEntity", "Note:"))
        self.g_supplierinfonotes_groupbox.setTitle(_translate("ManageEntity", "Private Supplier Notes for This Location"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_supplierterms_widget), _translate("ManageEntity", "Supplier Info"))
        self.g_startdate_label.setText(_translate("ManageEntity", "Start Date"))
        self.g_startdate_timeedit.setDisplayFormat(_translate("ManageEntity", "M/d/yyyy"))
        self.g_enddate_label.setText(_translate("ManageEntity", "End Date"))
        self.g_enddate_timeedit.setDisplayFormat(_translate("ManageEntity", "M/d/yyyy"))
        self.g_purchaseorder_table.setSortingEnabled(True)
        item = self.g_purchaseorder_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_purchaseorder_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_purchaseorder_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_purchaseorder_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_purchaseorder_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_purchaseorder_table.verticalHeaderItem(5)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_purchaseorder_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "PO #"))
        item = self.g_purchaseorder_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "PO Amount"))
        item = self.g_purchaseorder_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "# of Products"))
        item = self.g_purchaseorder_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Order Placed"))
        item = self.g_purchaseorder_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Order Received"))
        item = self.g_purchaseorder_table.horizontalHeaderItem(5)
        item.setText(_translate("ManageEntity", "Location"))
        __sortingEnabled = self.g_purchaseorder_table.isSortingEnabled()
        self.g_purchaseorder_table.setSortingEnabled(False)
        item = self.g_purchaseorder_table.item(0, 0)
        item.setText(_translate("ManageEntity", "12345"))
        item = self.g_purchaseorder_table.item(0, 1)
        item.setText(_translate("ManageEntity", "1000.00"))
        item = self.g_purchaseorder_table.item(0, 2)
        item.setText(_translate("ManageEntity", "167"))
        item = self.g_purchaseorder_table.item(0, 3)
        item.setText(_translate("ManageEntity", "10/01/2014"))
        item = self.g_purchaseorder_table.item(0, 4)
        item.setText(_translate("ManageEntity", "10/06/2014"))
        item = self.g_purchaseorder_table.item(0, 5)
        item.setText(_translate("ManageEntity", "Avalon Park"))
        self.g_purchaseorder_table.setSortingEnabled(__sortingEnabled)
        self.g_purchaseorder_tabwidget.setTabText(self.g_purchaseorder_tabwidget.indexOf(self.g_purchaseorder_widget), _translate("ManageEntity", "Purchase Order"))
        self.g_product_table.setSortingEnabled(True)
        item = self.g_product_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_product_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_product_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_product_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_product_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_product_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "PO #"))
        item = self.g_product_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Order Date"))
        item = self.g_product_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "SKU"))
        item = self.g_product_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Description"))
        item = self.g_product_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Ordered Qty"))
        item = self.g_product_table.horizontalHeaderItem(5)
        item.setText(_translate("ManageEntity", "Received Qty"))
        item = self.g_product_table.horizontalHeaderItem(6)
        item.setText(_translate("ManageEntity", "Cost"))
        item = self.g_product_table.horizontalHeaderItem(7)
        item.setText(_translate("ManageEntity", "Extended"))
        item = self.g_product_table.horizontalHeaderItem(8)
        item.setText(_translate("ManageEntity", "Location"))
        __sortingEnabled = self.g_product_table.isSortingEnabled()
        self.g_product_table.setSortingEnabled(False)
        item = self.g_product_table.item(0, 0)
        item.setText(_translate("ManageEntity", "12345"))
        item = self.g_product_table.item(0, 1)
        item.setText(_translate("ManageEntity", "10/01/2014"))
        item = self.g_product_table.item(0, 2)
        item.setText(_translate("ManageEntity", "642334"))
        item = self.g_product_table.item(0, 3)
        item.setText(_translate("ManageEntity", "Black coffee mug."))
        item = self.g_product_table.item(0, 4)
        item.setText(_translate("ManageEntity", "3"))
        item = self.g_product_table.item(0, 5)
        item.setText(_translate("ManageEntity", "3"))
        item = self.g_product_table.item(0, 6)
        item.setText(_translate("ManageEntity", "5"))
        item = self.g_product_table.item(0, 7)
        item.setText(_translate("ManageEntity", "15"))
        item = self.g_product_table.item(0, 8)
        item.setText(_translate("ManageEntity", "Avalon Park"))
        self.g_product_table.setSortingEnabled(__sortingEnabled)
        self.g_purchaseorder_tabwidget.setTabText(self.g_purchaseorder_tabwidget.indexOf(self.g_product_widget), _translate("ManageEntity", "Product"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_purchasehistory_widget), _translate("ManageEntity", "Purchase History"))
        self.g_manufacturers_table.setSortingEnabled(False)
        item = self.g_manufacturers_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(5)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(6)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(7)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(8)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(9)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(10)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(11)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(12)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_manufacturers_table.verticalHeaderItem(13)
        item.setText(_translate("ManageEntity", "New Row"))
        self.g_manfactuer_totalresults_label.setText(_translate("ManageEntity", "Loading results."))
        self.g_manufactuer_massaction_button.setText(_translate("ManageEntity", "Mass\n"
"Action"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_manurfacturers_tab), _translate("ManageEntity", "Manufactuers"))
        self.g_brands_table.setSortingEnabled(True)
        item = self.g_brands_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Name"))
        self.g_deletebrand_button.setText(_translate("ManageEntity", " Delete"))
        self.g_managebrand_button.setText(_translate("ManageEntity", " Manage"))
        self.g_addbrand_button.setText(_translate("ManageEntity", " Add"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_brands_widget), _translate("ManageEntity", "Brands"))
        self.g_description_mfg_label.setText(_translate("ManageEntity", "Alternative Description"))
        self.g_title_mfg_label.setText(_translate("ManageEntity", "Title"))
        self.g_tags_mfg_label.setText(_translate("ManageEntity", "Tags"))
        self.g_tags_mfg_lineedit.setPlaceholderText(_translate("ManageEntity", "Tags must be seperated by commas"))
        self.g_size_mfg_label.setText(_translate("ManageEntity", "0x0"))
        self.g_cropper_cancel_mfg_btn.setText(_translate("ManageEntity", "Cancel"))
        self.g_crop_and_save_mfg_btn.setText(_translate("ManageEntity", "Crop and\n"
"   Save"))
        self.g_addedit_mfg_groupBox.setTitle(_translate("ManageEntity", "Add / Edit Existing Mfg Media"))
        self.g_uploadnew_mfg_label.setText(_translate("ManageEntity", "Upload New"))
        self.g_uploadfilename_mfg_lineedit.setPlaceholderText(_translate("ManageEntity", "No file chosen."))
        self.g_selectfile_mfg_button.setText(_translate("ManageEntity", "Select File"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_mfgmedia_tab), _translate("ManageEntity", "Mfg Media"))
        self.g_featured_checkbox.setText(_translate("ManageEntity", "Featured"))
        self.g_public_checkbox.setText(_translate("ManageEntity", "Public"))
        self.g_marketing_label.setText(_translate("ManageEntity", "Marketing Header"))
        self.g_marketing_description_label.setText(_translate("ManageEntity", "Marketing Description"))
        self.g_marketinghelp_label.setText(_translate("ManageEntity", "The following dynamic fields are available: {LOCATION} {CITY} {STATE}."))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_mfgmarketing_tab), _translate("ManageEntity", "Marketing"))
        self.g_displayname_label.setText(_translate("ManageEntity", "Display Name"))
        self.g_employeerole_label.setText(_translate("ManageEntity", "Employee ID"))
        self.g_employeerole_label_2.setText(_translate("ManageEntity", "Employee Role"))
        self.g_employeerole_combobox.setItemText(0, _translate("ManageEntity", "Adminstrator"))
        self.g_employeerole_combobox.setItemText(1, _translate("ManageEntity", "Clerk"))
        self.g_employeerole_combobox.setItemText(2, _translate("ManageEntity", "Manager"))
        self.g_moduleaccess_label.setText(_translate("ManageEntity", "App Access"))
        __sortingEnabled = self.g_moduleaccess_list.isSortingEnabled()
        self.g_moduleaccess_list.setSortingEnabled(False)
        item = self.g_moduleaccess_list.item(0)
        item.setText(_translate("ManageEntity", "Cash Register"))
        item = self.g_moduleaccess_list.item(1)
        item.setText(_translate("ManageEntity", "Frequent Buyer"))
        item = self.g_moduleaccess_list.item(2)
        item.setText(_translate("ManageEntity", "Pet Tracker"))
        item = self.g_moduleaccess_list.item(3)
        item.setText(_translate("ManageEntity", "Products"))
        item = self.g_moduleaccess_list.item(4)
        item.setText(_translate("ManageEntity", "Purchasing & Receiving"))
        item = self.g_moduleaccess_list.item(5)
        item.setText(_translate("ManageEntity", "Settings & Configuration"))
        self.g_moduleaccess_list.setSortingEnabled(__sortingEnabled)
        self.g_password_label.setText(_translate("ManageEntity", "Password"))
        self.g_password_lineedit.setPlaceholderText(_translate("ManageEntity", "**********"))
        self.g_employeestatus_label.setText(_translate("ManageEntity", "Employee Status"))
        self.g_employeestatus_combobox.setItemText(0, _translate("ManageEntity", "Enabled"))
        self.g_employeestatus_combobox.setItemText(1, _translate("ManageEntity", "Disabled"))
        self.g_emplocations_label.setText(_translate("ManageEntity", "Billing Location"))
        self.g_emplocations_combobox.setItemText(0, _translate("ManageEntity", "Enabled"))
        self.g_emplocations_combobox.setItemText(1, _translate("ManageEntity", "Disabled"))
        self.g_locationaccess_label.setText(_translate("ManageEntity", "Location Access"))
        self.g_locationaccess_list.setSortingEnabled(False)
        __sortingEnabled = self.g_locationaccess_list.isSortingEnabled()
        self.g_locationaccess_list.setSortingEnabled(False)
        item = self.g_locationaccess_list.item(0)
        item.setText(_translate("ManageEntity", "Avalon Park East"))
        item = self.g_locationaccess_list.item(1)
        item.setText(_translate("ManageEntity", "Avalon Park West"))
        item = self.g_locationaccess_list.item(2)
        item.setText(_translate("ManageEntity", "Kissimmee"))
        item = self.g_locationaccess_list.item(3)
        item.setText(_translate("ManageEntity", "Oviedo"))
        self.g_locationaccess_list.setSortingEnabled(__sortingEnabled)
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_employeesettings_widget), _translate("ManageEntity", "Employee Settings"))
        self.label_5.setText(_translate("ManageEntity", "Pets are only available with full version of the POS."))
        self.g_pets_table.setSortingEnabled(True)
        item = self.g_pets_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(5)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(6)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(7)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(8)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(9)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(10)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(11)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(12)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(13)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.verticalHeaderItem(14)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_pets_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Name"))
        item = self.g_pets_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "ID"))
        item = self.g_pets_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Breed Name"))
        item = self.g_pets_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Type"))
        item = self.g_pets_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Microchip"))
        item = self.g_pets_table.horizontalHeaderItem(5)
        item.setText(_translate("ManageEntity", "Color/Markings"))
        item = self.g_pets_table.horizontalHeaderItem(6)
        item.setText(_translate("ManageEntity", "Indentifiers"))
        item = self.g_pets_table.horizontalHeaderItem(7)
        item.setText(_translate("ManageEntity", "Sex"))
        item = self.g_pets_table.horizontalHeaderItem(8)
        item.setText(_translate("ManageEntity", "Birth Date"))
        __sortingEnabled = self.g_pets_table.isSortingEnabled()
        self.g_pets_table.setSortingEnabled(False)
        item = self.g_pets_table.item(0, 1)
        item.setText(_translate("ManageEntity", "54345"))
        item = self.g_pets_table.item(0, 2)
        item.setText(_translate("ManageEntity", "Bull Terrier"))
        item = self.g_pets_table.item(0, 3)
        item.setText(_translate("ManageEntity", "Dog"))
        item = self.g_pets_table.item(0, 8)
        item.setText(_translate("ManageEntity", "01/08/2014"))
        item = self.g_pets_table.item(1, 1)
        item.setText(_translate("ManageEntity", "54348"))
        item = self.g_pets_table.item(1, 2)
        item.setText(_translate("ManageEntity", "Border Collie"))
        item = self.g_pets_table.item(1, 3)
        item.setText(_translate("ManageEntity", "Dog"))
        item = self.g_pets_table.item(1, 8)
        item.setText(_translate("ManageEntity", "01/08/2014"))
        self.g_pets_table.setSortingEnabled(__sortingEnabled)
        self.g_deletecustomerpet_button.setText(_translate("ManageEntity", "Delete"))
        self.g_manage_pet_button.setText(_translate("ManageEntity", " Manage"))
        self.g_addcustomerpet_button.setText(_translate("ManageEntity", "Add"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_pets_widget), _translate("ManageEntity", "Pets"))
        self.g_breederslug_label.setText(_translate("ManageEntity", "Breeder Slug"))
        self.g_usda_label.setText(_translate("ManageEntity", "USDA #"))
        self.g_state_label1.setText(_translate("ManageEntity", "State #"))
        self.g_previouspage_breeder_label.setToolTip(_translate("ManageEntity", "Previous Page"))
        self.g_nextpage_breeder_label.setToolTip(_translate("ManageEntity", "Next Page"))
        self.g_currentpage_breeder_lineedit.setText(_translate("ManageEntity", "999"))
        self.g_slash_label_6.setText(_translate("ManageEntity", "/"))
        self.g_totalpages_breeder_label.setToolTip(_translate("ManageEntity", "Total Pages"))
        self.g_totalpages_breeder_label.setText(_translate("ManageEntity", "999"))
        item = self.g_paperwork_breeder_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_breeder_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_breeder_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_breeder_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_breeder_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_breeder_table.verticalHeaderItem(5)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_breeder_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Name"))
        item = self.g_paperwork_breeder_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Classification"))
        item = self.g_paperwork_breeder_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Create Print Qty"))
        item = self.g_paperwork_breeder_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Sale Print Qty"))
        __sortingEnabled = self.g_paperwork_breeder_table.isSortingEnabled()
        self.g_paperwork_breeder_table.setSortingEnabled(False)
        item = self.g_paperwork_breeder_table.item(0, 0)
        item.setText(_translate("ManageEntity", "Breed Information"))
        item = self.g_paperwork_breeder_table.item(0, 1)
        item.setText(_translate("ManageEntity", "Warranty"))
        item = self.g_paperwork_breeder_table.item(0, 2)
        item.setText(_translate("ManageEntity", "0"))
        item = self.g_paperwork_breeder_table.item(0, 3)
        item.setText(_translate("ManageEntity", "1"))
        item = self.g_paperwork_breeder_table.item(1, 0)
        item.setText(_translate("ManageEntity", "Pedigree"))
        item = self.g_paperwork_breeder_table.item(1, 1)
        item.setText(_translate("ManageEntity", "Store Information"))
        item = self.g_paperwork_breeder_table.item(1, 2)
        item.setText(_translate("ManageEntity", "1"))
        item = self.g_paperwork_breeder_table.item(1, 3)
        item.setText(_translate("ManageEntity", "2"))
        self.g_paperwork_breeder_table.setSortingEnabled(__sortingEnabled)
        self.g_totalresults_breeder_label.setText(_translate("ManageEntity", "Loading results."))
        self.g_removepaperwork_breeder_button.setText(_translate("ManageEntity", " Delete"))
        self.g_managepaperwork_breeder_button.setText(_translate("ManageEntity", " Manage"))
        self.g_print_paperwork_breeder_button.setText(_translate("ManageEntity", "Preview"))
        self.g_addpaperwork_breeder_button.setText(_translate("ManageEntity", " Add"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_breederinfo_widget), _translate("ManageEntity", "Breeder Info"))
        self.g_broker_usda_label.setText(_translate("ManageEntity", "USDA #"))
        self.g_broker_state_label.setText(_translate("ManageEntity", "State #"))
        self.g_totalresults_broker_label.setText(_translate("ManageEntity", "Loading results."))
        self.g_previouspage_broker_label.setToolTip(_translate("ManageEntity", "Previous Page"))
        self.g_nextpage_broker_label.setToolTip(_translate("ManageEntity", "Next Page"))
        self.g_currentpage_broker_lineedit.setText(_translate("ManageEntity", "999"))
        self.g_slash_label_7.setText(_translate("ManageEntity", "/"))
        self.g_totalpages_broker_label.setToolTip(_translate("ManageEntity", "Total Pages"))
        self.g_totalpages_broker_label.setText(_translate("ManageEntity", "999"))
        item = self.g_paperwork_broker_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_broker_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_broker_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_broker_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_broker_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_broker_table.verticalHeaderItem(5)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_broker_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Name"))
        item = self.g_paperwork_broker_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Classification"))
        item = self.g_paperwork_broker_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Create Print Qty"))
        item = self.g_paperwork_broker_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Sale Print Qty"))
        __sortingEnabled = self.g_paperwork_broker_table.isSortingEnabled()
        self.g_paperwork_broker_table.setSortingEnabled(False)
        item = self.g_paperwork_broker_table.item(0, 0)
        item.setText(_translate("ManageEntity", "Breed Information"))
        item = self.g_paperwork_broker_table.item(0, 1)
        item.setText(_translate("ManageEntity", "Warranty"))
        item = self.g_paperwork_broker_table.item(0, 2)
        item.setText(_translate("ManageEntity", "0"))
        item = self.g_paperwork_broker_table.item(0, 3)
        item.setText(_translate("ManageEntity", "1"))
        item = self.g_paperwork_broker_table.item(1, 0)
        item.setText(_translate("ManageEntity", "Pedigree"))
        item = self.g_paperwork_broker_table.item(1, 1)
        item.setText(_translate("ManageEntity", "Store Information"))
        item = self.g_paperwork_broker_table.item(1, 2)
        item.setText(_translate("ManageEntity", "1"))
        item = self.g_paperwork_broker_table.item(1, 3)
        item.setText(_translate("ManageEntity", "2"))
        self.g_paperwork_broker_table.setSortingEnabled(__sortingEnabled)
        self.g_removepaperwork_broker_button.setText(_translate("ManageEntity", " Delete"))
        self.g_managepaperwork_broker_button.setText(_translate("ManageEntity", " Manage"))
        self.g_print_paperwork_broker_button.setText(_translate("ManageEntity", "Preview"))
        self.g_addpaperwork_broker_button.setText(_translate("ManageEntity", " Add"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_brokerinfo_widget), _translate("ManageEntity", "Broker Info"))
        self.g_vet_licence_label.setText(_translate("ManageEntity", "Licence #"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_vetinfo_widget), _translate("ManageEntity", "Vet Info"))
        self.g_transporter_usda_label.setText(_translate("ManageEntity", "USDA #"))
        self.g_transporter_usda_lineedit.setPlaceholderText(_translate("ManageEntity", "Enter USDA #"))
        self.g_transporter_state_label.setText(_translate("ManageEntity", "State #"))
        self.g_transporter_state_lineedit.setPlaceholderText(_translate("ManageEntity", "Enter State #"))
        self.g_totalresults_trans_label.setText(_translate("ManageEntity", "Loading results."))
        self.g_previouspage_trans_label.setToolTip(_translate("ManageEntity", "Previous Page"))
        self.g_nextpage_trans_label.setToolTip(_translate("ManageEntity", "Next Page"))
        self.g_currentpage_trans_lineedit.setText(_translate("ManageEntity", "999"))
        self.g_slash_label_8.setText(_translate("ManageEntity", "/"))
        self.g_totalpages_trans_label.setToolTip(_translate("ManageEntity", "Total Pages"))
        self.g_totalpages_trans_label.setText(_translate("ManageEntity", "999"))
        item = self.g_paperwork_trans_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_trans_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_trans_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_trans_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_trans_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_trans_table.verticalHeaderItem(5)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_paperwork_trans_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Name"))
        item = self.g_paperwork_trans_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Classification"))
        item = self.g_paperwork_trans_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Create Print Qty"))
        item = self.g_paperwork_trans_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Sale Print Qty"))
        __sortingEnabled = self.g_paperwork_trans_table.isSortingEnabled()
        self.g_paperwork_trans_table.setSortingEnabled(False)
        item = self.g_paperwork_trans_table.item(0, 0)
        item.setText(_translate("ManageEntity", "Breed Information"))
        item = self.g_paperwork_trans_table.item(0, 1)
        item.setText(_translate("ManageEntity", "Warranty"))
        item = self.g_paperwork_trans_table.item(0, 2)
        item.setText(_translate("ManageEntity", "0"))
        item = self.g_paperwork_trans_table.item(0, 3)
        item.setText(_translate("ManageEntity", "1"))
        item = self.g_paperwork_trans_table.item(1, 0)
        item.setText(_translate("ManageEntity", "Pedigree"))
        item = self.g_paperwork_trans_table.item(1, 1)
        item.setText(_translate("ManageEntity", "Store Information"))
        item = self.g_paperwork_trans_table.item(1, 2)
        item.setText(_translate("ManageEntity", "1"))
        item = self.g_paperwork_trans_table.item(1, 3)
        item.setText(_translate("ManageEntity", "2"))
        self.g_paperwork_trans_table.setSortingEnabled(__sortingEnabled)
        self.g_removepaperwork_trans_button.setText(_translate("ManageEntity", " Delete"))
        self.g_managepaperwork_trans_button.setText(_translate("ManageEntity", " Manage"))
        self.g_print_paperwork_trans_button.setText(_translate("ManageEntity", "Preview"))
        self.g_addpaperwork_trans_button.setText(_translate("ManageEntity", " Add"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_transporterinfo_widget), _translate("ManageEntity", "Transporter Info"))
        self.g_litters_table.setSortingEnabled(True)
        item = self.g_litters_table.verticalHeaderItem(0)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(1)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(2)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(3)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(4)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(5)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(6)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(7)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(8)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(9)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(10)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(11)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(12)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(13)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(14)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(15)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.verticalHeaderItem(16)
        item.setText(_translate("ManageEntity", "New Row"))
        item = self.g_litters_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Birth Date"))
        item = self.g_litters_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Breed Name"))
        item = self.g_litters_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Received On"))
        item = self.g_litters_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Days in House"))
        item = self.g_litters_table.horizontalHeaderItem(4)
        item.setText(_translate("ManageEntity", "Status"))
        __sortingEnabled = self.g_litters_table.isSortingEnabled()
        self.g_litters_table.setSortingEnabled(False)
        item = self.g_litters_table.item(0, 0)
        item.setText(_translate("ManageEntity", "01/08/2014"))
        item = self.g_litters_table.item(0, 1)
        item.setText(_translate("ManageEntity", "Bull Terrier"))
        item = self.g_litters_table.item(1, 0)
        item.setText(_translate("ManageEntity", "01/08/2014"))
        item = self.g_litters_table.item(1, 1)
        item.setText(_translate("ManageEntity", "Bull Terrier"))
        item = self.g_litters_table.item(2, 0)
        item.setText(_translate("ManageEntity", "01/08/2014"))
        item = self.g_litters_table.item(2, 1)
        item.setText(_translate("ManageEntity", "Labrador Retriever"))
        item = self.g_litters_table.item(3, 0)
        item.setText(_translate("ManageEntity", "01/08/2014"))
        item = self.g_litters_table.item(3, 1)
        item.setText(_translate("ManageEntity", "Border Collie"))
        self.g_litters_table.setSortingEnabled(__sortingEnabled)
        self.g_managelitter_button.setText(_translate("ManageEntity", " Manage"))
        self.g_managelitter_button.setShortcut(_translate("ManageEntity", "Enter"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_litters_widget), _translate("ManageEntity", "Pets From Breeder"))
        self.g_astromsg_label.setText(_translate("ManageEntity", "This customer is not linked with any Astro ID. Click \"Link\" to enroll this customer."))
        self.g_astrolink_button.setText(_translate("ManageEntity", "Link"))
        self.g_astroid_label.setText(_translate("ManageEntity", "Astro ID"))
        self.g_astroname_label.setText(_translate("ManageEntity", "Name"))
        self.g_astrounlink_button.setText(_translate("ManageEntity", "Unlink"))
        self.g_astrocards_table.setSortingEnabled(True)
        item = self.g_astrocards_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Card #"))
        item = self.g_astrocards_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Program Title"))
        item = self.g_astrocards_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Purchased/Required"))
        self.g_astro_tabwidget.setTabText(self.g_astro_tabwidget.indexOf(self.g_astrocards_tab), _translate("ManageEntity", "Cards"))
        item = self.g_astrooffers_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Title"))
        item = self.g_astrooffers_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Type"))
        item = self.g_astrooffers_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Amount"))
        item = self.g_astrooffers_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Expires On"))
        self.g_astro_tabwidget.setTabText(self.g_astro_tabwidget.indexOf(self.g_astrooffers_tab), _translate("ManageEntity", "Offer Rewards"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_astroinfo_widget), _translate("ManageEntity", "Astro Loyalty"))
        self.g_dmmsg_label.setText(_translate("ManageEntity", "This customer is not linked with any PETZ account. Click \"Link\" to enroll this customer."))
        self.g_dmunlink_button.setText(_translate("ManageEntity", "Unlink"))
        self.g_dmlink_toolbutton.setText(_translate("ManageEntity", "..."))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_dminfo_widget), _translate("ManageEntity", "PETZ"))
        self.label_6.setText(_translate("ManageEntity", "Saved Payments are only available with full version of the POS."))
        self.g_savedcards_table.setSortingEnabled(True)
        item = self.g_savedcards_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageEntity", "Name"))
        item = self.g_savedcards_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageEntity", "Cardholder"))
        item = self.g_savedcards_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageEntity", "Card"))
        item = self.g_savedcards_table.horizontalHeaderItem(3)
        item.setText(_translate("ManageEntity", "Expiry"))
        self.g_delete_card_button.setText(_translate("ManageEntity", "Remove Card"))
        self.g_savecard_button.setText(_translate("ManageEntity", "Save a Card"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_savedcards_widget), _translate("ManageEntity", "Saved Payments"))
        self.g_cancel_button.setText(_translate("ManageEntity", " Cancel"))
        self.g_save_button.setText(_translate("ManageEntity", " Save"))
from po_lib.checkcombobox import PyCheckCombobox
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageEntity = QtWidgets.QDialog()
    ui = Ui_ManageEntity()
    ui.setupUi(ManageEntity)
    ManageEntity.show()
    sys.exit(app.exec_())
