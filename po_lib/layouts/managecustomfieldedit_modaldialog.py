# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managecustomfieldedit_modaldialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ManageCustomFieldEdit(object):
    def setupUi(self, ManageCustomFieldEdit):
        ManageCustomFieldEdit.setObjectName("ManageCustomFieldEdit")
        ManageCustomFieldEdit.resize(490, 590)
        ManageCustomFieldEdit.setMinimumSize(QtCore.QSize(0, 590))
        ManageCustomFieldEdit.setMaximumSize(QtCore.QSize(769, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        ManageCustomFieldEdit.setFont(font)
        ManageCustomFieldEdit.setStyleSheet("#ManageCustomFieldEdit {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"QCheckBox {\n"
"padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"QListWidget::item\n"
"{\n"
"    background: rgb(255,255,255); \n"
"}\n"
"\n"
"QListWidget::item:selected\n"
"{\n"
"    background: rgb(128,128,255);\n"
"    color: #000000\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    background-image: url(:/pet_tracker/drop-down-arrow);\n"
"}\n"
"\n"
"QDateTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
" \n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/pet_tracker/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
".QListWidget { \n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    padding: 1px;\n"
"}    \n"
"\n"
".QListWidget::item {\n"
"    padding-bottom:1px;\n"
"    padding-right:1px;\n"
"}\n"
"\n"
"QListWidget::item:selected\n"
"{\n"
"    background: #f2f2f2;\n"
"}\n"
"\n"
".QListWidget::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"PyCheckComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"PyCheckComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    /*background-color: #000;*/\n"
"}\n"
"\n"
"PyCheckComboBox QAbstractItemView::item {\n"
"     margin-top: 5px;\n"
"}\n"
"\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"\n"
"#g_delete_button,\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_delete_button:pressed,\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_add_button,\n"
"#g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_add_button:pressed,\n"
"#g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_value_checkcombobox::drop-down,\n"
"#g_attribute_combobox::drop-down,\n"
"#g_value_combobox::drop-down {    \n"
"    background-image: url(:/pet_tracker/drop-down-arrow);\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_attribute_label_3,\n"
"#g_classification_label,\n"
"#g_value_label_6,\n"
"#g_value_label_9,\n"
"#g_value_label_2,\n"
"#g_value_label_3,\n"
"#g_value_label_8,\n"
"#g_value_label_4,\n"
"#g_value_label_3,\n"
"#g_value_label_5,\n"
"#g_list_label,\n"
"#label {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_customname_lineEdit,\n"
"#g_customfield_combobox,\n"
"#g_value_combobox,\n"
"#g_value_lineEdit,\n"
"#g_valuestart_lineEdit,\n"
"#g_valueend_lineEdit,\n"
"#g_value_timeedit,\n"
"#g_value_plaintextedit,\n"
"#g_list_listWidget,\n"
"#g_instruction_label, \n"
"#g_pagenav_frame QLabel, \n"
"#g_pagenav_frame QLineEdit {\n"
"    font-size: 11px;\n"
"}")
        self.verticalLayout = QtWidgets.QVBoxLayout(ManageCustomFieldEdit)
        self.verticalLayout.setContentsMargins(9, 0, 9, 9)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_progress_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.verticalLayout.addWidget(self.g_progress_frame)
        self.g_titlepagenav_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 33))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 33))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_30 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_30.setContentsMargins(0, 2, 0, 0)
        self.horizontalLayout_30.setSpacing(6)
        self.horizontalLayout_30.setObjectName("horizontalLayout_30")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout_23 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_23.setObjectName("verticalLayout_23")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_23.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_23.addItem(spacerItem)
        self.horizontalLayout_30.addWidget(self.g_title_frame)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout.setSpacing(6)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout.addItem(spacerItem1)
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/pet_tracker/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/pet_tracker/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagenav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_slash_label.setObjectName("g_slash_label")
        self.g_pagenav_hlayout.addWidget(self.g_slash_label)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout.addWidget(self.g_totalpages_label)
        self.horizontalLayout_30.addWidget(self.g_pagenav_frame)
        self.verticalLayout.addWidget(self.g_titlepagenav_frame)
        self.g_customename_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        self.g_customename_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_customename_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_customename_frame.setObjectName("g_customename_frame")
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout(self.g_customename_frame)
        self.horizontalLayout_14.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_14.setSpacing(6)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.g_name_label = QtWidgets.QLabel(self.g_customename_frame)
        self.g_name_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_name_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_name_label.setFont(font)
        self.g_name_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_name_label.setObjectName("g_name_label")
        self.horizontalLayout_14.addWidget(self.g_name_label)
        self.g_customname_lineEdit = QtWidgets.QLineEdit(self.g_customename_frame)
        self.g_customname_lineEdit.setMinimumSize(QtCore.QSize(350, 0))
        self.g_customname_lineEdit.setObjectName("g_customname_lineEdit")
        self.horizontalLayout_14.addWidget(self.g_customname_lineEdit)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem2)
        self.verticalLayout.addWidget(self.g_customename_frame)
        self.g_customfield_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_customfield_frame.sizePolicy().hasHeightForWidth())
        self.g_customfield_frame.setSizePolicy(sizePolicy)
        self.g_customfield_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_customfield_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_customfield_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_customfield_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_customfield_frame.setObjectName("g_customfield_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_customfield_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(6)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_classification_label = QtWidgets.QLabel(self.g_customfield_frame)
        self.g_classification_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_classification_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_classification_label.setFont(font)
        self.g_classification_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_classification_label.setObjectName("g_classification_label")
        self.horizontalLayout_8.addWidget(self.g_classification_label)
        self.g_customfield_combobox = QtWidgets.QComboBox(self.g_customfield_frame)
        self.g_customfield_combobox.setMinimumSize(QtCore.QSize(350, 0))
        self.g_customfield_combobox.setEditable(True)
        self.g_customfield_combobox.setProperty("qp_prodattr_name", "")
        self.g_customfield_combobox.setObjectName("g_customfield_combobox")
        self.horizontalLayout_8.addWidget(self.g_customfield_combobox)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_8.addItem(spacerItem3)
        self.verticalLayout.addWidget(self.g_customfield_frame)
        self.g_instruction_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        self.g_instruction_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_instruction_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_instruction_frame.setObjectName("g_instruction_frame")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.g_instruction_frame)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setSpacing(6)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        spacerItem4 = QtWidgets.QSpacerItem(117, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_10.addItem(spacerItem4)
        self.g_instruction_label = QtWidgets.QLabel(self.g_instruction_frame)
        self.g_instruction_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_instruction_label.setObjectName("g_instruction_label")
        self.horizontalLayout_10.addWidget(self.g_instruction_label)
        spacerItem5 = QtWidgets.QSpacerItem(58, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_10.addItem(spacerItem5)
        self.verticalLayout.addWidget(self.g_instruction_frame)
        self.g_combobox_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_combobox_frame.sizePolicy().hasHeightForWidth())
        self.g_combobox_frame.setSizePolicy(sizePolicy)
        self.g_combobox_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_combobox_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_combobox_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_combobox_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_combobox_frame.setObjectName("g_combobox_frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_combobox_frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setSpacing(6)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_value_label_6 = QtWidgets.QLabel(self.g_combobox_frame)
        self.g_value_label_6.setMinimumSize(QtCore.QSize(110, 0))
        self.g_value_label_6.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_value_label_6.setFont(font)
        self.g_value_label_6.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_value_label_6.setObjectName("g_value_label_6")
        self.horizontalLayout_7.addWidget(self.g_value_label_6)
        self.g_value_combobox = QtWidgets.QComboBox(self.g_combobox_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_value_combobox.sizePolicy().hasHeightForWidth())
        self.g_value_combobox.setSizePolicy(sizePolicy)
        self.g_value_combobox.setMinimumSize(QtCore.QSize(350, 31))
        self.g_value_combobox.setEditable(True)
        self.g_value_combobox.setCurrentText("")
        self.g_value_combobox.setProperty("qp_prodattr_value", "")
        self.g_value_combobox.setObjectName("g_value_combobox")
        self.horizontalLayout_7.addWidget(self.g_value_combobox)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem6)
        self.verticalLayout.addWidget(self.g_combobox_frame)
        self.g_checkcombobox_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        self.g_checkcombobox_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_checkcombobox_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_checkcombobox_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_checkcombobox_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_checkcombobox_frame.setObjectName("g_checkcombobox_frame")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.g_checkcombobox_frame)
        self.horizontalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_12.setSpacing(6)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.g_value_label_9 = QtWidgets.QLabel(self.g_checkcombobox_frame)
        self.g_value_label_9.setMinimumSize(QtCore.QSize(110, 0))
        self.g_value_label_9.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_value_label_9.setFont(font)
        self.g_value_label_9.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_value_label_9.setObjectName("g_value_label_9")
        self.horizontalLayout_12.addWidget(self.g_value_label_9)
        self.g_value_checkcombobox = PyCheckCombobox(self.g_checkcombobox_frame)
        self.g_value_checkcombobox.setMinimumSize(QtCore.QSize(350, 31))
        self.g_value_checkcombobox.setToolTip("")
        self.g_value_checkcombobox.setWhatsThis("")
        self.g_value_checkcombobox.setObjectName("g_value_checkcombobox")
        self.horizontalLayout_12.addWidget(self.g_value_checkcombobox)
        spacerItem7 = QtWidgets.QSpacerItem(94, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_12.addItem(spacerItem7)
        self.verticalLayout.addWidget(self.g_checkcombobox_frame)
        self.g_lineedit_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        self.g_lineedit_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_lineedit_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_lineedit_frame.setObjectName("g_lineedit_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_lineedit_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setSpacing(6)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_line_label = QtWidgets.QLabel(self.g_lineedit_frame)
        self.g_line_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_line_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_line_label.setFont(font)
        self.g_line_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_line_label.setObjectName("g_line_label")
        self.horizontalLayout_3.addWidget(self.g_line_label)
        self.g_value_lineEdit = QtWidgets.QLineEdit(self.g_lineedit_frame)
        self.g_value_lineEdit.setMinimumSize(QtCore.QSize(350, 0))
        self.g_value_lineEdit.setObjectName("g_value_lineEdit")
        self.horizontalLayout_3.addWidget(self.g_value_lineEdit)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem8)
        self.verticalLayout.addWidget(self.g_lineedit_frame)
        self.g_lineeditrange_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        self.g_lineeditrange_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_lineeditrange_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_lineeditrange_frame.setObjectName("g_lineeditrange_frame")
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout(self.g_lineeditrange_frame)
        self.horizontalLayout_15.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_15.setSpacing(6)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.g_minvalue_label = QtWidgets.QLabel(self.g_lineeditrange_frame)
        self.g_minvalue_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_minvalue_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_minvalue_label.setFont(font)
        self.g_minvalue_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_minvalue_label.setObjectName("g_minvalue_label")
        self.horizontalLayout_15.addWidget(self.g_minvalue_label)
        self.g_valuestart_lineEdit = QtWidgets.QLineEdit(self.g_lineeditrange_frame)
        self.g_valuestart_lineEdit.setMinimumSize(QtCore.QSize(129, 0))
        self.g_valuestart_lineEdit.setMaximumSize(QtCore.QSize(129, 16777215))
        self.g_valuestart_lineEdit.setObjectName("g_valuestart_lineEdit")
        self.horizontalLayout_15.addWidget(self.g_valuestart_lineEdit)
        self.g_maxvalue_label = QtWidgets.QLabel(self.g_lineeditrange_frame)
        self.g_maxvalue_label.setMinimumSize(QtCore.QSize(80, 0))
        self.g_maxvalue_label.setMaximumSize(QtCore.QSize(80, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_maxvalue_label.setFont(font)
        self.g_maxvalue_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_maxvalue_label.setObjectName("g_maxvalue_label")
        self.horizontalLayout_15.addWidget(self.g_maxvalue_label)
        self.g_valueend_lineEdit = QtWidgets.QLineEdit(self.g_lineeditrange_frame)
        self.g_valueend_lineEdit.setMinimumSize(QtCore.QSize(129, 0))
        self.g_valueend_lineEdit.setMaximumSize(QtCore.QSize(129, 16777215))
        self.g_valueend_lineEdit.setObjectName("g_valueend_lineEdit")
        self.horizontalLayout_15.addWidget(self.g_valueend_lineEdit)
        spacerItem9 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_15.addItem(spacerItem9)
        self.verticalLayout.addWidget(self.g_lineeditrange_frame)
        self.g_date_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        self.g_date_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_date_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_date_frame.setObjectName("g_date_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_date_frame)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setSpacing(6)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_date_label = QtWidgets.QLabel(self.g_date_frame)
        self.g_date_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_date_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_date_label.setFont(font)
        self.g_date_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_date_label.setObjectName("g_date_label")
        self.horizontalLayout_5.addWidget(self.g_date_label)
        self.g_value_timeedit = QtWidgets.QDateEdit(self.g_date_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_value_timeedit.sizePolicy().hasHeightForWidth())
        self.g_value_timeedit.setSizePolicy(sizePolicy)
        self.g_value_timeedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_value_timeedit.setMaximumSize(QtCore.QSize(150, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_value_timeedit.setFont(font)
        self.g_value_timeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_value_timeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 6), QtCore.QTime(16, 0, 0)))
        self.g_value_timeedit.setCalendarPopup(True)
        self.g_value_timeedit.setObjectName("g_value_timeedit")
        self.horizontalLayout_5.addWidget(self.g_value_timeedit)
        spacerItem10 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem10)
        self.verticalLayout.addWidget(self.g_date_frame)
        self.g_multiline_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        self.g_multiline_frame.setMinimumSize(QtCore.QSize(0, 100))
        self.g_multiline_frame.setMaximumSize(QtCore.QSize(16777215, 100))
        self.g_multiline_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_multiline_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_multiline_frame.setObjectName("g_multiline_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_multiline_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(6)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_plaintext_label = QtWidgets.QLabel(self.g_multiline_frame)
        self.g_plaintext_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_plaintext_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_plaintext_label.setFont(font)
        self.g_plaintext_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_plaintext_label.setObjectName("g_plaintext_label")
        self.horizontalLayout_6.addWidget(self.g_plaintext_label)
        self.g_value_plaintextedit = QtWidgets.QPlainTextEdit(self.g_multiline_frame)
        self.g_value_plaintextedit.setMinimumSize(QtCore.QSize(350, 100))
        self.g_value_plaintextedit.setMaximumSize(QtCore.QSize(16777215, 100))
        self.g_value_plaintextedit.setObjectName("g_value_plaintextedit")
        self.horizontalLayout_6.addWidget(self.g_value_plaintextedit)
        spacerItem11 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem11)
        self.verticalLayout.addWidget(self.g_multiline_frame)
        self.g_list_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        self.g_list_frame.setMinimumSize(QtCore.QSize(0, 100))
        self.g_list_frame.setMaximumSize(QtCore.QSize(16777215, 100))
        self.g_list_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_list_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_list_frame.setObjectName("g_list_frame")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.g_list_frame)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setSpacing(6)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.g_list_label = QtWidgets.QLabel(self.g_list_frame)
        self.g_list_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_list_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_list_label.setFont(font)
        self.g_list_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_list_label.setObjectName("g_list_label")
        self.horizontalLayout_9.addWidget(self.g_list_label)
        self.g_list_listWidget = QtWidgets.QListWidget(self.g_list_frame)
        self.g_list_listWidget.setMinimumSize(QtCore.QSize(350, 0))
        self.g_list_listWidget.setObjectName("g_list_listWidget")
        self.horizontalLayout_9.addWidget(self.g_list_listWidget)
        spacerItem12 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem12)
        self.verticalLayout.addWidget(self.g_list_frame)
        self.g_radio_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        self.g_radio_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_radio_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_radio_frame.setObjectName("g_radio_frame")
        self.horizontalLayout_48 = QtWidgets.QHBoxLayout(self.g_radio_frame)
        self.horizontalLayout_48.setContentsMargins(0, 0, 0, 9)
        self.horizontalLayout_48.setSpacing(6)
        self.horizontalLayout_48.setObjectName("horizontalLayout_48")
        self.frame = QtWidgets.QFrame(self.g_radio_frame)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_2.setContentsMargins(0, 20, 0, 0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_radio_label = QtWidgets.QLabel(self.frame)
        self.g_radio_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_radio_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_radio_label.setFont(font)
        self.g_radio_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTop|QtCore.Qt.AlignTrailing)
        self.g_radio_label.setObjectName("g_radio_label")
        self.verticalLayout_2.addWidget(self.g_radio_label, 0, QtCore.Qt.AlignRight)
        self.horizontalLayout_48.addWidget(self.frame)
        self.g_radio_scrollArea = QtWidgets.QScrollArea(self.g_radio_frame)
        self.g_radio_scrollArea.setMinimumSize(QtCore.QSize(350, 0))
        self.g_radio_scrollArea.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_radio_scrollArea.setStyleSheet("QWidget {\n"
"background-color: transparent;\n"
"}\n"
"")
        self.g_radio_scrollArea.setWidgetResizable(True)
        self.g_radio_scrollArea.setObjectName("g_radio_scrollArea")
        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 350, 35))
        self.scrollAreaWidgetContents.setObjectName("scrollAreaWidgetContents")
        self.g_radio_scrollArea.setWidget(self.scrollAreaWidgetContents)
        self.horizontalLayout_48.addWidget(self.g_radio_scrollArea)
        spacerItem13 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_48.addItem(spacerItem13)
        self.verticalLayout.addWidget(self.g_radio_frame)
        self.g_edit_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        self.g_edit_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_edit_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_edit_frame.setObjectName("g_edit_frame")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_edit_frame)
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.verticalLayout.addWidget(self.g_edit_frame)
        self.g_nofields_label = QtWidgets.QLabel(ManageCustomFieldEdit)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_nofields_label.sizePolicy().hasHeightForWidth())
        self.g_nofields_label.setSizePolicy(sizePolicy)
        self.g_nofields_label.setMinimumSize(QtCore.QSize(0, 22))
        self.g_nofields_label.setMaximumSize(QtCore.QSize(16777215, 22))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_nofields_label.setFont(font)
        self.g_nofields_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_nofields_label.setObjectName("g_nofields_label")
        self.verticalLayout.addWidget(self.g_nofields_label)
        spacerItem14 = QtWidgets.QSpacerItem(20, 5, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem14)
        spacerItem15 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem15)
        self.__g_bottombar_frame = QtWidgets.QFrame(ManageCustomFieldEdit)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_bottombar_frame.setSizePolicy(sizePolicy)
        self.__g_bottombar_frame.setMinimumSize(QtCore.QSize(247, 39))
        self.__g_bottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_bottombar_frame.setObjectName("__g_bottombar_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.__g_bottombar_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setSpacing(6)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        spacerItem16 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem16)
        self.g_cancel_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/pet_tracker/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_2.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/pet_tracker/check"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_2.addWidget(self.g_save_button)
        self.verticalLayout.addWidget(self.__g_bottombar_frame)
        self.g_progress_frame.raise_()
        self.g_titlepagenav_frame.raise_()
        self.g_combobox_frame.raise_()
        self.__g_bottombar_frame.raise_()
        self.g_lineedit_frame.raise_()
        self.g_date_frame.raise_()
        self.g_multiline_frame.raise_()
        self.g_edit_frame.raise_()
        self.g_checkcombobox_frame.raise_()
        self.g_customename_frame.raise_()
        self.g_lineeditrange_frame.raise_()
        self.g_customfield_frame.raise_()
        self.g_list_frame.raise_()
        self.g_radio_frame.raise_()
        self.g_instruction_frame.raise_()
        self.g_nofields_label.raise_()

        self.retranslateUi(ManageCustomFieldEdit)
        QtCore.QMetaObject.connectSlotsByName(ManageCustomFieldEdit)

    def retranslateUi(self, ManageCustomFieldEdit):
        _translate = QtCore.QCoreApplication.translate
        ManageCustomFieldEdit.setWindowTitle(_translate("ManageCustomFieldEdit", "Manage Custom Field"))
        self.g_main_label.setText(_translate("ManageCustomFieldEdit", "Manage Custom Field"))
        self.g_previouspage_label.setToolTip(_translate("ManageCustomFieldEdit", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("ManageCustomFieldEdit", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("ManageCustomFieldEdit", "999"))
        self.g_slash_label.setText(_translate("ManageCustomFieldEdit", "/"))
        self.g_totalpages_label.setToolTip(_translate("ManageCustomFieldEdit", "Total Pages"))
        self.g_totalpages_label.setText(_translate("ManageCustomFieldEdit", "999"))
        self.g_name_label.setText(_translate("ManageCustomFieldEdit", "Field Name"))
        self.g_classification_label.setText(_translate("ManageCustomFieldEdit", "Custom Field"))
        self.g_instruction_label.setText(_translate("ManageCustomFieldEdit", "TextLabel"))
        self.g_value_label_6.setText(_translate("ManageCustomFieldEdit", "Value"))
        self.g_value_label_9.setText(_translate("ManageCustomFieldEdit", "Value"))
        self.g_line_label.setText(_translate("ManageCustomFieldEdit", "Value"))
        self.g_minvalue_label.setText(_translate("ManageCustomFieldEdit", "Min Value"))
        self.g_maxvalue_label.setText(_translate("ManageCustomFieldEdit", "Max Value"))
        self.g_date_label.setText(_translate("ManageCustomFieldEdit", "Date"))
        self.g_plaintext_label.setText(_translate("ManageCustomFieldEdit", "Value"))
        self.g_list_label.setText(_translate("ManageCustomFieldEdit", "Value"))
        self.g_radio_label.setText(_translate("ManageCustomFieldEdit", "Value"))
        self.g_nofields_label.setText(_translate("ManageCustomFieldEdit", "No Customs Fields have been created.  Go to Settings to add Custom Fields."))
        self.g_cancel_button.setText(_translate("ManageCustomFieldEdit", " Cancel"))
        self.g_save_button.setText(_translate("ManageCustomFieldEdit", " Save"))
from po_lib.checkcombobox import PyCheckCombobox
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageCustomFieldEdit = QtWidgets.QDialog()
    ui = Ui_ManageCustomFieldEdit()
    ui.setupUi(ManageCustomFieldEdit)
    ManageCustomFieldEdit.show()
    sys.exit(app.exec_())
