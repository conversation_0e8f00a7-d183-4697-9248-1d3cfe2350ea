# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'manage_entity_address_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ManageEntityAddressDialog(object):
    def setupUi(self, ManageEntityAddressDialog):
        ManageEntityAddressDialog.setObjectName("ManageEntityAddressDialog")
        ManageEntityAddressDialog.resize(703, 481)
        ManageEntityAddressDialog.setMinimumSize(QtCore.QSize(0, 450))
        ManageEntityAddressDialog.setMaximumSize(QtCore.QSize(16777215, 16777215))
        ManageEntityAddressDialog.setStyleSheet("#ManageEntityAddressDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"QLabel {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"#g_main_label {\n"
"    font: 17px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"QFrame {\n"
"    border:0px;\n"
"    margin-top: 0px;\n"
"    margin-right: 0px;\n"
"    margin-bottom: 0px;\n"
"    margin-left: 0px;\n"
"    spacing: 0px;\n"
"    padding: 0px;\n"
"}\n"
"\n"
"QCheckBox {\n"
"    padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-15px; \n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/entity_dialog/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/entity_dialog/checked\');\n"
"    border:0px;\n"
"} \n"
"\n"
"QDateTimeEdit, QTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
" \n"
"QDateTimeEdit::drop-down, QTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"\n"
".QListWidget {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    padding: 2px;\n"
"    background: transparent;\n"
"}\n"
"\n"
"#g_mfg_images_list,\n"
"#g_phone_listWidget,\n"
"#g_email_listWidget {\n"
"    border: 0px;\n"
"    background: transparent;\n"
"}\n"
"\n"
".QListWidget::item {\n"
"    padding-bottom:1px;\n"
"    padding-right:1px;\n"
"}\n"
"\n"
".QListWidget::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
".QListWidget::item::selected {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    color: #000000\n"
"}\n"
"\n"
".QListWidget::indicator:unchecked {\n"
"    image: url(\':/entity_dialog/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
".QListWidget::indicator:checked {\n"
"    image: url(\':/entity_dialog/checked\');\n"
"    border:0px;\n"
"} \n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"QFontComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QFontComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    /*background-color: #000;*/\n"
"}\n"
"\n"
"PyCheckComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"PyCheckComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    /*background-color: #000;*/\n"
"}\n"
"\n"
"PyCheckComboBox QAbstractItemView::item {\n"
"     margin-top: 5px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QSpinBox {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px;\n"
"}\n"
"\n"
".QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-size: 13px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QTabBar::scroller {width: 48px;}\n"
"\n"
"QTabWidget::tab-bar {\n"
"   left: 0;\n"
"}\n"
"\n"
"QTabWidget {\n"
"    border:0px;\n"
"}\n"
"\n"
"#g_settings_tabs QTabWidget::tab-bar {\n"
"     left: 10px; \n"
" }\n"
"\n"
"QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"QRadioButton {\n"
"    font-size: 11px;\n"
"}\n"
"    \n"
"     #g_cancel_button,\n"
"       #g_delete_card_button {\n"
"       background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"       border:2px solid #c80319;\n"
"       }\n"
"\n"
"     #g_cancel_button:pressed,\n"
"       #g_delete_card_button:pressed {\n"
"       background: #c80319\n"
"       }\n"
"\n"
"       #g_save_button {\n"
"       background-color: #009c00;\n"
"       border:2px solid #007f00;\n"
"       }\n"
"\n"
"           #g_save_button:pressed {\n"
"       background-color: #007f00;\n"
"       }\n"
"\n"
"       #g_contactinfo_widget,\n"
"       #g_notes_widget\n"
"       {\n"
"       border:1px solid #073c83;\n"
"       border-radius: 5px;\n"
"       border-top-left-radius:0px;\n"
"       background-color: #f4f4f4;\n"
"       }\n"
"\n"
"       #g_groups_list {\n"
"       background-color: #007f00;\n"
"       border:0px;\n"
"       }\n"
"\n"
"\n"
"#g_location_checkcombobox::drop-down,\n"
"#g_state_combobox::drop-down,\n"
"#g_emailtype_combobox::drop-down,\n"
"#g_defaultlocation_combobox::drop-down,\n"
"#g_country_combobox::drop-down {    \n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"}\n"
"\n"
"#g_default_taxes_list {\n"
"    border:0px solid #073c83;\n"
"}\n"
"\n"
"#g_default_taxes_list::item {   \n"
"    padding-top: 5px; \n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_totalpages_label {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_selectgroupbox_label,\n"
"#g_entity_label,\n"
"#g_business_label,\n"
"#g_location_label,\n"
"#g_locationid_label,\n"
"#g_entity_label_7 {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_contactinfo_widget QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QTabWidget QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"/* Notes tab */\n"
"#g_warning_label, #g_info_label {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"\n"
"QListWidget {\n"
"    font-size: 12px;\n"
"\n"
"}\n"
"\n"
"")
        self.verticalLayout_22 = QtWidgets.QVBoxLayout(ManageEntityAddressDialog)
        self.verticalLayout_22.setContentsMargins(9, 0, 9, 9)
        self.verticalLayout_22.setSpacing(6)
        self.verticalLayout_22.setObjectName("verticalLayout_22")
        self.g_top_frame = QtWidgets.QFrame(ManageEntityAddressDialog)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setMaximumSize(QtCore.QSize(16777215, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_66 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_66.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_66.setSpacing(0)
        self.horizontalLayout_66.setObjectName("horizontalLayout_66")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_66.addWidget(self.g_progress_frame)
        self.verticalLayout_22.addWidget(self.g_top_frame)
        self.g_titlepagenav_frame = QtWidgets.QFrame(ManageEntityAddressDialog)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_30 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_30.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_30.setSpacing(6)
        self.horizontalLayout_30.setObjectName("horizontalLayout_30")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout_23 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_23.setObjectName("verticalLayout_23")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_23.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_23.addItem(spacerItem)
        self.horizontalLayout_30.addWidget(self.g_title_frame)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout.setSpacing(6)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout.addItem(spacerItem1)
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/entity_dialog/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagenav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_slash_label.setObjectName("g_slash_label")
        self.g_pagenav_hlayout.addWidget(self.g_slash_label)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout.addWidget(self.g_totalpages_label)
        self.horizontalLayout_30.addWidget(self.g_pagenav_frame)
        self.verticalLayout_22.addWidget(self.g_titlepagenav_frame)
        self.g_name_type_frame = QtWidgets.QFrame(ManageEntityAddressDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_name_type_frame.sizePolicy().hasHeightForWidth())
        self.g_name_type_frame.setSizePolicy(sizePolicy)
        self.g_name_type_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_name_type_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_name_type_frame.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.g_name_type_frame.setObjectName("g_name_type_frame")
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout(self.g_name_type_frame)
        self.horizontalLayout_17.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_17.setSpacing(6)
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        self.g_name_label = QtWidgets.QLabel(self.g_name_type_frame)
        self.g_name_label.setMinimumSize(QtCore.QSize(95, 31))
        self.g_name_label.setMaximumSize(QtCore.QSize(95, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_name_label.setFont(font)
        self.g_name_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_name_label.setObjectName("g_name_label")
        self.horizontalLayout_17.addWidget(self.g_name_label)
        self.g_name_lineedit = QtWidgets.QLineEdit(self.g_name_type_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_name_lineedit.sizePolicy().hasHeightForWidth())
        self.g_name_lineedit.setSizePolicy(sizePolicy)
        self.g_name_lineedit.setMinimumSize(QtCore.QSize(248, 31))
        self.g_name_lineedit.setMaximumSize(QtCore.QSize(248, 31))
        font = QtGui.QFont()
        self.g_name_lineedit.setFont(font)
        self.g_name_lineedit.setText("")
        self.g_name_lineedit.setReadOnly(False)
        self.g_name_lineedit.setProperty("qp_epn_names_before_key_names", "")
        self.g_name_lineedit.setObjectName("g_name_lineedit")
        self.horizontalLayout_17.addWidget(self.g_name_lineedit)
        self.g_state_label_2 = QtWidgets.QLabel(self.g_name_type_frame)
        self.g_state_label_2.setMinimumSize(QtCore.QSize(70, 0))
        self.g_state_label_2.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_state_label_2.setFont(font)
        self.g_state_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_state_label_2.setObjectName("g_state_label_2")
        self.horizontalLayout_17.addWidget(self.g_state_label_2)
        self.g_type_combobox = QtWidgets.QComboBox(self.g_name_type_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_type_combobox.sizePolicy().hasHeightForWidth())
        self.g_type_combobox.setSizePolicy(sizePolicy)
        self.g_type_combobox.setMinimumSize(QtCore.QSize(110, 31))
        self.g_type_combobox.setMaximumSize(QtCore.QSize(110, 31))
        self.g_type_combobox.setEditable(False)
        self.g_type_combobox.setProperty("qp_addr_region_id", "")
        self.g_type_combobox.setObjectName("g_type_combobox")
        self.horizontalLayout_17.addWidget(self.g_type_combobox)
        spacerItem2 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_17.addItem(spacerItem2)
        self.verticalLayout_22.addWidget(self.g_name_type_frame)
        self.g_entity_frame = QtWidgets.QFrame(ManageEntityAddressDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_entity_frame.sizePolicy().hasHeightForWidth())
        self.g_entity_frame.setSizePolicy(sizePolicy)
        self.g_entity_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_entity_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_entity_frame.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.g_entity_frame.setObjectName("g_entity_frame")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout(self.g_entity_frame)
        self.horizontalLayout_16.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_16.setSpacing(6)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.g_entity_label = QtWidgets.QLabel(self.g_entity_frame)
        self.g_entity_label.setMinimumSize(QtCore.QSize(95, 31))
        self.g_entity_label.setMaximumSize(QtCore.QSize(95, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_entity_label.setFont(font)
        self.g_entity_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entity_label.setObjectName("g_entity_label")
        self.horizontalLayout_16.addWidget(self.g_entity_label)
        self.g_entityfirst_lineedit = QtWidgets.QLineEdit(self.g_entity_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_entityfirst_lineedit.sizePolicy().hasHeightForWidth())
        self.g_entityfirst_lineedit.setSizePolicy(sizePolicy)
        self.g_entityfirst_lineedit.setMinimumSize(QtCore.QSize(248, 31))
        self.g_entityfirst_lineedit.setMaximumSize(QtCore.QSize(248, 31))
        font = QtGui.QFont()
        self.g_entityfirst_lineedit.setFont(font)
        self.g_entityfirst_lineedit.setReadOnly(False)
        self.g_entityfirst_lineedit.setProperty("qp_epn_names_before_key_names", "")
        self.g_entityfirst_lineedit.setObjectName("g_entityfirst_lineedit")
        self.horizontalLayout_16.addWidget(self.g_entityfirst_lineedit)
        self.g_entity_label_3 = QtWidgets.QLabel(self.g_entity_frame)
        self.g_entity_label_3.setMinimumSize(QtCore.QSize(70, 31))
        self.g_entity_label_3.setMaximumSize(QtCore.QSize(70, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_entity_label_3.setFont(font)
        self.g_entity_label_3.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_entity_label_3.setObjectName("g_entity_label_3")
        self.horizontalLayout_16.addWidget(self.g_entity_label_3)
        self.g_entitylast_lineedit = QtWidgets.QLineEdit(self.g_entity_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_entitylast_lineedit.sizePolicy().hasHeightForWidth())
        self.g_entitylast_lineedit.setSizePolicy(sizePolicy)
        self.g_entitylast_lineedit.setMinimumSize(QtCore.QSize(248, 31))
        self.g_entitylast_lineedit.setMaximumSize(QtCore.QSize(248, 31))
        font = QtGui.QFont()
        self.g_entitylast_lineedit.setFont(font)
        self.g_entitylast_lineedit.setReadOnly(False)
        self.g_entitylast_lineedit.setProperty("qp_epn_key_names", "")
        self.g_entitylast_lineedit.setObjectName("g_entitylast_lineedit")
        self.horizontalLayout_16.addWidget(self.g_entitylast_lineedit)
        spacerItem3 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_16.addItem(spacerItem3)
        self.verticalLayout_22.addWidget(self.g_entity_frame)
        self.g_businessname_frame = QtWidgets.QFrame(ManageEntityAddressDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_businessname_frame.sizePolicy().hasHeightForWidth())
        self.g_businessname_frame.setSizePolicy(sizePolicy)
        self.g_businessname_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_businessname_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_businessname_frame.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.g_businessname_frame.setObjectName("g_businessname_frame")
        self.horizontalLayout_19 = QtWidgets.QHBoxLayout(self.g_businessname_frame)
        self.horizontalLayout_19.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_19.setSpacing(6)
        self.horizontalLayout_19.setObjectName("horizontalLayout_19")
        self.g_businessname_label = QtWidgets.QLabel(self.g_businessname_frame)
        self.g_businessname_label.setMinimumSize(QtCore.QSize(95, 31))
        self.g_businessname_label.setMaximumSize(QtCore.QSize(95, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_businessname_label.setFont(font)
        self.g_businessname_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_businessname_label.setObjectName("g_businessname_label")
        self.horizontalLayout_19.addWidget(self.g_businessname_label)
        self.g_businessname_lineedit = QtWidgets.QLineEdit(self.g_businessname_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_businessname_lineedit.sizePolicy().hasHeightForWidth())
        self.g_businessname_lineedit.setSizePolicy(sizePolicy)
        self.g_businessname_lineedit.setMinimumSize(QtCore.QSize(248, 31))
        self.g_businessname_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        font = QtGui.QFont()
        self.g_businessname_lineedit.setFont(font)
        self.g_businessname_lineedit.setText("")
        self.g_businessname_lineedit.setReadOnly(False)
        self.g_businessname_lineedit.setProperty("qp_epn_names_before_key_names", "")
        self.g_businessname_lineedit.setObjectName("g_businessname_lineedit")
        self.horizontalLayout_19.addWidget(self.g_businessname_lineedit)
        self.verticalLayout_22.addWidget(self.g_businessname_frame)
        self.g_manageentity_tabwidget = QtWidgets.QTabWidget(ManageEntityAddressDialog)
        self.g_manageentity_tabwidget.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Maximum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_manageentity_tabwidget.sizePolicy().hasHeightForWidth())
        self.g_manageentity_tabwidget.setSizePolicy(sizePolicy)
        self.g_manageentity_tabwidget.setMinimumSize(QtCore.QSize(0, 235))
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        font.setKerning(True)
        self.g_manageentity_tabwidget.setFont(font)
        self.g_manageentity_tabwidget.setAutoFillBackground(False)
        self.g_manageentity_tabwidget.setTabPosition(QtWidgets.QTabWidget.North)
        self.g_manageentity_tabwidget.setTabShape(QtWidgets.QTabWidget.Rounded)
        self.g_manageentity_tabwidget.setIconSize(QtCore.QSize(50, 50))
        self.g_manageentity_tabwidget.setElideMode(QtCore.Qt.ElideNone)
        self.g_manageentity_tabwidget.setUsesScrollButtons(True)
        self.g_manageentity_tabwidget.setDocumentMode(False)
        self.g_manageentity_tabwidget.setTabsClosable(False)
        self.g_manageentity_tabwidget.setMovable(False)
        self.g_manageentity_tabwidget.setObjectName("g_manageentity_tabwidget")
        self.g_contactinfo_widget = QtWidgets.QWidget()
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.g_contactinfo_widget.setFont(font)
        self.g_contactinfo_widget.setStyleSheet("")
        self.g_contactinfo_widget.setObjectName("g_contactinfo_widget")
        self.horizontalLayout_47 = QtWidgets.QHBoxLayout(self.g_contactinfo_widget)
        self.horizontalLayout_47.setObjectName("horizontalLayout_47")
        self.frame_5 = QtWidgets.QFrame(self.g_contactinfo_widget)
        self.frame_5.setMinimumSize(QtCore.QSize(650, 0))
        self.frame_5.setMaximumSize(QtCore.QSize(650, 16777215))
        self.frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_5.setObjectName("frame_5")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frame_5)
        self.verticalLayout.setContentsMargins(0, 9, 0, 0)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_addresscontainer_frame = QtWidgets.QFrame(self.frame_5)
        self.g_addresscontainer_frame.setMinimumSize(QtCore.QSize(64, 68))
        self.g_addresscontainer_frame.setMaximumSize(QtCore.QSize(16777215, 68))
        self.g_addresscontainer_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_addresscontainer_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_addresscontainer_frame.setObjectName("g_addresscontainer_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_addresscontainer_frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setSpacing(6)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_address_label = QtWidgets.QLabel(self.g_addresscontainer_frame)
        self.g_address_label.setMinimumSize(QtCore.QSize(65, 28))
        self.g_address_label.setMaximumSize(QtCore.QSize(65, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_address_label.setFont(font)
        self.g_address_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_address_label.setObjectName("g_address_label")
        self.horizontalLayout_4.addWidget(self.g_address_label, 0, QtCore.Qt.AlignHCenter|QtCore.Qt.AlignVCenter)
        self.g_address_frame = QtWidgets.QFrame(self.g_addresscontainer_frame)
        self.g_address_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_address_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_address_frame.setObjectName("g_address_frame")
        self.verticalLayout_18 = QtWidgets.QVBoxLayout(self.g_address_frame)
        self.verticalLayout_18.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_18.setSpacing(6)
        self.verticalLayout_18.setObjectName("verticalLayout_18")
        self.g_addressline1_lineedit = QtWidgets.QLineEdit(self.g_address_frame)
        self.g_addressline1_lineedit.setMinimumSize(QtCore.QSize(443, 31))
        self.g_addressline1_lineedit.setMaximumSize(QtCore.QSize(443, 31))
        self.g_addressline1_lineedit.setReadOnly(False)
        self.g_addressline1_lineedit.setProperty("qp_addr_address_1", "")
        self.g_addressline1_lineedit.setProperty("qp_addr_address_type", "")
        self.g_addressline1_lineedit.setProperty("qp_addr_address_type_id", "")
        self.g_addressline1_lineedit.setProperty("qp_addr_rank", "")
        self.g_addressline1_lineedit.setObjectName("g_addressline1_lineedit")
        self.verticalLayout_18.addWidget(self.g_addressline1_lineedit)
        self.g_addressline2_lineedit = QtWidgets.QLineEdit(self.g_address_frame)
        self.g_addressline2_lineedit.setMinimumSize(QtCore.QSize(443, 31))
        self.g_addressline2_lineedit.setMaximumSize(QtCore.QSize(443, 31))
        self.g_addressline2_lineedit.setReadOnly(False)
        self.g_addressline2_lineedit.setProperty("qp_addr_address_2", "")
        self.g_addressline2_lineedit.setObjectName("g_addressline2_lineedit")
        self.verticalLayout_18.addWidget(self.g_addressline2_lineedit)
        self.horizontalLayout_4.addWidget(self.g_address_frame)
        spacerItem4 = QtWidgets.QSpacerItem(218, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem4)
        self.verticalLayout.addWidget(self.g_addresscontainer_frame)
        self.g_city_frame = QtWidgets.QFrame(self.frame_5)
        self.g_city_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_city_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_city_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_city_frame.setObjectName("g_city_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_city_frame)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setSpacing(6)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_city_label = QtWidgets.QLabel(self.g_city_frame)
        self.g_city_label.setMinimumSize(QtCore.QSize(65, 0))
        self.g_city_label.setMaximumSize(QtCore.QSize(65, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_city_label.setFont(font)
        self.g_city_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_city_label.setObjectName("g_city_label")
        self.horizontalLayout_5.addWidget(self.g_city_label)
        self.g_city_lineedit = QtWidgets.QLineEdit(self.g_city_frame)
        self.g_city_lineedit.setMinimumSize(QtCore.QSize(180, 31))
        self.g_city_lineedit.setMaximumSize(QtCore.QSize(180, 31))
        self.g_city_lineedit.setText("")
        self.g_city_lineedit.setReadOnly(False)
        self.g_city_lineedit.setProperty("qp_addr_city", "")
        self.g_city_lineedit.setObjectName("g_city_lineedit")
        self.horizontalLayout_5.addWidget(self.g_city_lineedit)
        self.g_state_label = QtWidgets.QLabel(self.g_city_frame)
        self.g_state_label.setMinimumSize(QtCore.QSize(60, 0))
        self.g_state_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_state_label.setFont(font)
        self.g_state_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_state_label.setObjectName("g_state_label")
        self.horizontalLayout_5.addWidget(self.g_state_label)
        self.g_state_combobox = QtWidgets.QComboBox(self.g_city_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_state_combobox.sizePolicy().hasHeightForWidth())
        self.g_state_combobox.setSizePolicy(sizePolicy)
        self.g_state_combobox.setMinimumSize(QtCore.QSize(110, 31))
        self.g_state_combobox.setMaximumSize(QtCore.QSize(110, 31))
        self.g_state_combobox.setEditable(False)
        self.g_state_combobox.setProperty("qp_addr_region_id", "")
        self.g_state_combobox.setObjectName("g_state_combobox")
        self.horizontalLayout_5.addWidget(self.g_state_combobox)
        self.g_zip_label = QtWidgets.QLabel(self.g_city_frame)
        self.g_zip_label.setMinimumSize(QtCore.QSize(64, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_zip_label.setFont(font)
        self.g_zip_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_zip_label.setObjectName("g_zip_label")
        self.horizontalLayout_5.addWidget(self.g_zip_label)
        self.g_zip_lineedit = QtWidgets.QLineEdit(self.g_city_frame)
        self.g_zip_lineedit.setMinimumSize(QtCore.QSize(119, 31))
        self.g_zip_lineedit.setMaximumSize(QtCore.QSize(119, 31))
        self.g_zip_lineedit.setText("")
        self.g_zip_lineedit.setReadOnly(False)
        self.g_zip_lineedit.setProperty("qp_addr_postal_code", "")
        self.g_zip_lineedit.setObjectName("g_zip_lineedit")
        self.horizontalLayout_5.addWidget(self.g_zip_lineedit)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem5)
        self.verticalLayout.addWidget(self.g_city_frame)
        self.g_statezipcountry_frame = QtWidgets.QFrame(self.frame_5)
        self.g_statezipcountry_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_statezipcountry_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_statezipcountry_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_statezipcountry_frame.setObjectName("g_statezipcountry_frame")
        self.horizontalLayout_18 = QtWidgets.QHBoxLayout(self.g_statezipcountry_frame)
        self.horizontalLayout_18.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_18.setSpacing(6)
        self.horizontalLayout_18.setObjectName("horizontalLayout_18")
        self.g_country_label = QtWidgets.QLabel(self.g_statezipcountry_frame)
        self.g_country_label.setMinimumSize(QtCore.QSize(65, 0))
        self.g_country_label.setMaximumSize(QtCore.QSize(65, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_country_label.setFont(font)
        self.g_country_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_country_label.setObjectName("g_country_label")
        self.horizontalLayout_18.addWidget(self.g_country_label)
        self.g_country_combobox = QtWidgets.QComboBox(self.g_statezipcountry_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_country_combobox.sizePolicy().hasHeightForWidth())
        self.g_country_combobox.setSizePolicy(sizePolicy)
        self.g_country_combobox.setMinimumSize(QtCore.QSize(180, 31))
        self.g_country_combobox.setMaximumSize(QtCore.QSize(180, 31))
        self.g_country_combobox.setProperty("qp_addr_country_id", "")
        self.g_country_combobox.setObjectName("g_country_combobox")
        self.g_country_combobox.addItem("")
        self.g_country_combobox.addItem("")
        self.g_country_combobox.addItem("")
        self.g_country_combobox.addItem("")
        self.horizontalLayout_18.addWidget(self.g_country_combobox)
        self.g_birthday_frame = QtWidgets.QFrame(self.g_statezipcountry_frame)
        self.g_birthday_frame.setMaximumSize(QtCore.QSize(177, 16777215))
        self.g_birthday_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_birthday_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_birthday_frame.setObjectName("g_birthday_frame")
        self.horizontalLayout_41 = QtWidgets.QHBoxLayout(self.g_birthday_frame)
        self.horizontalLayout_41.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_41.setObjectName("horizontalLayout_41")
        self.horizontalLayout_18.addWidget(self.g_birthday_frame)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_18.addItem(spacerItem6)
        self.verticalLayout.addWidget(self.g_statezipcountry_frame)
        spacerItem7 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem7)
        self.horizontalLayout_47.addWidget(self.frame_5)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_47.addItem(spacerItem8)
        self.g_manageentity_tabwidget.addTab(self.g_contactinfo_widget, "")
        self.verticalLayout_22.addWidget(self.g_manageentity_tabwidget)
        self.g_bottombar_frame = QtWidgets.QFrame(ManageEntityAddressDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.g_bottombar_frame.setSizePolicy(sizePolicy)
        self.g_bottombar_frame.setMinimumSize(QtCore.QSize(247, 39))
        self.g_bottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottombar_frame.setObjectName("g_bottombar_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_bottombar_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setSpacing(6)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        spacerItem9 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem9)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_bottombar_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_2.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_bottombar_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_save_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_2.addWidget(self.g_save_button)
        self.verticalLayout_22.addWidget(self.g_bottombar_frame)
        self.g_entity_label.setBuddy(self.g_entityfirst_lineedit)
        self.g_entity_label_3.setBuddy(self.g_entityfirst_lineedit)

        self.retranslateUi(ManageEntityAddressDialog)
        self.g_manageentity_tabwidget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(ManageEntityAddressDialog)
        ManageEntityAddressDialog.setTabOrder(self.g_manageentity_tabwidget, self.g_addressline1_lineedit)
        ManageEntityAddressDialog.setTabOrder(self.g_addressline1_lineedit, self.g_addressline2_lineedit)
        ManageEntityAddressDialog.setTabOrder(self.g_addressline2_lineedit, self.g_city_lineedit)
        ManageEntityAddressDialog.setTabOrder(self.g_city_lineedit, self.g_state_combobox)
        ManageEntityAddressDialog.setTabOrder(self.g_state_combobox, self.g_zip_lineedit)
        ManageEntityAddressDialog.setTabOrder(self.g_zip_lineedit, self.g_country_combobox)
        ManageEntityAddressDialog.setTabOrder(self.g_country_combobox, self.g_currentpage_lineedit)

    def retranslateUi(self, ManageEntityAddressDialog):
        _translate = QtCore.QCoreApplication.translate
        ManageEntityAddressDialog.setWindowTitle(_translate("ManageEntityAddressDialog", "Manage Entity Address"))
        self.g_main_label.setText(_translate("ManageEntityAddressDialog", "Manage Entity Address"))
        self.g_previouspage_label.setToolTip(_translate("ManageEntityAddressDialog", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("ManageEntityAddressDialog", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("ManageEntityAddressDialog", "999"))
        self.g_slash_label.setText(_translate("ManageEntityAddressDialog", "/"))
        self.g_totalpages_label.setToolTip(_translate("ManageEntityAddressDialog", "Total Pages"))
        self.g_totalpages_label.setText(_translate("ManageEntityAddressDialog", "999"))
        self.g_name_label.setText(_translate("ManageEntityAddressDialog", "Label"))
        self.g_name_lineedit.setPlaceholderText(_translate("ManageEntityAddressDialog", "Spouse, Vet, Friend, etc"))
        self.g_state_label_2.setText(_translate("ManageEntityAddressDialog", "Type"))
        self.g_entity_label.setText(_translate("ManageEntityAddressDialog", "First"))
        self.g_entityfirst_lineedit.setPlaceholderText(_translate("ManageEntityAddressDialog", "First"))
        self.g_entity_label_3.setText(_translate("ManageEntityAddressDialog", "Last"))
        self.g_entitylast_lineedit.setPlaceholderText(_translate("ManageEntityAddressDialog", "Last"))
        self.g_businessname_label.setText(_translate("ManageEntityAddressDialog", "Business"))
        self.g_businessname_lineedit.setPlaceholderText(_translate("ManageEntityAddressDialog", "Business Name"))
        self.g_address_label.setText(_translate("ManageEntityAddressDialog", "Address"))
        self.g_city_label.setText(_translate("ManageEntityAddressDialog", "City"))
        self.g_state_label.setText(_translate("ManageEntityAddressDialog", "State"))
        self.g_zip_label.setText(_translate("ManageEntityAddressDialog", "Zip Code"))
        self.g_country_label.setText(_translate("ManageEntityAddressDialog", "Country"))
        self.g_country_combobox.setItemText(0, _translate("ManageEntityAddressDialog", "US"))
        self.g_country_combobox.setItemText(1, _translate("ManageEntityAddressDialog", "UK"))
        self.g_country_combobox.setItemText(2, _translate("ManageEntityAddressDialog", "RU"))
        self.g_country_combobox.setItemText(3, _translate("ManageEntityAddressDialog", "MX"))
        self.g_manageentity_tabwidget.setTabText(self.g_manageentity_tabwidget.indexOf(self.g_contactinfo_widget), _translate("ManageEntityAddressDialog", "Contact Info"))
        self.g_cancel_button.setText(_translate("ManageEntityAddressDialog", " Cancel"))
        self.g_save_button.setText(_translate("ManageEntityAddressDialog", " Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageEntityAddressDialog = QtWidgets.QDialog()
    ui = Ui_ManageEntityAddressDialog()
    ui.setupUi(ManageEntityAddressDialog)
    ManageEntityAddressDialog.show()
    sys.exit(app.exec_())
