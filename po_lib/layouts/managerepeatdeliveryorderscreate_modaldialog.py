# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managerepeatdeliveryorderscreate_modaldialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_CreateDeliveryOrders(object):
    def setupUi(self, CreateDeliveryOrders):
        CreateDeliveryOrders.setObjectName("CreateDeliveryOrders")
        CreateDeliveryOrders.resize(601, 476)
        CreateDeliveryOrders.setMinimumSize(QtCore.QSize(601, 0))
        CreateDeliveryOrders.setMaximumSize(QtCore.QSize(769, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        CreateDeliveryOrders.setFont(font)
        CreateDeliveryOrders.setStyleSheet("#CreateDeliveryOrders {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QCheckBox {\n"
"    padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"QDateTimeEdit, QTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
" \n"
"QDateTimeEdit::drop-down, QTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"#g_cancel_button,\n"
"#g_delete_button, #__g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed,\n"
"#g_delete_button:pressed, #__g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_save_button,\n"
"#g_add_button, #__g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed,\n"
"#g_add_button:pressed, #__g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"\n"
"\n"
"")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(CreateDeliveryOrders)
        self.verticalLayout_2.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_top_frame = QtWidgets.QFrame(CreateDeliveryOrders)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_6.addWidget(self.g_progress_frame)
        self.verticalLayout_2.addWidget(self.g_top_frame)
        self.g_titlepagenav_frame = QtWidgets.QFrame(CreateDeliveryOrders)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_31 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_31.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_31.setObjectName("horizontalLayout_31")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout4 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout4.setObjectName("verticalLayout4")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout4.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout4.addItem(spacerItem)
        self.horizontalLayout_31.addWidget(self.g_title_frame)
        self.verticalLayout_2.addWidget(self.g_titlepagenav_frame)
        self.g_startdate_frame = QtWidgets.QFrame(CreateDeliveryOrders)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_startdate_frame.sizePolicy().hasHeightForWidth())
        self.g_startdate_frame.setSizePolicy(sizePolicy)
        self.g_startdate_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_startdate_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_startdate_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_startdate_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_startdate_frame.setObjectName("g_startdate_frame")
        self.horizontalLayout_42 = QtWidgets.QHBoxLayout(self.g_startdate_frame)
        self.horizontalLayout_42.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_42.setSpacing(6)
        self.horizontalLayout_42.setObjectName("horizontalLayout_42")
        self.g_startdate_label = QtWidgets.QLabel(self.g_startdate_frame)
        self.g_startdate_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_startdate_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_startdate_label.setFont(font)
        self.g_startdate_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_startdate_label.setObjectName("g_startdate_label")
        self.horizontalLayout_42.addWidget(self.g_startdate_label)
        self.g_start_datetimeedit = QtWidgets.QDateTimeEdit(self.g_startdate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_start_datetimeedit.sizePolicy().hasHeightForWidth())
        self.g_start_datetimeedit.setSizePolicy(sizePolicy)
        self.g_start_datetimeedit.setMinimumSize(QtCore.QSize(120, 31))
        self.g_start_datetimeedit.setMaximumSize(QtCore.QSize(120, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_start_datetimeedit.setFont(font)
        self.g_start_datetimeedit.setStyleSheet("")
        self.g_start_datetimeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_start_datetimeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2013, 12, 6), QtCore.QTime(0, 0, 0)))
        self.g_start_datetimeedit.setCalendarPopup(True)
        self.g_start_datetimeedit.setTimeSpec(QtCore.Qt.LocalTime)
        self.g_start_datetimeedit.setObjectName("g_start_datetimeedit")
        self.horizontalLayout_42.addWidget(self.g_start_datetimeedit)
        self.g_sales_type_frame_2 = QtWidgets.QFrame(self.g_startdate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sales_type_frame_2.sizePolicy().hasHeightForWidth())
        self.g_sales_type_frame_2.setSizePolicy(sizePolicy)
        self.g_sales_type_frame_2.setMinimumSize(QtCore.QSize(0, 0))
        self.g_sales_type_frame_2.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_sales_type_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_sales_type_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_sales_type_frame_2.setObjectName("g_sales_type_frame_2")
        self.horizontalLayout_68 = QtWidgets.QHBoxLayout(self.g_sales_type_frame_2)
        self.horizontalLayout_68.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_68.setObjectName("horizontalLayout_68")
        self.horizontalLayout_42.addWidget(self.g_sales_type_frame_2)
        self.g_sales_sumbyperiod_frame_2 = QtWidgets.QFrame(self.g_startdate_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_sales_sumbyperiod_frame_2.sizePolicy().hasHeightForWidth())
        self.g_sales_sumbyperiod_frame_2.setSizePolicy(sizePolicy)
        self.g_sales_sumbyperiod_frame_2.setMinimumSize(QtCore.QSize(0, 0))
        self.g_sales_sumbyperiod_frame_2.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_sales_sumbyperiod_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_sales_sumbyperiod_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_sales_sumbyperiod_frame_2.setObjectName("g_sales_sumbyperiod_frame_2")
        self.horizontalLayout_69 = QtWidgets.QHBoxLayout(self.g_sales_sumbyperiod_frame_2)
        self.horizontalLayout_69.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_69.setObjectName("horizontalLayout_69")
        self.horizontalLayout_42.addWidget(self.g_sales_sumbyperiod_frame_2)
        self.verticalLayout_2.addWidget(self.g_startdate_frame)
        self.g_frequency_frame = QtWidgets.QFrame(CreateDeliveryOrders)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_frequency_frame.sizePolicy().hasHeightForWidth())
        self.g_frequency_frame.setSizePolicy(sizePolicy)
        self.g_frequency_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_frequency_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_frequency_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_frequency_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_frequency_frame.setObjectName("g_frequency_frame")
        self.horizontalLayout_43 = QtWidgets.QHBoxLayout(self.g_frequency_frame)
        self.horizontalLayout_43.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_43.setSpacing(6)
        self.horizontalLayout_43.setObjectName("horizontalLayout_43")
        self.g_frequency_label = QtWidgets.QLabel(self.g_frequency_frame)
        self.g_frequency_label.setMinimumSize(QtCore.QSize(70, 31))
        self.g_frequency_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_frequency_label.setFont(font)
        self.g_frequency_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_frequency_label.setObjectName("g_frequency_label")
        self.horizontalLayout_43.addWidget(self.g_frequency_label)
        self.g_frequency_combbox = QtWidgets.QComboBox(self.g_frequency_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_frequency_combbox.sizePolicy().hasHeightForWidth())
        self.g_frequency_combbox.setSizePolicy(sizePolicy)
        self.g_frequency_combbox.setMinimumSize(QtCore.QSize(175, 31))
        self.g_frequency_combbox.setMaximumSize(QtCore.QSize(175, 31))
        self.g_frequency_combbox.setEditable(True)
        self.g_frequency_combbox.setCurrentText("")
        self.g_frequency_combbox.setObjectName("g_frequency_combbox")
        self.horizontalLayout_43.addWidget(self.g_frequency_combbox)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_43.addItem(spacerItem1)
        self.verticalLayout_2.addWidget(self.g_frequency_frame)
        self.g_location_frame = QtWidgets.QFrame(CreateDeliveryOrders)
        self.g_location_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_location_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_location_frame.setObjectName("g_location_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_location_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_location_label = QtWidgets.QLabel(self.g_location_frame)
        self.g_location_label.setMinimumSize(QtCore.QSize(70, 0))
        self.g_location_label.setMaximumSize(QtCore.QSize(70, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_location_label.setFont(font)
        self.g_location_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_location_label.setObjectName("g_location_label")
        self.horizontalLayout_3.addWidget(self.g_location_label)
        self.g_location_combobox = QtWidgets.QComboBox(self.g_location_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_location_combobox.sizePolicy().hasHeightForWidth())
        self.g_location_combobox.setSizePolicy(sizePolicy)
        self.g_location_combobox.setMinimumSize(QtCore.QSize(175, 31))
        self.g_location_combobox.setMaximumSize(QtCore.QSize(175, 31))
        self.g_location_combobox.setEditable(True)
        self.g_location_combobox.setCurrentText("")
        self.g_location_combobox.setObjectName("g_location_combobox")
        self.horizontalLayout_3.addWidget(self.g_location_combobox)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem2)
        self.verticalLayout_2.addWidget(self.g_location_frame)
        self.g_products_groupbox = QtWidgets.QGroupBox(CreateDeliveryOrders)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_products_groupbox.setFont(font)
        self.g_products_groupbox.setObjectName("g_products_groupbox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_products_groupbox)
        self.verticalLayout.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_products_table = QtWidgets.QTableWidget(self.g_products_groupbox)
        self.g_products_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_products_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_products_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_products_table.setAlternatingRowColors(True)
        self.g_products_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_products_table.setShowGrid(False)
        self.g_products_table.setObjectName("g_products_table")
        self.g_products_table.setColumnCount(5)
        self.g_products_table.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.g_products_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_products_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_products_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_products_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_products_table.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_products_table.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_products_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_products_table.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_products_table.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_products_table.setItem(0, 4, item)
        self.g_products_table.horizontalHeader().setDefaultSectionSize(150)
        self.g_products_table.horizontalHeader().setStretchLastSection(True)
        self.g_products_table.verticalHeader().setVisible(False)
        self.verticalLayout.addWidget(self.g_products_table)
        self.__g_bottombar_frame = QtWidgets.QFrame(self.g_products_groupbox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_bottombar_frame.setSizePolicy(sizePolicy)
        self.__g_bottombar_frame.setMinimumSize(QtCore.QSize(247, 39))
        self.__g_bottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_bottombar_frame.setObjectName("__g_bottombar_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.__g_bottombar_frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem3)
        self.g_delete_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_delete_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_delete_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_delete_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/fpe/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_delete_button.setIcon(icon)
        self.g_delete_button.setIconSize(QtCore.QSize(24, 24))
        self.g_delete_button.setObjectName("g_delete_button")
        self.horizontalLayout_4.addWidget(self.g_delete_button)
        self.g_manage_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_manage_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_manage_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/fpe/pencil"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_manage_button.setIcon(icon1)
        self.g_manage_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manage_button.setObjectName("g_manage_button")
        self.horizontalLayout_4.addWidget(self.g_manage_button)
        self.g_add_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_add_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_add_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_add_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/entity_dialog/icons/flat_add_white_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_add_button.setIcon(icon2)
        self.g_add_button.setIconSize(QtCore.QSize(24, 24))
        self.g_add_button.setObjectName("g_add_button")
        self.horizontalLayout_4.addWidget(self.g_add_button)
        self.verticalLayout.addWidget(self.__g_bottombar_frame)
        self.verticalLayout_2.addWidget(self.g_products_groupbox)
        self.__g_bottombar_frame_2 = QtWidgets.QFrame(CreateDeliveryOrders)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_bottombar_frame_2.sizePolicy().hasHeightForWidth())
        self.__g_bottombar_frame_2.setSizePolicy(sizePolicy)
        self.__g_bottombar_frame_2.setMinimumSize(QtCore.QSize(247, 39))
        self.__g_bottombar_frame_2.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_bottombar_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_bottombar_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_bottombar_frame_2.setObjectName("__g_bottombar_frame_2")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.__g_bottombar_frame_2)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem4)
        self.g_cancel_button = QtWidgets.QPushButton(self.__g_bottombar_frame_2)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_5.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.__g_bottombar_frame_2)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/fpe/check"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon3)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_5.addWidget(self.g_save_button)
        self.verticalLayout_2.addWidget(self.__g_bottombar_frame_2)

        self.retranslateUi(CreateDeliveryOrders)
        QtCore.QMetaObject.connectSlotsByName(CreateDeliveryOrders)

    def retranslateUi(self, CreateDeliveryOrders):
        _translate = QtCore.QCoreApplication.translate
        CreateDeliveryOrders.setWindowTitle(_translate("CreateDeliveryOrders", "Create Delivery Orders"))
        self.g_main_label.setText(_translate("CreateDeliveryOrders", "Create Repeat Delivery"))
        self.g_startdate_label.setText(_translate("CreateDeliveryOrders", "Start Date"))
        self.g_start_datetimeedit.setDisplayFormat(_translate("CreateDeliveryOrders", "MM/dd/yyyy"))
        self.g_frequency_label.setText(_translate("CreateDeliveryOrders", "Frequency"))
        self.g_location_label.setText(_translate("CreateDeliveryOrders", "Location"))
        self.g_products_groupbox.setTitle(_translate("CreateDeliveryOrders", "Products"))
        self.g_products_table.setSortingEnabled(True)
        item = self.g_products_table.verticalHeaderItem(0)
        item.setText(_translate("CreateDeliveryOrders", "New Row"))
        item = self.g_products_table.horizontalHeaderItem(0)
        item.setText(_translate("CreateDeliveryOrders", "SKU"))
        item = self.g_products_table.horizontalHeaderItem(1)
        item.setText(_translate("CreateDeliveryOrders", "Barcode"))
        item = self.g_products_table.horizontalHeaderItem(2)
        item.setText(_translate("CreateDeliveryOrders", "Description"))
        item = self.g_products_table.horizontalHeaderItem(3)
        item.setText(_translate("CreateDeliveryOrders", "Price"))
        item = self.g_products_table.horizontalHeaderItem(4)
        item.setText(_translate("CreateDeliveryOrders", "Qty"))
        __sortingEnabled = self.g_products_table.isSortingEnabled()
        self.g_products_table.setSortingEnabled(False)
        self.g_products_table.setSortingEnabled(__sortingEnabled)
        self.g_delete_button.setText(_translate("CreateDeliveryOrders", "Delete"))
        self.g_manage_button.setText(_translate("CreateDeliveryOrders", " Manage"))
        self.g_add_button.setText(_translate("CreateDeliveryOrders", "Add"))
        self.g_cancel_button.setText(_translate("CreateDeliveryOrders", "Cancel"))
        self.g_save_button.setText(_translate("CreateDeliveryOrders", "Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    CreateDeliveryOrders = QtWidgets.QDialog()
    ui = Ui_CreateDeliveryOrders()
    ui.setupUi(CreateDeliveryOrders)
    CreateDeliveryOrders.show()
    sys.exit(app.exec_())
