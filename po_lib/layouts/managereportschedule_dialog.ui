<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ManageReportSchedule</class>
 <widget class="QDialog" name="ManageReportSchedule">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>876</width>
    <height>600</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>550</width>
    <height>425</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Manage Report Schedule</string>
  </property>
  <property name="styleSheet">
   <string notr="true">#ManageReportSchedule{
	background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);
}

#g_schedule_groupBox,
#g_reportoptions_groupbox,
#g_settings_frame {
	background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);
	border:1px solid #073c83;
	border-radius:10px;
	background-color:#DDE4E9;
}

QFrame {
	border:0px;
    color: #000;
}

QLabel {
    color: #000;
}

QTreeWidget {
	border: 1px solid #aaa;
	border-radius:5px;
	background-color:#FFF;
	padding-top:5px;
}

QListWidget {
	border: 1px solid #aaa;
	border-radius:5px;
	background-color:#FFF;
}

QComboBox {
	height: 25px;
	line-height: 25px;
	border: 1px solid #aaa;
	border-radius: 5px;
	padding: 4px;
	color: #000;
	font-size: 11px;
 }

QComboBox::drop-down {
	subcontrol-origin: margin;
	background:;
	width:20px;
    /*background-color: #000;*/
}

QDateTimeEdit {
	height: 25px;
	line-height: 25px;
	border: 1px solid #aaa;
	border-radius: 5px;
	padding: 4px;
	color: #000;
 }

QDateTimeEdit::drop-down {
	subcontrol-origin: margin;
	background:;
	width:20px;
}

.QPushButton {
	font-size: 15px;
	font-weight: bold;
    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);
	border:2px solid #287599;
    color:white;
}

.QPushButton:pressed {
    background: #287599;
	border:2px solid #287599;
} 

QLineEdit {
	border: 1px solid #aaa;
	border-radius:5px;
	background-color:#FFF;
	height:31px
}

QPlainTextEdit {
	border: 1px solid #aaa;
	border-radius:5px;
	background-color:#FFF;
}

QTableWidget {
	border:0px;
	border-bottom: 1px solid #000000;
}

QTableWidget::item::focus {
	background-color:#0B256B;
	border: 1px solid #000000;
	color:#ffffff;
}

#g_tabs QTabWidget::tab-bar {
     left: 10px; 
 }

QTabBar::tab {
    background-color: white;
    border: 1px solid #C4C4C3;
    border-bottom-color: #C2C7CB; 
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    min-width: 20ex;
    padding: 2px;
	height:34px;
}

QTabBar::tab:selected, 
QTabBar::tab:hover {
     background-color: white;
 }

QTabBar::tab:selected {
	border-color: #073c83;
	border-bottom-color: none;
 }


#g_create_tab,
#g_scheduled_tab {
	border:1px solid #073c83;
	border-radius: 5px;
	border-top-left-radius:0px;
	background-color: #e7eff4;
}

QHeaderView::section {
	spacing: 15px;
	background-color:#ffffff;
	color: #000000;
	font-weight:bold;
	border: 0px;
	height:15px;
	border-bottom: 1px solid #000;
	font-size:12px;
	padding-top:5px;
	padding-bottom: 5px;
}

.QGroupBox {
	border:1px solid #073c83;
	border-radius: 5px;
    margin-top: 0.5em;
	font-size: 12px;
	font-weight: bold;
}

.QGroupBox::title {
	 subcontrol-origin: margin;
    left: 10px;
    padding: 0 3px 0 3px;
}

#g_tabs QTabWidget::tab-bar {
     left: 5px; 
}

#g_update_schedule_button,
#g_add_schedule_button {
	background-color: #009c00;
	border:2px solid #007f00;
}

#g_update_schedule_button:pressed,
#g_add_schedule_button:pressed {
	background-color: #007f00;
}

#g_delete_button,
#g_cancel_button {
    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);
    border:2px solid #c80319;
}

#g_delete_button:pressed,
#g_cancel_button:pressed {
    background: #c80319
}
 
#g_date_dateedit::drop-down,
#g_custom_combobox::drop-down,
#g_repeat_combobox::drop-down,
#g_time_combobox::drop-down,
#g_day_checkcombobox::drop-down {
	background-image: url(:/entity_dialog/drop-down-arrow);
}

#g_pagenav_frame QLabel,
#g_pagenav_frame QLineEdit,
#g_date_dateedit,
#g_day_checkcombobox {
	font-size: 11px;
}

#g_main_label {
	font-size: 17px;
	font-weight: bold;
}

#g_notifyemail_label, 
#g_notifyemail_label_2,
#g_schedule_groupBox QLabel {
	font-size: 11px;
	font-weight: bold;
}
</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>4</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="g_progress_frame">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>2</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QFrame {
	background-color: white;
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>0</number>
     </property>
     <widget class="QFrame" name="g_completed_frame">
      <property name="geometry">
       <rect>
        <x>-1</x>
        <y>0</y>
        <width>191</width>
        <height>10</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>2</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">QFrame {
	background-color: blue;
}</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Plain</enum>
      </property>
      <property name="lineWidth">
       <number>0</number>
      </property>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="g_titlepagenav_frame">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>31</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>36</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_30">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QFrame" name="g_title_frame">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_23">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="g_main_label">
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>31</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>-1</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>Update Report: </string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="g_titlepagenav_vspacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="g_pagenav_frame">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>31</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>31</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="g_pagenav_hlayout">
         <property name="spacing">
          <number>6</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <spacer name="g_pagenav_hspacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="g_previouspage_label">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="toolTip">
            <string>Previous Page</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap resource="resources.qrc">:/entity_dialog/previous</pixmap>
           </property>
           <property name="scaledContents">
            <bool>false</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="g_nextpage_label">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="toolTip">
            <string>Next Page</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap resource="resources.qrc">:/entity_dialog/next</pixmap>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="g_currentpage_lineedit">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="minimumSize">
            <size>
             <width>31</width>
             <height>31</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>31</width>
             <height>31</height>
            </size>
           </property>
           <property name="text">
            <string>999</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="g_slash_label">
           <property name="text">
            <string>/</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="g_totalpages_label">
           <property name="toolTip">
            <string>Total Pages</string>
           </property>
           <property name="text">
            <string>999</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="g_notifyemail_label">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>-1</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>Emails will be <NAME_EMAIL> related to when pets are sold.</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="g_notifyemail_label_2">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>-1</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>Want to ensure that you get every email? <NAME_EMAIL> to your address book!</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="g_reportoptions_groupbox">
     <property name="font">
      <font>
       <pointsize>-1</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Report Options</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <property name="spacing">
       <number>0</number>
      </property>
      <item>
       <widget class="QStackedWidget" name="g_reportoptions_stack">
        <widget class="QWidget" name="page"/>
        <widget class="QWidget" name="page_2"/>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="g_schedule_groupBox">
     <property name="font">
      <font>
       <pointsize>-1</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Schedule Options</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <widget class="QFrame" name="frame">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_7">
         <property name="spacing">
          <number>6</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="g_startdate_label">
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>100</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>-1</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>Start Date:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QDateEdit" name="g_date_dateedit">
           <property name="minimumSize">
            <size>
             <width>150</width>
             <height>31</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>150</width>
             <height>31</height>
            </size>
           </property>
           <property name="dateTime">
            <datetime>
             <hour>0</hour>
             <minute>0</minute>
             <second>0</second>
             <year>2014</year>
             <month>1</month>
             <day>1</day>
            </datetime>
           </property>
           <property name="calendarPopup">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="g_repeat_label">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>90</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>90</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>-1</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>Repeat:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
           <property name="margin">
            <number>0</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="g_repeat_combobox">
           <property name="minimumSize">
            <size>
             <width>175</width>
             <height>31</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>175</width>
             <height>30</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>-1</pointsize>
            </font>
           </property>
           <property name="cursor">
            <cursorShape>PointingHandCursor</cursorShape>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="currentText">
            <string>Smart Search</string>
           </property>
           <property name="frame">
            <bool>false</bool>
           </property>
           <item>
            <property name="text">
             <string>Smart Search</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Breed Name</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Breeder</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Pet #</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Microchip</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Sex</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Status</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Identifiers</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="g_time_label">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>90</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>90</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>-1</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>Time:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
           <property name="margin">
            <number>0</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="g_time_combobox">
           <property name="minimumSize">
            <size>
             <width>175</width>
             <height>31</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>175</width>
             <height>30</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>-1</pointsize>
            </font>
           </property>
           <property name="cursor">
            <cursorShape>PointingHandCursor</cursorShape>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="currentText">
            <string>Smart Search</string>
           </property>
           <property name="frame">
            <bool>false</bool>
           </property>
           <item>
            <property name="text">
             <string>Smart Search</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Breed Name</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Breeder</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Pet #</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Microchip</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Sex</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Status</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Search Identifiers</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="g_custom_frame">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <spacer name="horizontalSpacer_4">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>261</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="g_months_label">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>90</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>90</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>-1</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>Select Months:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
           <property name="margin">
            <number>0</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="PyCheckCombobox" name="g_month_checkcombobox">
           <property name="minimumSize">
            <size>
             <width>175</width>
             <height>31</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>175</width>
             <height>31</height>
            </size>
           </property>
           <property name="toolTip">
            <string/>
           </property>
           <property name="whatsThis">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="g_days_label">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>90</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>90</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>-1</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>Select Days:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
           <property name="margin">
            <number>0</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="PyCheckCombobox" name="g_day_checkcombobox">
           <property name="minimumSize">
            <size>
             <width>175</width>
             <height>31</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>175</width>
             <height>31</height>
            </size>
           </property>
           <property name="toolTip">
            <string/>
           </property>
           <property name="whatsThis">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_5">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="frame_2">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>6</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="g_cancel_button">
        <property name="minimumSize">
         <size>
          <width>120</width>
          <height>40</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>0</width>
          <height>40</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string>Cancel</string>
        </property>
        <property name="icon">
         <iconset resource="resources.qrc">
          <normaloff>:/report_modaldialog/delete</normaloff>:/report_modaldialog/delete</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>24</width>
          <height>24</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="g_update_schedule_button">
        <property name="minimumSize">
         <size>
          <width>120</width>
          <height>40</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>0</width>
          <height>40</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string>Update</string>
        </property>
        <property name="icon">
         <iconset resource="resources.qrc">
          <normaloff>:/report_modaldialog/check</normaloff>:/report_modaldialog/check</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>24</width>
          <height>24</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="g_add_schedule_button">
        <property name="minimumSize">
         <size>
          <width>120</width>
          <height>40</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>0</width>
          <height>40</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string> Add</string>
        </property>
        <property name="icon">
         <iconset resource="resources.qrc">
          <normaloff>:/report_modaldialog/add</normaloff>:/report_modaldialog/add</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>24</width>
          <height>24</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="g_bottombar_frame">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="layoutDirection">
      <enum>Qt::LeftToRight</enum>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>PyCheckCombobox</class>
   <extends>QComboBox</extends>
   <header>po_lib.checkcombobox</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="resources.qrc"/>
  <include location="../../products/layouts/resources.qrc"/>
  <include location="../../pet_tracker/layouts/resources.qrc"/>
 </resources>
 <connections/>
</ui>
