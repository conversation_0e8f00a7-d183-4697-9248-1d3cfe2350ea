# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managecustomfield_modaldialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ManageCustomField(object):
    def setupUi(self, ManageCustomField):
        ManageCustomField.setObjectName("ManageCustomField")
        ManageCustomField.resize(606, 590)
        ManageCustomField.setMinimumSize(QtCore.QSize(0, 590))
        ManageCustomField.setMaximumSize(QtCore.QSize(769, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        ManageCustomField.setFont(font)
        ManageCustomField.setStyleSheet("#ManageCustomField {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QScrollArea QWidget  {\n"
"    background-color: #ffffff;\n"
"}\n"
"\n"
"QCheckBox {\n"
"padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/pet_tracker/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/pet_tracker/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    background-image: url(:/pet_tracker/drop-down-arrow);\n"
"}\n"
"\n"
"QDateTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
" \n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/pet_tracker/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
".QListWidget { \n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    padding: 1px;\n"
"}    \n"
"\n"
".QListWidget::item {\n"
"    padding-bottom:1px;\n"
"    padding-right:1px;\n"
"}\n"
"\n"
".QListWidget::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"PyCheckComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"PyCheckComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    /*background-color: #000;*/\n"
"}\n"
"\n"
"PyCheckComboBox QAbstractItemView::item {\n"
"     margin-top: 5px;\n"
"}\n"
"\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"\n"
"#g_delete_button,\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_delete_button:pressed,\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_add_button,\n"
"#g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_add_button:pressed,\n"
"#g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_value_checkcombobox::drop-down,\n"
"#g_attribute_combobox::drop-down,\n"
"#g_value_combobox::drop-down {    \n"
"    background-image: url(:/pet_tracker/drop-down-arrow);\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_attribute_label_3,\n"
"#g_table_label_3,\n"
"#g_required_checkBox,\n"
"#g_filter_checkBox,\n"
"#g_location_label,\n"
"#g_classification_label,\n"
"#g_type_label,\n"
"#g_appliestotype_label,\n"
"#g_customtype_label,\n"
"#g_value_label_2,\n"
"#g_value_label_3,\n"
"#g_value_label_8,\n"
"#g_datestart_label,\n"
"#g_dateend_label,\n"
"#g_value_label_5,\n"
"#g_list_label_3 {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_customname_lineEdit,\n"
"#g_value_lineEdit,\n"
"#g_valuestart_lineEdit,\n"
"#g_valueend_lineEdit,\n"
"#g_value_plaintextedit,\n"
"#g_list_listWidget,\n"
"#g_value_label, \n"
"#g_instruction_label, \n"
"#g_pagenav_frame QLabel, \n"
"#g_pagenav_frame QLineEdit {\n"
"    font-size: 11px;\n"
"}")
        self.verticalLayout = QtWidgets.QVBoxLayout(ManageCustomField)
        self.verticalLayout.setContentsMargins(9, 0, 9, 9)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_titlepagenav_frame = QtWidgets.QFrame(ManageCustomField)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 33))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 33))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_30 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_30.setContentsMargins(0, 2, 0, 0)
        self.horizontalLayout_30.setSpacing(6)
        self.horizontalLayout_30.setObjectName("horizontalLayout_30")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout_23 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout_23.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_23.setObjectName("verticalLayout_23")
        self.g_top_frame = QtWidgets.QFrame(self.g_title_frame)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setSpacing(0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_7.addWidget(self.g_progress_frame)
        self.verticalLayout_23.addWidget(self.g_top_frame)
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_23.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_23.addItem(spacerItem)
        self.horizontalLayout_30.addWidget(self.g_title_frame)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout.setSpacing(6)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout.addItem(spacerItem1)
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/pet_tracker/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/pet_tracker/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagenav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_slash_label.setObjectName("g_slash_label")
        self.g_pagenav_hlayout.addWidget(self.g_slash_label)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout.addWidget(self.g_totalpages_label)
        self.horizontalLayout_30.addWidget(self.g_pagenav_frame)
        self.verticalLayout.addWidget(self.g_titlepagenav_frame)
        self.g_customename_frame = QtWidgets.QFrame(ManageCustomField)
        self.g_customename_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_customename_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_customename_frame.setObjectName("g_customename_frame")
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout(self.g_customename_frame)
        self.horizontalLayout_14.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_14.setSpacing(6)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.g_name_label = QtWidgets.QLabel(self.g_customename_frame)
        self.g_name_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_name_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_name_label.setFont(font)
        self.g_name_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_name_label.setObjectName("g_name_label")
        self.horizontalLayout_14.addWidget(self.g_name_label)
        self.g_customname_lineEdit = QtWidgets.QLineEdit(self.g_customename_frame)
        self.g_customname_lineEdit.setMinimumSize(QtCore.QSize(350, 0))
        self.g_customname_lineEdit.setObjectName("g_customname_lineEdit")
        self.horizontalLayout_14.addWidget(self.g_customname_lineEdit)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem2)
        self.verticalLayout.addWidget(self.g_customename_frame)
        self.g_params_frame = QtWidgets.QFrame(ManageCustomField)
        self.g_params_frame.setMaximumSize(QtCore.QSize(16777215, 32))
        self.g_params_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_params_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_params_frame.setObjectName("g_params_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_params_frame)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_required_frame = QtWidgets.QFrame(self.g_params_frame)
        self.g_required_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_required_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_required_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_required_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_required_frame.setObjectName("g_required_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_required_frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 2)
        self.horizontalLayout_4.setSpacing(6)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_options_label = QtWidgets.QLabel(self.g_required_frame)
        self.g_options_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_options_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_options_label.setFont(font)
        self.g_options_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_options_label.setObjectName("g_options_label")
        self.horizontalLayout_4.addWidget(self.g_options_label)
        self.g_required_checkBox = QtWidgets.QCheckBox(self.g_required_frame)
        self.g_required_checkBox.setMinimumSize(QtCore.QSize(100, 0))
        self.g_required_checkBox.setMaximumSize(QtCore.QSize(20, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_required_checkBox.setFont(font)
        self.g_required_checkBox.setObjectName("g_required_checkBox")
        self.horizontalLayout_4.addWidget(self.g_required_checkBox, 0, QtCore.Qt.AlignTop)
        self.g_filter_checkBox = QtWidgets.QCheckBox(self.g_required_frame)
        self.g_filter_checkBox.setMinimumSize(QtCore.QSize(150, 0))
        self.g_filter_checkBox.setMaximumSize(QtCore.QSize(20, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_filter_checkBox.setFont(font)
        self.g_filter_checkBox.setObjectName("g_filter_checkBox")
        self.horizontalLayout_4.addWidget(self.g_filter_checkBox, 0, QtCore.Qt.AlignTop)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem3)
        self.verticalLayout_2.addWidget(self.g_required_frame)
        self.verticalLayout.addWidget(self.g_params_frame)
        self.g_location_frame = QtWidgets.QFrame(ManageCustomField)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_location_frame.sizePolicy().hasHeightForWidth())
        self.g_location_frame.setSizePolicy(sizePolicy)
        self.g_location_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_location_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_location_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_location_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_location_frame.setObjectName("g_location_frame")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout(self.g_location_frame)
        self.horizontalLayout_16.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_16.setSpacing(6)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.g_location_label = QtWidgets.QLabel(self.g_location_frame)
        self.g_location_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_location_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_location_label.setFont(font)
        self.g_location_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_location_label.setObjectName("g_location_label")
        self.horizontalLayout_16.addWidget(self.g_location_label)
        self.g_location_checkcombobox = PyCheckCombobox(self.g_location_frame)
        self.g_location_checkcombobox.setMinimumSize(QtCore.QSize(350, 31))
        self.g_location_checkcombobox.setToolTip("")
        self.g_location_checkcombobox.setWhatsThis("")
        self.g_location_checkcombobox.setObjectName("g_location_checkcombobox")
        self.horizontalLayout_16.addWidget(self.g_location_checkcombobox)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_16.addItem(spacerItem4)
        self.verticalLayout.addWidget(self.g_location_frame)
        self.g_classification_frame = QtWidgets.QFrame(ManageCustomField)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_classification_frame.sizePolicy().hasHeightForWidth())
        self.g_classification_frame.setSizePolicy(sizePolicy)
        self.g_classification_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_classification_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_classification_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_classification_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_classification_frame.setObjectName("g_classification_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_classification_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(6)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_classification_label = QtWidgets.QLabel(self.g_classification_frame)
        self.g_classification_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_classification_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_classification_label.setFont(font)
        self.g_classification_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_classification_label.setObjectName("g_classification_label")
        self.horizontalLayout_8.addWidget(self.g_classification_label)
        self.g_classification_combobox = QtWidgets.QComboBox(self.g_classification_frame)
        self.g_classification_combobox.setMinimumSize(QtCore.QSize(350, 0))
        self.g_classification_combobox.setEditable(True)
        self.g_classification_combobox.setProperty("qp_prodattr_name", "")
        self.g_classification_combobox.setObjectName("g_classification_combobox")
        self.horizontalLayout_8.addWidget(self.g_classification_combobox)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_8.addItem(spacerItem5)
        self.verticalLayout.addWidget(self.g_classification_frame)
        self.g_type_frame = QtWidgets.QFrame(ManageCustomField)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_type_frame.sizePolicy().hasHeightForWidth())
        self.g_type_frame.setSizePolicy(sizePolicy)
        self.g_type_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_type_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_type_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_type_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_type_frame.setObjectName("g_type_frame")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.g_type_frame)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setSpacing(6)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.g_type_label = QtWidgets.QLabel(self.g_type_frame)
        self.g_type_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_type_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_type_label.setFont(font)
        self.g_type_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_type_label.setObjectName("g_type_label")
        self.horizontalLayout_10.addWidget(self.g_type_label)
        self.g_type_checkcombobox = PyCheckCombobox(self.g_type_frame)
        self.g_type_checkcombobox.setMinimumSize(QtCore.QSize(350, 31))
        self.g_type_checkcombobox.setToolTip("")
        self.g_type_checkcombobox.setWhatsThis("")
        self.g_type_checkcombobox.setObjectName("g_type_checkcombobox")
        self.horizontalLayout_10.addWidget(self.g_type_checkcombobox)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_10.addItem(spacerItem6)
        self.verticalLayout.addWidget(self.g_type_frame)
        self.g_appliestotype_frame = QtWidgets.QFrame(ManageCustomField)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_appliestotype_frame.sizePolicy().hasHeightForWidth())
        self.g_appliestotype_frame.setSizePolicy(sizePolicy)
        self.g_appliestotype_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_appliestotype_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_appliestotype_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_appliestotype_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_appliestotype_frame.setObjectName("g_appliestotype_frame")
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout(self.g_appliestotype_frame)
        self.horizontalLayout_17.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_17.setSpacing(6)
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        self.g_appliestotype_label = QtWidgets.QLabel(self.g_appliestotype_frame)
        self.g_appliestotype_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_appliestotype_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_appliestotype_label.setFont(font)
        self.g_appliestotype_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_appliestotype_label.setObjectName("g_appliestotype_label")
        self.horizontalLayout_17.addWidget(self.g_appliestotype_label)
        self.g_appliestotype_checkcombobox = PyCheckCombobox(self.g_appliestotype_frame)
        self.g_appliestotype_checkcombobox.setMinimumSize(QtCore.QSize(350, 31))
        self.g_appliestotype_checkcombobox.setToolTip("")
        self.g_appliestotype_checkcombobox.setWhatsThis("")
        self.g_appliestotype_checkcombobox.setObjectName("g_appliestotype_checkcombobox")
        self.horizontalLayout_17.addWidget(self.g_appliestotype_checkcombobox)
        self.g_loadingbreeds_label = QtWidgets.QLabel(self.g_appliestotype_frame)
        self.g_loadingbreeds_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_loadingbreeds_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_loadingbreeds_label.setFont(font)
        self.g_loadingbreeds_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_loadingbreeds_label.setObjectName("g_loadingbreeds_label")
        self.horizontalLayout_17.addWidget(self.g_loadingbreeds_label)
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_17.addItem(spacerItem7)
        self.verticalLayout.addWidget(self.g_appliestotype_frame)
        self.g_customtype_frame = QtWidgets.QFrame(ManageCustomField)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_customtype_frame.sizePolicy().hasHeightForWidth())
        self.g_customtype_frame.setSizePolicy(sizePolicy)
        self.g_customtype_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_customtype_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_customtype_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_customtype_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_customtype_frame.setObjectName("g_customtype_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_customtype_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setSpacing(6)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_customtype_label = QtWidgets.QLabel(self.g_customtype_frame)
        self.g_customtype_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_customtype_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_customtype_label.setFont(font)
        self.g_customtype_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_customtype_label.setObjectName("g_customtype_label")
        self.horizontalLayout.addWidget(self.g_customtype_label)
        self.g_customtype_combobox = QtWidgets.QComboBox(self.g_customtype_frame)
        self.g_customtype_combobox.setMinimumSize(QtCore.QSize(350, 0))
        self.g_customtype_combobox.setEditable(True)
        self.g_customtype_combobox.setProperty("qp_prodattr_name", "")
        self.g_customtype_combobox.setObjectName("g_customtype_combobox")
        self.horizontalLayout.addWidget(self.g_customtype_combobox)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem8)
        self.verticalLayout.addWidget(self.g_customtype_frame)
        self.g_value_frame = QtWidgets.QFrame(ManageCustomField)
        self.g_value_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_value_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_value_frame.setObjectName("g_value_frame")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.g_value_frame)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setSpacing(6)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        spacerItem9 = QtWidgets.QSpacerItem(117, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem9)
        self.g_value_label = QtWidgets.QLabel(self.g_value_frame)
        self.g_value_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_value_label.setObjectName("g_value_label")
        self.horizontalLayout_9.addWidget(self.g_value_label)
        spacerItem10 = QtWidgets.QSpacerItem(58, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem10)
        self.verticalLayout.addWidget(self.g_value_frame)
        self.g_lineedit_frame = QtWidgets.QFrame(ManageCustomField)
        self.g_lineedit_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_lineedit_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_lineedit_frame.setObjectName("g_lineedit_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_lineedit_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setSpacing(6)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_helper_label_2 = QtWidgets.QLabel(self.g_lineedit_frame)
        self.g_helper_label_2.setMinimumSize(QtCore.QSize(110, 0))
        self.g_helper_label_2.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_helper_label_2.setFont(font)
        self.g_helper_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_helper_label_2.setObjectName("g_helper_label_2")
        self.horizontalLayout_3.addWidget(self.g_helper_label_2)
        self.g_value_lineEdit = QtWidgets.QLineEdit(self.g_lineedit_frame)
        self.g_value_lineEdit.setMinimumSize(QtCore.QSize(350, 0))
        self.g_value_lineEdit.setObjectName("g_value_lineEdit")
        self.horizontalLayout_3.addWidget(self.g_value_lineEdit)
        spacerItem11 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem11)
        self.verticalLayout.addWidget(self.g_lineedit_frame)
        self.g_lineeditrange_frame = QtWidgets.QFrame(ManageCustomField)
        self.g_lineeditrange_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_lineeditrange_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_lineeditrange_frame.setObjectName("g_lineeditrange_frame")
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout(self.g_lineeditrange_frame)
        self.horizontalLayout_15.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_15.setSpacing(6)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.g_minvalue_label = QtWidgets.QLabel(self.g_lineeditrange_frame)
        self.g_minvalue_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_minvalue_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_minvalue_label.setFont(font)
        self.g_minvalue_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_minvalue_label.setObjectName("g_minvalue_label")
        self.horizontalLayout_15.addWidget(self.g_minvalue_label)
        self.g_valuestart_lineEdit = QtWidgets.QLineEdit(self.g_lineeditrange_frame)
        self.g_valuestart_lineEdit.setMinimumSize(QtCore.QSize(129, 0))
        self.g_valuestart_lineEdit.setMaximumSize(QtCore.QSize(129, 16777215))
        self.g_valuestart_lineEdit.setObjectName("g_valuestart_lineEdit")
        self.horizontalLayout_15.addWidget(self.g_valuestart_lineEdit)
        self.g_maxvalue_label = QtWidgets.QLabel(self.g_lineeditrange_frame)
        self.g_maxvalue_label.setMinimumSize(QtCore.QSize(80, 0))
        self.g_maxvalue_label.setMaximumSize(QtCore.QSize(80, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_maxvalue_label.setFont(font)
        self.g_maxvalue_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_maxvalue_label.setObjectName("g_maxvalue_label")
        self.horizontalLayout_15.addWidget(self.g_maxvalue_label)
        self.g_valueend_lineEdit = QtWidgets.QLineEdit(self.g_lineeditrange_frame)
        self.g_valueend_lineEdit.setMinimumSize(QtCore.QSize(129, 0))
        self.g_valueend_lineEdit.setObjectName("g_valueend_lineEdit")
        self.horizontalLayout_15.addWidget(self.g_valueend_lineEdit)
        spacerItem12 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_15.addItem(spacerItem12)
        self.verticalLayout.addWidget(self.g_lineeditrange_frame)
        self.g_daterange_frame = QtWidgets.QFrame(ManageCustomField)
        self.g_daterange_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_daterange_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_daterange_frame.setObjectName("g_daterange_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_daterange_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(6)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.g_datestart_label = QtWidgets.QLabel(self.g_daterange_frame)
        self.g_datestart_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_datestart_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_datestart_label.setFont(font)
        self.g_datestart_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_datestart_label.setObjectName("g_datestart_label")
        self.horizontalLayout_11.addWidget(self.g_datestart_label)
        self.g_valuestart_timeedit = QtWidgets.QDateEdit(self.g_daterange_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_valuestart_timeedit.sizePolicy().hasHeightForWidth())
        self.g_valuestart_timeedit.setSizePolicy(sizePolicy)
        self.g_valuestart_timeedit.setMinimumSize(QtCore.QSize(129, 31))
        self.g_valuestart_timeedit.setMaximumSize(QtCore.QSize(129, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_valuestart_timeedit.setFont(font)
        self.g_valuestart_timeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_valuestart_timeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 6), QtCore.QTime(16, 0, 0)))
        self.g_valuestart_timeedit.setCalendarPopup(True)
        self.g_valuestart_timeedit.setObjectName("g_valuestart_timeedit")
        self.horizontalLayout_11.addWidget(self.g_valuestart_timeedit)
        self.g_dateend_label = QtWidgets.QLabel(self.g_daterange_frame)
        self.g_dateend_label.setMinimumSize(QtCore.QSize(80, 0))
        self.g_dateend_label.setMaximumSize(QtCore.QSize(80, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_dateend_label.setFont(font)
        self.g_dateend_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_dateend_label.setObjectName("g_dateend_label")
        self.horizontalLayout_11.addWidget(self.g_dateend_label)
        self.g_valueend_timeedit = QtWidgets.QDateEdit(self.g_daterange_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_valueend_timeedit.sizePolicy().hasHeightForWidth())
        self.g_valueend_timeedit.setSizePolicy(sizePolicy)
        self.g_valueend_timeedit.setMinimumSize(QtCore.QSize(129, 31))
        self.g_valueend_timeedit.setMaximumSize(QtCore.QSize(129, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_valueend_timeedit.setFont(font)
        self.g_valueend_timeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_valueend_timeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 6), QtCore.QTime(16, 0, 0)))
        self.g_valueend_timeedit.setCalendarPopup(True)
        self.g_valueend_timeedit.setObjectName("g_valueend_timeedit")
        self.horizontalLayout_11.addWidget(self.g_valueend_timeedit)
        spacerItem13 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem13)
        self.verticalLayout.addWidget(self.g_daterange_frame)
        self.g_multiline_frame = QtWidgets.QFrame(ManageCustomField)
        self.g_multiline_frame.setMinimumSize(QtCore.QSize(0, 100))
        self.g_multiline_frame.setMaximumSize(QtCore.QSize(16777215, 100))
        self.g_multiline_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_multiline_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_multiline_frame.setObjectName("g_multiline_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_multiline_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(6)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_helper_label = QtWidgets.QLabel(self.g_multiline_frame)
        self.g_helper_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_helper_label.setMaximumSize(QtCore.QSize(110, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_helper_label.setFont(font)
        self.g_helper_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_helper_label.setObjectName("g_helper_label")
        self.horizontalLayout_6.addWidget(self.g_helper_label)
        self.g_value_plaintextedit = QtWidgets.QPlainTextEdit(self.g_multiline_frame)
        self.g_value_plaintextedit.setMinimumSize(QtCore.QSize(350, 100))
        self.g_value_plaintextedit.setMaximumSize(QtCore.QSize(16777215, 100))
        self.g_value_plaintextedit.setObjectName("g_value_plaintextedit")
        self.horizontalLayout_6.addWidget(self.g_value_plaintextedit)
        spacerItem14 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem14)
        self.verticalLayout.addWidget(self.g_multiline_frame)
        self.g_scrolllist_frame = QtWidgets.QFrame(ManageCustomField)
        self.g_scrolllist_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_scrolllist_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_scrolllist_frame.setObjectName("g_scrolllist_frame")
        self.horizontalLayout_49 = QtWidgets.QHBoxLayout(self.g_scrolllist_frame)
        self.horizontalLayout_49.setContentsMargins(0, 0, 0, 9)
        self.horizontalLayout_49.setSpacing(6)
        self.horizontalLayout_49.setObjectName("horizontalLayout_49")
        self.g_list_label = QtWidgets.QLabel(self.g_scrolllist_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_list_label.sizePolicy().hasHeightForWidth())
        self.g_list_label.setSizePolicy(sizePolicy)
        self.g_list_label.setMinimumSize(QtCore.QSize(110, 0))
        self.g_list_label.setMaximumSize(QtCore.QSize(100, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_list_label.setFont(font)
        self.g_list_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_list_label.setObjectName("g_list_label")
        self.horizontalLayout_49.addWidget(self.g_list_label)
        self.g_list_listWidget = QtWidgets.QListWidget(self.g_scrolllist_frame)
        self.g_list_listWidget.setMinimumSize(QtCore.QSize(350, 0))
        self.g_list_listWidget.setObjectName("g_list_listWidget")
        self.horizontalLayout_49.addWidget(self.g_list_listWidget)
        spacerItem15 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_49.addItem(spacerItem15)
        self.verticalLayout.addWidget(self.g_scrolllist_frame)
        self.g_instruction_label = QtWidgets.QLabel(ManageCustomField)
        self.g_instruction_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_instruction_label.setObjectName("g_instruction_label")
        self.verticalLayout.addWidget(self.g_instruction_label)
        self.g_edit_frame = QtWidgets.QFrame(ManageCustomField)
        self.g_edit_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_edit_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_edit_frame.setObjectName("g_edit_frame")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_edit_frame)
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_3.setSpacing(6)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.verticalLayout.addWidget(self.g_edit_frame)
        self.g_bottom_verticalSpacer_frame = QtWidgets.QFrame(ManageCustomField)
        self.g_bottom_verticalSpacer_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_bottom_verticalSpacer_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_bottom_verticalSpacer_frame.setObjectName("g_bottom_verticalSpacer_frame")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_bottom_verticalSpacer_frame)
        self.verticalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_4.setSpacing(6)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        spacerItem16 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem16)
        self.verticalLayout.addWidget(self.g_bottom_verticalSpacer_frame)
        self.__g_bottombar_frame = QtWidgets.QFrame(ManageCustomField)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_bottombar_frame.setSizePolicy(sizePolicy)
        self.__g_bottombar_frame.setMinimumSize(QtCore.QSize(247, 39))
        self.__g_bottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_bottombar_frame.setObjectName("__g_bottombar_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.__g_bottombar_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setSpacing(6)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        spacerItem17 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem17)
        self.g_cancel_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/custom_fields/Cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_2.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/custom_fields/Save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_2.addWidget(self.g_save_button)
        self.verticalLayout.addWidget(self.__g_bottombar_frame)
        self.g_titlepagenav_frame.raise_()
        self.__g_bottombar_frame.raise_()
        self.g_customtype_frame.raise_()
        self.g_lineedit_frame.raise_()
        self.g_multiline_frame.raise_()
        self.g_edit_frame.raise_()
        self.g_instruction_label.raise_()
        self.g_customename_frame.raise_()
        self.g_params_frame.raise_()
        self.g_daterange_frame.raise_()
        self.g_lineeditrange_frame.raise_()
        self.g_location_frame.raise_()
        self.g_classification_frame.raise_()
        self.g_type_frame.raise_()
        self.g_appliestotype_frame.raise_()
        self.g_value_frame.raise_()
        self.g_bottom_verticalSpacer_frame.raise_()
        self.g_scrolllist_frame.raise_()

        self.retranslateUi(ManageCustomField)
        QtCore.QMetaObject.connectSlotsByName(ManageCustomField)

    def retranslateUi(self, ManageCustomField):
        _translate = QtCore.QCoreApplication.translate
        ManageCustomField.setWindowTitle(_translate("ManageCustomField", "Manage Custom Field"))
        self.g_main_label.setText(_translate("ManageCustomField", "Manage Custom Field"))
        self.g_previouspage_label.setToolTip(_translate("ManageCustomField", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("ManageCustomField", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("ManageCustomField", "999"))
        self.g_slash_label.setText(_translate("ManageCustomField", "/"))
        self.g_totalpages_label.setToolTip(_translate("ManageCustomField", "Total Pages"))
        self.g_totalpages_label.setText(_translate("ManageCustomField", "999"))
        self.g_name_label.setText(_translate("ManageCustomField", "Field Name"))
        self.g_customname_lineEdit.setPlaceholderText(_translate("ManageCustomField", "Enter Field Name"))
        self.g_options_label.setText(_translate("ManageCustomField", "Options"))
        self.g_required_checkBox.setText(_translate("ManageCustomField", "Required"))
        self.g_filter_checkBox.setText(_translate("ManageCustomField", "Advanced Search"))
        self.g_location_label.setText(_translate("ManageCustomField", "Locations"))
        self.g_classification_label.setText(_translate("ManageCustomField", "Classification"))
        self.g_type_label.setText(_translate("ManageCustomField", "By Type"))
        self.g_appliestotype_label.setText(_translate("ManageCustomField", "Applies to Type"))
        self.g_loadingbreeds_label.setText(_translate("ManageCustomField", "Loading Breeds..."))
        self.g_customtype_label.setText(_translate("ManageCustomField", "Field Type"))
        self.g_value_label.setText(_translate("ManageCustomField", "TextLabel"))
        self.g_helper_label_2.setText(_translate("ManageCustomField", "Helper Text"))
        self.g_value_lineEdit.setPlaceholderText(_translate("ManageCustomField", "This is an example of helper text."))
        self.g_minvalue_label.setText(_translate("ManageCustomField", "Min Value:"))
        self.g_valuestart_lineEdit.setPlaceholderText(_translate("ManageCustomField", "Minimum Value"))
        self.g_maxvalue_label.setText(_translate("ManageCustomField", "Max Value"))
        self.g_valueend_lineEdit.setPlaceholderText(_translate("ManageCustomField", "Maximum Value"))
        self.g_datestart_label.setText(_translate("ManageCustomField", "Min Value"))
        self.g_dateend_label.setText(_translate("ManageCustomField", "Max Value"))
        self.g_helper_label.setText(_translate("ManageCustomField", "Helper Text"))
        self.g_value_plaintextedit.setPlaceholderText(_translate("ManageCustomField", "This is an example of helper text."))
        self.g_list_label.setText(_translate("ManageCustomField", "Value"))
        self.g_instruction_label.setText(_translate("ManageCustomField", "TextLabel"))
        self.g_cancel_button.setText(_translate("ManageCustomField", " Cancel"))
        self.g_save_button.setText(_translate("ManageCustomField", " Save"))
from po_lib.checkcombobox import PyCheckCombobox
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageCustomField = QtWidgets.QDialog()
    ui = Ui_ManageCustomField()
    ui.setupUi(ManageCustomField)
    ManageCustomField.show()
    sys.exit(app.exec_())
