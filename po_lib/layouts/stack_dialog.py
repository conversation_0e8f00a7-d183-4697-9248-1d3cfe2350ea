# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'stack_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_StackDialog(object):
    def setupUi(self, StackDialog):
        StackDialog.setObjectName("StackDialog")
        StackDialog.resize(600, 360)
        StackDialog.setMinimumSize(QtCore.QSize(0, 360))
        StackDialog.setWindowTitle("")
        StackDialog.setStyleSheet("#g_cancel_btn  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_btn:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_save_btn {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_btn:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#StackDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QDateTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTreeWidget {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    padding-top:5px;\n"
"}\n"
"\n"
"QListWidget {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
" QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QTabWidget {\n"
"    border:0px;\n"
"}\n"
"\n"
"QDateTimeEdit::drop-down,\n"
"QComboBox::drop-down  {        \n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"}\n"
"\n"
"#::drop-down {    \n"
"    background-image: url(:/entity_dialog/drop_down_3dot);\n"
"}\n"
"\n"
"#g_title {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(StackDialog)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_progress_frame = QtWidgets.QFrame(StackDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.verticalLayout_2.addWidget(self.g_progress_frame)
        self.g_title = QtWidgets.QLabel(StackDialog)
        self.g_title.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(13)
        font.setBold(True)
        font.setWeight(75)
        self.g_title.setFont(font)
        self.g_title.setStyleSheet("")
        self.g_title.setObjectName("g_title")
        self.verticalLayout_2.addWidget(self.g_title)
        self.g_frame = QtWidgets.QFrame(StackDialog)
        self.g_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_frame.setObjectName("g_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_stack = QtWidgets.QStackedWidget(self.g_frame)
        self.g_stack.setObjectName("g_stack")
        self.page = QtWidgets.QWidget()
        self.page.setObjectName("page")
        self.g_stack.addWidget(self.page)
        self.page_2 = QtWidgets.QWidget()
        self.page_2.setObjectName("page_2")
        self.g_stack.addWidget(self.page_2)
        self.horizontalLayout.addWidget(self.g_stack)
        self.verticalLayout_2.addWidget(self.g_frame)
        self.g_controls_frame = QtWidgets.QFrame(StackDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controls_frame.sizePolicy().hasHeightForWidth())
        self.g_controls_frame.setSizePolicy(sizePolicy)
        self.g_controls_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.g_controls_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controls_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controls_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controls_frame.setObjectName("g_controls_frame")
        self.horizontalLayout_20 = QtWidgets.QHBoxLayout(self.g_controls_frame)
        self.horizontalLayout_20.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_20.setObjectName("horizontalLayout_20")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_20.addItem(spacerItem)
        self.g_cancel_btn = QtWidgets.QPushButton(self.g_controls_frame)
        self.g_cancel_btn.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_btn.setObjectName("g_cancel_btn")
        self.horizontalLayout_20.addWidget(self.g_cancel_btn)
        self.g_save_btn = QtWidgets.QPushButton(self.g_controls_frame)
        self.g_save_btn.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_btn.setObjectName("g_save_btn")
        self.horizontalLayout_20.addWidget(self.g_save_btn)
        self.verticalLayout_2.addWidget(self.g_controls_frame)

        self.retranslateUi(StackDialog)
        QtCore.QMetaObject.connectSlotsByName(StackDialog)

    def retranslateUi(self, StackDialog):
        _translate = QtCore.QCoreApplication.translate
        self.g_title.setText(_translate("StackDialog", "Title"))
        self.g_cancel_btn.setText(_translate("StackDialog", "Cancel"))
        self.g_save_btn.setText(_translate("StackDialog", "Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    StackDialog = QtWidgets.QDialog()
    ui = Ui_StackDialog()
    ui.setupUi(StackDialog)
    StackDialog.show()
    sys.exit(app.exec_())
