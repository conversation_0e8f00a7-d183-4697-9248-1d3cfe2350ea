# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'light_payment_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_LightPaymentDialog(object):
    def setupUi(self, LightPaymentDialog):
        LightPaymentDialog.setObjectName("LightPaymentDialog")
        LightPaymentDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        LightPaymentDialog.resize(554, 420)
        LightPaymentDialog.setMinimumSize(QtCore.QSize(554, 420))
        LightPaymentDialog.setMaximumSize(QtCore.QSize(554, 420))
        LightPaymentDialog.setStyleSheet("#LightPaymentDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_totals_table {\n"
"    border: 1px solid #073c83;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_totals_table::item {\n"
"    padding-left: 5px;\n"
"    padding-right: 5px;\n"
"}\n"
"\n"
"#g_totals_table QHeaderView::section {\n"
"    font-size: 13px;\n"
"    font-weight: bold;    \n"
"    spacing: 20px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    border-bottom: 1px solid #073c83;\n"
"    font-size:12px;\n"
"    height: 29px;\n"
"    padding-left: 5px;\n"
"    padding-right: 5px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"#g_finish_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_finish_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_title_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_change_frame QLabel {\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}")
        LightPaymentDialog.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(LightPaymentDialog)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_title_label = QtWidgets.QLabel(LightPaymentDialog)
        self.g_title_label.setObjectName("g_title_label")
        self.verticalLayout.addWidget(self.g_title_label)
        self.g_totals_table = QtWidgets.QTableWidget(LightPaymentDialog)
        self.g_totals_table.setObjectName("g_totals_table")
        self.g_totals_table.setColumnCount(2)
        self.g_totals_table.setRowCount(6)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_totals_table.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_totals_table.setItem(1, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_totals_table.setItem(2, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_totals_table.setItem(3, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_totals_table.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_totals_table.setItem(4, 1, item)
        item = QtWidgets.QTableWidgetItem()
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_totals_table.setItem(5, 0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        item.setFont(font)
        self.g_totals_table.setItem(5, 1, item)
        self.g_totals_table.horizontalHeader().setVisible(False)
        self.g_totals_table.horizontalHeader().setCascadingSectionResizes(True)
        self.g_totals_table.horizontalHeader().setDefaultSectionSize(450)
        self.g_totals_table.horizontalHeader().setStretchLastSection(True)
        self.g_totals_table.verticalHeader().setVisible(False)
        self.verticalLayout.addWidget(self.g_totals_table)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_back_button = QtWidgets.QPushButton(LightPaymentDialog)
        self.g_back_button.setMinimumSize(QtCore.QSize(160, 40))
        self.g_back_button.setMaximumSize(QtCore.QSize(160, 40))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/sales_screen/backspace"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_back_button.setIcon(icon)
        self.g_back_button.setIconSize(QtCore.QSize(24, 24))
        self.g_back_button.setObjectName("g_back_button")
        self.horizontalLayout.addWidget(self.g_back_button)
        self.g_finish_button = QtWidgets.QPushButton(LightPaymentDialog)
        self.g_finish_button.setMinimumSize(QtCore.QSize(160, 40))
        self.g_finish_button.setMaximumSize(QtCore.QSize(160, 40))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/sales_screen/save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_finish_button.setIcon(icon1)
        self.g_finish_button.setIconSize(QtCore.QSize(24, 24))
        self.g_finish_button.setObjectName("g_finish_button")
        self.horizontalLayout.addWidget(self.g_finish_button)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(LightPaymentDialog)
        QtCore.QMetaObject.connectSlotsByName(LightPaymentDialog)

    def retranslateUi(self, LightPaymentDialog):
        _translate = QtCore.QCoreApplication.translate
        LightPaymentDialog.setWindowTitle(_translate("LightPaymentDialog", "Payment"))
        self.g_title_label.setText(_translate("LightPaymentDialog", "Enter the following amounts into your POS"))
        item = self.g_totals_table.verticalHeaderItem(0)
        item.setText(_translate("LightPaymentDialog", "New Row"))
        item = self.g_totals_table.verticalHeaderItem(1)
        item.setText(_translate("LightPaymentDialog", "New Row"))
        item = self.g_totals_table.verticalHeaderItem(2)
        item.setText(_translate("LightPaymentDialog", "New Row"))
        item = self.g_totals_table.verticalHeaderItem(3)
        item.setText(_translate("LightPaymentDialog", "New Row"))
        item = self.g_totals_table.verticalHeaderItem(4)
        item.setText(_translate("LightPaymentDialog", "New Row"))
        item = self.g_totals_table.verticalHeaderItem(5)
        item.setText(_translate("LightPaymentDialog", "New Row"))
        item = self.g_totals_table.horizontalHeaderItem(0)
        item.setText(_translate("LightPaymentDialog", "Title"))
        item = self.g_totals_table.horizontalHeaderItem(1)
        item.setText(_translate("LightPaymentDialog", " Amount"))
        __sortingEnabled = self.g_totals_table.isSortingEnabled()
        self.g_totals_table.setSortingEnabled(False)
        item = self.g_totals_table.item(0, 0)
        item.setText(_translate("LightPaymentDialog", "PETZ Store Discounts"))
        item = self.g_totals_table.item(0, 1)
        item.setText(_translate("LightPaymentDialog", "10.00"))
        item = self.g_totals_table.item(1, 0)
        item.setText(_translate("LightPaymentDialog", "PETZ Brand Coupons"))
        item = self.g_totals_table.item(1, 1)
        item.setText(_translate("LightPaymentDialog", "10.00"))
        item = self.g_totals_table.item(2, 0)
        item.setText(_translate("LightPaymentDialog", "Astro Frequent Buyer"))
        item = self.g_totals_table.item(2, 1)
        item.setText(_translate("LightPaymentDialog", "10.00"))
        item = self.g_totals_table.item(3, 0)
        item.setText(_translate("LightPaymentDialog", "Astro Offer Rebates"))
        item = self.g_totals_table.item(3, 1)
        item.setText(_translate("LightPaymentDialog", "10.00"))
        item = self.g_totals_table.item(4, 0)
        item.setText(_translate("LightPaymentDialog", "Astro Points Rewards"))
        item = self.g_totals_table.item(4, 1)
        item.setText(_translate("LightPaymentDialog", "10.00"))
        item = self.g_totals_table.item(5, 0)
        item.setText(_translate("LightPaymentDialog", "Sub-Total"))
        item = self.g_totals_table.item(5, 1)
        item.setText(_translate("LightPaymentDialog", "40.00"))
        self.g_totals_table.setSortingEnabled(__sortingEnabled)
        self.g_back_button.setText(_translate("LightPaymentDialog", "Back to Sale"))
        self.g_finish_button.setText(_translate("LightPaymentDialog", "Finish"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    LightPaymentDialog = QtWidgets.QDialog()
    ui = Ui_LightPaymentDialog()
    ui.setupUi(LightPaymentDialog)
    LightPaymentDialog.show()
    sys.exit(app.exec_())
