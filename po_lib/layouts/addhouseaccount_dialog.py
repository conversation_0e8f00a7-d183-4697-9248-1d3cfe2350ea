# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'addhouseaccount_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_AddHouseAccount(object):
    def setupUi(self, AddHouseAccount):
        AddHouseAccount.setObjectName("AddHouseAccount")
        AddHouseAccount.resize(387, 196)
        AddHouseAccount.setMinimumSize(QtCore.QSize(0, 0))
        AddHouseAccount.setMaximumSize(QtCore.QSize(********, ********))
        AddHouseAccount.setStyleSheet("#AddHouseAccount {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QLabel {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font: 17px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/products/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/products/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/products/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
"QGroupBox {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"#g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_save_button, #__g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"")
        self.verticalLayout = QtWidgets.QVBoxLayout(AddHouseAccount)
        self.verticalLayout.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_top_frame = QtWidgets.QFrame(AddHouseAccount)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setMaximumSize(QtCore.QSize(********, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_13.setSpacing(0)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_13.addWidget(self.g_progress_frame)
        self.verticalLayout.addWidget(self.g_top_frame)
        self.g_main_label = QtWidgets.QLabel(AddHouseAccount)
        self.g_main_label.setMaximumSize(QtCore.QSize(********, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setStyleSheet("")
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 5, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout.addItem(spacerItem)
        self.g_houseaccountname_frame = QtWidgets.QFrame(AddHouseAccount)
        self.g_houseaccountname_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_houseaccountname_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_houseaccountname_frame.setObjectName("g_houseaccountname_frame")
        self.horizontalLayout_34 = QtWidgets.QHBoxLayout(self.g_houseaccountname_frame)
        self.horizontalLayout_34.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_34.setSpacing(6)
        self.horizontalLayout_34.setObjectName("horizontalLayout_34")
        self.g_houseaccountname_label = QtWidgets.QLabel(self.g_houseaccountname_frame)
        self.g_houseaccountname_label.setMinimumSize(QtCore.QSize(75, 0))
        self.g_houseaccountname_label.setMaximumSize(QtCore.QSize(75, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_houseaccountname_label.setFont(font)
        self.g_houseaccountname_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_houseaccountname_label.setObjectName("g_houseaccountname_label")
        self.horizontalLayout_34.addWidget(self.g_houseaccountname_label)
        self.g_houseaccountname_lineedit = QtWidgets.QLineEdit(self.g_houseaccountname_frame)
        self.g_houseaccountname_lineedit.setMinimumSize(QtCore.QSize(200, 0))
        self.g_houseaccountname_lineedit.setMaximumSize(QtCore.QSize(200, 31))
        self.g_houseaccountname_lineedit.setStyleSheet("")
        self.g_houseaccountname_lineedit.setText("")
        self.g_houseaccountname_lineedit.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_houseaccountname_lineedit.setReadOnly(False)
        self.g_houseaccountname_lineedit.setPlaceholderText("")
        self.g_houseaccountname_lineedit.setProperty("qp_prlocs_min_safety_stock_days", "")
        self.g_houseaccountname_lineedit.setObjectName("g_houseaccountname_lineedit")
        self.horizontalLayout_34.addWidget(self.g_houseaccountname_lineedit)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_34.addItem(spacerItem1)
        self.verticalLayout.addWidget(self.g_houseaccountname_frame)
        self.frame_22 = QtWidgets.QFrame(AddHouseAccount)
        self.frame_22.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_22.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_22.setObjectName("frame_22")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.frame_22)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_houseaccountname_label_2 = QtWidgets.QLabel(self.frame_22)
        self.g_houseaccountname_label_2.setMinimumSize(QtCore.QSize(75, 0))
        self.g_houseaccountname_label_2.setMaximumSize(QtCore.QSize(75, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_houseaccountname_label_2.setFont(font)
        self.g_houseaccountname_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_houseaccountname_label_2.setObjectName("g_houseaccountname_label_2")
        self.horizontalLayout.addWidget(self.g_houseaccountname_label_2)
        self.g_houseaccount_radioButton = QtWidgets.QRadioButton(self.frame_22)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_houseaccount_radioButton.setFont(font)
        self.g_houseaccount_radioButton.setChecked(True)
        self.g_houseaccount_radioButton.setObjectName("g_houseaccount_radioButton")
        self.horizontalLayout.addWidget(self.g_houseaccount_radioButton)
        self.g_storecredit_radioButton = QtWidgets.QRadioButton(self.frame_22)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_storecredit_radioButton.setFont(font)
        self.g_storecredit_radioButton.setChecked(False)
        self.g_storecredit_radioButton.setObjectName("g_storecredit_radioButton")
        self.horizontalLayout.addWidget(self.g_storecredit_radioButton)
        self.verticalLayout.addWidget(self.frame_22)
        self.g_creditlimit_frame = QtWidgets.QFrame(AddHouseAccount)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_creditlimit_frame.sizePolicy().hasHeightForWidth())
        self.g_creditlimit_frame.setSizePolicy(sizePolicy)
        self.g_creditlimit_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_creditlimit_frame.setMaximumSize(QtCore.QSize(********, 31))
        self.g_creditlimit_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_creditlimit_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_creditlimit_frame.setObjectName("g_creditlimit_frame")
        self.horizontalLayout_28 = QtWidgets.QHBoxLayout(self.g_creditlimit_frame)
        self.horizontalLayout_28.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_28.setSpacing(6)
        self.horizontalLayout_28.setObjectName("horizontalLayout_28")
        self.g_creditlimit_label = QtWidgets.QLabel(self.g_creditlimit_frame)
        self.g_creditlimit_label.setMinimumSize(QtCore.QSize(75, 0))
        self.g_creditlimit_label.setMaximumSize(QtCore.QSize(75, ********))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_creditlimit_label.setFont(font)
        self.g_creditlimit_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_creditlimit_label.setObjectName("g_creditlimit_label")
        self.horizontalLayout_28.addWidget(self.g_creditlimit_label)
        self.g_creditlimit_lineEdit = QtWidgets.QLineEdit(self.g_creditlimit_frame)
        self.g_creditlimit_lineEdit.setMinimumSize(QtCore.QSize(100, 31))
        self.g_creditlimit_lineEdit.setMaximumSize(QtCore.QSize(89, ********))
        self.g_creditlimit_lineEdit.setObjectName("g_creditlimit_lineEdit")
        self.horizontalLayout_28.addWidget(self.g_creditlimit_lineEdit)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_28.addItem(spacerItem2)
        self.verticalLayout.addWidget(self.g_creditlimit_frame)
        spacerItem3 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem3)
        self.__g_controlbuttons_frame = QtWidgets.QFrame(AddHouseAccount)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.__g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.__g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.__g_controlbuttons_frame.setMaximumSize(QtCore.QSize(********, 40))
        self.__g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_controlbuttons_frame.setObjectName("__g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.__g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(6)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem4 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem4)
        self.g_cancel_button = QtWidgets.QPushButton(self.__g_controlbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_11.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.__g_controlbuttons_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_11.addWidget(self.g_save_button)
        self.verticalLayout.addWidget(self.__g_controlbuttons_frame)

        self.retranslateUi(AddHouseAccount)
        QtCore.QMetaObject.connectSlotsByName(AddHouseAccount)

    def retranslateUi(self, AddHouseAccount):
        _translate = QtCore.QCoreApplication.translate
        AddHouseAccount.setWindowTitle(_translate("AddHouseAccount", "Add House Account/Store Credit"))
        self.g_main_label.setText(_translate("AddHouseAccount", "Add House Account/Store Credit"))
        self.g_houseaccountname_label.setText(_translate("AddHouseAccount", "Name"))
        self.g_houseaccountname_label_2.setText(_translate("AddHouseAccount", "Type"))
        self.g_houseaccount_radioButton.setText(_translate("AddHouseAccount", "House Account"))
        self.g_storecredit_radioButton.setText(_translate("AddHouseAccount", "Store Credit"))
        self.g_creditlimit_label.setText(_translate("AddHouseAccount", "Credit Limit"))
        self.g_cancel_button.setText(_translate("AddHouseAccount", " Cancel"))
        self.g_save_button.setText(_translate("AddHouseAccount", " Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    AddHouseAccount = QtWidgets.QDialog()
    ui = Ui_AddHouseAccount()
    ui.setupUi(AddHouseAccount)
    AddHouseAccount.show()
    sys.exit(app.exec_())
