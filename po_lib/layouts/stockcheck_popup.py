# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'stockcheck_popup.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_StockCheck(object):
    def setupUi(self, StockCheck):
        StockCheck.setObjectName("StockCheck")
        StockCheck.resize(580, 480)
        StockCheck.setMinimumSize(QtCore.QSize(0, 480))
        StockCheck.setMaximumSize(QtCore.QSize(16777215, 16777215))
        StockCheck.setStyleSheet("#StockCheck {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QLabel {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font: 17px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_receiptdisclaimer_groupbox {\n"
"    background: #fff;\n"
"}\n"
"\n"
"#g_stockinfo_label {\n"
"    font: 12px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_convertospecialorder_label  {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #669CFF;\n"
"}\n"
"\n"
"\n"
"#g_convertospecialorder_label \n"
"#g_exptectedstockdata_label,\n"
"#g_stocknearbydata_label,\n"
"#g_altproductsdata_label {\n"
"    font: 11px;\n"
"    font-weight: normal;\n"
"    color: #000;\n"
"}\n"
"\n"
"\n"
"#g_exptectedstockdata_label,\n"
"#g_stocknearbydata_label,\n"
"#g_altproductsdata_label {\n"
"    font: 11px;\n"
"    font-weight: normal;\n"
"    color: #000;\n"
"}\n"
"\n"
"#g_selectbreeder_frame, #g_selectbreeder_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border:1px solid #073c83;\n"
"    border-radius:10px;\n"
"    background-color:#DDE4E9;\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QGroupBox {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
".QScrollArea {\n"
"    background-color: #f4f4f4;\n"
"    border-radius:5px;\n"
"    border:1px solid #073c83;\n"
"    padding:4px;\n"
"}\n"
"\n"
"QScrollBar:horizontal {\n"
"height:0px;\n"
"}\n"
"\n"
"#scrollAreaWidgetContents {\n"
"background-color: #f4f4f4;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"#g_cancel_button, #g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button, #g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_select_button, #g_new_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_select_button:pressed, #g_new_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_delete_button, #g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_delete_button:pressed, #g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_search_frame, #__g_search_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_search_box_frame, #__g_search_box_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_clearsearch_button, #__g_clearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_search_lineedit, #__g_search_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_search_combobox, #__g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_search_combobox::drop-down, #__g_search_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"}\n"
"\n"
"#g_searchtext_lineedit, #__g_searchtext_lineedit {\n"
"    border: 0px;\n"
"}")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(StockCheck)
        self.verticalLayout_2.setContentsMargins(9, 0, 9, 9)
        self.verticalLayout_2.setSpacing(6)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_top_frame = QtWidgets.QFrame(StockCheck)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_8.addWidget(self.g_progress_frame)
        self.verticalLayout_2.addWidget(self.g_top_frame)
        self.g_main_label = QtWidgets.QLabel(StockCheck)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setStyleSheet("")
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_2.addWidget(self.g_main_label)
        self.frame_3 = QtWidgets.QFrame(StockCheck)
        self.frame_3.setMinimumSize(QtCore.QSize(0, 40))
        self.frame_3.setMaximumSize(QtCore.QSize(16777215, 40))
        self.frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.frame_3)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.frame = QtWidgets.QFrame(self.frame_3)
        self.frame.setMinimumSize(QtCore.QSize(180, 0))
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.frame_4 = QtWidgets.QFrame(self.frame)
        self.frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_4.setObjectName("frame_4")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.frame_4)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_skulablel_label = QtWidgets.QLabel(self.frame_4)
        self.g_skulablel_label.setMaximumSize(QtCore.QSize(80, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_skulablel_label.setFont(font)
        self.g_skulablel_label.setStyleSheet("")
        self.g_skulablel_label.setObjectName("g_skulablel_label")
        self.horizontalLayout_2.addWidget(self.g_skulablel_label)
        self.g_sku_label = QtWidgets.QLabel(self.frame_4)
        self.g_sku_label.setMaximumSize(QtCore.QSize(150, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_sku_label.setFont(font)
        self.g_sku_label.setStyleSheet("")
        self.g_sku_label.setObjectName("g_sku_label")
        self.horizontalLayout_2.addWidget(self.g_sku_label)
        self.verticalLayout.addWidget(self.frame_4)
        self.frame_5 = QtWidgets.QFrame(self.frame)
        self.frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_5.setObjectName("frame_5")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.frame_5)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_qtylabel_label = QtWidgets.QLabel(self.frame_5)
        self.g_qtylabel_label.setMaximumSize(QtCore.QSize(80, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_qtylabel_label.setFont(font)
        self.g_qtylabel_label.setStyleSheet("")
        self.g_qtylabel_label.setObjectName("g_qtylabel_label")
        self.horizontalLayout_3.addWidget(self.g_qtylabel_label)
        self.g_qty_label = QtWidgets.QLabel(self.frame_5)
        self.g_qty_label.setMaximumSize(QtCore.QSize(150, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_qty_label.setFont(font)
        self.g_qty_label.setStyleSheet("")
        self.g_qty_label.setObjectName("g_qty_label")
        self.horizontalLayout_3.addWidget(self.g_qty_label)
        self.verticalLayout.addWidget(self.frame_5)
        self.horizontalLayout.addWidget(self.frame)
        self.frame_6 = QtWidgets.QFrame(self.frame_3)
        self.frame_6.setMinimumSize(QtCore.QSize(180, 0))
        self.frame_6.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_6.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_6.setObjectName("frame_6")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.frame_6)
        self.verticalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.frame_7 = QtWidgets.QFrame(self.frame_6)
        self.frame_7.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_7.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_7.setObjectName("frame_7")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.frame_7)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_descriptionlablel_label = QtWidgets.QLabel(self.frame_7)
        self.g_descriptionlablel_label.setMaximumSize(QtCore.QSize(80, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_descriptionlablel_label.setFont(font)
        self.g_descriptionlablel_label.setStyleSheet("")
        self.g_descriptionlablel_label.setObjectName("g_descriptionlablel_label")
        self.horizontalLayout_4.addWidget(self.g_descriptionlablel_label)
        self.g_description_label = QtWidgets.QLabel(self.frame_7)
        self.g_description_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_description_label.setFont(font)
        self.g_description_label.setStyleSheet("")
        self.g_description_label.setObjectName("g_description_label")
        self.horizontalLayout_4.addWidget(self.g_description_label)
        self.verticalLayout_4.addWidget(self.frame_7)
        self.frame_8 = QtWidgets.QFrame(self.frame_6)
        self.frame_8.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_8.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_8.setObjectName("frame_8")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.frame_8)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_barcodelabel_label = QtWidgets.QLabel(self.frame_8)
        self.g_barcodelabel_label.setMaximumSize(QtCore.QSize(80, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_barcodelabel_label.setFont(font)
        self.g_barcodelabel_label.setStyleSheet("")
        self.g_barcodelabel_label.setObjectName("g_barcodelabel_label")
        self.horizontalLayout_5.addWidget(self.g_barcodelabel_label)
        self.g_barcode_label = QtWidgets.QLabel(self.frame_8)
        self.g_barcode_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_barcode_label.setFont(font)
        self.g_barcode_label.setStyleSheet("")
        self.g_barcode_label.setObjectName("g_barcode_label")
        self.horizontalLayout_5.addWidget(self.g_barcode_label)
        self.verticalLayout_4.addWidget(self.frame_8)
        self.horizontalLayout.addWidget(self.frame_6)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.verticalLayout_2.addWidget(self.frame_3)
        self.g_receiptdisclaimer_groupbox = QtWidgets.QGroupBox(StockCheck)
        self.g_receiptdisclaimer_groupbox.setMinimumSize(QtCore.QSize(0, 0))
        self.g_receiptdisclaimer_groupbox.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_receiptdisclaimer_groupbox.setFont(font)
        self.g_receiptdisclaimer_groupbox.setTitle("")
        self.g_receiptdisclaimer_groupbox.setObjectName("g_receiptdisclaimer_groupbox")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.g_receiptdisclaimer_groupbox)
        self.verticalLayout_5.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout_5.setSpacing(6)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.g_stockinfo_label = QtWidgets.QLabel(self.g_receiptdisclaimer_groupbox)
        self.g_stockinfo_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_stockinfo_label.setFont(font)
        self.g_stockinfo_label.setStyleSheet("")
        self.g_stockinfo_label.setObjectName("g_stockinfo_label")
        self.verticalLayout_5.addWidget(self.g_stockinfo_label)
        spacerItem1 = QtWidgets.QSpacerItem(20, 10, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_5.addItem(spacerItem1)
        self.frame_9 = QtWidgets.QFrame(self.g_receiptdisclaimer_groupbox)
        self.frame_9.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_9.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_9.setObjectName("frame_9")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.frame_9)
        self.verticalLayout_3.setContentsMargins(-1, 0, 0, 0)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.frame_2 = QtWidgets.QFrame(self.frame_9)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.frame_2)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_expectedstock_label = QtWidgets.QLabel(self.frame_2)
        self.g_expectedstock_label.setMaximumSize(QtCore.QSize(175, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_expectedstock_label.setFont(font)
        self.g_expectedstock_label.setStyleSheet("")
        self.g_expectedstock_label.setObjectName("g_expectedstock_label")
        self.horizontalLayout_6.addWidget(self.g_expectedstock_label)
        self.g_convertospecialorder_label = QtWidgets.QLabel(self.frame_2)
        self.g_convertospecialorder_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_convertospecialorder_label.setFont(font)
        self.g_convertospecialorder_label.setStyleSheet("")
        self.g_convertospecialorder_label.setObjectName("g_convertospecialorder_label")
        self.horizontalLayout_6.addWidget(self.g_convertospecialorder_label)
        self.verticalLayout_3.addWidget(self.frame_2)
        self.g_exptectedstockdata_label = QtWidgets.QLabel(self.frame_9)
        self.g_exptectedstockdata_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setItalic(False)
        font.setWeight(50)
        self.g_exptectedstockdata_label.setFont(font)
        self.g_exptectedstockdata_label.setStyleSheet("")
        self.g_exptectedstockdata_label.setObjectName("g_exptectedstockdata_label")
        self.verticalLayout_3.addWidget(self.g_exptectedstockdata_label)
        self.verticalLayout_5.addWidget(self.frame_9)
        spacerItem2 = QtWidgets.QSpacerItem(20, 15, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_5.addItem(spacerItem2)
        self.g_stockatnearbylocations_frame = QtWidgets.QFrame(self.g_receiptdisclaimer_groupbox)
        self.g_stockatnearbylocations_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_stockatnearbylocations_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_stockatnearbylocations_frame.setObjectName("g_stockatnearbylocations_frame")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.g_stockatnearbylocations_frame)
        self.verticalLayout_6.setContentsMargins(-1, 0, 0, 0)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.g_stocknearby_label = QtWidgets.QLabel(self.g_stockatnearbylocations_frame)
        self.g_stocknearby_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_stocknearby_label.setFont(font)
        self.g_stocknearby_label.setStyleSheet("")
        self.g_stocknearby_label.setObjectName("g_stocknearby_label")
        self.verticalLayout_6.addWidget(self.g_stocknearby_label)
        self.g_stocknearbydata_label = QtWidgets.QLabel(self.g_stockatnearbylocations_frame)
        self.g_stocknearbydata_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setItalic(False)
        font.setWeight(50)
        self.g_stocknearbydata_label.setFont(font)
        self.g_stocknearbydata_label.setStyleSheet("")
        self.g_stocknearbydata_label.setObjectName("g_stocknearbydata_label")
        self.verticalLayout_6.addWidget(self.g_stocknearbydata_label)
        self.verticalLayout_5.addWidget(self.g_stockatnearbylocations_frame)
        spacerItem3 = QtWidgets.QSpacerItem(20, 15, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_5.addItem(spacerItem3)
        self.g_alternativeproducts_frame = QtWidgets.QFrame(self.g_receiptdisclaimer_groupbox)
        self.g_alternativeproducts_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_alternativeproducts_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_alternativeproducts_frame.setObjectName("g_alternativeproducts_frame")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.g_alternativeproducts_frame)
        self.verticalLayout_7.setContentsMargins(-1, 0, 0, 0)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.g_altproducts_label = QtWidgets.QLabel(self.g_alternativeproducts_frame)
        self.g_altproducts_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_altproducts_label.setFont(font)
        self.g_altproducts_label.setStyleSheet("")
        self.g_altproducts_label.setObjectName("g_altproducts_label")
        self.verticalLayout_7.addWidget(self.g_altproducts_label)
        self.g_altproductsdata_label = QtWidgets.QLabel(self.g_alternativeproducts_frame)
        self.g_altproductsdata_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setItalic(False)
        font.setWeight(50)
        self.g_altproductsdata_label.setFont(font)
        self.g_altproductsdata_label.setStyleSheet("")
        self.g_altproductsdata_label.setObjectName("g_altproductsdata_label")
        self.verticalLayout_7.addWidget(self.g_altproductsdata_label)
        self.verticalLayout_5.addWidget(self.g_alternativeproducts_frame)
        spacerItem4 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_5.addItem(spacerItem4)
        self.verticalLayout_2.addWidget(self.g_receiptdisclaimer_groupbox)
        self.g_controlbuttons_frame = QtWidgets.QFrame(StockCheck)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(6)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem5)
        self.g_delete_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_delete_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_delete_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_delete_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_delete_button.setIcon(icon)
        self.g_delete_button.setIconSize(QtCore.QSize(24, 24))
        self.g_delete_button.setObjectName("g_delete_button")
        self.horizontalLayout_11.addWidget(self.g_delete_button)
        self.g_manage_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_manage_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/edit"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_manage_button.setIcon(icon1)
        self.g_manage_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manage_button.setObjectName("g_manage_button")
        self.horizontalLayout_11.addWidget(self.g_manage_button)
        self.g_select_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_select_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_select_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_select_button.setIcon(icon2)
        self.g_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_select_button.setObjectName("g_select_button")
        self.horizontalLayout_11.addWidget(self.g_select_button)
        self.verticalLayout_2.addWidget(self.g_controlbuttons_frame)

        self.retranslateUi(StockCheck)
        QtCore.QMetaObject.connectSlotsByName(StockCheck)

    def retranslateUi(self, StockCheck):
        _translate = QtCore.QCoreApplication.translate
        StockCheck.setWindowTitle(_translate("StockCheck", "Stock Check"))
        self.g_main_label.setText(_translate("StockCheck", "Stock Check"))
        self.g_skulablel_label.setText(_translate("StockCheck", "SKU:"))
        self.g_sku_label.setText(_translate("StockCheck", "12345"))
        self.g_qtylabel_label.setText(_translate("StockCheck", "Available Qty:"))
        self.g_qty_label.setText(_translate("StockCheck", "0"))
        self.g_descriptionlablel_label.setText(_translate("StockCheck", "Description:"))
        self.g_description_label.setText(_translate("StockCheck", "12345"))
        self.g_barcodelabel_label.setText(_translate("StockCheck", "Barcode:"))
        self.g_barcode_label.setText(_translate("StockCheck", "0"))
        self.g_stockinfo_label.setText(_translate("StockCheck", "Stock Info / Alternative Options are shown below:"))
        self.g_expectedstock_label.setText(_translate("StockCheck", "Expected Stock from Orders"))
        self.g_convertospecialorder_label.setText(_translate("StockCheck", "Convert to Special Order"))
        self.g_exptectedstockdata_label.setText(_translate("StockCheck", "Data goes here"))
        self.g_stocknearby_label.setText(_translate("StockCheck", "Stock at Nearby Locations"))
        self.g_stocknearbydata_label.setText(_translate("StockCheck", "Data goes here"))
        self.g_altproducts_label.setText(_translate("StockCheck", "Alernative Products"))
        self.g_altproductsdata_label.setText(_translate("StockCheck", "Data goes here"))
        self.g_delete_button.setText(_translate("StockCheck", "Remove\n"
"from Inv."))
        self.g_manage_button.setText(_translate("StockCheck", "Adjust\n"
"Inventory"))
        self.g_select_button.setText(_translate("StockCheck", "Keep On\n"
"Invoice"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    StockCheck = QtWidgets.QDialog()
    ui = Ui_StockCheck()
    ui.setupUi(StockCheck)
    StockCheck.show()
    sys.exit(app.exec_())
