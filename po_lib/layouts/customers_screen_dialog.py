# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'customers_screen_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_CustomersScreenDialog(object):
    def setupUi(self, CustomersScreenDialog):
        CustomersScreenDialog.setObjectName("CustomersScreenDialog")
        CustomersScreenDialog.resize(965, 546)
        CustomersScreenDialog.setMinimumSize(QtCore.QSize(730, 360))
        CustomersScreenDialog.setStyleSheet("#CustomersScreenDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QLabel {\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_deleted_label {\n"
"    color: red\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_disabled_label {\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    background: white;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    background-image: url(:/customers_screen_dialog/drop_down_arrow);\n"
"}\n"
"\n"
"#g_groups_checkcombobox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    background: transparent;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"#g_unifiedsearch_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_searchtext_lineedit,\n"
"#g_clearsearch_frame,\n"
"#g_searchbox_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"#g_clearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_searchtext_lineedit {\n"
"    border: 0px;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"#g_search_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QDateTimeEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    height:31px\n"
"}\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QTableView {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"    font-size: 12px;\n"
"    font-weight: normal;\n"
"}\n"
"\n"
"QTableView::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size: 12px;\n"
"    font-weight: bold;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"#g_dialog_frame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"#g_description_label {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color: #dddddd;\n"
"    color: #333333;\n"
"}\n"
"\n"
"#g_restore_button,\n"
"#g_select_button,\n"
"#g_massaction_button,\n"
"#g_new_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_restore_button:pressed,\n"
"#g_select_button:pressed\n"
"#g_massaction_button:pressed,\n"
"#g_new_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_delete_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_delete_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_search_combobox::drop-down,\n"
"QDateTimeEdit::drop-down {\n"
"    background-image: url(:/customers_screen_dialog/drop_down_arrow);\n"
"}\n"
"\n"
"#g_totalresults_label {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"\n"
"#g_clearselected_button {\n"
"    padding: 6px;\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"    color: #000000;\n"
"    border: 1px solid #606260;\n"
"    border-radius: 5px;\n"
"    background-color: #d9dcda;\n"
"}")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/customers_screen_dialog/customers"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        CustomersScreenDialog.setProperty("screen_toolbutton_icon", icon)
        self.verticalLayout = QtWidgets.QVBoxLayout(CustomersScreenDialog)
        self.verticalLayout.setContentsMargins(9, 0, 9, 9)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_top_frame = QtWidgets.QFrame(CustomersScreenDialog)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_8.addWidget(self.g_progress_frame)
        self.verticalLayout.addWidget(self.g_top_frame)
        self.frame = QtWidgets.QFrame(CustomersScreenDialog)
        self.frame.setMinimumSize(QtCore.QSize(0, 30))
        self.frame.setMaximumSize(QtCore.QSize(16777215, 30))
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setSpacing(6)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_main_label = QtWidgets.QLabel(self.frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setStyleSheet("")
        self.g_main_label.setObjectName("g_main_label")
        self.horizontalLayout_2.addWidget(self.g_main_label)
        self.g_deleted_label = QtWidgets.QLabel(self.frame)
        self.g_deleted_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_deleted_label.setFont(font)
        self.g_deleted_label.setStyleSheet("")
        self.g_deleted_label.setObjectName("g_deleted_label")
        self.horizontalLayout_2.addWidget(self.g_deleted_label)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem)
        self.g_filterwarning_checkbox = QtWidgets.QCheckBox(self.frame)
        self.g_filterwarning_checkbox.setMinimumSize(QtCore.QSize(0, 22))
        self.g_filterwarning_checkbox.setMaximumSize(QtCore.QSize(280, 16777215))
        self.g_filterwarning_checkbox.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_filterwarning_checkbox.setChecked(False)
        self.g_filterwarning_checkbox.setObjectName("g_filterwarning_checkbox")
        self.horizontalLayout_2.addWidget(self.g_filterwarning_checkbox)
        self.g_warningdisabled_label = QtWidgets.QLabel(self.frame)
        self.g_warningdisabled_label.setEnabled(True)
        self.g_warningdisabled_label.setText("")
        self.g_warningdisabled_label.setPixmap(QtGui.QPixmap(":/customers_screen_dialog/warning_hide"))
        self.g_warningdisabled_label.setObjectName("g_warningdisabled_label")
        self.horizontalLayout_2.addWidget(self.g_warningdisabled_label)
        self.g_warning_label = QtWidgets.QLabel(self.frame)
        self.g_warning_label.setEnabled(True)
        self.g_warning_label.setText("")
        self.g_warning_label.setPixmap(QtGui.QPixmap(":/customers_screen_dialog/warning_show"))
        self.g_warning_label.setObjectName("g_warning_label")
        self.horizontalLayout_2.addWidget(self.g_warning_label)
        spacerItem1 = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem1)
        self.g_edittable_label = QtWidgets.QLabel(self.frame)
        self.g_edittable_label.setEnabled(True)
        self.g_edittable_label.setText("")
        self.g_edittable_label.setPixmap(QtGui.QPixmap(":/customers_screen_dialog/pencil"))
        self.g_edittable_label.setObjectName("g_edittable_label")
        self.horizontalLayout_2.addWidget(self.g_edittable_label)
        spacerItem2 = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem2)
        self.g_export_label = QtWidgets.QLabel(self.frame)
        self.g_export_label.setEnabled(True)
        self.g_export_label.setText("")
        self.g_export_label.setPixmap(QtGui.QPixmap(":/customers_screen_dialog/export"))
        self.g_export_label.setObjectName("g_export_label")
        self.horizontalLayout_2.addWidget(self.g_export_label)
        spacerItem3 = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem3)
        self.g_tablesettings_label = QtWidgets.QLabel(self.frame)
        self.g_tablesettings_label.setEnabled(True)
        self.g_tablesettings_label.setText("")
        self.g_tablesettings_label.setPixmap(QtGui.QPixmap(":/customers_screen_dialog/gear"))
        self.g_tablesettings_label.setObjectName("g_tablesettings_label")
        self.horizontalLayout_2.addWidget(self.g_tablesettings_label)
        spacerItem4 = QtWidgets.QSpacerItem(5, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem4)
        self.verticalLayout.addWidget(self.frame)
        self.frame_2 = QtWidgets.QFrame(CustomersScreenDialog)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_unifiedsearch_frame = QtWidgets.QFrame(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_unifiedsearch_frame.sizePolicy().hasHeightForWidth())
        self.g_unifiedsearch_frame.setSizePolicy(sizePolicy)
        self.g_unifiedsearch_frame.setMinimumSize(QtCore.QSize(561, 41))
        self.g_unifiedsearch_frame.setMaximumSize(QtCore.QSize(166546, 41))
        self.g_unifiedsearch_frame.setStyleSheet("")
        self.g_unifiedsearch_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_unifiedsearch_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_unifiedsearch_frame.setObjectName("g_unifiedsearch_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_unifiedsearch_frame)
        self.horizontalLayout_4.setContentsMargins(0, 4, 0, 6)
        self.horizontalLayout_4.setSpacing(6)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_search_combobox = QtWidgets.QComboBox(self.g_unifiedsearch_frame)
        self.g_search_combobox.setMinimumSize(QtCore.QSize(125, 0))
        self.g_search_combobox.setMaximumSize(QtCore.QSize(125, 31))
        self.g_search_combobox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_search_combobox.setStyleSheet("")
        self.g_search_combobox.setFrame(False)
        self.g_search_combobox.setObjectName("g_search_combobox")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.g_search_combobox.addItem("")
        self.horizontalLayout_4.addWidget(self.g_search_combobox)
        self.g_searchbox_frame = QtWidgets.QFrame(self.g_unifiedsearch_frame)
        self.g_searchbox_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_searchbox_frame.setStyleSheet("")
        self.g_searchbox_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_searchbox_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_searchbox_frame.setObjectName("g_searchbox_frame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.g_searchbox_frame)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setSpacing(6)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.g_groups_checkcombobox = PyCheckCombobox(self.g_searchbox_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_groups_checkcombobox.sizePolicy().hasHeightForWidth())
        self.g_groups_checkcombobox.setSizePolicy(sizePolicy)
        self.g_groups_checkcombobox.setMinimumSize(QtCore.QSize(200, 31))
        self.g_groups_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_groups_checkcombobox.setToolTip("")
        self.g_groups_checkcombobox.setWhatsThis("")
        self.g_groups_checkcombobox.setCurrentText("")
        self.g_groups_checkcombobox.setObjectName("g_groups_checkcombobox")
        self.horizontalLayout_5.addWidget(self.g_groups_checkcombobox)
        self.g_unisearch_combobox = QtWidgets.QComboBox(self.g_searchbox_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_unisearch_combobox.sizePolicy().hasHeightForWidth())
        self.g_unisearch_combobox.setSizePolicy(sizePolicy)
        self.g_unisearch_combobox.setMinimumSize(QtCore.QSize(200, 0))
        self.g_unisearch_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_unisearch_combobox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_unisearch_combobox.setStyleSheet("")
        self.g_unisearch_combobox.setFrame(False)
        self.g_unisearch_combobox.setObjectName("g_unisearch_combobox")
        self.g_unisearch_combobox.addItem("")
        self.g_unisearch_combobox.addItem("")
        self.g_unisearch_combobox.addItem("")
        self.g_unisearch_combobox.addItem("")
        self.horizontalLayout_5.addWidget(self.g_unisearch_combobox)
        self.g_searchtext_lineedit = QtWidgets.QLineEdit(self.g_searchbox_frame)
        self.g_searchtext_lineedit.setStyleSheet("")
        self.g_searchtext_lineedit.setText("")
        self.g_searchtext_lineedit.setObjectName("g_searchtext_lineedit")
        self.horizontalLayout_5.addWidget(self.g_searchtext_lineedit)
        self.g_cross_frame = QtWidgets.QFrame(self.g_searchbox_frame)
        self.g_cross_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cross_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cross_frame.setObjectName("g_cross_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_cross_frame)
        self.horizontalLayout_6.setContentsMargins(6, 0, 9, 0)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_clearsearch_button = QtWidgets.QPushButton(self.g_cross_frame)
        self.g_clearsearch_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_clearsearch_button.setStyleSheet("")
        self.g_clearsearch_button.setText("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/customers_screen_dialog/delete_search_terms"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_clearsearch_button.setIcon(icon1)
        self.g_clearsearch_button.setIconSize(QtCore.QSize(18, 18))
        self.g_clearsearch_button.setObjectName("g_clearsearch_button")
        self.horizontalLayout_6.addWidget(self.g_clearsearch_button)
        self.horizontalLayout_5.addWidget(self.g_cross_frame)
        self.horizontalLayout_4.addWidget(self.g_searchbox_frame)
        self.g_nav_frame = QtWidgets.QFrame(self.g_unifiedsearch_frame)
        self.g_nav_frame.setMinimumSize(QtCore.QSize(150, 0))
        self.g_nav_frame.setMaximumSize(QtCore.QSize(150, 16777215))
        self.g_nav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_nav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_nav_frame.setObjectName("g_nav_frame")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_nav_frame)
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_3.setSpacing(0)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_pagenav_hlayout_3 = QtWidgets.QHBoxLayout()
        self.g_pagenav_hlayout_3.setSpacing(6)
        self.g_pagenav_hlayout_3.setObjectName("g_pagenav_hlayout_3")
        self.g_previouspage_label = QtWidgets.QLabel(self.g_nav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_previouspage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/customers_screen_dialog/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout_3.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_nav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_nextpage_label.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/customers_screen_dialog/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout_3.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_nav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(0, 28))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout_3.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label_4 = QtWidgets.QLabel(self.g_nav_frame)
        self.g_slash_label_4.setMinimumSize(QtCore.QSize(7, 0))
        self.g_slash_label_4.setMaximumSize(QtCore.QSize(7, 16777215))
        self.g_slash_label_4.setObjectName("g_slash_label_4")
        self.g_pagenav_hlayout_3.addWidget(self.g_slash_label_4)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_nav_frame)
        self.g_totalpages_label.setMinimumSize(QtCore.QSize(30, 0))
        self.g_totalpages_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout_3.addWidget(self.g_totalpages_label)
        self.verticalLayout_3.addLayout(self.g_pagenav_hlayout_3)
        self.horizontalLayout_4.addWidget(self.g_nav_frame)
        self.verticalLayout_2.addWidget(self.g_unifiedsearch_frame)
        self.g_quickfilters_frame = QtWidgets.QFrame(self.frame_2)
        self.g_quickfilters_frame.setMinimumSize(QtCore.QSize(0, 38))
        self.g_quickfilters_frame.setMaximumSize(QtCore.QSize(16777215, 38))
        self.g_quickfilters_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_quickfilters_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_quickfilters_frame.setObjectName("g_quickfilters_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_quickfilters_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 6)
        self.horizontalLayout_3.setSpacing(6)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        spacerItem5 = QtWidgets.QSpacerItem(130, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem5)
        self.g_serachlist_horizontalLayout = QtWidgets.QHBoxLayout()
        self.g_serachlist_horizontalLayout.setObjectName("g_serachlist_horizontalLayout")
        self.horizontalLayout_3.addLayout(self.g_serachlist_horizontalLayout)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem6)
        self.verticalLayout_2.addWidget(self.g_quickfilters_frame)
        self.verticalLayout.addWidget(self.frame_2)
        self.g_customerlist_tableView = QtWidgets.QTableView(CustomersScreenDialog)
        self.g_customerlist_tableView.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_customerlist_tableView.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_customerlist_tableView.setEditTriggers(QtWidgets.QAbstractItemView.AllEditTriggers)
        self.g_customerlist_tableView.setProperty("showDropIndicator", True)
        self.g_customerlist_tableView.setAlternatingRowColors(True)
        self.g_customerlist_tableView.setSelectionMode(QtWidgets.QAbstractItemView.MultiSelection)
        self.g_customerlist_tableView.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_customerlist_tableView.setShowGrid(False)
        self.g_customerlist_tableView.setSortingEnabled(True)
        self.g_customerlist_tableView.setObjectName("g_customerlist_tableView")
        self.g_customerlist_tableView.horizontalHeader().setDefaultSectionSize(90)
        self.g_customerlist_tableView.horizontalHeader().setStretchLastSection(True)
        self.verticalLayout.addWidget(self.g_customerlist_tableView)
        self.g_dialogbuttons_frame = QtWidgets.QFrame(CustomersScreenDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_dialogbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_dialogbuttons_frame.setSizePolicy(sizePolicy)
        self.g_dialogbuttons_frame.setMinimumSize(QtCore.QSize(0, 46))
        self.g_dialogbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 46))
        self.g_dialogbuttons_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_dialogbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_dialogbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_dialogbuttons_frame.setObjectName("g_dialogbuttons_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_dialogbuttons_frame)
        self.horizontalLayout.setContentsMargins(0, 6, 0, 0)
        self.horizontalLayout.setSpacing(6)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.frame_3 = QtWidgets.QFrame(self.g_dialogbuttons_frame)
        self.frame_3.setMinimumSize(QtCore.QSize(0, 0))
        self.frame_3.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.frame_3)
        self.horizontalLayout_10.setContentsMargins(6, 0, 10, 0)
        self.horizontalLayout_10.setSpacing(6)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.g_totalresults_label = QtWidgets.QLabel(self.frame_3)
        self.g_totalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_totalresults_label.setFont(font)
        self.g_totalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_totalresults_label.setObjectName("g_totalresults_label")
        self.horizontalLayout_10.addWidget(self.g_totalresults_label)
        self.g_clearselected_button = QtWidgets.QPushButton(self.frame_3)
        self.g_clearselected_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_clearselected_button.sizePolicy().hasHeightForWidth())
        self.g_clearselected_button.setSizePolicy(sizePolicy)
        self.g_clearselected_button.setMinimumSize(QtCore.QSize(130, 30))
        self.g_clearselected_button.setMaximumSize(QtCore.QSize(135, 30))
        self.g_clearselected_button.setLayoutDirection(QtCore.Qt.RightToLeft)
        self.g_clearselected_button.setStyleSheet("")
        self.g_clearselected_button.setIcon(icon1)
        self.g_clearselected_button.setIconSize(QtCore.QSize(12, 12))
        self.g_clearselected_button.setFlat(False)
        self.g_clearselected_button.setObjectName("g_clearselected_button")
        self.horizontalLayout_10.addWidget(self.g_clearselected_button)
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_10.addItem(spacerItem7)
        self.horizontalLayout.addWidget(self.frame_3)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem8)
        spacerItem9 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem9)
        self.g_restore_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_restore_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_restore_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_restore_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/customers_screen_dialog/save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_restore_button.setIcon(icon2)
        self.g_restore_button.setIconSize(QtCore.QSize(24, 24))
        self.g_restore_button.setObjectName("g_restore_button")
        self.horizontalLayout.addWidget(self.g_restore_button)
        self.g_control_frame = QtWidgets.QFrame(self.g_dialogbuttons_frame)
        self.g_control_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_control_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_control_frame.setObjectName("g_control_frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_control_frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setSpacing(6)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_delete_button = QtWidgets.QPushButton(self.g_control_frame)
        self.g_delete_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_delete_button.sizePolicy().hasHeightForWidth())
        self.g_delete_button.setSizePolicy(sizePolicy)
        self.g_delete_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_delete_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_delete_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_delete_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/customers_screen_dialog/delete"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_delete_button.setIcon(icon3)
        self.g_delete_button.setIconSize(QtCore.QSize(24, 24))
        self.g_delete_button.setProperty("screen_toolbutton_icon", icon)
        self.g_delete_button.setObjectName("g_delete_button")
        self.horizontalLayout_7.addWidget(self.g_delete_button)
        self.g_merge_button = QtWidgets.QPushButton(self.g_control_frame)
        self.g_merge_button.setEnabled(True)
        self.g_merge_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_merge_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_merge_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_merge_button.setStyleSheet("")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/customers_screen_dialog/merge"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_merge_button.setIcon(icon4)
        self.g_merge_button.setIconSize(QtCore.QSize(24, 24))
        self.g_merge_button.setObjectName("g_merge_button")
        self.horizontalLayout_7.addWidget(self.g_merge_button)
        self.g_manage_button = QtWidgets.QPushButton(self.g_control_frame)
        self.g_manage_button.setEnabled(True)
        self.g_manage_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_manage_button.setStyleSheet("")
        icon5 = QtGui.QIcon()
        icon5.addPixmap(QtGui.QPixmap(":/customers_screen_dialog/manage"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_manage_button.setIcon(icon5)
        self.g_manage_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manage_button.setObjectName("g_manage_button")
        self.horizontalLayout_7.addWidget(self.g_manage_button)
        self.g_select_button = QtWidgets.QPushButton(self.g_control_frame)
        self.g_select_button.setEnabled(True)
        self.g_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_select_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_select_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_select_button.setStyleSheet("")
        self.g_select_button.setIcon(icon2)
        self.g_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_select_button.setObjectName("g_select_button")
        self.horizontalLayout_7.addWidget(self.g_select_button)
        self.g_massaction_button = QtWidgets.QPushButton(self.g_control_frame)
        self.g_massaction_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_massaction_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_massaction_button.setStyleSheet("")
        icon6 = QtGui.QIcon()
        icon6.addPixmap(QtGui.QPixmap(":/customers_screen_dialog/mass_action"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_massaction_button.setIcon(icon6)
        self.g_massaction_button.setIconSize(QtCore.QSize(24, 24))
        self.g_massaction_button.setObjectName("g_massaction_button")
        self.horizontalLayout_7.addWidget(self.g_massaction_button)
        self.g_new_button = QtWidgets.QPushButton(self.g_control_frame)
        self.g_new_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_new_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_new_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_new_button.setStyleSheet("")
        icon7 = QtGui.QIcon()
        icon7.addPixmap(QtGui.QPixmap(":/customers_screen_dialog/new"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_new_button.setIcon(icon7)
        self.g_new_button.setIconSize(QtCore.QSize(24, 24))
        self.g_new_button.setObjectName("g_new_button")
        self.horizontalLayout_7.addWidget(self.g_new_button)
        self.horizontalLayout.addWidget(self.g_control_frame)
        self.verticalLayout.addWidget(self.g_dialogbuttons_frame)

        self.retranslateUi(CustomersScreenDialog)
        QtCore.QMetaObject.connectSlotsByName(CustomersScreenDialog)

    def retranslateUi(self, CustomersScreenDialog):
        _translate = QtCore.QCoreApplication.translate
        CustomersScreenDialog.setWindowTitle(_translate("CustomersScreenDialog", "Customer Management"))
        CustomersScreenDialog.setProperty("screen_toolbutton_tooltip_text", _translate("CustomersScreenDialog", "View or manage customers"))
        CustomersScreenDialog.setProperty("screen_toolbutton_title_text", _translate("CustomersScreenDialog", "Customers"))
        CustomersScreenDialog.setProperty("screen_toolbutton_stylesheet_text", _translate("CustomersScreenDialog", "\n"
"QToolButton {\n"
"    background: #bb1d48;\n"
"    border: 2px solid #97173A;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #97173A;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}"))
        CustomersScreenDialog.setProperty("screen_indicator_stylesheet_text", _translate("CustomersScreenDialog", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"            the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #97173A;\n"
"    border: 0;\n"
"}"))
        self.g_main_label.setText(_translate("CustomersScreenDialog", "Customer Management"))
        self.g_deleted_label.setText(_translate("CustomersScreenDialog", "Deleted Customer Management"))
        self.g_filterwarning_checkbox.setText(_translate("CustomersScreenDialog", "Show Only Warnings"))
        self.g_warningdisabled_label.setToolTip(_translate("CustomersScreenDialog", "Smart Warnings"))
        self.g_warning_label.setToolTip(_translate("CustomersScreenDialog", "Smart Warnings"))
        self.g_edittable_label.setToolTip(_translate("CustomersScreenDialog", "Smart Lines"))
        self.g_export_label.setToolTip(_translate("CustomersScreenDialog", "Smart Export"))
        self.g_tablesettings_label.setToolTip(_translate("CustomersScreenDialog", "Smart Table Settings"))
        self.g_search_combobox.setItemText(0, _translate("CustomersScreenDialog", "Smart Search"))
        self.g_search_combobox.setItemText(1, _translate("CustomersScreenDialog", "Search Location"))
        self.g_search_combobox.setItemText(2, _translate("CustomersScreenDialog", "Search Phone"))
        self.g_search_combobox.setItemText(3, _translate("CustomersScreenDialog", "Search Email"))
        self.g_unisearch_combobox.setItemText(0, _translate("CustomersScreenDialog", "Smart Search"))
        self.g_unisearch_combobox.setItemText(1, _translate("CustomersScreenDialog", "Search Location"))
        self.g_unisearch_combobox.setItemText(2, _translate("CustomersScreenDialog", "Search Phone"))
        self.g_unisearch_combobox.setItemText(3, _translate("CustomersScreenDialog", "Search Email"))
        self.g_searchtext_lineedit.setPlaceholderText(_translate("CustomersScreenDialog", "Enter your search terms then press Enter..."))
        self.g_previouspage_label.setToolTip(_translate("CustomersScreenDialog", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("CustomersScreenDialog", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("CustomersScreenDialog", "999"))
        self.g_slash_label_4.setText(_translate("CustomersScreenDialog", "/"))
        self.g_totalpages_label.setToolTip(_translate("CustomersScreenDialog", "Total Pages"))
        self.g_totalpages_label.setText(_translate("CustomersScreenDialog", "999"))
        self.g_totalresults_label.setText(_translate("CustomersScreenDialog", "Loading results."))
        self.g_clearselected_button.setText(_translate("CustomersScreenDialog", "Clear Selected  "))
        self.g_restore_button.setText(_translate("CustomersScreenDialog", "Restore"))
        self.g_delete_button.setText(_translate("CustomersScreenDialog", " Delete"))
        self.g_delete_button.setProperty("screen_toolbutton_tooltip_text", _translate("CustomersScreenDialog", "View or manage customers"))
        self.g_delete_button.setProperty("screen_toolbutton_title_text", _translate("CustomersScreenDialog", "Customers"))
        self.g_delete_button.setProperty("screen_toolbutton_stylesheet_text", _translate("CustomersScreenDialog", "\n"
"QToolButton {\n"
"    background: #bb1d48;\n"
"    border: 2px solid #97173A;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #97173A;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}"))
        self.g_delete_button.setProperty("screen_indicator_stylesheet_text", _translate("CustomersScreenDialog", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"            the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #97173A;\n"
"    border: 0;\n"
"}"))
        self.g_merge_button.setText(_translate("CustomersScreenDialog", " Merge"))
        self.g_manage_button.setText(_translate("CustomersScreenDialog", " Manage"))
        self.g_select_button.setText(_translate("CustomersScreenDialog", "Select"))
        self.g_massaction_button.setText(_translate("CustomersScreenDialog", "Mass \n"
"Action"))
        self.g_new_button.setText(_translate("CustomersScreenDialog", " New"))
from po_lib.checkcombobox import PyCheckCombobox
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    CustomersScreenDialog = QtWidgets.QDialog()
    ui = Ui_CustomersScreenDialog()
    ui.setupUi(CustomersScreenDialog)
    CustomersScreenDialog.show()
    sys.exit(app.exec_())
