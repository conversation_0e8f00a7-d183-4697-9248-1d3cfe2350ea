# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'eligiblefreeoffers_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_EligibleFreeOffers(object):
    def setupUi(self, EligibleFreeOffers):
        EligibleFreeOffers.setObjectName("EligibleFreeOffers")
        EligibleFreeOffers.setWindowModality(QtCore.Qt.ApplicationModal)
        EligibleFreeOffers.setEnabled(True)
        EligibleFreeOffers.resize(865, 524)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(False)
        font.setWeight(50)
        EligibleFreeOffers.setFont(font)
        EligibleFreeOffers.setStyleSheet("#EligibleFreeOffers {\n"
"       background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"       }\n"
"\n"
"       QHeaderView::section {\n"
"       font-weight: bold;\n"
"       }\n"
"\n"
"       QDialog {\n"
"       background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"       }\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    border: 2px solid #287599;\n"
"    color: white;\n"
"}\n"
"\n"
"#g_select_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_select_button:pressed {\n"
"    background: #007f00;\n"
"}\n"
"\n"
"#g_select_button:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}\n"
"\n"
"\n"
"       #g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"       }\n"
"\n"
"       #g_cancel_button:pressed {\n"
"       background: #c80319\n"
"       }\n"
"\n"
"       QTableWidget {\n"
"       border:5px;\n"
"       border-bottom: 1px solid #000000;\n"
"       font-size: 12px;\n"
"       font-weight: normal;\n"
"       }\n"
"\n"
"       QTableWidget::item::focus {\n"
"       background-color:#0B256B;\n"
"       border: 1px solid #000000;\n"
"       color:#ffffff;\n"
"       }\n"
"\n"
"       QHeaderView::section {\n"
"       spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"QLabel {\n"
"    font: 11px;\n"
"    font-weight: bold;\n"
"    color: #000;\n"
"}\n"
"\n"
"\n"
"#g_main_label {\n"
"    font: 17px;\n"
"    font-weight: bold;\n"
"       color: #000;\n"
"       }\n"
"\n"
"       QLineEdit {\n"
"       border: 1px solid #aaa;\n"
"       border-radius:5px;\n"
"       background-color:#FFF;\n"
"       height:31px\n"
"       }\n"
"       #g_add_button {\n"
"       background-color: #009C00;\n"
"       border:2px solid #007F00;\n"
"       }\n"
"       #g_add_button:pressed {\n"
"       background-color: #007F00;\n"
"       }")
        EligibleFreeOffers.setModal(True)
        EligibleFreeOffers.setProperty("column_widths_list", ['545', '86'])
        self.verticalLayout = QtWidgets.QVBoxLayout(EligibleFreeOffers)
        self.verticalLayout.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_program_title_label = QtWidgets.QLabel(EligibleFreeOffers)
        self.g_program_title_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_program_title_label.setObjectName("g_program_title_label")
        self.verticalLayout.addWidget(self.g_program_title_label)
        self.g_program_subtitle_label = QtWidgets.QLabel(EligibleFreeOffers)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_program_subtitle_label.setFont(font)
        self.g_program_subtitle_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_program_subtitle_label.setWordWrap(True)
        self.g_program_subtitle_label.setObjectName("g_program_subtitle_label")
        self.verticalLayout.addWidget(self.g_program_subtitle_label)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_barcode_label = QtWidgets.QLabel(EligibleFreeOffers)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setItalic(False)
        font.setWeight(75)
        self.g_barcode_label.setFont(font)
        self.g_barcode_label.setObjectName("g_barcode_label")
        self.horizontalLayout_2.addWidget(self.g_barcode_label)
        self.g_barcode_lineedit = QtWidgets.QLineEdit(EligibleFreeOffers)
        self.g_barcode_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_barcode_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_barcode_lineedit.setClearButtonEnabled(True)
        self.g_barcode_lineedit.setObjectName("g_barcode_lineedit")
        self.horizontalLayout_2.addWidget(self.g_barcode_lineedit)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        self.g_freeofferproducts_table = QtWidgets.QTableWidget(EligibleFreeOffers)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_freeofferproducts_table.sizePolicy().hasHeightForWidth())
        self.g_freeofferproducts_table.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(False)
        font.setWeight(50)
        self.g_freeofferproducts_table.setFont(font)
        self.g_freeofferproducts_table.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.g_freeofferproducts_table.setStyleSheet("")
        self.g_freeofferproducts_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_freeofferproducts_table.setSizeAdjustPolicy(QtWidgets.QAbstractScrollArea.AdjustToContents)
        self.g_freeofferproducts_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_freeofferproducts_table.setAlternatingRowColors(True)
        self.g_freeofferproducts_table.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.g_freeofferproducts_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_freeofferproducts_table.setRowCount(0)
        self.g_freeofferproducts_table.setObjectName("g_freeofferproducts_table")
        self.g_freeofferproducts_table.setColumnCount(4)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_freeofferproducts_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_freeofferproducts_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_freeofferproducts_table.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_freeofferproducts_table.setHorizontalHeaderItem(3, item)
        self.g_freeofferproducts_table.horizontalHeader().setHighlightSections(False)
        self.g_freeofferproducts_table.horizontalHeader().setSortIndicatorShown(True)
        self.g_freeofferproducts_table.horizontalHeader().setStretchLastSection(True)
        self.g_freeofferproducts_table.verticalHeader().setVisible(False)
        self.g_freeofferproducts_table.verticalHeader().setStretchLastSection(False)
        self.verticalLayout.addWidget(self.g_freeofferproducts_table)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(6)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(EligibleFreeOffers)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/sales_screen/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_add_button = QtWidgets.QPushButton(EligibleFreeOffers)
        self.g_add_button.setEnabled(True)
        self.g_add_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_add_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_add_button.setAutoFillBackground(False)
        self.g_add_button.setStyleSheet("")
        icon = QtGui.QIcon.fromTheme(":/astro_enroll_dialog/add")
        self.g_add_button.setIcon(icon)
        self.g_add_button.setIconSize(QtCore.QSize(24, 24))
        self.g_add_button.setObjectName("g_add_button")
        self.horizontalLayout.addWidget(self.g_add_button)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(EligibleFreeOffers)
        QtCore.QMetaObject.connectSlotsByName(EligibleFreeOffers)

    def retranslateUi(self, EligibleFreeOffers):
        _translate = QtCore.QCoreApplication.translate
        EligibleFreeOffers.setWindowTitle(_translate("EligibleFreeOffers", "Choose a Product"))
        self.g_program_title_label.setText(_translate("EligibleFreeOffers", "{Program Title}"))
        self.g_program_subtitle_label.setText(_translate("EligibleFreeOffers", "One product(s) can be added to the sale for FREE. Scan or select a row and click \"+ Add\" to add the product to the invoice."))
        self.g_barcode_label.setText(_translate("EligibleFreeOffers", "Barcode"))
        self.g_barcode_lineedit.setPlaceholderText(_translate("EligibleFreeOffers", "Scan/enter barcode"))
        self.g_freeofferproducts_table.setSortingEnabled(True)
        item = self.g_freeofferproducts_table.horizontalHeaderItem(0)
        item.setText(_translate("EligibleFreeOffers", "Description"))
        item = self.g_freeofferproducts_table.horizontalHeaderItem(1)
        item.setText(_translate("EligibleFreeOffers", "Barcode"))
        item = self.g_freeofferproducts_table.horizontalHeaderItem(2)
        item.setText(_translate("EligibleFreeOffers", "Qty"))
        item = self.g_freeofferproducts_table.horizontalHeaderItem(3)
        item.setText(_translate("EligibleFreeOffers", "Price"))
        self.g_cancel_button.setText(_translate("EligibleFreeOffers", "Close"))
        self.g_add_button.setText(_translate("EligibleFreeOffers", " Add"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    EligibleFreeOffers = QtWidgets.QDialog()
    ui = Ui_EligibleFreeOffers()
    ui.setupUi(EligibleFreeOffers)
    EligibleFreeOffers.show()
    sys.exit(app.exec_())
