# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'closetraycashcount_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_CloseTrayCashCountDialog(object):
    def setupUi(self, CloseTrayCashCountDialog):
        CloseTrayCashCountDialog.setObjectName("CloseTrayCashCountDialog")
        CloseTrayCashCountDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        CloseTrayCashCountDialog.resize(680, 690)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(CloseTrayCashCountDialog.sizePolicy().hasHeightForWidth())
        CloseTrayCashCountDialog.setSizePolicy(sizePolicy)
        CloseTrayCashCountDialog.setMinimumSize(QtCore.QSize(680, 690))
        CloseTrayCashCountDialog.setMaximumSize(QtCore.QSize(680, 760))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/All Icons/money_bill_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.On)
        CloseTrayCashCountDialog.setWindowIcon(icon)
        CloseTrayCashCountDialog.setStyleSheet("\n"
"QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 11pt;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"\n"
"#g_dialog_frame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-weight: bold;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"\n"
"#g_amounttotal_lineedit {\n"
"    color: #000000\n"
"}\n"
"\n"
"#g_closeregister_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_closeregister_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_return_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"#g_return_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_keypad_frame QPushButton {\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-weight: bold;\n"
"    font-size: 17px;\n"
"}\n"
"\n"
"#g_notifyemail_label, #g_total_label, #g_bills_label, #g_coins_label {\n"
"    font-weight: bold;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#frame QLabel {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"")
        CloseTrayCashCountDialog.setModal(True)
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(CloseTrayCashCountDialog)
        self.verticalLayout_3.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout_3.setSpacing(9)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_main_label = QtWidgets.QLabel(CloseTrayCashCountDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_3.addWidget(self.g_main_label)
        self.g_notifyemail_label = QtWidgets.QLabel(CloseTrayCashCountDialog)
        self.g_notifyemail_label.setMinimumSize(QtCore.QSize(0, 25))
        self.g_notifyemail_label.setMaximumSize(QtCore.QSize(16777215, 25))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_notifyemail_label.setFont(font)
        self.g_notifyemail_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_notifyemail_label.setWordWrap(True)
        self.g_notifyemail_label.setObjectName("g_notifyemail_label")
        self.verticalLayout_3.addWidget(self.g_notifyemail_label)
        self.g_countyourdrawer_groupbox = QtWidgets.QGroupBox(CloseTrayCashCountDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_countyourdrawer_groupbox.sizePolicy().hasHeightForWidth())
        self.g_countyourdrawer_groupbox.setSizePolicy(sizePolicy)
        self.g_countyourdrawer_groupbox.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_countyourdrawer_groupbox.setFont(font)
        self.g_countyourdrawer_groupbox.setObjectName("g_countyourdrawer_groupbox")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_countyourdrawer_groupbox)
        self.horizontalLayout_2.setContentsMargins(6, 6, 6, 6)
        self.horizontalLayout_2.setSpacing(0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.frame = QtWidgets.QFrame(self.g_countyourdrawer_groupbox)
        self.frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setSpacing(4)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.frame_35 = QtWidgets.QFrame(self.frame)
        self.frame_35.setMaximumSize(QtCore.QSize(16777215, 31))
        self.frame_35.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_35.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_35.setObjectName("frame_35")
        self.horizontalLayout_33 = QtWidgets.QHBoxLayout(self.frame_35)
        self.horizontalLayout_33.setContentsMargins(0, 1, 0, 0)
        self.horizontalLayout_33.setSpacing(0)
        self.horizontalLayout_33.setObjectName("horizontalLayout_33")
        self.g_bills_label = QtWidgets.QLabel(self.frame_35)
        self.g_bills_label.setMinimumSize(QtCore.QSize(240, 15))
        self.g_bills_label.setMaximumSize(QtCore.QSize(16777215, 15))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_bills_label.setFont(font)
        self.g_bills_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_bills_label.setWordWrap(True)
        self.g_bills_label.setObjectName("g_bills_label")
        self.horizontalLayout_33.addWidget(self.g_bills_label)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_33.addItem(spacerItem)
        self.verticalLayout_2.addWidget(self.frame_35)
        self.frame_8 = QtWidgets.QFrame(self.frame)
        self.frame_8.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_8.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_8.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_8.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_8.setObjectName("frame_8")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.frame_8)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.g_dollar_label = QtWidgets.QLabel(self.frame_8)
        self.g_dollar_label.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label.setObjectName("g_dollar_label")
        self.horizontalLayout_11.addWidget(self.g_dollar_label)
        self.g_money_label_1 = QtWidgets.QLabel(self.frame_8)
        self.g_money_label_1.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_1.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_1.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_1.setObjectName("g_money_label_1")
        self.horizontalLayout_11.addWidget(self.g_money_label_1)
        self.g_d100_lineedit = QtWidgets.QLineEdit(self.frame_8)
        self.g_d100_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_d100_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_d100_lineedit.setObjectName("g_d100_lineedit")
        self.horizontalLayout_11.addWidget(self.g_d100_lineedit)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem1)
        self.verticalLayout_2.addWidget(self.frame_8)
        self.frame_7 = QtWidgets.QFrame(self.frame)
        self.frame_7.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_7.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_7.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_7.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_7.setObjectName("frame_7")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.frame_7)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.g_dollar_label_2 = QtWidgets.QLabel(self.frame_7)
        self.g_dollar_label_2.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label_2.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label_2.setObjectName("g_dollar_label_2")
        self.horizontalLayout_9.addWidget(self.g_dollar_label_2)
        self.g_money_label_2 = QtWidgets.QLabel(self.frame_7)
        self.g_money_label_2.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_2.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_2.setObjectName("g_money_label_2")
        self.horizontalLayout_9.addWidget(self.g_money_label_2)
        self.g_d50_lineedit = QtWidgets.QLineEdit(self.frame_7)
        self.g_d50_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_d50_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_d50_lineedit.setObjectName("g_d50_lineedit")
        self.horizontalLayout_9.addWidget(self.g_d50_lineedit)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem2)
        self.verticalLayout_2.addWidget(self.frame_7)
        self.frame_6 = QtWidgets.QFrame(self.frame)
        self.frame_6.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_6.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_6.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_6.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_6.setObjectName("frame_6")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.frame_6)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_dollar_label_3 = QtWidgets.QLabel(self.frame_6)
        self.g_dollar_label_3.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label_3.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label_3.setObjectName("g_dollar_label_3")
        self.horizontalLayout_8.addWidget(self.g_dollar_label_3)
        self.g_money_label_3 = QtWidgets.QLabel(self.frame_6)
        self.g_money_label_3.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_3.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_3.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_3.setObjectName("g_money_label_3")
        self.horizontalLayout_8.addWidget(self.g_money_label_3)
        self.g_d20_lineedit = QtWidgets.QLineEdit(self.frame_6)
        self.g_d20_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_d20_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_d20_lineedit.setObjectName("g_d20_lineedit")
        self.horizontalLayout_8.addWidget(self.g_d20_lineedit)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_8.addItem(spacerItem3)
        self.verticalLayout_2.addWidget(self.frame_6)
        self.frame_5 = QtWidgets.QFrame(self.frame)
        self.frame_5.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_5.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_5.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_5.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_5.setObjectName("frame_5")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.frame_5)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_dollar_label_4 = QtWidgets.QLabel(self.frame_5)
        self.g_dollar_label_4.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label_4.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label_4.setObjectName("g_dollar_label_4")
        self.horizontalLayout_7.addWidget(self.g_dollar_label_4)
        self.g_money_label_4 = QtWidgets.QLabel(self.frame_5)
        self.g_money_label_4.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_4.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_4.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_4.setObjectName("g_money_label_4")
        self.horizontalLayout_7.addWidget(self.g_money_label_4)
        self.g_d10_lineedit = QtWidgets.QLineEdit(self.frame_5)
        self.g_d10_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_d10_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_d10_lineedit.setObjectName("g_d10_lineedit")
        self.horizontalLayout_7.addWidget(self.g_d10_lineedit)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem4)
        self.verticalLayout_2.addWidget(self.frame_5)
        self.frame_4 = QtWidgets.QFrame(self.frame)
        self.frame_4.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_4.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_4.setObjectName("frame_4")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.frame_4)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_dollar_label_5 = QtWidgets.QLabel(self.frame_4)
        self.g_dollar_label_5.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label_5.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label_5.setObjectName("g_dollar_label_5")
        self.horizontalLayout_4.addWidget(self.g_dollar_label_5)
        self.g_money_label_5 = QtWidgets.QLabel(self.frame_4)
        self.g_money_label_5.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_5.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_5.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_5.setObjectName("g_money_label_5")
        self.horizontalLayout_4.addWidget(self.g_money_label_5)
        self.g_d05_lineedit = QtWidgets.QLineEdit(self.frame_4)
        self.g_d05_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_d05_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_d05_lineedit.setObjectName("g_d05_lineedit")
        self.horizontalLayout_4.addWidget(self.g_d05_lineedit)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem5)
        self.verticalLayout_2.addWidget(self.frame_4)
        self.frame_3 = QtWidgets.QFrame(self.frame)
        self.frame_3.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_3.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.frame_3)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_dollar_label_6 = QtWidgets.QLabel(self.frame_3)
        self.g_dollar_label_6.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label_6.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label_6.setObjectName("g_dollar_label_6")
        self.horizontalLayout_3.addWidget(self.g_dollar_label_6)
        self.g_money_label_6 = QtWidgets.QLabel(self.frame_3)
        self.g_money_label_6.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_6.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_6.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_6.setObjectName("g_money_label_6")
        self.horizontalLayout_3.addWidget(self.g_money_label_6)
        self.g_d02_lineedit = QtWidgets.QLineEdit(self.frame_3)
        self.g_d02_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_d02_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_d02_lineedit.setObjectName("g_d02_lineedit")
        self.horizontalLayout_3.addWidget(self.g_d02_lineedit)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem6)
        self.verticalLayout_2.addWidget(self.frame_3)
        self.frame_9 = QtWidgets.QFrame(self.frame)
        self.frame_9.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_9.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_9.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_9.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_9.setObjectName("frame_9")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.frame_9)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.g_dollar_label_7 = QtWidgets.QLabel(self.frame_9)
        self.g_dollar_label_7.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label_7.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label_7.setObjectName("g_dollar_label_7")
        self.horizontalLayout_10.addWidget(self.g_dollar_label_7)
        self.g_money_label_7 = QtWidgets.QLabel(self.frame_9)
        self.g_money_label_7.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_7.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_7.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_7.setObjectName("g_money_label_7")
        self.horizontalLayout_10.addWidget(self.g_money_label_7)
        self.g_d01_lineedit = QtWidgets.QLineEdit(self.frame_9)
        self.g_d01_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_d01_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_d01_lineedit.setObjectName("g_d01_lineedit")
        self.horizontalLayout_10.addWidget(self.g_d01_lineedit)
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_10.addItem(spacerItem7)
        self.verticalLayout_2.addWidget(self.frame_9)
        self.g_coins_frame = QtWidgets.QFrame(self.frame)
        self.g_coins_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_coins_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_coins_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_coins_frame.setObjectName("g_coins_frame")
        self.horizontalLayout_32 = QtWidgets.QHBoxLayout(self.g_coins_frame)
        self.horizontalLayout_32.setContentsMargins(0, 1, 0, 0)
        self.horizontalLayout_32.setSpacing(0)
        self.horizontalLayout_32.setObjectName("horizontalLayout_32")
        self.g_coins_label = QtWidgets.QLabel(self.g_coins_frame)
        self.g_coins_label.setMinimumSize(QtCore.QSize(240, 15))
        self.g_coins_label.setMaximumSize(QtCore.QSize(16777215, 15))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_coins_label.setFont(font)
        self.g_coins_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_coins_label.setWordWrap(True)
        self.g_coins_label.setObjectName("g_coins_label")
        self.horizontalLayout_32.addWidget(self.g_coins_label)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_32.addItem(spacerItem8)
        self.verticalLayout_2.addWidget(self.g_coins_frame)
        self.frame_16 = QtWidgets.QFrame(self.frame)
        self.frame_16.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_16.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_16.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_16.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_16.setObjectName("frame_16")
        self.horizontalLayout_18 = QtWidgets.QHBoxLayout(self.frame_16)
        self.horizontalLayout_18.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_18.setObjectName("horizontalLayout_18")
        self.g_dollar_label_8 = QtWidgets.QLabel(self.frame_16)
        self.g_dollar_label_8.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label_8.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label_8.setObjectName("g_dollar_label_8")
        self.horizontalLayout_18.addWidget(self.g_dollar_label_8)
        self.g_money_label_8 = QtWidgets.QLabel(self.frame_16)
        self.g_money_label_8.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_8.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_8.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_8.setObjectName("g_money_label_8")
        self.horizontalLayout_18.addWidget(self.g_money_label_8)
        self.g_c1_lineedit = QtWidgets.QLineEdit(self.frame_16)
        self.g_c1_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_c1_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_c1_lineedit.setObjectName("g_c1_lineedit")
        self.horizontalLayout_18.addWidget(self.g_c1_lineedit)
        self.g_roles_label_1 = QtWidgets.QLabel(self.frame_16)
        self.g_roles_label_1.setObjectName("g_roles_label_1")
        self.horizontalLayout_18.addWidget(self.g_roles_label_1)
        self.g_rc1_lineedit = QtWidgets.QLineEdit(self.frame_16)
        self.g_rc1_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_rc1_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_rc1_lineedit.setObjectName("g_rc1_lineedit")
        self.horizontalLayout_18.addWidget(self.g_rc1_lineedit)
        self.verticalLayout_2.addWidget(self.frame_16)
        self.frame_10 = QtWidgets.QFrame(self.frame)
        self.frame_10.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_10.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_10.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_10.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_10.setObjectName("frame_10")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.frame_10)
        self.horizontalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.g_dollar_label_9 = QtWidgets.QLabel(self.frame_10)
        self.g_dollar_label_9.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label_9.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label_9.setObjectName("g_dollar_label_9")
        self.horizontalLayout_12.addWidget(self.g_dollar_label_9)
        self.g_money_label_9 = QtWidgets.QLabel(self.frame_10)
        self.g_money_label_9.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_9.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_9.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_9.setObjectName("g_money_label_9")
        self.horizontalLayout_12.addWidget(self.g_money_label_9)
        self.g_c50_lineedit = QtWidgets.QLineEdit(self.frame_10)
        self.g_c50_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_c50_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_c50_lineedit.setObjectName("g_c50_lineedit")
        self.horizontalLayout_12.addWidget(self.g_c50_lineedit)
        self.g_roles_label_2 = QtWidgets.QLabel(self.frame_10)
        self.g_roles_label_2.setObjectName("g_roles_label_2")
        self.horizontalLayout_12.addWidget(self.g_roles_label_2)
        self.g_rc50_lineedit = QtWidgets.QLineEdit(self.frame_10)
        self.g_rc50_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_rc50_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_rc50_lineedit.setObjectName("g_rc50_lineedit")
        self.horizontalLayout_12.addWidget(self.g_rc50_lineedit)
        self.verticalLayout_2.addWidget(self.frame_10)
        self.frame_11 = QtWidgets.QFrame(self.frame)
        self.frame_11.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_11.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_11.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_11.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_11.setObjectName("frame_11")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.frame_11)
        self.horizontalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.g_dollar_label_10 = QtWidgets.QLabel(self.frame_11)
        self.g_dollar_label_10.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label_10.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label_10.setObjectName("g_dollar_label_10")
        self.horizontalLayout_13.addWidget(self.g_dollar_label_10)
        self.g_money_label_10 = QtWidgets.QLabel(self.frame_11)
        self.g_money_label_10.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_10.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_10.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_10.setObjectName("g_money_label_10")
        self.horizontalLayout_13.addWidget(self.g_money_label_10)
        self.g_c25_lineedit = QtWidgets.QLineEdit(self.frame_11)
        self.g_c25_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_c25_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_c25_lineedit.setObjectName("g_c25_lineedit")
        self.horizontalLayout_13.addWidget(self.g_c25_lineedit)
        self.g_roles_label_3 = QtWidgets.QLabel(self.frame_11)
        self.g_roles_label_3.setObjectName("g_roles_label_3")
        self.horizontalLayout_13.addWidget(self.g_roles_label_3)
        self.g_rc25_lineedit = QtWidgets.QLineEdit(self.frame_11)
        self.g_rc25_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_rc25_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_rc25_lineedit.setObjectName("g_rc25_lineedit")
        self.horizontalLayout_13.addWidget(self.g_rc25_lineedit)
        self.verticalLayout_2.addWidget(self.frame_11)
        self.frame_12 = QtWidgets.QFrame(self.frame)
        self.frame_12.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_12.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_12.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_12.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_12.setObjectName("frame_12")
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout(self.frame_12)
        self.horizontalLayout_14.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.g_dollar_label_11 = QtWidgets.QLabel(self.frame_12)
        self.g_dollar_label_11.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label_11.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label_11.setObjectName("g_dollar_label_11")
        self.horizontalLayout_14.addWidget(self.g_dollar_label_11)
        self.g_money_label_11 = QtWidgets.QLabel(self.frame_12)
        self.g_money_label_11.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_11.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_11.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_11.setObjectName("g_money_label_11")
        self.horizontalLayout_14.addWidget(self.g_money_label_11)
        self.g_c10_lineedit = QtWidgets.QLineEdit(self.frame_12)
        self.g_c10_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_c10_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_c10_lineedit.setObjectName("g_c10_lineedit")
        self.horizontalLayout_14.addWidget(self.g_c10_lineedit)
        self.g_roles_label_4 = QtWidgets.QLabel(self.frame_12)
        self.g_roles_label_4.setObjectName("g_roles_label_4")
        self.horizontalLayout_14.addWidget(self.g_roles_label_4)
        self.g_rc10_lineedit = QtWidgets.QLineEdit(self.frame_12)
        self.g_rc10_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_rc10_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_rc10_lineedit.setObjectName("g_rc10_lineedit")
        self.horizontalLayout_14.addWidget(self.g_rc10_lineedit)
        self.verticalLayout_2.addWidget(self.frame_12)
        self.frame_13 = QtWidgets.QFrame(self.frame)
        self.frame_13.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_13.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_13.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_13.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_13.setObjectName("frame_13")
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout(self.frame_13)
        self.horizontalLayout_15.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.g_dollar_label_12 = QtWidgets.QLabel(self.frame_13)
        self.g_dollar_label_12.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label_12.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label_12.setObjectName("g_dollar_label_12")
        self.horizontalLayout_15.addWidget(self.g_dollar_label_12)
        self.g_money_label_12 = QtWidgets.QLabel(self.frame_13)
        self.g_money_label_12.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_12.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_12.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_12.setObjectName("g_money_label_12")
        self.horizontalLayout_15.addWidget(self.g_money_label_12)
        self.g_c05_lineedit = QtWidgets.QLineEdit(self.frame_13)
        self.g_c05_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_c05_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_c05_lineedit.setObjectName("g_c05_lineedit")
        self.horizontalLayout_15.addWidget(self.g_c05_lineedit)
        self.g_roles_label_5 = QtWidgets.QLabel(self.frame_13)
        self.g_roles_label_5.setObjectName("g_roles_label_5")
        self.horizontalLayout_15.addWidget(self.g_roles_label_5)
        self.g_rc05_lineedit = QtWidgets.QLineEdit(self.frame_13)
        self.g_rc05_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_rc05_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_rc05_lineedit.setObjectName("g_rc05_lineedit")
        self.horizontalLayout_15.addWidget(self.g_rc05_lineedit)
        self.verticalLayout_2.addWidget(self.frame_13)
        self.frame_14 = QtWidgets.QFrame(self.frame)
        self.frame_14.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_14.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_14.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_14.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_14.setObjectName("frame_14")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout(self.frame_14)
        self.horizontalLayout_16.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_16.setSpacing(6)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.g_dollar_label_13 = QtWidgets.QLabel(self.frame_14)
        self.g_dollar_label_13.setMinimumSize(QtCore.QSize(10, 0))
        self.g_dollar_label_13.setMaximumSize(QtCore.QSize(10, 16777215))
        self.g_dollar_label_13.setObjectName("g_dollar_label_13")
        self.horizontalLayout_16.addWidget(self.g_dollar_label_13)
        self.g_money_label_13 = QtWidgets.QLabel(self.frame_14)
        self.g_money_label_13.setMinimumSize(QtCore.QSize(30, 0))
        self.g_money_label_13.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_money_label_13.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_money_label_13.setObjectName("g_money_label_13")
        self.horizontalLayout_16.addWidget(self.g_money_label_13)
        self.g_c01_lineedit = QtWidgets.QLineEdit(self.frame_14)
        self.g_c01_lineedit.setMinimumSize(QtCore.QSize(150, 31))
        self.g_c01_lineedit.setMaximumSize(QtCore.QSize(150, 31))
        self.g_c01_lineedit.setObjectName("g_c01_lineedit")
        self.horizontalLayout_16.addWidget(self.g_c01_lineedit)
        self.g_roles_label_6 = QtWidgets.QLabel(self.frame_14)
        self.g_roles_label_6.setObjectName("g_roles_label_6")
        self.horizontalLayout_16.addWidget(self.g_roles_label_6)
        self.g_rc01_lineedit = QtWidgets.QLineEdit(self.frame_14)
        self.g_rc01_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_rc01_lineedit.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_rc01_lineedit.setObjectName("g_rc01_lineedit")
        self.horizontalLayout_16.addWidget(self.g_rc01_lineedit)
        self.verticalLayout_2.addWidget(self.frame_14)
        self.horizontalLayout_2.addWidget(self.frame)
        self.frame_2 = QtWidgets.QFrame(self.g_countyourdrawer_groupbox)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout.setContentsMargins(15, 0, 0, 0)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        spacerItem9 = QtWidgets.QSpacerItem(20, 58, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem9)
        self.frame_15 = QtWidgets.QFrame(self.frame_2)
        self.frame_15.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_15.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_15.setObjectName("frame_15")
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout(self.frame_15)
        self.horizontalLayout_17.setContentsMargins(0, 0, 20, 0)
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        self.g_total_label = QtWidgets.QLabel(self.frame_15)
        self.g_total_label.setMinimumSize(QtCore.QSize(45, 0))
        self.g_total_label.setMaximumSize(QtCore.QSize(45, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_total_label.setFont(font)
        self.g_total_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_total_label.setObjectName("g_total_label")
        self.horizontalLayout_17.addWidget(self.g_total_label)
        self.g_amounttotal_lineedit = QtWidgets.QLineEdit(self.frame_15)
        self.g_amounttotal_lineedit.setMinimumSize(QtCore.QSize(0, 0))
        self.g_amounttotal_lineedit.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_amounttotal_lineedit.setFont(font)
        self.g_amounttotal_lineedit.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_amounttotal_lineedit.setObjectName("g_amounttotal_lineedit")
        self.horizontalLayout_17.addWidget(self.g_amounttotal_lineedit)
        self.verticalLayout.addWidget(self.frame_15)
        self.g_keypad_frame = QtWidgets.QFrame(self.frame_2)
        self.g_keypad_frame.setMinimumSize(QtCore.QSize(271, 280))
        self.g_keypad_frame.setMaximumSize(QtCore.QSize(271, 280))
        self.g_keypad_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_keypad_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_keypad_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_keypad_frame.setObjectName("g_keypad_frame")
        self.g_zero_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_zero_button.setGeometry(QtCore.QRect(10, 201, 59, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_zero_button.sizePolicy().hasHeightForWidth())
        self.g_zero_button.setSizePolicy(sizePolicy)
        self.g_zero_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_zero_button.setObjectName("g_zero_button")
        self.g_four_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_four_button.setGeometry(QtCore.QRect(10, 74, 59, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_four_button.sizePolicy().hasHeightForWidth())
        self.g_four_button.setSizePolicy(sizePolicy)
        self.g_four_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_four_button.setObjectName("g_four_button")
        self.g_seven_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_seven_button.setGeometry(QtCore.QRect(10, 10, 59, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_seven_button.sizePolicy().hasHeightForWidth())
        self.g_seven_button.setSizePolicy(sizePolicy)
        self.g_seven_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_seven_button.setObjectName("g_seven_button")
        self.g_one_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_one_button.setGeometry(QtCore.QRect(10, 138, 59, 58))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_one_button.sizePolicy().hasHeightForWidth())
        self.g_one_button.setSizePolicy(sizePolicy)
        self.g_one_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_one_button.setObjectName("g_one_button")
        self.g_doublezero_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_doublezero_button.setGeometry(QtCore.QRect(74, 201, 59, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_doublezero_button.sizePolicy().hasHeightForWidth())
        self.g_doublezero_button.setSizePolicy(sizePolicy)
        self.g_doublezero_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_doublezero_button.setObjectName("g_doublezero_button")
        self.g_enter_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_enter_button.setGeometry(QtCore.QRect(201, 138, 59, 122))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_enter_button.sizePolicy().hasHeightForWidth())
        self.g_enter_button.setSizePolicy(sizePolicy)
        palette = QtGui.QPalette()
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255, 128))
        brush.setStyle(QtCore.Qt.NoBrush)
        palette.setBrush(QtGui.QPalette.Active, QtGui.QPalette.PlaceholderText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255, 128))
        brush.setStyle(QtCore.Qt.NoBrush)
        palette.setBrush(QtGui.QPalette.Inactive, QtGui.QPalette.PlaceholderText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.WindowText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Button, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Light, brush)
        brush = QtGui.QBrush(QtGui.QColor(221, 240, 221))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Midlight, brush)
        brush = QtGui.QBrush(QtGui.QColor(93, 113, 93))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Dark, brush)
        brush = QtGui.QBrush(QtGui.QColor(125, 151, 125))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Mid, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Text, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.BrightText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ButtonText, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Base, brush)
        gradient = QtGui.QLinearGradient(0.0, 0.0, 1.0, 1.0)
        gradient.setSpread(QtGui.QGradient.PadSpread)
        gradient.setCoordinateMode(QtGui.QGradient.ObjectBoundingMode)
        gradient.setColorAt(0.0, QtGui.QColor(44, 135, 178))
        gradient.setColorAt(1.0, QtGui.QColor(87, 163, 210))
        brush = QtGui.QBrush(gradient)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Window, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.Shadow, brush)
        brush = QtGui.QBrush(QtGui.QColor(187, 226, 187))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.AlternateBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 220))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ToolTipBase, brush)
        brush = QtGui.QBrush(QtGui.QColor(0, 0, 0))
        brush.setStyle(QtCore.Qt.SolidPattern)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.ToolTipText, brush)
        brush = QtGui.QBrush(QtGui.QColor(255, 255, 255, 128))
        brush.setStyle(QtCore.Qt.NoBrush)
        palette.setBrush(QtGui.QPalette.Disabled, QtGui.QPalette.PlaceholderText, brush)
        self.g_enter_button.setPalette(palette)
        self.g_enter_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_enter_button.setObjectName("g_enter_button")
        self.g_nine_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_nine_button.setGeometry(QtCore.QRect(138, 10, 58, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_nine_button.sizePolicy().hasHeightForWidth())
        self.g_nine_button.setSizePolicy(sizePolicy)
        self.g_nine_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_nine_button.setObjectName("g_nine_button")
        self.g_two_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_two_button.setGeometry(QtCore.QRect(74, 138, 59, 58))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_two_button.sizePolicy().hasHeightForWidth())
        self.g_two_button.setSizePolicy(sizePolicy)
        self.g_two_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_two_button.setObjectName("g_two_button")
        self.g_decimal_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_decimal_button.setGeometry(QtCore.QRect(138, 201, 58, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_decimal_button.sizePolicy().hasHeightForWidth())
        self.g_decimal_button.setSizePolicy(sizePolicy)
        self.g_decimal_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_decimal_button.setObjectName("g_decimal_button")
        self.g_eight_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_eight_button.setGeometry(QtCore.QRect(74, 10, 59, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_eight_button.sizePolicy().hasHeightForWidth())
        self.g_eight_button.setSizePolicy(sizePolicy)
        self.g_eight_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_eight_button.setObjectName("g_eight_button")
        self.g_backspace_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_backspace_button.setGeometry(QtCore.QRect(201, 10, 59, 123))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_backspace_button.sizePolicy().hasHeightForWidth())
        self.g_backspace_button.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_backspace_button.setFont(font)
        self.g_backspace_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_backspace_button.setText("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/invoices_screen/backspace"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_backspace_button.setIcon(icon1)
        self.g_backspace_button.setIconSize(QtCore.QSize(32, 32))
        self.g_backspace_button.setObjectName("g_backspace_button")
        self.g_six_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_six_button.setGeometry(QtCore.QRect(138, 74, 58, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_six_button.sizePolicy().hasHeightForWidth())
        self.g_six_button.setSizePolicy(sizePolicy)
        self.g_six_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_six_button.setObjectName("g_six_button")
        self.g_five_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_five_button.setGeometry(QtCore.QRect(74, 74, 59, 59))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_five_button.sizePolicy().hasHeightForWidth())
        self.g_five_button.setSizePolicy(sizePolicy)
        self.g_five_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_five_button.setObjectName("g_five_button")
        self.g_three_button = QtWidgets.QPushButton(self.g_keypad_frame)
        self.g_three_button.setGeometry(QtCore.QRect(138, 138, 58, 58))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_three_button.sizePolicy().hasHeightForWidth())
        self.g_three_button.setSizePolicy(sizePolicy)
        self.g_three_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_three_button.setObjectName("g_three_button")
        self.verticalLayout.addWidget(self.g_keypad_frame)
        self.horizontalLayout_2.addWidget(self.frame_2)
        self.verticalLayout_3.addWidget(self.g_countyourdrawer_groupbox)
        self.g_dialogbuttons_frame = QtWidgets.QFrame(CloseTrayCashCountDialog)
        self.g_dialogbuttons_frame.setStyleSheet("#g_dialog_buttons_frame, #g_dialog_buttons_frame {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"")
        self.g_dialogbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_dialogbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_dialogbuttons_frame.setObjectName("g_dialogbuttons_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_dialogbuttons_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(12)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        spacerItem10 = QtWidgets.QSpacerItem(116, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem10)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(175, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(100, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/invoices_screen/icons/flat_x_white_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon2)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_6.addWidget(self.g_cancel_button)
        self.g_return_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_return_button.setMinimumSize(QtCore.QSize(175, 40))
        self.g_return_button.setMaximumSize(QtCore.QSize(100, 40))
        self.g_return_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_return_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/invoices_screen/icons/flat_checkmark_white_64.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_return_button.setIcon(icon3)
        self.g_return_button.setIconSize(QtCore.QSize(24, 24))
        self.g_return_button.setObjectName("g_return_button")
        self.horizontalLayout_6.addWidget(self.g_return_button)
        self.verticalLayout_3.addWidget(self.g_dialogbuttons_frame)

        self.retranslateUi(CloseTrayCashCountDialog)
        QtCore.QMetaObject.connectSlotsByName(CloseTrayCashCountDialog)

    def retranslateUi(self, CloseTrayCashCountDialog):
        _translate = QtCore.QCoreApplication.translate
        CloseTrayCashCountDialog.setWindowTitle(_translate("CloseTrayCashCountDialog", "Tray Cash Count"))
        self.g_main_label.setText(_translate("CloseTrayCashCountDialog", "Closing Cash Tray"))
        self.g_notifyemail_label.setText(_translate("CloseTrayCashCountDialog", "Count how many bills, coins, or rolls you have of each and enter that number (the number you have, not the value amount), and then press \"Return Total\" at the bottom to return the value to the previous screen."))
        self.g_countyourdrawer_groupbox.setTitle(_translate("CloseTrayCashCountDialog", "Cash Count"))
        self.g_bills_label.setText(_translate("CloseTrayCashCountDialog", "Bills"))
        self.g_dollar_label.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_1.setText(_translate("CloseTrayCashCountDialog", "100"))
        self.g_dollar_label_2.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_2.setText(_translate("CloseTrayCashCountDialog", "50"))
        self.g_dollar_label_3.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_3.setText(_translate("CloseTrayCashCountDialog", "20"))
        self.g_dollar_label_4.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_4.setText(_translate("CloseTrayCashCountDialog", "10"))
        self.g_dollar_label_5.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_5.setText(_translate("CloseTrayCashCountDialog", "5"))
        self.g_dollar_label_6.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_6.setText(_translate("CloseTrayCashCountDialog", "2"))
        self.g_dollar_label_7.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_7.setText(_translate("CloseTrayCashCountDialog", "1"))
        self.g_coins_label.setText(_translate("CloseTrayCashCountDialog", "Coins"))
        self.g_dollar_label_8.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_8.setText(_translate("CloseTrayCashCountDialog", "1"))
        self.g_roles_label_1.setText(_translate("CloseTrayCashCountDialog", "/ Rolls"))
        self.g_dollar_label_9.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_9.setText(_translate("CloseTrayCashCountDialog", ".50"))
        self.g_roles_label_2.setText(_translate("CloseTrayCashCountDialog", "/ Rolls"))
        self.g_dollar_label_10.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_10.setText(_translate("CloseTrayCashCountDialog", ".25"))
        self.g_roles_label_3.setText(_translate("CloseTrayCashCountDialog", "/ Rolls"))
        self.g_dollar_label_11.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_11.setText(_translate("CloseTrayCashCountDialog", ".10"))
        self.g_roles_label_4.setText(_translate("CloseTrayCashCountDialog", "/ Rolls"))
        self.g_dollar_label_12.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_12.setText(_translate("CloseTrayCashCountDialog", ".05"))
        self.g_roles_label_5.setText(_translate("CloseTrayCashCountDialog", "/ Rolls"))
        self.g_dollar_label_13.setText(_translate("CloseTrayCashCountDialog", "$"))
        self.g_money_label_13.setText(_translate("CloseTrayCashCountDialog", ".01"))
        self.g_roles_label_6.setText(_translate("CloseTrayCashCountDialog", "/ Rolls"))
        self.g_total_label.setText(_translate("CloseTrayCashCountDialog", "Total:"))
        self.g_zero_button.setText(_translate("CloseTrayCashCountDialog", "0"))
        self.g_four_button.setText(_translate("CloseTrayCashCountDialog", "4"))
        self.g_seven_button.setText(_translate("CloseTrayCashCountDialog", "7"))
        self.g_one_button.setText(_translate("CloseTrayCashCountDialog", "1"))
        self.g_doublezero_button.setText(_translate("CloseTrayCashCountDialog", "00"))
        self.g_enter_button.setText(_translate("CloseTrayCashCountDialog", "Enter"))
        self.g_nine_button.setText(_translate("CloseTrayCashCountDialog", "9"))
        self.g_two_button.setText(_translate("CloseTrayCashCountDialog", "2"))
        self.g_decimal_button.setText(_translate("CloseTrayCashCountDialog", "."))
        self.g_eight_button.setText(_translate("CloseTrayCashCountDialog", "8"))
        self.g_six_button.setText(_translate("CloseTrayCashCountDialog", "6"))
        self.g_five_button.setText(_translate("CloseTrayCashCountDialog", "5"))
        self.g_three_button.setText(_translate("CloseTrayCashCountDialog", "3"))
        self.g_cancel_button.setText(_translate("CloseTrayCashCountDialog", "  Cancel"))
        self.g_cancel_button.setShortcut(_translate("CloseTrayCashCountDialog", "Enter"))
        self.g_return_button.setText(_translate("CloseTrayCashCountDialog", "Return Total"))
        self.g_return_button.setShortcut(_translate("CloseTrayCashCountDialog", "Enter"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    CloseTrayCashCountDialog = QtWidgets.QDialog()
    ui = Ui_CloseTrayCashCountDialog()
    ui.setupUi(CloseTrayCashCountDialog)
    CloseTrayCashCountDialog.show()
    sys.exit(app.exec_())
