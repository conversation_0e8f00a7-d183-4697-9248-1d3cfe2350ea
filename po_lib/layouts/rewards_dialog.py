# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'rewards_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ChooseRewardsDialog(object):
    def setupUi(self, ChooseRewardsDialog):
        ChooseRewardsDialog.setObjectName("ChooseRewardsDialog")
        ChooseRewardsDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        ChooseRewardsDialog.setEnabled(True)
        ChooseRewardsDialog.resize(682, 456)
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(False)
        font.setWeight(50)
        ChooseRewardsDialog.setFont(font)
        ChooseRewardsDialog.setStyleSheet("#ChooseRewardsDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    border: 2px solid #287599;\n"
"    color:white\n"
"}\n"
"\n"
"#g_select_button {\n"
"    background: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_select_button:pressed {\n"
"    background: #007f00;\n"
"}\n"
"\n"
"#g_select_button:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QTableWidget QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"        border:2px solid #007f00;\n"
"    color: white;\n"
"    background: #009c00;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"#g_main_label,  #g_bottom_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}")
        ChooseRewardsDialog.setModal(True)
        ChooseRewardsDialog.setProperty("column_widths_list", ['305', '306', '20'])
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/rewards_dialog/pencil"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        ChooseRewardsDialog.setProperty("pencil_icon", icon)
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/rewards_dialog/magnifier_clear"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        ChooseRewardsDialog.setProperty("magnifying_glass_icon", icon1)
        self.verticalLayout = QtWidgets.QVBoxLayout(ChooseRewardsDialog)
        self.verticalLayout.setContentsMargins(18, 18, 18, 18)
        self.verticalLayout.setSpacing(18)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_main_label = QtWidgets.QLabel(ChooseRewardsDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout.addWidget(self.g_main_label)
        self.g_productresults_table = QtWidgets.QTableWidget(ChooseRewardsDialog)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_productresults_table.setFont(font)
        self.g_productresults_table.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.g_productresults_table.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.g_productresults_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_productresults_table.setAlternatingRowColors(True)
        self.g_productresults_table.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
        self.g_productresults_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_productresults_table.setRowCount(0)
        self.g_productresults_table.setObjectName("g_productresults_table")
        self.g_productresults_table.setColumnCount(3)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_productresults_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_productresults_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.g_productresults_table.setHorizontalHeaderItem(2, item)
        self.g_productresults_table.horizontalHeader().setHighlightSections(False)
        self.g_productresults_table.horizontalHeader().setSortIndicatorShown(True)
        self.g_productresults_table.horizontalHeader().setStretchLastSection(True)
        self.g_productresults_table.verticalHeader().setVisible(False)
        self.g_productresults_table.verticalHeader().setStretchLastSection(False)
        self.verticalLayout.addWidget(self.g_productresults_table)
        self.g_bottom_label = QtWidgets.QLabel(ChooseRewardsDialog)
        self.g_bottom_label.setText("")
        self.g_bottom_label.setWordWrap(True)
        self.g_bottom_label.setObjectName("g_bottom_label")
        self.verticalLayout.addWidget(self.g_bottom_label)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setSpacing(18)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(ChooseRewardsDialog)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/sales_screen/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon2)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_select_button = QtWidgets.QPushButton(ChooseRewardsDialog)
        self.g_select_button.setEnabled(True)
        self.g_select_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_select_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/sales_screen/save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_select_button.setIcon(icon3)
        self.g_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_select_button.setObjectName("g_select_button")
        self.horizontalLayout.addWidget(self.g_select_button)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(ChooseRewardsDialog)
        QtCore.QMetaObject.connectSlotsByName(ChooseRewardsDialog)

    def retranslateUi(self, ChooseRewardsDialog):
        _translate = QtCore.QCoreApplication.translate
        ChooseRewardsDialog.setWindowTitle(_translate("ChooseRewardsDialog", "Choose a Product"))
        self.g_main_label.setText(_translate("ChooseRewardsDialog", "The following search results were found. Please choose one."))
        self.g_productresults_table.setSortingEnabled(True)
        item = self.g_productresults_table.horizontalHeaderItem(0)
        item.setText(_translate("ChooseRewardsDialog", "Description"))
        item = self.g_productresults_table.horizontalHeaderItem(1)
        item.setText(_translate("ChooseRewardsDialog", "Selected"))
        self.g_cancel_button.setText(_translate("ChooseRewardsDialog", " Cancel"))
        self.g_select_button.setText(_translate("ChooseRewardsDialog", "Redeem\n"
"Selected"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ChooseRewardsDialog = QtWidgets.QDialog()
    ui = Ui_ChooseRewardsDialog()
    ui.setupUi(ChooseRewardsDialog)
    ChooseRewardsDialog.show()
    sys.exit(app.exec_())
