# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'main_window.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.setEnabled(True)
        MainWindow.resize(824, 535)
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/main_window/app"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        MainWindow.setWindowIcon(icon)
        MainWindow.setStyleSheet("")
        MainWindow.setProperty("app_tvc_height_int", 10)
        MainWindow.setProperty("app_server_timeout_int", 30)
        MainWindow.setProperty("app_name_text", "New Application")
        MainWindow.setProperty("app_window_width_int", 824)
        MainWindow.setProperty("app_window_height_int", 535)
        MainWindow.setProperty("app_queue_interval_int", 10)
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/main_window/refresh"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        MainWindow.setProperty("refresh_toolbutton_icon", icon1)
        MainWindow.setProperty("app_po_exes_list", ['cr.exe', 'sac.exe', 'fb.exe', 'sw.exe', '!windbag_server.exe', 'pt.exe', 'prod.exe', 'par.exe', 'updater_service.exe'])
        MainWindow.setProperty("app_po_programs_list", ['Cash Register', 'Settings and Configuration', 'Frequent Buyer', 'Setup Wizard', '!Windbag Server', 'Pet Tracker', 'Products', 'Purchasing and Receiving'])
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/main_window/help"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        MainWindow.setProperty("whatsthis_toolbutton_icon", icon2)
        self.g_main_widget = QtWidgets.QWidget(MainWindow)
        self.g_main_widget.setObjectName("g_main_widget")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_main_widget)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_toolbar_frame = QtWidgets.QFrame(self.g_main_widget)
        self.g_toolbar_frame.setMinimumSize(QtCore.QSize(0, 79))
        self.g_toolbar_frame.setMaximumSize(QtCore.QSize(16777215, 79))
        self.g_toolbar_frame.setStyleSheet("\n"
"QFrame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #d9edf8, stop: 1 #e7eff4);\n"
"    border:0px;\n"
"    border-bottom: 1px solid #333;\n"
"    background-image: url(\':/main_window/up\');\n"
"    background-repeat: no-repeat;\n"
"    background-position: bottom center;\n"
"}\n"
"\n"
"QFrame QToolButton {\n"
"    border:0px;\n"
"    color:white;\n"
"    font-weight:bold;\n"
"    font-size: 11px;\n"
"    width: 70px;\n"
"    height: 53px;\n"
"}\n"
"")
        self.g_toolbar_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_toolbar_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_toolbar_frame.setObjectName("g_toolbar_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_toolbar_frame)
        self.verticalLayout.setContentsMargins(-1, 9, -1, 0)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_left_toolbar_frame = QtWidgets.QFrame(self.g_toolbar_frame)
        self.g_left_toolbar_frame.setStyleSheet("QFrame {\n"
"    background: none;\n"
"    border: 0;\n"
"}")
        self.g_left_toolbar_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_left_toolbar_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_left_toolbar_frame.setObjectName("g_left_toolbar_frame")
        self.g_left_toolbar_glayout = QtWidgets.QGridLayout(self.g_left_toolbar_frame)
        self.g_left_toolbar_glayout.setContentsMargins(0, 0, 0, 0)
        self.g_left_toolbar_glayout.setHorizontalSpacing(6)
        self.g_left_toolbar_glayout.setVerticalSpacing(4)
        self.g_left_toolbar_glayout.setObjectName("g_left_toolbar_glayout")
        self.g_sales_toolbutton = QtWidgets.QToolButton(self.g_left_toolbar_frame)
        self.g_sales_toolbutton.setEnabled(True)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_sales_toolbutton.setFont(font)
        self.g_sales_toolbutton.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_sales_toolbutton.setStyleSheet("\n"
"QToolButton {\n"
"    background: #db552d;\n"
"    border: 2px solid #B74726;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #B74726;\n"
"}\n"
"\n"
"QToolButton:disabled {\n"
"    background: grey;\n"
"    border: 2px solid grey;\n"
"}")
        self.g_sales_toolbutton.setText("Sales")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/main_window/sales"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_sales_toolbutton.setIcon(icon3)
        self.g_sales_toolbutton.setIconSize(QtCore.QSize(32, 32))
        self.g_sales_toolbutton.setCheckable(True)
        self.g_sales_toolbutton.setChecked(False)
        self.g_sales_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.g_sales_toolbutton.setArrowType(QtCore.Qt.NoArrow)
        self.g_sales_toolbutton.setObjectName("g_sales_toolbutton")
        self.g_left_toolbar_glayout.addWidget(self.g_sales_toolbutton, 0, 0, 1, 1)
        self.g_customers_toolbutton = QtWidgets.QToolButton(self.g_left_toolbar_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_customers_toolbutton.setFont(font)
        self.g_customers_toolbutton.setStyleSheet("\n"
"QToolButton {\n"
"    background: #bb1d48;\n"
"    border: 2px solid #97173A;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #97173A;\n"
"}")
        self.g_customers_toolbutton.setText("Customers")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/main_window/customers"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_customers_toolbutton.setIcon(icon4)
        self.g_customers_toolbutton.setIconSize(QtCore.QSize(32, 32))
        self.g_customers_toolbutton.setCheckable(True)
        self.g_customers_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.g_customers_toolbutton.setObjectName("g_customers_toolbutton")
        self.g_left_toolbar_glayout.addWidget(self.g_customers_toolbutton, 0, 1, 1, 1)
        self.g_products_toolbutton = QtWidgets.QToolButton(self.g_left_toolbar_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_products_toolbutton.setFont(font)
        self.g_products_toolbutton.setStyleSheet("\n"
"QToolButton {\n"
"    background: #9f00a7;\n"
"    border: 2px solid #7D0083;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #7D0083;\n"
"}")
        self.g_products_toolbutton.setText("Products")
        icon5 = QtGui.QIcon()
        icon5.addPixmap(QtGui.QPixmap(":/main_window/products"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_products_toolbutton.setIcon(icon5)
        self.g_products_toolbutton.setIconSize(QtCore.QSize(32, 32))
        self.g_products_toolbutton.setCheckable(True)
        self.g_products_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.g_products_toolbutton.setObjectName("g_products_toolbutton")
        self.g_left_toolbar_glayout.addWidget(self.g_products_toolbutton, 0, 2, 1, 1)
        self.g_invoices_toolbutton = QtWidgets.QToolButton(self.g_left_toolbar_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_invoices_toolbutton.setFont(font)
        self.g_invoices_toolbutton.setStyleSheet("\n"
"QToolButton {\n"
"    background: #925A24;\n"
"    border: 2px solid #523314;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #523314;\n"
"}")
        self.g_invoices_toolbutton.setText("Invoices")
        icon6 = QtGui.QIcon()
        icon6.addPixmap(QtGui.QPixmap(":/main_window/invoices"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_invoices_toolbutton.setIcon(icon6)
        self.g_invoices_toolbutton.setIconSize(QtCore.QSize(32, 32))
        self.g_invoices_toolbutton.setCheckable(True)
        self.g_invoices_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.g_invoices_toolbutton.setObjectName("g_invoices_toolbutton")
        self.g_left_toolbar_glayout.addWidget(self.g_invoices_toolbutton, 0, 3, 1, 1)
        self.g_left_indicator_line = QtWidgets.QFrame(self.g_left_toolbar_frame)
        self.g_left_indicator_line.setEnabled(True)
        self.g_left_indicator_line.setMinimumSize(QtCore.QSize(74, 5))
        self.g_left_indicator_line.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_left_indicator_line.setStyleSheet("\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"            the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #B74726;\n"
"    border-bottom: 0;\n"
"}")
        self.g_left_indicator_line.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_left_indicator_line.setLineWidth(0)
        self.g_left_indicator_line.setFrameShape(QtWidgets.QFrame.HLine)
        self.g_left_indicator_line.setObjectName("g_left_indicator_line")
        self.g_left_toolbar_glayout.addWidget(self.g_left_indicator_line, 1, 0, 1, 1)
        self.horizontalLayout_2.addWidget(self.g_left_toolbar_frame)
        spacerItem = QtWidgets.QSpacerItem(6, 20, QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem)
        self.g_right_toolbar_frame = QtWidgets.QFrame(self.g_toolbar_frame)
        self.g_right_toolbar_frame.setStyleSheet("QFrame {\n"
"    background: none;\n"
"    border: 0;\n"
"}")
        self.g_right_toolbar_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_right_toolbar_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_right_toolbar_frame.setObjectName("g_right_toolbar_frame")
        self.g_right_toolbar_glayout = QtWidgets.QGridLayout(self.g_right_toolbar_frame)
        self.g_right_toolbar_glayout.setContentsMargins(0, 0, 0, 0)
        self.g_right_toolbar_glayout.setHorizontalSpacing(6)
        self.g_right_toolbar_glayout.setVerticalSpacing(4)
        self.g_right_toolbar_glayout.setObjectName("g_right_toolbar_glayout")
        self.g_login_toolbutton = QtWidgets.QToolButton(self.g_right_toolbar_frame)
        self.g_login_toolbutton.setEnabled(True)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_login_toolbutton.setFont(font)
        self.g_login_toolbutton.setStyleSheet("\n"
"QToolButton {\n"
"    background: #009c00;\n"
"    border: 2px solid #007f00;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #007f00;\n"
"}")
        self.g_login_toolbutton.setText("Sign In")
        icon7 = QtGui.QIcon()
        icon7.addPixmap(QtGui.QPixmap(":/main_window/login"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_login_toolbutton.setIcon(icon7)
        self.g_login_toolbutton.setIconSize(QtCore.QSize(32, 32))
        self.g_login_toolbutton.setCheckable(True)
        self.g_login_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.g_login_toolbutton.setObjectName("g_login_toolbutton")
        self.g_right_toolbar_glayout.addWidget(self.g_login_toolbutton, 0, 1, 1, 1)
        self.g_right_indicator_line = QtWidgets.QFrame(self.g_right_toolbar_frame)
        self.g_right_indicator_line.setMinimumSize(QtCore.QSize(74, 5))
        self.g_right_indicator_line.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_right_indicator_line.setStyleSheet("\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"            the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #214273;\n"
"    border-bottom: 0;\n"
"}")
        self.g_right_indicator_line.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_right_indicator_line.setLineWidth(0)
        self.g_right_indicator_line.setFrameShape(QtWidgets.QFrame.HLine)
        self.g_right_indicator_line.setObjectName("g_right_indicator_line")
        self.g_right_toolbar_glayout.addWidget(self.g_right_indicator_line, 1, 4, 1, 1)
        self.g_settings_toolbutton = QtWidgets.QToolButton(self.g_right_toolbar_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_settings_toolbutton.setFont(font)
        self.g_settings_toolbutton.setStyleSheet("\n"
"QToolButton {\n"
"    background: #9f00a7;\n"
"    border: 2px solid #7D0083;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #7D0083;\n"
"}")
        self.g_settings_toolbutton.setText("Settings")
        icon8 = QtGui.QIcon()
        icon8.addPixmap(QtGui.QPixmap(":/main_window/settings"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_settings_toolbutton.setIcon(icon8)
        self.g_settings_toolbutton.setIconSize(QtCore.QSize(32, 32))
        self.g_settings_toolbutton.setCheckable(True)
        self.g_settings_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.g_settings_toolbutton.setObjectName("g_settings_toolbutton")
        self.g_right_toolbar_glayout.addWidget(self.g_settings_toolbutton, 0, 3, 1, 1)
        self.g_logout_toolbutton = QtWidgets.QToolButton(self.g_right_toolbar_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_logout_toolbutton.setFont(font)
        self.g_logout_toolbutton.setStyleSheet("\n"
"QToolButton {\n"
"    background: #ee1111;\n"
"    border: 2px solid #CA0E0E;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #CA0E0E;\n"
"}")
        self.g_logout_toolbutton.setText("Sign Out")
        icon9 = QtGui.QIcon()
        icon9.addPixmap(QtGui.QPixmap(":/main_window/logout"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_logout_toolbutton.setIcon(icon9)
        self.g_logout_toolbutton.setIconSize(QtCore.QSize(32, 32))
        self.g_logout_toolbutton.setCheckable(False)
        self.g_logout_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.g_logout_toolbutton.setObjectName("g_logout_toolbutton")
        self.g_right_toolbar_glayout.addWidget(self.g_logout_toolbutton, 0, 2, 1, 1)
        self.g_help_toolbutton = QtWidgets.QToolButton(self.g_right_toolbar_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_help_toolbutton.setFont(font)
        self.g_help_toolbutton.setStyleSheet("\n"
"QToolButton {\n"
"    background: #2b5797;\n"
"    border: 2px solid #214273;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #214273;\n"
"}")
        self.g_help_toolbutton.setText("Help")
        self.g_help_toolbutton.setIcon(icon2)
        self.g_help_toolbutton.setIconSize(QtCore.QSize(32, 32))
        self.g_help_toolbutton.setCheckable(True)
        self.g_help_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.g_help_toolbutton.setObjectName("g_help_toolbutton")
        self.g_right_toolbar_glayout.addWidget(self.g_help_toolbutton, 0, 4, 1, 1)
        self.g_refresh_toolbutton = QtWidgets.QToolButton(self.g_right_toolbar_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_refresh_toolbutton.setFont(font)
        self.g_refresh_toolbutton.setStyleSheet("\n"
"QToolButton    {\n"
"    background: #5a38b5;\n"
"    border: 2px solid #482D91;\n"
"}\n"
"\n"
"QToolButton:pressed {\n"
"    background: #482D91;\n"
"}\n"
"\n"
"")
        self.g_refresh_toolbutton.setText("Refresh")
        self.g_refresh_toolbutton.setIcon(icon1)
        self.g_refresh_toolbutton.setIconSize(QtCore.QSize(32, 32))
        self.g_refresh_toolbutton.setToolButtonStyle(QtCore.Qt.ToolButtonTextUnderIcon)
        self.g_refresh_toolbutton.setObjectName("g_refresh_toolbutton")
        self.g_right_toolbar_glayout.addWidget(self.g_refresh_toolbutton, 0, 0, 1, 1)
        self.horizontalLayout_2.addWidget(self.g_right_toolbar_frame)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        self.verticalLayout_2.addWidget(self.g_toolbar_frame)
        self.g_screens_frame = QtWidgets.QFrame(self.g_main_widget)
        self.g_screens_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_screens_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_screens_frame.setObjectName("g_screens_frame")
        self.g_screens_vlayout = QtWidgets.QVBoxLayout(self.g_screens_frame)
        self.g_screens_vlayout.setContentsMargins(0, 0, 0, 0)
        self.g_screens_vlayout.setSpacing(0)
        self.g_screens_vlayout.setObjectName("g_screens_vlayout")
        self.verticalLayout_2.addWidget(self.g_screens_frame)
        MainWindow.setCentralWidget(self.g_main_widget)

        self.retranslateUi(MainWindow)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "New Application"))
        MainWindow.setProperty("app_no_config_text", _translate("MainWindow", "No configuration was found. Please create one using the Setup Wizard then restart this application."))
        MainWindow.setProperty("app_config_incorrect_text", _translate("MainWindow", "The detected configuration is invalid. Please delete this configuration and create a new one by running the Setup Wizard."))
        MainWindow.setProperty("app_host_invalid_text", _translate("MainWindow", "The hostname in your configuration is invalid. Please delete this configuration and create a new one using the Setup Wizard."))
        MainWindow.setProperty("app_host_unavailable_text", _translate("MainWindow", "The hostname in your configuration could not be contacted. Please delete this configuration and create a new one using the Setup Wizard."))
        MainWindow.setProperty("app_server_unresponsive_text", _translate("MainWindow", "The Pinogy POS server failed to respond. Please contact Pinogy support at 877-360-7381."))
        MainWindow.setProperty("app_contacting_server_text", _translate("MainWindow", "Contacting Pinogy POS server and validating configuration ..."))
        MainWindow.setProperty("app_unexpected_error_text", _translate("MainWindow", "An unexpected error occurred while processing your last request. The last operation could not be completed. Please contact Pinogy support at 877-360-7381."))
        MainWindow.setProperty("app_window_title_text", _translate("MainWindow", "New Application"))
        MainWindow.setProperty("app_toolbar_stylesheet_text", _translate("MainWindow", "/* Create a string property named \"app_toolbar_stylesheet_text\" to override the default QtCss directives\n"
"    for the application toolbar.\n"
"\n"
"    The contents of \"app_toolbar_stylesheet_text\" will be applied even if it contains nothing\n"
"    (this can be useful for removing any existing styling from the application toolbar). */"))
        MainWindow.setProperty("app_client_root_text", _translate("MainWindow", "/apps/any/test"))
        MainWindow.setProperty("app_dev_server_text", _translate("MainWindow", "api-corepos-v1-dev.pinogy.net"))
        MainWindow.setProperty("refresh_toolbutton_tooltip_text", _translate("MainWindow", "Refresh this screen"))
        MainWindow.setProperty("refresh_toolbutton_title_text", _translate("MainWindow", "Refresh"))
        MainWindow.setProperty("refresh_toolbutton_stylesheet_text", _translate("MainWindow", "\n"
"QToolButton {\n"
"    background: #5a38b5;\n"
"    border: 2px solid #482D91;\n"
"}\n"
"\n"
"QToolButton:pressed {\n"
"    background: #482D91;\n"
"}\n"
"\n"
"QToolButton:disabled {\n"
"    background: grey;\n"
"    border: 2px solid grey;\n"
"}"))
        MainWindow.setProperty("app_po_version_text", _translate("MainWindow", "2.163"))
        MainWindow.setProperty("app_po_build_text", _translate("MainWindow", "Run From Source"))
        MainWindow.setProperty("app_po_latest_release_text", _translate("MainWindow", "/apps/pinogy_online/releases/latest"))
        MainWindow.setProperty("app_po_latest_download_text", _translate("MainWindow", "/apps/pinogy_online/releases/latest/download"))
        MainWindow.setProperty("app_update_failed_msg_text", _translate("MainWindow", "The software update failed to run. Please contact Pinogy support at 877-360-7381."))
        MainWindow.setProperty("app_inactivity_timeout_text", _translate("MainWindow", "30 minutes"))
        MainWindow.setProperty("app_po_suite_name_text", _translate("MainWindow", "Pinogy POS"))
        MainWindow.setProperty("whatsthis_toolbutton_stylesheet_text", _translate("MainWindow", "QToolButton {background: #2b5797; border: 2px solid #214273;} QToolButton:disabled {background:\n"
"             rgb(164, 164, 164); border: 2px solid rgb(164, 164, 164);}\n"
"         "))
        MainWindow.setProperty("whatsthis_toolbutton_title_text", _translate("MainWindow", "What\'s This"))
        MainWindow.setProperty("whatsthis_toolbutton_tooltip_text", _translate("MainWindow", "What\'s This"))
        MainWindow.setProperty("missing_settings_msg_text", _translate("MainWindow", "One or more required settings\n"
"             failed to load.\n"
"\n"
"             This application may not function\n"
"             correctly as a result.\n"
"\n"
"             It is strongly recommended to\n"
"             contact Pinogy support at\n"
"             877-360-7381.\n"
"         "))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    MainWindow = QtWidgets.QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(MainWindow)
    MainWindow.show()
    sys.exit(app.exec_())
