# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managetaxexempt_modaldialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ManageTaxExemptCertificates(object):
    def setupUi(self, ManageTaxExemptCertificates):
        ManageTaxExemptCertificates.setObjectName("ManageTaxExemptCertificates")
        ManageTaxExemptCertificates.resize(900, 476)
        ManageTaxExemptCertificates.setMinimumSize(QtCore.QSize(900, 0))
        ManageTaxExemptCertificates.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        ManageTaxExemptCertificates.setFont(font)
        ManageTaxExemptCertificates.setStyleSheet("#ManageTaxExemptCertificates {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox {\n"
"    padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-bottom: 4px;; \n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/entity_dialog/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/entity_dialog/checked\');\n"
"    border:0px;\n"
"} \n"
"\n"
"QDateTimeEdit, QTimeEdit {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
" \n"
"QDateTimeEdit::drop-down, QTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"    width:20px;\n"
"}\n"
"\n"
".QListWidget {\n"
"    padding:2px;\n"
"    background: transparent;\n"
"}\n"
"\n"
".QListWidget::item {\n"
"    padding-bottom:1px;\n"
"    padding-right:1px;\n"
"}\n"
"\n"
".QListWidget::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"\n"
"#g_unifiedsearch_frame, #__g_unifiedsearch_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_searchbox_frame, #__g_searchbox_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_clearsearch_button, #__g_clearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_searchtext_lineedit, #__g_searchtext_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_search_combobox, #__g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_search_combobox, #g_search_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"#g_cancel_button,\n"
"#g_delete_button, #__g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed,\n"
"#g_delete_button:pressed, #__g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_save_button,\n"
"#g_add_button, #__g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed,\n"
"#g_add_button:pressed, #__g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QFrame QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_totalpages_label_2,\n"
"#g_totalresults_label,\n"
"#g_totalpages_label {\n"
"    font-size: 11px;\n"
"    font-weight: normal;\n"
"}\n"
"\n"
"")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(ManageTaxExemptCertificates)
        self.verticalLayout_2.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_top_frame = QtWidgets.QFrame(ManageTaxExemptCertificates)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setMaximumSize(QtCore.QSize(16777215, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_6.addWidget(self.g_progress_frame)
        self.verticalLayout_2.addWidget(self.g_top_frame)
        self.g_main_label = QtWidgets.QLabel(ManageTaxExemptCertificates)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout_2.addWidget(self.g_main_label)
        self.g_taxrates_groupbox = QtWidgets.QGroupBox(ManageTaxExemptCertificates)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_taxrates_groupbox.setFont(font)
        self.g_taxrates_groupbox.setObjectName("g_taxrates_groupbox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_taxrates_groupbox)
        self.verticalLayout.setContentsMargins(2, 2, 2, 2)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_taxes_listWidget = QtWidgets.QListWidget(self.g_taxrates_groupbox)
        self.g_taxes_listWidget.setMinimumSize(QtCore.QSize(300, 0))
        self.g_taxes_listWidget.setProperty("showDropIndicator", False)
        self.g_taxes_listWidget.setSelectionMode(QtWidgets.QAbstractItemView.MultiSelection)
        self.g_taxes_listWidget.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_taxes_listWidget.setResizeMode(QtWidgets.QListView.Fixed)
        self.g_taxes_listWidget.setObjectName("g_taxes_listWidget")
        self.verticalLayout.addWidget(self.g_taxes_listWidget)
        self.verticalLayout_2.addWidget(self.g_taxrates_groupbox)
        self.__g_bottombar_frame_2 = QtWidgets.QFrame(ManageTaxExemptCertificates)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_bottombar_frame_2.sizePolicy().hasHeightForWidth())
        self.__g_bottombar_frame_2.setSizePolicy(sizePolicy)
        self.__g_bottombar_frame_2.setMinimumSize(QtCore.QSize(247, 39))
        self.__g_bottombar_frame_2.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_bottombar_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_bottombar_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_bottombar_frame_2.setObjectName("__g_bottombar_frame_2")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.__g_bottombar_frame_2)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(self.__g_bottombar_frame_2)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_5.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.__g_bottombar_frame_2)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_5.addWidget(self.g_save_button)
        self.verticalLayout_2.addWidget(self.__g_bottombar_frame_2)

        self.retranslateUi(ManageTaxExemptCertificates)
        QtCore.QMetaObject.connectSlotsByName(ManageTaxExemptCertificates)

    def retranslateUi(self, ManageTaxExemptCertificates):
        _translate = QtCore.QCoreApplication.translate
        ManageTaxExemptCertificates.setWindowTitle(_translate("ManageTaxExemptCertificates", "Manage Tax Exempt Certificates"))
        self.g_main_label.setText(_translate("ManageTaxExemptCertificates", "Manage Tax Exempt Certificates"))
        self.g_taxrates_groupbox.setTitle(_translate("ManageTaxExemptCertificates", "Taxes"))
        self.g_cancel_button.setText(_translate("ManageTaxExemptCertificates", "Cancel"))
        self.g_save_button.setText(_translate("ManageTaxExemptCertificates", "Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageTaxExemptCertificates = QtWidgets.QDialog()
    ui = Ui_ManageTaxExemptCertificates()
    ui.setupUi(ManageTaxExemptCertificates)
    ManageTaxExemptCertificates.show()
    sys.exit(app.exec_())
