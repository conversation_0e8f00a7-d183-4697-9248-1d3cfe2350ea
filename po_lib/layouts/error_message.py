# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'error_message.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ErrorMessage(object):
    def setupUi(self, ErrorMessage):
        ErrorMessage.setObjectName("ErrorMessage")
        ErrorMessage.setWindowModality(QtCore.Qt.ApplicationModal)
        ErrorMessage.resize(450, 367)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Maximum, QtWidgets.QSizePolicy.Maximum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(ErrorMessage.sizePolicy().hasHeightForWidth())
        ErrorMessage.setSizePolicy(sizePolicy)
        ErrorMessage.setMinimumSize(QtCore.QSize(450, 0))
        font = QtGui.QFont()
        font.setPointSize(5)
        ErrorMessage.setFont(font)
        ErrorMessage.setToolTipDuration(-5)
        ErrorMessage.setStyleSheet("#ErrorMessage, #__ErrorMessage {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
" }\n"
"\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"#g_close_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_close_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_titlepagenav_frame QLabel, #g_titlepagenav_frame QLineEdit {\n"
"    font-size: 11px;\n"
"} \n"
"\n"
"#g_primary_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold\n"
"}\n"
"#g_secondary_label, #g_moreinfourl_label {\n"
"    font-size: 11px;\n"
"}")
        ErrorMessage.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(ErrorMessage)
        self.verticalLayout.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout.setObjectName("verticalLayout")
        spacerItem = QtWidgets.QSpacerItem(20, 3, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout.addItem(spacerItem)
        self.g_titlepagenav_frame = QtWidgets.QFrame(ErrorMessage)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_30 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_30.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_30.setObjectName("horizontalLayout_30")
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout.setSpacing(6)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout.addItem(spacerItem1)
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/error_message/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/error_message/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagenav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_slash_label.setObjectName("g_slash_label")
        self.g_pagenav_hlayout.addWidget(self.g_slash_label)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout.addWidget(self.g_totalpages_label)
        self.horizontalLayout_30.addWidget(self.g_pagenav_frame)
        self.verticalLayout.addWidget(self.g_titlepagenav_frame)
        spacerItem2 = QtWidgets.QSpacerItem(20, 1, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout.addItem(spacerItem2)
        self.g_content_frame = QtWidgets.QFrame(ErrorMessage)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_content_frame.sizePolicy().hasHeightForWidth())
        self.g_content_frame.setSizePolicy(sizePolicy)
        self.g_content_frame.setMinimumSize(QtCore.QSize(435, 0))
        self.g_content_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_content_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_content_frame.setObjectName("g_content_frame")
        self.gridLayout = QtWidgets.QGridLayout(self.g_content_frame)
        self.gridLayout.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
        self.gridLayout.setObjectName("gridLayout")
        self.g_whitecross_label = QtWidgets.QLabel(self.g_content_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_whitecross_label.sizePolicy().hasHeightForWidth())
        self.g_whitecross_label.setSizePolicy(sizePolicy)
        self.g_whitecross_label.setMinimumSize(QtCore.QSize(32, 31))
        self.g_whitecross_label.setMaximumSize(QtCore.QSize(32, 31))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_whitecross_label.setFont(font)
        self.g_whitecross_label.setText("")
        self.g_whitecross_label.setPixmap(QtGui.QPixmap(":/error_message/error32"))
        self.g_whitecross_label.setScaledContents(False)
        self.g_whitecross_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_whitecross_label.setObjectName("g_whitecross_label")
        self.gridLayout.addWidget(self.g_whitecross_label, 0, 0, 1, 1)
        self.g_primary_label = QtWidgets.QLabel(self.g_content_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_primary_label.sizePolicy().hasHeightForWidth())
        self.g_primary_label.setSizePolicy(sizePolicy)
        self.g_primary_label.setMinimumSize(QtCore.QSize(380, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_primary_label.setFont(font)
        self.g_primary_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)
        self.g_primary_label.setWordWrap(True)
        self.g_primary_label.setObjectName("g_primary_label")
        self.gridLayout.addWidget(self.g_primary_label, 0, 1, 1, 1)
        self.g_secondary_label = QtWidgets.QLabel(self.g_content_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_secondary_label.sizePolicy().hasHeightForWidth())
        self.g_secondary_label.setSizePolicy(sizePolicy)
        self.g_secondary_label.setMinimumSize(QtCore.QSize(380, 0))
        self.g_secondary_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)
        self.g_secondary_label.setWordWrap(True)
        self.g_secondary_label.setObjectName("g_secondary_label")
        self.gridLayout.addWidget(self.g_secondary_label, 1, 1, 1, 1)
        self.g_moreinfourl_label = QtWidgets.QLabel(self.g_content_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_moreinfourl_label.sizePolicy().hasHeightForWidth())
        self.g_moreinfourl_label.setSizePolicy(sizePolicy)
        self.g_moreinfourl_label.setMinimumSize(QtCore.QSize(380, 0))
        self.g_moreinfourl_label.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_moreinfourl_label.setTextFormat(QtCore.Qt.RichText)
        self.g_moreinfourl_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignTop)
        self.g_moreinfourl_label.setWordWrap(True)
        self.g_moreinfourl_label.setOpenExternalLinks(True)
        self.g_moreinfourl_label.setTextInteractionFlags(QtCore.Qt.TextBrowserInteraction)
        self.g_moreinfourl_label.setObjectName("g_moreinfourl_label")
        self.gridLayout.addWidget(self.g_moreinfourl_label, 2, 1, 1, 1)
        self.verticalLayout.addWidget(self.g_content_frame)
        self.g_details_frame = QtWidgets.QFrame(ErrorMessage)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(1)
        sizePolicy.setVerticalStretch(1)
        sizePolicy.setHeightForWidth(self.g_details_frame.sizePolicy().hasHeightForWidth())
        self.g_details_frame.setSizePolicy(sizePolicy)
        self.g_details_frame.setMinimumSize(QtCore.QSize(435, 0))
        self.g_details_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_details_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_details_frame.setObjectName("g_details_frame")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_details_frame)
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_3.setSpacing(0)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_details = QtWidgets.QTextEdit(self.g_details_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(1)
        sizePolicy.setHeightForWidth(self.g_details.sizePolicy().hasHeightForWidth())
        self.g_details.setSizePolicy(sizePolicy)
        self.g_details.setMinimumSize(QtCore.QSize(435, 0))
        self.g_details.setObjectName("g_details")
        self.verticalLayout_3.addWidget(self.g_details)
        self.verticalLayout.addWidget(self.g_details_frame)
        self.__g_bottombar_frame = QtWidgets.QFrame(ErrorMessage)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_bottombar_frame.setSizePolicy(sizePolicy)
        self.__g_bottombar_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_bottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_bottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_bottombar_frame.setStyleSheet("")
        self.__g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_bottombar_frame.setObjectName("__g_bottombar_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.__g_bottombar_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_details_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_details_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_details_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_details_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_details_button.setObjectName("g_details_button")
        self.horizontalLayout.addWidget(self.g_details_button)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem3)
        self.g_close_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_close_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_close_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_close_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/error_message/close"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_close_button.setIcon(icon)
        self.g_close_button.setIconSize(QtCore.QSize(24, 24))
        self.g_close_button.setObjectName("g_close_button")
        self.horizontalLayout.addWidget(self.g_close_button)
        self.verticalLayout.addWidget(self.__g_bottombar_frame)

        self.retranslateUi(ErrorMessage)
        QtCore.QMetaObject.connectSlotsByName(ErrorMessage)

    def retranslateUi(self, ErrorMessage):
        _translate = QtCore.QCoreApplication.translate
        ErrorMessage.setWindowTitle(_translate("ErrorMessage", "Error Message"))
        self.g_previouspage_label.setToolTip(_translate("ErrorMessage", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("ErrorMessage", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("ErrorMessage", "999"))
        self.g_slash_label.setText(_translate("ErrorMessage", "/"))
        self.g_totalpages_label.setToolTip(_translate("ErrorMessage", "Total Pages"))
        self.g_totalpages_label.setText(_translate("ErrorMessage", "999"))
        self.g_primary_label.setText(_translate("ErrorMessage", "Human readable error message, something that describes what happened. Don\'t overcommunicate, be short and precise."))
        self.g_secondary_label.setText(_translate("ErrorMessage", "TextLabel"))
        self.g_moreinfourl_label.setText(_translate("ErrorMessage", "More info"))
        self.g_details_button.setText(_translate("ErrorMessage", "More »"))
        self.g_close_button.setText(_translate("ErrorMessage", "Close"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ErrorMessage = QtWidgets.QDialog()
    ui = Ui_ErrorMessage()
    ui.setupUi(ErrorMessage)
    ErrorMessage.show()
    sys.exit(app.exec_())
