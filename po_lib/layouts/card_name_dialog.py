# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'card_name_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_CardNameDialog(object):
    def setupUi(self, CardNameDialog):
        CardNameDialog.setObjectName("CardNameDialog")
        CardNameDialog.resize(455, 204)
        CardNameDialog.setStyleSheet("#CardNameDialog {\n"
"                background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"                }\n"
"\n"
"                QFrame {\n"
"                border:0px;\n"
"                }\n"
"\n"
"                #g_save_button {\n"
"                background-color: #009c00;\n"
"                border:2px solid #007f00;\n"
"                }\n"
"\n"
"                #g_save_button:pressed {\n"
"                background-color: #007f00;\n"
"                }\n"
"\n"
"                #g_cancel_button {\n"
"                background-color: #ee1111;\n"
"                border:2px solid #CA0E0E;\n"
"                }\n"
"\n"
"                #g_cancel_button:pressed {\n"
"                background-color:#CA0E0E;\n"
"                }\n"
"\n"
"                #CardNameDialog QPushButton, QPushButton {\n"
"                color:white;\n"
"                font-weight:bold;\n"
"                font-size:15px;\n"
"                }\n"
"\n"
"                #g_message_label {\n"
"                font-weight:bold;\n"
"                font-size:17px;\n"
"                }\n"
"            ")
        self.verticalLayout = QtWidgets.QVBoxLayout(CardNameDialog)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_message_label = QtWidgets.QLabel(CardNameDialog)
        self.g_message_label.setObjectName("g_message_label")
        self.verticalLayout.addWidget(self.g_message_label)
        self.frame = QtWidgets.QFrame(CardNameDialog)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_cardname_lineedit = QtWidgets.QLineEdit(self.frame)
        self.g_cardname_lineedit.setMinimumSize(QtCore.QSize(0, 40))
        self.g_cardname_lineedit.setObjectName("g_cardname_lineedit")
        self.verticalLayout_2.addWidget(self.g_cardname_lineedit)
        self.verticalLayout.addWidget(self.frame)
        self.g_buttons_frame = QtWidgets.QFrame(CardNameDialog)
        self.g_buttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_buttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_buttons_frame.setObjectName("g_buttons_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_buttons_frame)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(203, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_buttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(100, 40))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/entity_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_buttons_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(100, 40))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout.addWidget(self.g_save_button)
        self.verticalLayout.addWidget(self.g_buttons_frame)

        self.retranslateUi(CardNameDialog)
        QtCore.QMetaObject.connectSlotsByName(CardNameDialog)

    def retranslateUi(self, CardNameDialog):
        _translate = QtCore.QCoreApplication.translate
        CardNameDialog.setWindowTitle(_translate("CardNameDialog", "Card Name"))
        self.g_message_label.setText(_translate("CardNameDialog", "Provide Card Name"))
        self.g_cancel_button.setText(_translate("CardNameDialog", "Cancel"))
        self.g_save_button.setText(_translate("CardNameDialog", "Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    CardNameDialog = QtWidgets.QDialog()
    ui = Ui_CardNameDialog()
    ui.setupUi(CardNameDialog)
    CardNameDialog.show()
    sys.exit(app.exec_())
