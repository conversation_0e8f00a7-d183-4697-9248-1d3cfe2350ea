# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'canned_discount_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_CannedDiscountDialog(object):
    def setupUi(self, CannedDiscountDialog):
        CannedDiscountDialog.setObjectName("CannedDiscountDialog")
        CannedDiscountDialog.resize(397, 171)
        CannedDiscountDialog.setStyleSheet("#CannedDiscountDialog {\n"
"                background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"                }\n"
"\n"
"                QFrame {\n"
"                border:0px;\n"
"                }\n"
"\n"
"                QComboBox {\n"
"                height: 25px;\n"
"                line-height: 25px;\n"
"                border: 1px solid #aaa;\n"
"                border-radius: 5px;\n"
"                padding: 4px;\n"
"                color: #555;\n"
"                font-size: 11px;\n"
"                }\n"
"\n"
"                QComboBox::drop-down {\n"
"                subcontrol-origin: margin;\n"
"                background:;\n"
"                background-image: url(:/entity_dialog/drop-down-arrow);\n"
"                width: 20px;\n"
"                }\n"
"\n"
"                #g_apply_button {\n"
"                background-color: #009c00;\n"
"                border:2px solid #007f00;\n"
"                }\n"
"\n"
"                #g_apply_button:pressed {\n"
"                background-color: #007f00;\n"
"                }\n"
"\n"
"                #g_cancel_button {\n"
"                background-color: #ee1111;\n"
"                border:2px solid #CA0E0E;\n"
"                }\n"
"\n"
"                #g_cancel_button:pressed {\n"
"                background-color:#CA0E0E;\n"
"                }\n"
"\n"
"                #CannedDiscountDialog QPushButton, QPushButton {\n"
"                color:white;\n"
"                font-weight:bold;\n"
"                font-size:15px;\n"
"                }\n"
"\n"
"                #g_message_label {\n"
"                font-weight:bold;\n"
"                font-size:17px;\n"
"                }\n"
"            ")
        self.verticalLayout = QtWidgets.QVBoxLayout(CannedDiscountDialog)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_message_label = QtWidgets.QLabel(CannedDiscountDialog)
        self.g_message_label.setStyleSheet("")
        self.g_message_label.setObjectName("g_message_label")
        self.verticalLayout.addWidget(self.g_message_label)
        self.g_main_frame = QtWidgets.QFrame(CannedDiscountDialog)
        self.g_main_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_main_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_main_frame.setObjectName("g_main_frame")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.g_main_frame)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_discount_type_combobox = QtWidgets.QComboBox(self.g_main_frame)
        self.g_discount_type_combobox.setObjectName("g_discount_type_combobox")
        self.verticalLayout_2.addWidget(self.g_discount_type_combobox)
        self.verticalLayout.addWidget(self.g_main_frame)
        self.g_buttons_frame = QtWidgets.QFrame(CannedDiscountDialog)
        self.g_buttons_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_buttons_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_buttons_frame.setObjectName("g_buttons_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_buttons_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_buttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(100, 40))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/canned_discount_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_apply_button = QtWidgets.QPushButton(self.g_buttons_frame)
        self.g_apply_button.setMinimumSize(QtCore.QSize(100, 40))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/canned_discount_dialog/apply"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_apply_button.setIcon(icon1)
        self.g_apply_button.setIconSize(QtCore.QSize(24, 24))
        self.g_apply_button.setObjectName("g_apply_button")
        self.horizontalLayout.addWidget(self.g_apply_button)
        self.verticalLayout.addWidget(self.g_buttons_frame)

        self.retranslateUi(CannedDiscountDialog)
        QtCore.QMetaObject.connectSlotsByName(CannedDiscountDialog)

    def retranslateUi(self, CannedDiscountDialog):
        _translate = QtCore.QCoreApplication.translate
        CannedDiscountDialog.setWindowTitle(_translate("CannedDiscountDialog", "Discount Reason"))
        self.g_message_label.setText(_translate("CannedDiscountDialog", "Select a Discount Reason"))
        self.g_cancel_button.setText(_translate("CannedDiscountDialog", "Cancel"))
        self.g_apply_button.setText(_translate("CannedDiscountDialog", "Apply"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    CannedDiscountDialog = QtWidgets.QDialog()
    ui = Ui_CannedDiscountDialog()
    ui.setupUi(CannedDiscountDialog)
    CannedDiscountDialog.show()
    sys.exit(app.exec_())
