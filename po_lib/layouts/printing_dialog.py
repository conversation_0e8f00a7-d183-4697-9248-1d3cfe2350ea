# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'printing_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_PrintingDialog(object):
    def setupUi(self, PrintingDialog):
        PrintingDialog.setObjectName("PrintingDialog")
        PrintingDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        PrintingDialog.resize(366, 61)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.MinimumExpanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(PrintingDialog.sizePolicy().hasHeightForWidth())
        PrintingDialog.setSizePolicy(sizePolicy)
        PrintingDialog.setMinimumSize(QtCore.QSize(0, 0))
        PrintingDialog.setMaximumSize(QtCore.QSize(16777215, 16777215))
        PrintingDialog.setStyleSheet("#g_yes_button, #__g_yes_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_yes_button:pressed, #__g_yes_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_no_button, #__g_no_button {\n"
"    background-color: #ee1111;\n"
"    border:2px solid #CA0E0E;\n"
"}\n"
"\n"
"#g_no_button:pressed, #__g_no_button:pressed {\n"
"    background-color:#CA0E0E;\n"
"}\n"
"\n"
"#LoadingDialog QPushButton, #__LoadingDialog QPushButton {\n"
"    color:white;\n"
"    font-weight:bold;\n"
"    font-size:11pt;\n"
"}\n"
"\n"
"#LoadingDialog, #__LoadingDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_message_label {\n"
"    font-size: 20px;\n"
"    font-weight: bold;\n"
"}")
        PrintingDialog.setModal(True)
        self.verticalLayout = QtWidgets.QVBoxLayout(PrintingDialog)
        self.verticalLayout.setContentsMargins(16, 0, 16, 16)
        self.verticalLayout.setSpacing(10)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_progress_frame = QtWidgets.QFrame(PrintingDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.verticalLayout.addWidget(self.g_progress_frame)
        self.g_message_label = QtWidgets.QLabel(PrintingDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.MinimumExpanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_message_label.sizePolicy().hasHeightForWidth())
        self.g_message_label.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_message_label.setFont(font)
        self.g_message_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_message_label.setWordWrap(True)
        self.g_message_label.setObjectName("g_message_label")
        self.verticalLayout.addWidget(self.g_message_label)

        self.retranslateUi(PrintingDialog)
        QtCore.QMetaObject.connectSlotsByName(PrintingDialog)

    def retranslateUi(self, PrintingDialog):
        _translate = QtCore.QCoreApplication.translate
        PrintingDialog.setWindowTitle(_translate("PrintingDialog", "Printing..."))
        PrintingDialog.setProperty("default_window_title_text", _translate("PrintingDialog", "Confirm Action"))
        self.g_message_label.setText(_translate("PrintingDialog", "Printing..."))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    PrintingDialog = QtWidgets.QDialog()
    ui = Ui_PrintingDialog()
    ui.setupUi(PrintingDialog)
    PrintingDialog.show()
    sys.exit(app.exec_())
