# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'quote_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_QuoteDialog(object):
    def setupUi(self, QuoteDialog):
        QuoteDialog.setObjectName("QuoteDialog")
        QuoteDialog.resize(438, 181)
        QuoteDialog.setMinimumSize(QtCore.QSize(0, 0))
        QuoteDialog.setMaximumSize(QtCore.QSize(16777215, 16777215))
        QuoteDialog.setStyleSheet("#QuoteDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB; \n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"QTabBar::tab:selected, \n"
"QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"#g_holdssearchbox_frame, \n"
"#g_quotessearchbox_frame, \n"
"#g_layawayssearchbox_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_holdssearchbox_frame, \n"
"#g_quotessearchbox_frame,\n"
"#g_layawayssearchbox_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_holdsclearsearch_button, \n"
"#g_quotesclearsearch_button,\n"
"#g_layawaysclearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_holdssearchtext_lineedit, \n"
"#g_quotessearchtext_lineedit,\n"
"#g_layawayssearchtext_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_holdssearch_combobox, \n"
"#g_quotessearch_combobox, \n"
"#g_layawayssearch_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" } \n"
" \n"
"#g_expires_datetimeedit::drop-down {\n"
"    background-image: url(:/entity_dialog/drop-down-arrow);\n"
"}\n"
"\n"
"#g_holdssearch_combobox::drop-down, \n"
"#g_quotessearch_combobox::drop-down, \n"
"#g_layawayssearch_combobox::drop-down  {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"#g_main_tabs QTabWidget::tab-bar {\n"
"     left: 5px;\n"
" }\n"
"\n"
"#g_main_tabs QTabBar::tab {\n"
"    background-color: white;\n"
"    border: 1px solid #C4C4C3;\n"
"    border-bottom-color: #C2C7CB;\n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 20ex;\n"
"    padding: 2px;\n"
"    height:34px;\n"
"}\n"
"\n"
"#g_main_tabs QTabBar::tab:selected, QTabBar::tab:hover {\n"
"     background-color: white;\n"
" }\n"
"\n"
"#g_main_tabs QTabBar::tab:selected {\n"
"    border-color: #073c83;\n"
"    border-bottom-color: none;\n"
" }  \n"
"\n"
"#g_printquote_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_printquote_button:pressed {\n"
"    background-color: #007f00;\n"
"} \n"
"\n"
"\n"
"QDateTimeEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    height:31px;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QDateTimeEdit::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
" #__g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"#g_expires_label {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"\n"
"}")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/paymentmanagement_screen/payments"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        QuoteDialog.setProperty("screen_toolbutton_icon", icon)
        self.verticalLayout = QtWidgets.QVBoxLayout(QuoteDialog)
        self.verticalLayout.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_progress_frame = QtWidgets.QFrame(QuoteDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.verticalLayout.addWidget(self.g_progress_frame)
        self.__g_main_label = QtWidgets.QLabel(QuoteDialog)
        self.__g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.__g_main_label.setFont(font)
        self.__g_main_label.setObjectName("__g_main_label")
        self.verticalLayout.addWidget(self.__g_main_label)
        self.g_expires_frame = QtWidgets.QFrame(QuoteDialog)
        self.g_expires_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_expires_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_expires_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_expires_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_expires_frame.setObjectName("g_expires_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_expires_frame)
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_expires_label = QtWidgets.QLabel(self.g_expires_frame)
        self.g_expires_label.setMinimumSize(QtCore.QSize(85, 0))
        self.g_expires_label.setMaximumSize(QtCore.QSize(85, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_expires_label.setFont(font)
        self.g_expires_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_expires_label.setObjectName("g_expires_label")
        self.horizontalLayout_3.addWidget(self.g_expires_label)
        self.g_expires_datetimeedit = QtWidgets.QDateEdit(self.g_expires_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_expires_datetimeedit.sizePolicy().hasHeightForWidth())
        self.g_expires_datetimeedit.setSizePolicy(sizePolicy)
        self.g_expires_datetimeedit.setMinimumSize(QtCore.QSize(120, 31))
        self.g_expires_datetimeedit.setMaximumSize(QtCore.QSize(120, 31))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_expires_datetimeedit.setFont(font)
        self.g_expires_datetimeedit.setStyleSheet("")
        self.g_expires_datetimeedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_expires_datetimeedit.setDateTime(QtCore.QDateTime(QtCore.QDate(2014, 1, 6), QtCore.QTime(0, 0, 0)))
        self.g_expires_datetimeedit.setCalendarPopup(True)
        self.g_expires_datetimeedit.setTimeSpec(QtCore.Qt.LocalTime)
        self.g_expires_datetimeedit.setProperty("qp_txn_expiration_date", "")
        self.g_expires_datetimeedit.setObjectName("g_expires_datetimeedit")
        self.horizontalLayout_3.addWidget(self.g_expires_datetimeedit)
        spacerItem = QtWidgets.QSpacerItem(188, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem)
        self.verticalLayout.addWidget(self.g_expires_frame)
        self.g_ss_frame = QtWidgets.QFrame(QuoteDialog)
        self.g_ss_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_ss_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_ss_frame.setObjectName("g_ss_frame")
        self.verticalLayout.addWidget(self.g_ss_frame)
        self.__g_layawaysbottombar_frame = QtWidgets.QFrame(QuoteDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_layawaysbottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_layawaysbottombar_frame.setSizePolicy(sizePolicy)
        self.__g_layawaysbottombar_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.__g_layawaysbottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_layawaysbottombar_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.__g_layawaysbottombar_frame.setStyleSheet("")
        self.__g_layawaysbottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_layawaysbottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_layawaysbottombar_frame.setObjectName("__g_layawaysbottombar_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.__g_layawaysbottombar_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.g_controlbuttons_frame = QtWidgets.QFrame(self.__g_layawaysbottombar_frame)
        self.g_controlbuttons_frame.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_controlbuttons_frame.setMaximumSize(QtCore.QSize(495, 40))
        self.g_controlbuttons_frame.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setSpacing(5)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_backtosale_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_backtosale_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_backtosale_button.sizePolicy().hasHeightForWidth())
        self.g_backtosale_button.setSizePolicy(sizePolicy)
        self.g_backtosale_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_backtosale_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_backtosale_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_backtosale_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/entity_dialog/back_arrow"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_backtosale_button.setIcon(icon1)
        self.g_backtosale_button.setIconSize(QtCore.QSize(24, 24))
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/customers_screen_dialog/customers"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_backtosale_button.setProperty("screen_toolbutton_icon", icon2)
        self.g_backtosale_button.setObjectName("g_backtosale_button")
        self.horizontalLayout_2.addWidget(self.g_backtosale_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_save_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_save_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/entity_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon3)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_2.addWidget(self.g_save_button)
        self.g_printquote_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_printquote_button.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_printquote_button.sizePolicy().hasHeightForWidth())
        self.g_printquote_button.setSizePolicy(sizePolicy)
        self.g_printquote_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_printquote_button.setMaximumSize(QtCore.QSize(120, 40))
        self.g_printquote_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_printquote_button.setStyleSheet("")
        icon4 = QtGui.QIcon()
        icon4.addPixmap(QtGui.QPixmap(":/entity_dialog/printer"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_printquote_button.setIcon(icon4)
        self.g_printquote_button.setIconSize(QtCore.QSize(24, 24))
        self.g_printquote_button.setProperty("screen_toolbutton_icon", icon2)
        self.g_printquote_button.setObjectName("g_printquote_button")
        self.horizontalLayout_2.addWidget(self.g_printquote_button)
        self.horizontalLayout.addWidget(self.g_controlbuttons_frame)
        self.verticalLayout.addWidget(self.__g_layawaysbottombar_frame)

        self.retranslateUi(QuoteDialog)
        QtCore.QMetaObject.connectSlotsByName(QuoteDialog)

    def retranslateUi(self, QuoteDialog):
        _translate = QtCore.QCoreApplication.translate
        QuoteDialog.setWindowTitle(_translate("QuoteDialog", "Quote"))
        QuoteDialog.setProperty("screen_toolbutton_tooltip_text", _translate("QuoteDialog", "View or manage payments"))
        QuoteDialog.setProperty("screen_toolbutton_title_text", _translate("QuoteDialog", "Payments"))
        QuoteDialog.setProperty("screen_toolbutton_stylesheet_text", _translate("QuoteDialog", "\n"
"QToolButton {\n"
"    background: #925A24;\n"
"    border: 2px solid #523314;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #523314;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: grey;\n"
"    border: 2px solid grey;\n"
"}"))
        QuoteDialog.setProperty("screen_indicator_stylesheet_text", _translate("QuoteDialog", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"        the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #523314;\n"
"    border: 0;\n"
"}"))
        self.__g_main_label.setText(_translate("QuoteDialog", "Quote"))
        self.g_expires_label.setText(_translate("QuoteDialog", "Expires"))
        self.g_expires_datetimeedit.setDisplayFormat(_translate("QuoteDialog", "MM/dd/yyyy"))
        self.g_backtosale_button.setText(_translate("QuoteDialog", "  Back to\n"
"  Sale"))
        self.g_backtosale_button.setProperty("screen_toolbutton_tooltip_text", _translate("QuoteDialog", "View or manage customers"))
        self.g_backtosale_button.setProperty("screen_toolbutton_title_text", _translate("QuoteDialog", "Customers"))
        self.g_backtosale_button.setProperty("screen_toolbutton_stylesheet_text", _translate("QuoteDialog", "\n"
"QToolButton {\n"
"    background: #bb1d48;\n"
"    border: 2px solid #97173A;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #97173A;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}"))
        self.g_backtosale_button.setProperty("screen_indicator_stylesheet_text", _translate("QuoteDialog", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"        the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #97173A;\n"
"    border: 0;\n"
"}"))
        self.g_save_button.setText(_translate("QuoteDialog", "  Save\n"
"  Quote"))
        self.g_printquote_button.setText(_translate("QuoteDialog", "  Save &&\n"
"  Print"))
        self.g_printquote_button.setProperty("screen_toolbutton_tooltip_text", _translate("QuoteDialog", "View or manage customers"))
        self.g_printquote_button.setProperty("screen_toolbutton_title_text", _translate("QuoteDialog", "Customers"))
        self.g_printquote_button.setProperty("screen_toolbutton_stylesheet_text", _translate("QuoteDialog", "\n"
"QToolButton {\n"
"    background: #bb1d48;\n"
"    border: 2px solid #97173A;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #97173A;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}"))
        self.g_printquote_button.setProperty("screen_indicator_stylesheet_text", _translate("QuoteDialog", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"        the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #97173A;\n"
"    border: 0;\n"
"}"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    QuoteDialog = QtWidgets.QDialog()
    ui = Ui_QuoteDialog()
    ui.setupUi(QuoteDialog)
    QuoteDialog.show()
    sys.exit(app.exec_())
