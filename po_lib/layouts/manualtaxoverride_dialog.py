# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'manualtaxoverride_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_ManualTaxOverrideDialog(object):
    def setupUi(self, ManualTaxOverrideDialog):
        ManualTaxOverrideDialog.setObjectName("ManualTaxOverrideDialog")
        ManualTaxOverrideDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        ManualTaxOverrideDialog.resize(637, 505)
        ManualTaxOverrideDialog.setStyleSheet("\n"
"#ManualTaxOverrideDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    background-color: #fff;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"QListWidget { \n"
"    background: transparent;\n"
"    border: 1px;\n"
"    font-size: 13px;\n"
"}\n"
"\n"
"QListWidget::item {\n"
"    padding-top: 5px;\n"
"}\n"
"\n"
"QListWidget::indicator {\n"
"    width: 20px;\n"
"    height: 20px;\n"
"    margin-top: -7px;\n"
"}\n"
"\n"
"QListWidget::indicator:unchecked {\n"
"    image: url(\':/manualtaxoverride_dialog/unchecked\');\n"
"    border: 0px;\n"
"}\n"
"\n"
"QListWidget::indicator:checked {\n"
"    image: url(\':/manualtaxoverride_dialog/checked\');\n"
"    border: 0px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border: 2px solid #287599;\n"
"    color: white;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border: 2px solid #287599;\n"
"}\n"
"\n"
"#g_container_frame {\n"
"    border-radius: 5px;\n"
"    border: 1px solid #073c83;\n"
"}\n"
"\n"
"#g_save_button {\n"
"    background-color: #009c00;\n"
"    border: 2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border: 2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#label_2 {\n"
"    font-size: 13px;\n"
"    font-weight: bold;\n"
"}\n"
"")
        ManualTaxOverrideDialog.setModal(True)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(ManualTaxOverrideDialog)
        self.verticalLayout_2.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout_2.setSpacing(9)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label = QtWidgets.QLabel(ManualTaxOverrideDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.label.setFont(font)
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.g_help_label = QtWidgets.QLabel(ManualTaxOverrideDialog)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_help_label.setFont(font)
        self.g_help_label.setObjectName("g_help_label")
        self.verticalLayout.addWidget(self.g_help_label)
        self.verticalLayout_2.addLayout(self.verticalLayout)
        self.g_container_frame = QtWidgets.QFrame(ManualTaxOverrideDialog)
        self.g_container_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_container_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_container_frame.setObjectName("g_container_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_container_frame)
        self.horizontalLayout_2.setContentsMargins(12, 12, 12, 12)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_taxes_list = QtWidgets.QListWidget(self.g_container_frame)
        self.g_taxes_list.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_taxes_list.setFont(font)
        self.g_taxes_list.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_taxes_list.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_taxes_list.setAlternatingRowColors(False)
        self.g_taxes_list.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.g_taxes_list.setResizeMode(QtWidgets.QListView.Adjust)
        self.g_taxes_list.setViewMode(QtWidgets.QListView.ListMode)
        self.g_taxes_list.setObjectName("g_taxes_list")
        item = QtWidgets.QListWidgetItem()
        item.setCheckState(QtCore.Qt.Checked)
        self.g_taxes_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        item.setCheckState(QtCore.Qt.Checked)
        self.g_taxes_list.addItem(item)
        item = QtWidgets.QListWidgetItem()
        item.setFlags(QtCore.Qt.ItemIsSelectable|QtCore.Qt.ItemIsDragEnabled|QtCore.Qt.ItemIsUserCheckable)
        item.setCheckState(QtCore.Qt.Unchecked)
        self.g_taxes_list.addItem(item)
        self.horizontalLayout_2.addWidget(self.g_taxes_list)
        self.g_notes_textedit = QtWidgets.QPlainTextEdit(self.g_container_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_notes_textedit.setFont(font)
        self.g_notes_textedit.setObjectName("g_notes_textedit")
        self.horizontalLayout_2.addWidget(self.g_notes_textedit)
        self.verticalLayout_2.addWidget(self.g_container_frame)
        self.g_controlbuttons_frame = QtWidgets.QFrame(ManualTaxOverrideDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(6)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem1 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem1)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/manualtaxoverride_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_11.addWidget(self.g_cancel_button)
        self.g_reset_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_reset_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_reset_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_reset_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_reset_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/manualtaxoverride_dialog/reset"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_reset_button.setIcon(icon1)
        self.g_reset_button.setIconSize(QtCore.QSize(24, 24))
        self.g_reset_button.setObjectName("g_reset_button")
        self.horizontalLayout_11.addWidget(self.g_reset_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_save_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/manualtaxoverride_dialog/save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon2)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_11.addWidget(self.g_save_button)
        self.verticalLayout_2.addWidget(self.g_controlbuttons_frame)

        self.retranslateUi(ManualTaxOverrideDialog)
        self.g_taxes_list.setCurrentRow(-1)
        QtCore.QMetaObject.connectSlotsByName(ManualTaxOverrideDialog)

    def retranslateUi(self, ManualTaxOverrideDialog):
        _translate = QtCore.QCoreApplication.translate
        ManualTaxOverrideDialog.setWindowTitle(_translate("ManualTaxOverrideDialog", "Manual Tax Override"))
        ManualTaxOverrideDialog.setProperty("read_tax_charts_url_text", _translate("ManualTaxOverrideDialog", "/apps/cash_register/queries/read__tbl__tax_charts"))
        ManualTaxOverrideDialog.setProperty("retrieving_tax_types_msg_text", _translate("ManualTaxOverrideDialog", "Retrieving known tax types ..."))
        ManualTaxOverrideDialog.setProperty("read_tax_chart_rates_url_text", _translate("ManualTaxOverrideDialog", "/apps/cash_register/queries/read__tbl__tax_chart_rates"))
        ManualTaxOverrideDialog.setProperty("confirm_multiple_tax_overrides_msg_text", _translate("ManualTaxOverrideDialog", "You have selected multiple taxes to apply.\n"
"Is this correct?"))
        ManualTaxOverrideDialog.setProperty("warning_title_text", _translate("ManualTaxOverrideDialog", "Warning"))
        ManualTaxOverrideDialog.setProperty("at_least_one_tax_msg_text", _translate("ManualTaxOverrideDialog", "You must select at least one tax to apply"))
        self.label.setText(_translate("ManualTaxOverrideDialog", "Manual Tax Override"))
        self.g_help_label.setText(_translate("ManualTaxOverrideDialog", "Select one or more taxes below to override the configured taxes on this sale."))
        __sortingEnabled = self.g_taxes_list.isSortingEnabled()
        self.g_taxes_list.setSortingEnabled(False)
        item = self.g_taxes_list.item(0)
        item.setText(_translate("ManualTaxOverrideDialog", "State Tax"))
        item = self.g_taxes_list.item(1)
        item.setText(_translate("ManualTaxOverrideDialog", "Other Tax"))
        item = self.g_taxes_list.item(2)
        item.setText(_translate("ManualTaxOverrideDialog", "Federal Tax"))
        self.g_taxes_list.setSortingEnabled(__sortingEnabled)
        self.g_notes_textedit.setPlaceholderText(_translate("ManualTaxOverrideDialog", "Enter any notes for this transaction here."))
        self.g_cancel_button.setText(_translate("ManualTaxOverrideDialog", "  Cancel"))
        self.g_reset_button.setText(_translate("ManualTaxOverrideDialog", "  Reset"))
        self.g_save_button.setText(_translate("ManualTaxOverrideDialog", "  Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManualTaxOverrideDialog = QtWidgets.QDialog()
    ui = Ui_ManualTaxOverrideDialog()
    ui.setupUi(ManualTaxOverrideDialog)
    ManualTaxOverrideDialog.show()
    sys.exit(app.exec_())
