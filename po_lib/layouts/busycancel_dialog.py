# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'busycancel_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_BusyCancelDialog(object):
    def setupUi(self, BusyCancelDialog):
        BusyCancelDialog.setObjectName("BusyCancelDialog")
        BusyCancelDialog.setWindowModality(QtCore.Qt.ApplicationModal)
        BusyCancelDialog.resize(421, 214)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.MinimumExpanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(BusyCancelDialog.sizePolicy().hasHeightForWidth())
        BusyCancelDialog.setSizePolicy(sizePolicy)
        BusyCancelDialog.setStyleSheet("\n"
"#BusyCancelDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_cancel_button, #__g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed, #__g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"\n"
"#BusyCancelDialog QPushButton, \n"
"#__BusyCancelDialog QPushButton {\n"
"    color:white;\n"
"    font-weight:bold;\n"
"    font-size:15px;\n"
"}\n"
"\n"
"#g_message_label {\n"
"    font-weight: bold;\n"
"    font-size: 16px;\n"
"}\n"
"\n"
"")
        BusyCancelDialog.setModal(True)
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/busycancel_dialog/checkmark"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        BusyCancelDialog.setProperty("message_dialog_button_icon", icon)
        self.verticalLayout = QtWidgets.QVBoxLayout(BusyCancelDialog)
        self.verticalLayout.setContentsMargins(16, 16, 16, 16)
        self.verticalLayout.setSpacing(24)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_message_label = QtWidgets.QLabel(BusyCancelDialog)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.MinimumExpanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_message_label.sizePolicy().hasHeightForWidth())
        self.g_message_label.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_message_label.setFont(font)
        self.g_message_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_message_label.setWordWrap(True)
        self.g_message_label.setOpenExternalLinks(True)
        self.g_message_label.setTextInteractionFlags(QtCore.Qt.TextBrowserInteraction)
        self.g_message_label.setObjectName("g_message_label")
        self.verticalLayout.addWidget(self.g_message_label)
        self.g_progress_bar = QtWidgets.QProgressBar(BusyCancelDialog)
        self.g_progress_bar.setMaximum(0)
        self.g_progress_bar.setProperty("value", -1)
        self.g_progress_bar.setTextVisible(False)
        self.g_progress_bar.setObjectName("g_progress_bar")
        self.verticalLayout.addWidget(self.g_progress_bar)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setContentsMargins(-1, 0, -1, -1)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(BusyCancelDialog)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/busycancel_dialog/x"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon1)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(BusyCancelDialog)
        QtCore.QMetaObject.connectSlotsByName(BusyCancelDialog)

    def retranslateUi(self, BusyCancelDialog):
        _translate = QtCore.QCoreApplication.translate
        BusyCancelDialog.setWindowTitle(_translate("BusyCancelDialog", "Please Wait"))
        BusyCancelDialog.setProperty("message_dialog_stylesheet_text", _translate("BusyCancelDialog", "\n"
"/* Sets the cancel button to a green color */\n"
"\n"
"#g_cancel_button, #__g_cancel_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_cancel_button:pressed, #__g_cancel_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
""))
        BusyCancelDialog.setProperty("message_dialog_button_text", _translate("BusyCancelDialog", "  Ok"))
        self.g_message_label.setText(_translate("BusyCancelDialog", "Contacting Pinogy server and validating configuration ... "))
        self.g_cancel_button.setText(_translate("BusyCancelDialog", "  Cancel"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    BusyCancelDialog = QtWidgets.QDialog()
    ui = Ui_BusyCancelDialog()
    ui.setupUi(BusyCancelDialog)
    BusyCancelDialog.show()
    sys.exit(app.exec_())
