# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'delivery_date_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_DeliveryDateDialog(object):
    def setupUi(self, DeliveryDateDialog):
        DeliveryDateDialog.setObjectName("DeliveryDateDialog")
        DeliveryDateDialog.resize(391, 223)
        DeliveryDateDialog.setStyleSheet("#DeliveryDateDialog {\n"
"       background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"       }\n"
"\n"
"       QFrame {\n"
"       border:0px;\n"
"       }\n"
"       QComboBox {\n"
"       height: 25px;\n"
"       line-height: 25px;\n"
"       border: 1px solid #aaa;\n"
"       border-radius: 5px;\n"
"       padding: 4px;\n"
"       color: #555;\n"
"       font-size: 11px;\n"
"       }\n"
"\n"
"       QComboBox::drop-down {\n"
"       subcontrol-origin: margin;\n"
"       background:;\n"
"       background-image: url(:/entity_dialog/drop-down-arrow);\n"
"       width: 20px;\n"
"       }\n"
"\n"
"       #g_select_button {\n"
"       background-color: #009c00;\n"
"       border:2px solid #007f00;\n"
"       }\n"
"\n"
"       #g_select_button:pressed {\n"
"       background-color: #007f00;\n"
"       }\n"
"\n"
"                #g_cancel_button {\n"
"                background-color: #ee1111;\n"
"                border:2px solid #CA0E0E;\n"
"                }\n"
"\n"
"       #g_cancel_button:pressed {\n"
"       background-color:#CA0E0E;\n"
"       }\n"
"\n"
"       #DeliveryDateDialog QPushButton, QPushButton {\n"
"       color:white;\n"
"       font-weight:bold;\n"
"       font-size:15px;\n"
"       }\n"
"       #g_title_label {\n"
"       font-weight:bold;\n"
"       font-size:17px;\n"
"       }\n"
"       #g_message_label {\n"
"       font-size:10px;\n"
"       }\n"
"   ")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(DeliveryDateDialog)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_title_label = QtWidgets.QLabel(DeliveryDateDialog)
        self.g_title_label.setObjectName("g_title_label")
        self.verticalLayout_2.addWidget(self.g_title_label)
        self.g_message_label = QtWidgets.QLabel(DeliveryDateDialog)
        self.g_message_label.setObjectName("g_message_label")
        self.verticalLayout_2.addWidget(self.g_message_label)
        self.g_main_frame = QtWidgets.QFrame(DeliveryDateDialog)
        self.g_main_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_main_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_main_frame.setObjectName("g_main_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_main_frame)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_dateedit_combobox = QtWidgets.QComboBox(self.g_main_frame)
        self.g_dateedit_combobox.setObjectName("g_dateedit_combobox")
        self.verticalLayout.addWidget(self.g_dateedit_combobox)
        self.verticalLayout_2.addWidget(self.g_main_frame)
        self.g_buttons_frame = QtWidgets.QFrame(DeliveryDateDialog)
        self.g_buttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_buttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_buttons_frame.setObjectName("g_buttons_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_buttons_frame)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(145, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_buttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(100, 40))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/canned_discount_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_select_button = QtWidgets.QPushButton(self.g_buttons_frame)
        self.g_select_button.setMinimumSize(QtCore.QSize(100, 40))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/canned_discount_dialog/apply"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_select_button.setIcon(icon1)
        self.g_select_button.setIconSize(QtCore.QSize(24, 24))
        self.g_select_button.setObjectName("g_select_button")
        self.horizontalLayout.addWidget(self.g_select_button)
        self.verticalLayout_2.addWidget(self.g_buttons_frame)

        self.retranslateUi(DeliveryDateDialog)
        QtCore.QMetaObject.connectSlotsByName(DeliveryDateDialog)

    def retranslateUi(self, DeliveryDateDialog):
        _translate = QtCore.QCoreApplication.translate
        DeliveryDateDialog.setWindowTitle(_translate("DeliveryDateDialog", "Dialog"))
        self.g_title_label.setText(_translate("DeliveryDateDialog", "Select a Delivery Date"))
        self.g_message_label.setText(_translate("DeliveryDateDialog", "TextLabel"))
        self.g_cancel_button.setText(_translate("DeliveryDateDialog", "Cancel"))
        self.g_select_button.setText(_translate("DeliveryDateDialog", "Select"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    DeliveryDateDialog = QtWidgets.QDialog()
    ui = Ui_DeliveryDateDialog()
    ui.setupUi(DeliveryDateDialog)
    DeliveryDateDialog.show()
    sys.exit(app.exec_())
