# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'import_screen.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_Import(object):
    def setupUi(self, Import):
        Import.setObjectName("Import")
        Import.resize(1267, 766)
        Import.setMinimumSize(QtCore.QSize(0, 360))
        Import.setStyleSheet("#Import  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"#g_desc_label {\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"\n"
"}\n"
"\n"
"#g_disabled_frame {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_main_label {\n"
"font-size: 17px;\n"
"font-weight: bold;\n"
"}\n"
"\n"
"#g_formats_and_types_frame QLabel, #g_file_frame QLabel, #g_uploadfile_lineedit {\n"
"    font-size: 11px;\n"
"}")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/main_window/import"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        Import.setProperty("screen_toolbutton_icon", icon)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(Import)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_top_frame = QtWidgets.QFrame(Import)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setSpacing(0)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_7.addWidget(self.g_progress_frame)
        self.verticalLayout_2.addWidget(self.g_top_frame)
        self.g_disabled_frame = QtWidgets.QFrame(Import)
        self.g_disabled_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_disabled_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_disabled_frame.setObjectName("g_disabled_frame")
        self.verticalLayout_10 = QtWidgets.QVBoxLayout(self.g_disabled_frame)
        self.verticalLayout_10.setObjectName("verticalLayout_10")
        self.g_disabled_label = QtWidgets.QLabel(self.g_disabled_frame)
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_disabled_label.setFont(font)
        self.g_disabled_label.setAlignment(QtCore.Qt.AlignCenter)
        self.g_disabled_label.setObjectName("g_disabled_label")
        self.verticalLayout_10.addWidget(self.g_disabled_label)
        spacerItem = QtWidgets.QSpacerItem(822, 106, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_10.addItem(spacerItem)
        self.verticalLayout_2.addWidget(self.g_disabled_frame)
        self.g_main_frame = QtWidgets.QFrame(Import)
        self.g_main_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_main_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_main_frame.setObjectName("g_main_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_main_frame)
        self.verticalLayout.setContentsMargins(9, 5, 9, 9)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_main_label = QtWidgets.QLabel(self.g_main_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setStyleSheet("")
        self.g_main_label.setObjectName("g_main_label")
        self.horizontalLayout_6.addWidget(self.g_main_label)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem1)
        self.g_back_btn = QtWidgets.QPushButton(self.g_main_frame)
        self.g_back_btn.setMinimumSize(QtCore.QSize(100, 30))
        self.g_back_btn.setObjectName("g_back_btn")
        self.horizontalLayout_6.addWidget(self.g_back_btn)
        self.verticalLayout.addLayout(self.horizontalLayout_6)
        self.g_desc_label = QtWidgets.QLabel(self.g_main_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_desc_label.setFont(font)
        self.g_desc_label.setObjectName("g_desc_label")
        self.verticalLayout.addWidget(self.g_desc_label)
        self.g_formats_and_types_frame = QtWidgets.QFrame(self.g_main_frame)
        self.g_formats_and_types_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_formats_and_types_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_formats_and_types_frame.setObjectName("g_formats_and_types_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.g_formats_and_types_frame)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.g_types_frame = QtWidgets.QFrame(self.g_formats_and_types_frame)
        self.g_types_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_types_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_types_frame.setObjectName("g_types_frame")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.g_types_frame)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.g_types_label = QtWidgets.QLabel(self.g_types_frame)
        self.g_types_label.setObjectName("g_types_label")
        self.verticalLayout_5.addWidget(self.g_types_label)
        self.g_types_list = QtWidgets.QListWidget(self.g_types_frame)
        self.g_types_list.setObjectName("g_types_list")
        self.verticalLayout_5.addWidget(self.g_types_list)
        self.horizontalLayout_4.addWidget(self.g_types_frame)
        self.g_formats_frame = QtWidgets.QFrame(self.g_formats_and_types_frame)
        self.g_formats_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_formats_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_formats_frame.setObjectName("g_formats_frame")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.g_formats_frame)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.g_formats_label = QtWidgets.QLabel(self.g_formats_frame)
        self.g_formats_label.setObjectName("g_formats_label")
        self.verticalLayout_7.addWidget(self.g_formats_label)
        self.g_formats_list = QtWidgets.QListWidget(self.g_formats_frame)
        self.g_formats_list.setObjectName("g_formats_list")
        self.verticalLayout_7.addWidget(self.g_formats_list)
        self.horizontalLayout_4.addWidget(self.g_formats_frame)
        self.verticalLayout.addWidget(self.g_formats_and_types_frame)
        self.g_file_frame = QtWidgets.QFrame(self.g_main_frame)
        self.g_file_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_file_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_file_frame.setObjectName("g_file_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_file_frame)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label = QtWidgets.QLabel(self.g_file_frame)
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.g_uploadfile_lineedit = QtWidgets.QLineEdit(self.g_file_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_uploadfile_lineedit.sizePolicy().hasHeightForWidth())
        self.g_uploadfile_lineedit.setSizePolicy(sizePolicy)
        self.g_uploadfile_lineedit.setMinimumSize(QtCore.QSize(100, 30))
        self.g_uploadfile_lineedit.setMaximumSize(QtCore.QSize(16777215, 30))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_uploadfile_lineedit.setFont(font)
        self.g_uploadfile_lineedit.setReadOnly(False)
        self.g_uploadfile_lineedit.setObjectName("g_uploadfile_lineedit")
        self.horizontalLayout.addWidget(self.g_uploadfile_lineedit)
        self.g_selectfile_btn = QtWidgets.QPushButton(self.g_file_frame)
        self.g_selectfile_btn.setMinimumSize(QtCore.QSize(120, 30))
        self.g_selectfile_btn.setMaximumSize(QtCore.QSize(120, 30))
        self.g_selectfile_btn.setStyleSheet("")
        self.g_selectfile_btn.setIconSize(QtCore.QSize(24, 24))
        self.g_selectfile_btn.setObjectName("g_selectfile_btn")
        self.horizontalLayout.addWidget(self.g_selectfile_btn)
        self.verticalLayout.addWidget(self.g_file_frame)
        self.g_options_frame = QtWidgets.QFrame(self.g_main_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_options_frame.sizePolicy().hasHeightForWidth())
        self.g_options_frame.setSizePolicy(sizePolicy)
        self.g_options_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_options_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_options_frame.setObjectName("g_options_frame")
        self.verticalLayout.addWidget(self.g_options_frame)
        self.g_opts_frame = QtWidgets.QFrame(self.g_main_frame)
        self.g_opts_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_opts_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_opts_frame.setObjectName("g_opts_frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.g_opts_frame)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_opts_name = QtWidgets.QLineEdit(self.g_opts_frame)
        self.g_opts_name.setMinimumSize(QtCore.QSize(200, 0))
        self.g_opts_name.setMaximumSize(QtCore.QSize(16777215, 28))
        self.g_opts_name.setObjectName("g_opts_name")
        self.horizontalLayout_3.addWidget(self.g_opts_name)
        self.g_opts_save_btn = QtWidgets.QPushButton(self.g_opts_frame)
        self.g_opts_save_btn.setMinimumSize(QtCore.QSize(120, 28))
        self.g_opts_save_btn.setObjectName("g_opts_save_btn")
        self.horizontalLayout_3.addWidget(self.g_opts_save_btn)
        self.g_saved_options_combobox = QtWidgets.QComboBox(self.g_opts_frame)
        self.g_saved_options_combobox.setMinimumSize(QtCore.QSize(200, 0))
        self.g_saved_options_combobox.setObjectName("g_saved_options_combobox")
        self.horizontalLayout_3.addWidget(self.g_saved_options_combobox)
        self.g_opts_load_btn = QtWidgets.QPushButton(self.g_opts_frame)
        self.g_opts_load_btn.setMinimumSize(QtCore.QSize(120, 28))
        self.g_opts_load_btn.setObjectName("g_opts_load_btn")
        self.horizontalLayout_3.addWidget(self.g_opts_load_btn)
        self.verticalLayout.addWidget(self.g_opts_frame)
        self.g_progress = QtWidgets.QProgressBar(self.g_main_frame)
        self.g_progress.setProperty("value", 1)
        self.g_progress.setObjectName("g_progress")
        self.verticalLayout.addWidget(self.g_progress)
        self.frame_2 = QtWidgets.QFrame(self.g_main_frame)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.frame_2)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_save_report_btn = QtWidgets.QPushButton(self.frame_2)
        self.g_save_report_btn.setMinimumSize(QtCore.QSize(0, 30))
        self.g_save_report_btn.setObjectName("g_save_report_btn")
        self.horizontalLayout_2.addWidget(self.g_save_report_btn)
        self.g_btn = QtWidgets.QPushButton(self.frame_2)
        self.g_btn.setMinimumSize(QtCore.QSize(0, 30))
        self.g_btn.setObjectName("g_btn")
        self.horizontalLayout_2.addWidget(self.g_btn)
        self.g_cancel_btn = QtWidgets.QPushButton(self.frame_2)
        self.g_cancel_btn.setMinimumSize(QtCore.QSize(0, 30))
        self.g_cancel_btn.setObjectName("g_cancel_btn")
        self.horizontalLayout_2.addWidget(self.g_cancel_btn)
        self.verticalLayout.addWidget(self.frame_2)
        self.g_spacer = QtWidgets.QWidget(self.g_main_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_spacer.sizePolicy().hasHeightForWidth())
        self.g_spacer.setSizePolicy(sizePolicy)
        self.g_spacer.setObjectName("g_spacer")
        self.verticalLayout.addWidget(self.g_spacer)
        self.verticalLayout_2.addWidget(self.g_main_frame)

        self.retranslateUi(Import)
        QtCore.QMetaObject.connectSlotsByName(Import)

    def retranslateUi(self, Import):
        _translate = QtCore.QCoreApplication.translate
        Import.setWindowTitle(_translate("Import", "Import"))
        Import.setProperty("screen_toolbutton_tooltip_text", _translate("Import", "Import"))
        Import.setProperty("screen_toolbutton_title_text", _translate("Import", "Import"))
        Import.setProperty("screen_toolbutton_stylesheet_text", _translate("Import", "\n"
"QToolButton {\n"
"    background: #5a38b5;\n"
"    border: 2px solid #482D91;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #482D91;\n"
"}\n"
"\n"
"\n"
"QToolButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}"))
        Import.setProperty("screen_indicator_stylesheet_text", _translate("Import", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"            the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #482D91;\n"
"    border: 0;\n"
"} "))
        self.g_disabled_label.setText(_translate("Import", "There are no settings available for your access level."))
        self.g_main_label.setText(_translate("Import", "Import"))
        self.g_back_btn.setText(_translate("Import", "Back"))
        self.g_desc_label.setText(_translate("Import", "Import"))
        self.g_types_label.setText(_translate("Import", "Types"))
        self.g_formats_label.setText(_translate("Import", "Formats"))
        self.label.setText(_translate("Import", "File to import"))
        self.g_uploadfile_lineedit.setPlaceholderText(_translate("Import", "No file chosen."))
        self.g_selectfile_btn.setText(_translate("Import", "Select File"))
        self.g_opts_save_btn.setText(_translate("Import", "Save Options"))
        self.g_opts_load_btn.setText(_translate("Import", "Load Options"))
        self.g_save_report_btn.setText(_translate("Import", "Save Report"))
        self.g_btn.setText(_translate("Import", "Next"))
        self.g_cancel_btn.setText(_translate("Import", "Cancel"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    Import = QtWidgets.QDialog()
    ui = Ui_Import()
    ui.setupUi(Import)
    Import.show()
    sys.exit(app.exec_())
