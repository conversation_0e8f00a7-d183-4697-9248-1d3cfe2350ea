# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managetax_modaldialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ManageTax(object):
    def setupUi(self, ManageTax):
        ManageTax.setObjectName("ManageTax")
        ManageTax.resize(601, 476)
        ManageTax.setMinimumSize(QtCore.QSize(601, 0))
        ManageTax.setMaximumSize(QtCore.QSize(769, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        ManageTax.setFont(font)
        ManageTax.setStyleSheet("#ManageTax {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QFrame {\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox {\n"
"    padding-top:10px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/managetax_modaldialog/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/managetax_modaldialog/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
".QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QTableWidget {\n"
"    border:0px;\n"
"    border-bottom: 1px solid #000000;\n"
"}\n"
"\n"
"QTableWidget::item::focus {\n"
"    background-color:#0B256B;\n"
"    border: 1px solid #000000;\n"
"    color:#ffffff;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"\n"
"#g_unifiedsearch_frame, #__g_unifiedsearch_frame {\n"
"    border:none;\n"
"}\n"
"\n"
"#g_searchbox_frame, #__g_searchbox_frame {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#g_clearsearch_button, #__g_clearsearch_button {\n"
"    border:0px;\n"
"    background:none;\n"
"}\n"
"\n"
"#g_searchtext_lineedit, #__g_searchtext_lineedit {\n"
"    border: 0px;\n"
"}\n"
"\n"
"#g_search_combobox, #__g_search_combobox {\n"
"    height: 28px;\n"
"    line-height: 28px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #555;\n"
" }\n"
"\n"
"#g_search_combobox, #g_search_combobox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"}\n"
"\n"
"#g_cancel_button,\n"
"#g_delete_button, #__g_cancel_button  {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed,\n"
"#g_delete_button:pressed, #__g_cancel_button:pressed  {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_save_button,\n"
"#g_add_button, #__g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed,\n"
"#g_add_button:pressed, #__g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_pagenav_frame QLabel,\n"
"#g_pagenav_frame QLineEdit,\n"
"#g_totalresults_label {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#__g_taxgroup_label {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_taxgroup_lineedit {\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"#g_pagination_frame QLineEdit,\n"
"#g_pagination_frame QLabel {\n"
"    font-size: 11px;\n"
"}")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(ManageTax)
        self.verticalLayout_2.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.g_top_frame = QtWidgets.QFrame(ManageTax)
        self.g_top_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_top_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_top_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_top_frame.setObjectName("g_top_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_top_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_progress_frame = QtWidgets.QFrame(self.g_top_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.horizontalLayout_6.addWidget(self.g_progress_frame)
        self.verticalLayout_2.addWidget(self.g_top_frame)
        self.g_titlepagenav_frame = QtWidgets.QFrame(ManageTax)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_31 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_31.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_31.setObjectName("horizontalLayout_31")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.verticalLayout4 = QtWidgets.QVBoxLayout(self.g_title_frame)
        self.verticalLayout4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout4.setObjectName("verticalLayout4")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMaximumSize(QtCore.QSize(16777215, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setObjectName("g_main_label")
        self.verticalLayout4.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout4.addItem(spacerItem)
        self.horizontalLayout_31.addWidget(self.g_title_frame)
        self.g_pagenav_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_pagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_pagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pagenav_frame.setObjectName("g_pagenav_frame")
        self.g_pagenav_hlayout = QtWidgets.QHBoxLayout(self.g_pagenav_frame)
        self.g_pagenav_hlayout.setContentsMargins(0, 0, 0, 0)
        self.g_pagenav_hlayout.setSpacing(6)
        self.g_pagenav_hlayout.setObjectName("g_pagenav_hlayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.g_pagenav_hlayout.addItem(spacerItem1)
        self.g_previouspage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_previouspage_label.setEnabled(False)
        self.g_previouspage_label.setText("")
        self.g_previouspage_label.setPixmap(QtGui.QPixmap(":/managedevice_modaldialog/previous"))
        self.g_previouspage_label.setScaledContents(False)
        self.g_previouspage_label.setObjectName("g_previouspage_label")
        self.g_pagenav_hlayout.addWidget(self.g_previouspage_label)
        self.g_nextpage_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_nextpage_label.setEnabled(True)
        self.g_nextpage_label.setText("")
        self.g_nextpage_label.setPixmap(QtGui.QPixmap(":/managedevice_modaldialog/next"))
        self.g_nextpage_label.setObjectName("g_nextpage_label")
        self.g_pagenav_hlayout.addWidget(self.g_nextpage_label)
        self.g_currentpage_lineedit = QtWidgets.QLineEdit(self.g_pagenav_frame)
        self.g_currentpage_lineedit.setEnabled(True)
        self.g_currentpage_lineedit.setMinimumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setMaximumSize(QtCore.QSize(31, 31))
        self.g_currentpage_lineedit.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit.setObjectName("g_currentpage_lineedit")
        self.g_pagenav_hlayout.addWidget(self.g_currentpage_lineedit)
        self.g_slash_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_slash_label.setObjectName("g_slash_label")
        self.g_pagenav_hlayout.addWidget(self.g_slash_label)
        self.g_totalpages_label = QtWidgets.QLabel(self.g_pagenav_frame)
        self.g_totalpages_label.setObjectName("g_totalpages_label")
        self.g_pagenav_hlayout.addWidget(self.g_totalpages_label)
        self.horizontalLayout_31.addWidget(self.g_pagenav_frame)
        self.verticalLayout_2.addWidget(self.g_titlepagenav_frame)
        self.__g_taxgroup_frame = QtWidgets.QFrame(ManageTax)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_taxgroup_frame.sizePolicy().hasHeightForWidth())
        self.__g_taxgroup_frame.setSizePolicy(sizePolicy)
        self.__g_taxgroup_frame.setMinimumSize(QtCore.QSize(0, 0))
        self.__g_taxgroup_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.__g_taxgroup_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_taxgroup_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_taxgroup_frame.setObjectName("__g_taxgroup_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.__g_taxgroup_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(4)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_taxgroup_label = QtWidgets.QLabel(self.__g_taxgroup_frame)
        self.g_taxgroup_label.setMinimumSize(QtCore.QSize(75, 0))
        self.g_taxgroup_label.setMaximumSize(QtCore.QSize(60, 16777215))
        font = QtGui.QFont()
        font.setBold(True)
        font.setWeight(75)
        self.g_taxgroup_label.setFont(font)
        self.g_taxgroup_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_taxgroup_label.setObjectName("g_taxgroup_label")
        self.horizontalLayout_8.addWidget(self.g_taxgroup_label)
        self.g_taxgroup_lineedit = QtWidgets.QLineEdit(self.__g_taxgroup_frame)
        self.g_taxgroup_lineedit.setMinimumSize(QtCore.QSize(400, 0))
        self.g_taxgroup_lineedit.setMaximumSize(QtCore.QSize(150, 16777215))
        self.g_taxgroup_lineedit.setProperty("qp_taxch_description", "")
        self.g_taxgroup_lineedit.setObjectName("g_taxgroup_lineedit")
        self.horizontalLayout_8.addWidget(self.g_taxgroup_lineedit)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_8.addItem(spacerItem2)
        self.verticalLayout_2.addWidget(self.__g_taxgroup_frame)
        self.g_taxrates_groupbox = QtWidgets.QGroupBox(ManageTax)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_taxrates_groupbox.setFont(font)
        self.g_taxrates_groupbox.setObjectName("g_taxrates_groupbox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_taxrates_groupbox)
        self.verticalLayout.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setSpacing(4)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.g_totalresults_label = QtWidgets.QLabel(self.g_taxrates_groupbox)
        self.g_totalresults_label.setMinimumSize(QtCore.QSize(0, 24))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_totalresults_label.setFont(font)
        self.g_totalresults_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_totalresults_label.setObjectName("g_totalresults_label")
        self.horizontalLayout_3.addWidget(self.g_totalresults_label)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem3)
        self.g_pagination_frame = QtWidgets.QFrame(self.g_taxrates_groupbox)
        self.g_pagination_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pagination_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pagination_frame.setObjectName("g_pagination_frame")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.g_pagination_frame)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setSpacing(8)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem4)
        self.g_previouspage_label_2 = QtWidgets.QLabel(self.g_pagination_frame)
        self.g_previouspage_label_2.setEnabled(False)
        self.g_previouspage_label_2.setText("")
        self.g_previouspage_label_2.setPixmap(QtGui.QPixmap(":/devicemanagement_screen/previous"))
        self.g_previouspage_label_2.setScaledContents(False)
        self.g_previouspage_label_2.setObjectName("g_previouspage_label_2")
        self.horizontalLayout_9.addWidget(self.g_previouspage_label_2)
        self.g_nextpage_label_2 = QtWidgets.QLabel(self.g_pagination_frame)
        self.g_nextpage_label_2.setEnabled(False)
        self.g_nextpage_label_2.setText("")
        self.g_nextpage_label_2.setPixmap(QtGui.QPixmap(":/devicemanagement_screen/next"))
        self.g_nextpage_label_2.setObjectName("g_nextpage_label_2")
        self.horizontalLayout_9.addWidget(self.g_nextpage_label_2)
        self.g_currentpage_lineedit_2 = QtWidgets.QLineEdit(self.g_pagination_frame)
        self.g_currentpage_lineedit_2.setEnabled(True)
        self.g_currentpage_lineedit_2.setMinimumSize(QtCore.QSize(0, 28))
        self.g_currentpage_lineedit_2.setMaximumSize(QtCore.QSize(30, 16777215))
        self.g_currentpage_lineedit_2.setAlignment(QtCore.Qt.AlignCenter)
        self.g_currentpage_lineedit_2.setReadOnly(True)
        self.g_currentpage_lineedit_2.setObjectName("g_currentpage_lineedit_2")
        self.horizontalLayout_9.addWidget(self.g_currentpage_lineedit_2)
        self.g_slash_label_2 = QtWidgets.QLabel(self.g_pagination_frame)
        self.g_slash_label_2.setObjectName("g_slash_label_2")
        self.horizontalLayout_9.addWidget(self.g_slash_label_2)
        self.g_totalpages_label_2 = QtWidgets.QLabel(self.g_pagination_frame)
        self.g_totalpages_label_2.setObjectName("g_totalpages_label_2")
        self.horizontalLayout_9.addWidget(self.g_totalpages_label_2)
        self.horizontalLayout_3.addWidget(self.g_pagination_frame)
        self.verticalLayout.addLayout(self.horizontalLayout_3)
        self.g_tax_details_table = QtWidgets.QTableWidget(self.g_taxrates_groupbox)
        self.g_tax_details_table.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_tax_details_table.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_tax_details_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)
        self.g_tax_details_table.setAlternatingRowColors(True)
        self.g_tax_details_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.g_tax_details_table.setShowGrid(False)
        self.g_tax_details_table.setObjectName("g_tax_details_table")
        self.g_tax_details_table.setColumnCount(3)
        self.g_tax_details_table.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tax_details_table.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tax_details_table.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        item.setTextAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignVCenter)
        self.g_tax_details_table.setHorizontalHeaderItem(2, item)
        self.g_tax_details_table.horizontalHeader().setDefaultSectionSize(150)
        self.g_tax_details_table.horizontalHeader().setStretchLastSection(True)
        self.g_tax_details_table.verticalHeader().setVisible(False)
        self.verticalLayout.addWidget(self.g_tax_details_table)
        self.__g_bottombar_frame = QtWidgets.QFrame(self.g_taxrates_groupbox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_bottombar_frame.sizePolicy().hasHeightForWidth())
        self.__g_bottombar_frame.setSizePolicy(sizePolicy)
        self.__g_bottombar_frame.setMinimumSize(QtCore.QSize(247, 39))
        self.__g_bottombar_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_bottombar_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_bottombar_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_bottombar_frame.setObjectName("__g_bottombar_frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.__g_bottombar_frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem5)
        self.g_delete_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_delete_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_delete_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_delete_button.setStyleSheet("")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/fpe/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_delete_button.setIcon(icon)
        self.g_delete_button.setIconSize(QtCore.QSize(24, 24))
        self.g_delete_button.setObjectName("g_delete_button")
        self.horizontalLayout_4.addWidget(self.g_delete_button)
        self.g_manage_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_manage_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_manage_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_manage_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/fpe/pencil"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_manage_button.setIcon(icon1)
        self.g_manage_button.setIconSize(QtCore.QSize(24, 24))
        self.g_manage_button.setObjectName("g_manage_button")
        self.horizontalLayout_4.addWidget(self.g_manage_button)
        self.g_add_button = QtWidgets.QPushButton(self.__g_bottombar_frame)
        self.g_add_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_add_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_add_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/fpe/add"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_add_button.setIcon(icon2)
        self.g_add_button.setIconSize(QtCore.QSize(24, 24))
        self.g_add_button.setObjectName("g_add_button")
        self.horizontalLayout_4.addWidget(self.g_add_button)
        self.verticalLayout.addWidget(self.__g_bottombar_frame)
        self.verticalLayout_2.addWidget(self.g_taxrates_groupbox)
        self.__g_bottombar_frame_2 = QtWidgets.QFrame(ManageTax)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.__g_bottombar_frame_2.sizePolicy().hasHeightForWidth())
        self.__g_bottombar_frame_2.setSizePolicy(sizePolicy)
        self.__g_bottombar_frame_2.setMinimumSize(QtCore.QSize(247, 39))
        self.__g_bottombar_frame_2.setMaximumSize(QtCore.QSize(16777215, 40))
        self.__g_bottombar_frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.__g_bottombar_frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.__g_bottombar_frame_2.setObjectName("__g_bottombar_frame_2")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.__g_bottombar_frame_2)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem6)
        self.g_cancel_button = QtWidgets.QPushButton(self.__g_bottombar_frame_2)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setStyleSheet("")
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_5.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.__g_bottombar_frame_2)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setStyleSheet("")
        icon3 = QtGui.QIcon()
        icon3.addPixmap(QtGui.QPixmap(":/fpe/check"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon3)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_5.addWidget(self.g_save_button)
        self.verticalLayout_2.addWidget(self.__g_bottombar_frame_2)

        self.retranslateUi(ManageTax)
        QtCore.QMetaObject.connectSlotsByName(ManageTax)

    def retranslateUi(self, ManageTax):
        _translate = QtCore.QCoreApplication.translate
        ManageTax.setWindowTitle(_translate("ManageTax", "Manage Tax"))
        self.g_main_label.setText(_translate("ManageTax", "Manage Tax "))
        self.g_previouspage_label.setToolTip(_translate("ManageTax", "Previous Page"))
        self.g_nextpage_label.setToolTip(_translate("ManageTax", "Next Page"))
        self.g_currentpage_lineedit.setText(_translate("ManageTax", "999"))
        self.g_slash_label.setText(_translate("ManageTax", "/"))
        self.g_totalpages_label.setToolTip(_translate("ManageTax", "Total Pages"))
        self.g_totalpages_label.setText(_translate("ManageTax", "999"))
        self.g_taxgroup_label.setText(_translate("ManageTax", "Tax Name"))
        self.g_taxgroup_lineedit.setPlaceholderText(_translate("ManageTax", "Enter a name for this tax..."))
        self.g_taxrates_groupbox.setTitle(_translate("ManageTax", "Rates"))
        self.g_totalresults_label.setText(_translate("ManageTax", "Loading results."))
        self.g_previouspage_label_2.setToolTip(_translate("ManageTax", "Previous Page"))
        self.g_nextpage_label_2.setToolTip(_translate("ManageTax", "Next Page"))
        self.g_currentpage_lineedit_2.setText(_translate("ManageTax", "1"))
        self.g_slash_label_2.setText(_translate("ManageTax", "/"))
        self.g_totalpages_label_2.setToolTip(_translate("ManageTax", "Total Pages"))
        self.g_totalpages_label_2.setText(_translate("ManageTax", "1"))
        self.g_tax_details_table.setSortingEnabled(True)
        item = self.g_tax_details_table.horizontalHeaderItem(0)
        item.setText(_translate("ManageTax", "Tax %"))
        item = self.g_tax_details_table.horizontalHeaderItem(1)
        item.setText(_translate("ManageTax", "Date Range"))
        item = self.g_tax_details_table.horizontalHeaderItem(2)
        item.setText(_translate("ManageTax", "Dollar Range"))
        self.g_delete_button.setText(_translate("ManageTax", "Delete"))
        self.g_manage_button.setText(_translate("ManageTax", " Manage"))
        self.g_add_button.setText(_translate("ManageTax", "Add"))
        self.g_cancel_button.setText(_translate("ManageTax", "Cancel"))
        self.g_save_button.setText(_translate("ManageTax", "Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageTax = QtWidgets.QDialog()
    ui = Ui_ManageTax()
    ui.setupUi(ManageTax)
    ManageTax.show()
    sys.exit(app.exec_())
