# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'voice_auth_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_VoiceAuthorizationDialog(object):
    def setupUi(self, VoiceAuthorizationDialog):
        VoiceAuthorizationDialog.setObjectName("VoiceAuthorizationDialog")
        VoiceAuthorizationDialog.resize(400, 350)
        VoiceAuthorizationDialog.setStyleSheet("QDialog {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QPushButton {\n"
"    color: white;\n"
"    font-weight: bold;\n"
"    font-size: 15px;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border: 2px solid #287599;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QPushButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: None;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QPlainTextEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    background-color: #fff;\n"
"}\n"
"\n"
"QComboBox {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"}\n"
"\n"
"QComboBox::drop-down:enabled {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/login_screen/combobox_down_arrow);\n"
"    height: 31px;\n"
"    width: 20px;\n"
"}\n"
"\n"
"#g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_title_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#frame QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;    \n"
"}\n"
"\n"
"#frame QLineEdit,\n"
"#frame QComboBox,\n"
"#frame QPlainTextEdit {\n"
"    font-size: 11px;\n"
"\n"
"}")
        self.verticalLayout = QtWidgets.QVBoxLayout(VoiceAuthorizationDialog)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_title_frame = QtWidgets.QFrame(VoiceAuthorizationDialog)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.g_title_frame)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.g_title_label = QtWidgets.QLabel(self.g_title_frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_title_label.setFont(font)
        self.g_title_label.setObjectName("g_title_label")
        self.horizontalLayout_2.addWidget(self.g_title_label)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem)
        self.verticalLayout.addWidget(self.g_title_frame)
        self.frame = QtWidgets.QFrame(VoiceAuthorizationDialog)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.formLayout = QtWidgets.QFormLayout(self.frame)
        self.formLayout.setObjectName("formLayout")
        self.label = QtWidgets.QLabel(self.frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.label.setFont(font)
        self.label.setObjectName("label")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.g_method_combobox = QtWidgets.QComboBox(self.frame)
        self.g_method_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_method_combobox.setObjectName("g_method_combobox")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.g_method_combobox)
        self.label_2 = QtWidgets.QLabel(self.frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.label_2.setFont(font)
        self.label_2.setObjectName("label_2")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_2)
        self.g_auth_lineedit = QtWidgets.QLineEdit(self.frame)
        self.g_auth_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_auth_lineedit.setObjectName("g_auth_lineedit")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.g_auth_lineedit)
        self.label_3 = QtWidgets.QLabel(self.frame)
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.label_3.setFont(font)
        self.label_3.setObjectName("label_3")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_3)
        self.g_note_lineedit = QtWidgets.QPlainTextEdit(self.frame)
        self.g_note_lineedit.setObjectName("g_note_lineedit")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.g_note_lineedit)
        self.verticalLayout.addWidget(self.frame)
        self.g_dialogbuttons_frame = QtWidgets.QFrame(VoiceAuthorizationDialog)
        self.g_dialogbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_dialogbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_dialogbuttons_frame.setObjectName("g_dialogbuttons_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_dialogbuttons_frame)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(100, 40))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/voice_auth_dialog/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_dialogbuttons_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(100, 40))
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/voice_auth_dialog/save"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout.addWidget(self.g_save_button)
        self.verticalLayout.addWidget(self.g_dialogbuttons_frame)

        self.retranslateUi(VoiceAuthorizationDialog)
        QtCore.QMetaObject.connectSlotsByName(VoiceAuthorizationDialog)

    def retranslateUi(self, VoiceAuthorizationDialog):
        _translate = QtCore.QCoreApplication.translate
        VoiceAuthorizationDialog.setWindowTitle(_translate("VoiceAuthorizationDialog", "Voice Authorization"))
        self.g_title_label.setText(_translate("VoiceAuthorizationDialog", "Voice Authorization"))
        self.label.setText(_translate("VoiceAuthorizationDialog", "Payment Method"))
        self.label_2.setText(_translate("VoiceAuthorizationDialog", "Authorization #"))
        self.label_3.setText(_translate("VoiceAuthorizationDialog", "Notes"))
        self.g_cancel_button.setText(_translate("VoiceAuthorizationDialog", "Cancel"))
        self.g_save_button.setText(_translate("VoiceAuthorizationDialog", "Save"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    VoiceAuthorizationDialog = QtWidgets.QDialog()
    ui = Ui_VoiceAuthorizationDialog()
    ui.setupUi(VoiceAuthorizationDialog)
    VoiceAuthorizationDialog.show()
    sys.exit(app.exec_())
