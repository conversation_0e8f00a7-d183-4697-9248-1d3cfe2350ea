# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'managetablesettings_dialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_ManageTableSettings(object):
    def setupUi(self, ManageTableSettings):
        ManageTableSettings.setObjectName("ManageTableSettings")
        ManageTableSettings.resize(739, 455)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(ManageTableSettings.sizePolicy().hasHeightForWidth())
        ManageTableSettings.setSizePolicy(sizePolicy)
        ManageTableSettings.setMinimumSize(QtCore.QSize(500, 0))
        ManageTableSettings.setMaximumSize(QtCore.QSize(16777215, 16777215))
        ManageTableSettings.setStyleSheet("#ManageTableSettings {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"#g_settings_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border:1px solid #073c83;\n"
"    border-radius:10px;\n"
"    background-color:#DDE4E9;\n"
"}\n"
"\n"
"QCheckBox {\n"
"padding-top:5px;\n"
"}\n"
"\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/entity_dialog/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/entity_dialog/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    height: 25px;\n"
"    line-height: 25px;\n"
"    border: 1px solid #aaa;\n"
"    border-radius: 5px;\n"
"    padding: 4px;\n"
"    color: #000;\n"
"    font-size: 11px;\n"
" }\n"
"\n"
"QComboBox::drop-down {\n"
"    subcontrol-origin: margin;\n"
"    background:;\n"
"    width:20px;\n"
"    /*background-color: #000;*/\n"
"}\n"
"\n"
"#g_status_checkcombobox:drop-down,\n"
"#g_other_combobox::drop-down,\n"
"#g_producttypes_checkcombobox::drop-down,\n"
"#g_deleted_combobox::drop-down,\n"
"#g_effect_combobox::drop-down,\n"
"#g_page_combobox::drop-down,\n"
"#g_column_checkcombobox::drop-down {\n"
"    background-image: url(:/price_review/dropdown);\n"
"}\n"
"\n"
"QPushButton {\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    border:2px solid #287599;\n"
"    color:white;\n"
"}\n"
"\n"
".QPushButton:pressed {\n"
"    background: #287599;\n"
"    border:2px solid #287599;\n"
"}\n"
"\n"
"QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"    height:31px\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    spacing: 15px;\n"
"    background-color:#ffffff;\n"
"    color: #000000;\n"
"    font-weight:bold;\n"
"    border: 0px;\n"
"    height:15px;\n"
"    border-bottom: 1px solid #000;\n"
"    font-size:12px;\n"
"    padding-top:5px;\n"
"    padding-bottom: 5px;\n"
"}\n"
"\n"
"#g_dialog_frame {\n"
"    border:0px;\n"
"}\n"
"\n"
"\n"
".QGroupBox {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    margin-top: 0.5em;\n"
"\n"
"}\n"
"\n"
".QGroupBox::title {\n"
"     subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 3px 0 3px;\n"
"}\n"
"\n"
"#g_printingoptions_widget,\n"
"#g_verify_widget {\n"
"    border:1px solid #073c83;\n"
"    border-radius: 5px;\n"
"    border-top-left-radius:0px;\n"
"    background-color: #f4f4f4;\n"
"}\n"
"\n"
"#g_save_button {\n"
"    background-color: #009c00;\n"
"    border:2px solid #007f00;\n"
"}\n"
"\n"
"#g_save_button:pressed {\n"
"    background-color: #007f00;\n"
"}\n"
"\n"
"#g_delete_pushButton,\n"
"#g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border:2px solid #c80319;\n"
"}\n"
"\n"
"#g_delete_pushButton:pressed,\n"
"#g_cancel_button:pressed {\n"
"    background: #c80319\n"
"}\n"
"\n"
"#g_main_label {\n"
"    font-size: 17px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"QFrame QLabel {\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
"#g_column_checkcombobox {\n"
"    font-size: 11px;\n"
"}")
        ManageTableSettings.setProperty("screen_toolbutton_tooltip_text", "")
        ManageTableSettings.setProperty("screen_toolbutton_title_text", "")
        ManageTableSettings.setProperty("screen_toolbutton_stylesheet_text", "")
        ManageTableSettings.setProperty("screen_indicator_stylesheet_text", "")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(ManageTableSettings)
        self.verticalLayout_3.setContentsMargins(9, 0, 9, 9)
        self.verticalLayout_3.setSpacing(6)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_progress_frame = QtWidgets.QFrame(ManageTableSettings)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_progress_frame.sizePolicy().hasHeightForWidth())
        self.g_progress_frame.setSizePolicy(sizePolicy)
        self.g_progress_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_progress_frame.setStyleSheet("QFrame {\n"
"    background-color: white;\n"
"}")
        self.g_progress_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_progress_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_progress_frame.setLineWidth(0)
        self.g_progress_frame.setObjectName("g_progress_frame")
        self.g_completed_frame = QtWidgets.QFrame(self.g_progress_frame)
        self.g_completed_frame.setGeometry(QtCore.QRect(-1, 0, 191, 10))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_completed_frame.sizePolicy().hasHeightForWidth())
        self.g_completed_frame.setSizePolicy(sizePolicy)
        self.g_completed_frame.setMinimumSize(QtCore.QSize(0, 2))
        self.g_completed_frame.setStyleSheet("QFrame {\n"
"    background-color: blue;\n"
"}")
        self.g_completed_frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.g_completed_frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.g_completed_frame.setLineWidth(0)
        self.g_completed_frame.setObjectName("g_completed_frame")
        self.verticalLayout_3.addWidget(self.g_progress_frame)
        self.g_settings_frame = QtWidgets.QFrame(ManageTableSettings)
        self.g_settings_frame.setToolTipDuration(0)
        self.g_settings_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_settings_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_settings_frame.setObjectName("g_settings_frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.g_settings_frame)
        self.verticalLayout.setContentsMargins(9, 9, 9, 9)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_titlepagenav_frame = QtWidgets.QFrame(self.g_settings_frame)
        self.g_titlepagenav_frame.setMinimumSize(QtCore.QSize(0, 31))
        self.g_titlepagenav_frame.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_titlepagenav_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_titlepagenav_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_titlepagenav_frame.setObjectName("g_titlepagenav_frame")
        self.horizontalLayout_33 = QtWidgets.QHBoxLayout(self.g_titlepagenav_frame)
        self.horizontalLayout_33.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_33.setObjectName("horizontalLayout_33")
        self.g_title_frame = QtWidgets.QFrame(self.g_titlepagenav_frame)
        self.g_title_frame.setToolTipDuration(0)
        self.g_title_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_title_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_title_frame.setObjectName("g_title_frame")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.g_title_frame)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setSpacing(6)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.g_main_label = QtWidgets.QLabel(self.g_title_frame)
        self.g_main_label.setMinimumSize(QtCore.QSize(300, 0))
        self.g_main_label.setMaximumSize(QtCore.QSize(500, 20))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_main_label.setFont(font)
        self.g_main_label.setToolTipDuration(0)
        self.g_main_label.setObjectName("g_main_label")
        self.horizontalLayout.addWidget(self.g_main_label)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.horizontalLayout_33.addWidget(self.g_title_frame)
        self.verticalLayout.addWidget(self.g_titlepagenav_frame)
        self.g_effects_frame = QtWidgets.QFrame(self.g_settings_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_effects_frame.sizePolicy().hasHeightForWidth())
        self.g_effects_frame.setSizePolicy(sizePolicy)
        self.g_effects_frame.setMinimumSize(QtCore.QSize(0, 45))
        self.g_effects_frame.setMaximumSize(QtCore.QSize(166546, 45))
        self.g_effects_frame.setToolTipDuration(0)
        self.g_effects_frame.setStyleSheet("")
        self.g_effects_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_effects_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_effects_frame.setObjectName("g_effects_frame")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.g_effects_frame)
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_8.setSpacing(6)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.g_effects_label = QtWidgets.QLabel(self.g_effects_frame)
        self.g_effects_label.setMinimumSize(QtCore.QSize(130, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_effects_label.setFont(font)
        self.g_effects_label.setToolTipDuration(0)
        self.g_effects_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_effects_label.setObjectName("g_effects_label")
        self.horizontalLayout_8.addWidget(self.g_effects_label)
        self.g_effect_combobox = QtWidgets.QComboBox(self.g_effects_frame)
        self.g_effect_combobox.setMinimumSize(QtCore.QSize(175, 0))
        self.g_effect_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_effect_combobox.setCursor(QtGui.QCursor(QtCore.Qt.ArrowCursor))
        self.g_effect_combobox.setToolTipDuration(3)
        self.g_effect_combobox.setStyleSheet("")
        self.g_effect_combobox.setEditable(True)
        self.g_effect_combobox.setCurrentText("")
        self.g_effect_combobox.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
        self.g_effect_combobox.setFrame(True)
        self.g_effect_combobox.setObjectName("g_effect_combobox")
        self.horizontalLayout_8.addWidget(self.g_effect_combobox)
        self.g_delete_pushButton = QtWidgets.QPushButton(self.g_effects_frame)
        self.g_delete_pushButton.setMinimumSize(QtCore.QSize(150, 31))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/price_review/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_delete_pushButton.setIcon(icon)
        self.g_delete_pushButton.setObjectName("g_delete_pushButton")
        self.horizontalLayout_8.addWidget(self.g_delete_pushButton)
        self.g_nosettings_label = QtWidgets.QLabel(self.g_effects_frame)
        self.g_nosettings_label.setMinimumSize(QtCore.QSize(130, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_nosettings_label.setFont(font)
        self.g_nosettings_label.setToolTipDuration(0)
        self.g_nosettings_label.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.g_nosettings_label.setObjectName("g_nosettings_label")
        self.horizontalLayout_8.addWidget(self.g_nosettings_label)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_8.addItem(spacerItem1)
        self.verticalLayout.addWidget(self.g_effects_frame)
        self.g_perpage_frame = QtWidgets.QFrame(self.g_settings_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_perpage_frame.sizePolicy().hasHeightForWidth())
        self.g_perpage_frame.setSizePolicy(sizePolicy)
        self.g_perpage_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_perpage_frame.setMaximumSize(QtCore.QSize(166546, 40))
        self.g_perpage_frame.setStyleSheet("")
        self.g_perpage_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_perpage_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_perpage_frame.setObjectName("g_perpage_frame")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.g_perpage_frame)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(6)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.g_perpage_label = QtWidgets.QLabel(self.g_perpage_frame)
        self.g_perpage_label.setMinimumSize(QtCore.QSize(130, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_perpage_label.setFont(font)
        self.g_perpage_label.setToolTipDuration(0)
        self.g_perpage_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_perpage_label.setObjectName("g_perpage_label")
        self.horizontalLayout_6.addWidget(self.g_perpage_label)
        self.g_page_combobox = QtWidgets.QComboBox(self.g_perpage_frame)
        self.g_page_combobox.setMinimumSize(QtCore.QSize(100, 0))
        self.g_page_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_page_combobox.setCursor(QtGui.QCursor(QtCore.Qt.ArrowCursor))
        self.g_page_combobox.setToolTipDuration(3)
        self.g_page_combobox.setStyleSheet("")
        self.g_page_combobox.setEditable(True)
        self.g_page_combobox.setCurrentText("")
        self.g_page_combobox.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
        self.g_page_combobox.setFrame(True)
        self.g_page_combobox.setObjectName("g_page_combobox")
        self.horizontalLayout_6.addWidget(self.g_page_combobox)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem2)
        self.verticalLayout.addWidget(self.g_perpage_frame)
        self.g_columns_frame = QtWidgets.QFrame(self.g_settings_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_columns_frame.sizePolicy().hasHeightForWidth())
        self.g_columns_frame.setSizePolicy(sizePolicy)
        self.g_columns_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_columns_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_columns_frame.setToolTipDuration(0)
        self.g_columns_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_columns_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_columns_frame.setObjectName("g_columns_frame")
        self.horizontalLayout_57 = QtWidgets.QHBoxLayout(self.g_columns_frame)
        self.horizontalLayout_57.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_57.setSpacing(6)
        self.horizontalLayout_57.setObjectName("horizontalLayout_57")
        self.g_columns_label = QtWidgets.QLabel(self.g_columns_frame)
        self.g_columns_label.setMinimumSize(QtCore.QSize(130, 0))
        self.g_columns_label.setMaximumSize(QtCore.QSize(130, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_columns_label.setFont(font)
        self.g_columns_label.setToolTipDuration(0)
        self.g_columns_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_columns_label.setObjectName("g_columns_label")
        self.horizontalLayout_57.addWidget(self.g_columns_label)
        self.g_column_checkcombobox = PyCheckCombobox(self.g_columns_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_column_checkcombobox.sizePolicy().hasHeightForWidth())
        self.g_column_checkcombobox.setSizePolicy(sizePolicy)
        self.g_column_checkcombobox.setMinimumSize(QtCore.QSize(500, 31))
        self.g_column_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_column_checkcombobox.setToolTip("")
        self.g_column_checkcombobox.setToolTipDuration(3)
        self.g_column_checkcombobox.setWhatsThis("")
        self.g_column_checkcombobox.setCurrentText("")
        self.g_column_checkcombobox.setObjectName("g_column_checkcombobox")
        self.horizontalLayout_57.addWidget(self.g_column_checkcombobox)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_57.addItem(spacerItem3)
        self.verticalLayout.addWidget(self.g_columns_frame)
        self.g_deleted_frame = QtWidgets.QFrame(self.g_settings_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_deleted_frame.sizePolicy().hasHeightForWidth())
        self.g_deleted_frame.setSizePolicy(sizePolicy)
        self.g_deleted_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_deleted_frame.setMaximumSize(QtCore.QSize(166546, 40))
        self.g_deleted_frame.setToolTipDuration(0)
        self.g_deleted_frame.setStyleSheet("")
        self.g_deleted_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_deleted_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_deleted_frame.setObjectName("g_deleted_frame")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.g_deleted_frame)
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_7.setSpacing(6)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.g_active_label = QtWidgets.QLabel(self.g_deleted_frame)
        self.g_active_label.setMinimumSize(QtCore.QSize(130, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_active_label.setFont(font)
        self.g_active_label.setToolTipDuration(0)
        self.g_active_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_active_label.setObjectName("g_active_label")
        self.horizontalLayout_7.addWidget(self.g_active_label)
        self.g_deleted_combobox = QtWidgets.QComboBox(self.g_deleted_frame)
        self.g_deleted_combobox.setMinimumSize(QtCore.QSize(200, 0))
        self.g_deleted_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_deleted_combobox.setCursor(QtGui.QCursor(QtCore.Qt.ArrowCursor))
        self.g_deleted_combobox.setToolTipDuration(3)
        self.g_deleted_combobox.setStyleSheet("")
        self.g_deleted_combobox.setEditable(True)
        self.g_deleted_combobox.setCurrentText("")
        self.g_deleted_combobox.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
        self.g_deleted_combobox.setFrame(True)
        self.g_deleted_combobox.setObjectName("g_deleted_combobox")
        self.horizontalLayout_7.addWidget(self.g_deleted_combobox)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem4)
        self.verticalLayout.addWidget(self.g_deleted_frame)
        self.g_status_frame = QtWidgets.QFrame(self.g_settings_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_status_frame.sizePolicy().hasHeightForWidth())
        self.g_status_frame.setSizePolicy(sizePolicy)
        self.g_status_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_status_frame.setMaximumSize(QtCore.QSize(166546, 40))
        self.g_status_frame.setToolTipDuration(0)
        self.g_status_frame.setStyleSheet("")
        self.g_status_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_status_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_status_frame.setObjectName("g_status_frame")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.g_status_frame)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setSpacing(6)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.g_status_label = QtWidgets.QLabel(self.g_status_frame)
        self.g_status_label.setMinimumSize(QtCore.QSize(130, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_status_label.setFont(font)
        self.g_status_label.setToolTipDuration(0)
        self.g_status_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_status_label.setObjectName("g_status_label")
        self.horizontalLayout_10.addWidget(self.g_status_label)
        self.g_status_checkcombobox = PyCheckCombobox(self.g_status_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_status_checkcombobox.sizePolicy().hasHeightForWidth())
        self.g_status_checkcombobox.setSizePolicy(sizePolicy)
        self.g_status_checkcombobox.setMinimumSize(QtCore.QSize(500, 31))
        self.g_status_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_status_checkcombobox.setToolTip("")
        self.g_status_checkcombobox.setToolTipDuration(3)
        self.g_status_checkcombobox.setWhatsThis("")
        self.g_status_checkcombobox.setCurrentText("")
        self.g_status_checkcombobox.setObjectName("g_status_checkcombobox")
        self.horizontalLayout_10.addWidget(self.g_status_checkcombobox)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_10.addItem(spacerItem5)
        self.verticalLayout.addWidget(self.g_status_frame)
        self.g_producttypes_frame = QtWidgets.QFrame(self.g_settings_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_producttypes_frame.sizePolicy().hasHeightForWidth())
        self.g_producttypes_frame.setSizePolicy(sizePolicy)
        self.g_producttypes_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_producttypes_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_producttypes_frame.setToolTipDuration(0)
        self.g_producttypes_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_producttypes_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_producttypes_frame.setObjectName("g_producttypes_frame")
        self.horizontalLayout_58 = QtWidgets.QHBoxLayout(self.g_producttypes_frame)
        self.horizontalLayout_58.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_58.setSpacing(6)
        self.horizontalLayout_58.setObjectName("horizontalLayout_58")
        self.g_producttype_label = QtWidgets.QLabel(self.g_producttypes_frame)
        self.g_producttype_label.setMinimumSize(QtCore.QSize(130, 0))
        self.g_producttype_label.setMaximumSize(QtCore.QSize(130, 16777215))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_producttype_label.setFont(font)
        self.g_producttype_label.setToolTipDuration(0)
        self.g_producttype_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_producttype_label.setObjectName("g_producttype_label")
        self.horizontalLayout_58.addWidget(self.g_producttype_label)
        self.g_producttypes_checkcombobox = PyCheckCombobox(self.g_producttypes_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_producttypes_checkcombobox.sizePolicy().hasHeightForWidth())
        self.g_producttypes_checkcombobox.setSizePolicy(sizePolicy)
        self.g_producttypes_checkcombobox.setMinimumSize(QtCore.QSize(500, 31))
        self.g_producttypes_checkcombobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_producttypes_checkcombobox.setToolTip("")
        self.g_producttypes_checkcombobox.setToolTipDuration(3)
        self.g_producttypes_checkcombobox.setWhatsThis("")
        self.g_producttypes_checkcombobox.setCurrentText("")
        self.g_producttypes_checkcombobox.setObjectName("g_producttypes_checkcombobox")
        self.horizontalLayout_58.addWidget(self.g_producttypes_checkcombobox)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_58.addItem(spacerItem6)
        self.verticalLayout.addWidget(self.g_producttypes_frame)
        self.g_pidb_frame = QtWidgets.QFrame(self.g_settings_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_pidb_frame.sizePolicy().hasHeightForWidth())
        self.g_pidb_frame.setSizePolicy(sizePolicy)
        self.g_pidb_frame.setMinimumSize(QtCore.QSize(0, 40))
        self.g_pidb_frame.setMaximumSize(QtCore.QSize(166546, 40))
        self.g_pidb_frame.setStyleSheet("")
        self.g_pidb_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_pidb_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_pidb_frame.setObjectName("g_pidb_frame")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.g_pidb_frame)
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_9.setSpacing(6)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.g_pidb_label = QtWidgets.QLabel(self.g_pidb_frame)
        self.g_pidb_label.setMinimumSize(QtCore.QSize(130, 0))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_pidb_label.setFont(font)
        self.g_pidb_label.setToolTipDuration(0)
        self.g_pidb_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_pidb_label.setObjectName("g_pidb_label")
        self.horizontalLayout_9.addWidget(self.g_pidb_label)
        self.g_pidb_combobox = QtWidgets.QComboBox(self.g_pidb_frame)
        self.g_pidb_combobox.setMinimumSize(QtCore.QSize(500, 0))
        self.g_pidb_combobox.setMaximumSize(QtCore.QSize(16777215, 31))
        self.g_pidb_combobox.setCursor(QtGui.QCursor(QtCore.Qt.ArrowCursor))
        self.g_pidb_combobox.setToolTipDuration(3)
        self.g_pidb_combobox.setStyleSheet("")
        self.g_pidb_combobox.setEditable(True)
        self.g_pidb_combobox.setCurrentText("")
        self.g_pidb_combobox.setInsertPolicy(QtWidgets.QComboBox.NoInsert)
        self.g_pidb_combobox.setFrame(True)
        self.g_pidb_combobox.setObjectName("g_pidb_combobox")
        self.horizontalLayout_9.addWidget(self.g_pidb_combobox)
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem7)
        self.verticalLayout.addWidget(self.g_pidb_frame)
        spacerItem8 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem8)
        self.g_controlbuttons_frame = QtWidgets.QFrame(self.g_settings_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_controlbuttons_frame.sizePolicy().hasHeightForWidth())
        self.g_controlbuttons_frame.setSizePolicy(sizePolicy)
        self.g_controlbuttons_frame.setMinimumSize(QtCore.QSize(247, 40))
        self.g_controlbuttons_frame.setMaximumSize(QtCore.QSize(16777215, 40))
        self.g_controlbuttons_frame.setToolTipDuration(0)
        self.g_controlbuttons_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_controlbuttons_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_controlbuttons_frame.setObjectName("g_controlbuttons_frame")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.g_controlbuttons_frame)
        self.horizontalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_11.setSpacing(6)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem9 = QtWidgets.QSpacerItem(495, 37, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem9)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_cancel_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_cancel_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_cancel_button.setToolTipDuration(3)
        self.g_cancel_button.setStyleSheet("")
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_11.addWidget(self.g_cancel_button)
        self.g_save_button = QtWidgets.QPushButton(self.g_controlbuttons_frame)
        self.g_save_button.setMinimumSize(QtCore.QSize(120, 40))
        self.g_save_button.setMaximumSize(QtCore.QSize(0, 40))
        self.g_save_button.setToolTipDuration(3)
        self.g_save_button.setStyleSheet("")
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/price_review/check"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_save_button.setIcon(icon1)
        self.g_save_button.setIconSize(QtCore.QSize(24, 24))
        self.g_save_button.setObjectName("g_save_button")
        self.horizontalLayout_11.addWidget(self.g_save_button)
        self.verticalLayout.addWidget(self.g_controlbuttons_frame)
        self.verticalLayout_3.addWidget(self.g_settings_frame)

        self.retranslateUi(ManageTableSettings)
        self.g_effect_combobox.setCurrentIndex(-1)
        self.g_page_combobox.setCurrentIndex(-1)
        self.g_deleted_combobox.setCurrentIndex(-1)
        self.g_pidb_combobox.setCurrentIndex(-1)
        QtCore.QMetaObject.connectSlotsByName(ManageTableSettings)

    def retranslateUi(self, ManageTableSettings):
        _translate = QtCore.QCoreApplication.translate
        ManageTableSettings.setWindowTitle(_translate("ManageTableSettings", "Manage Table Settings"))
        self.g_main_label.setText(_translate("ManageTableSettings", "Manage Smart Table Settings"))
        self.g_effects_label.setText(_translate("ManageTableSettings", "This Change Effects"))
        self.g_delete_pushButton.setText(_translate("ManageTableSettings", "  Delete Setting"))
        self.g_nosettings_label.setText(_translate("ManageTableSettings", "No Settings Found."))
        self.g_perpage_label.setText(_translate("ManageTableSettings", "Records Per Page"))
        self.g_columns_label.setText(_translate("ManageTableSettings", "Show Columns"))
        self.g_active_label.setText(_translate("ManageTableSettings", "Show Active/Deleted"))
        self.g_status_label.setText(_translate("ManageTableSettings", "Show Status"))
        self.g_producttype_label.setText(_translate("ManageTableSettings", "Product Types"))
        self.g_pidb_label.setText(_translate("ManageTableSettings", "PIDB Suppliers"))
        self.g_cancel_button.setText(_translate("ManageTableSettings", " Close"))
        self.g_save_button.setText(_translate("ManageTableSettings", "Save"))
from po_lib.checkcombobox import PyCheckCombobox
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    ManageTableSettings = QtWidgets.QDialog()
    ui = Ui_ManageTableSettings()
    ui.setupUi(ManageTableSettings)
    ManageTableSettings.show()
    sys.exit(app.exec_())
