# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'trayfunctions_popup.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_TrayFunctionsPopup(object):
    def setupUi(self, TrayFunctionsPopup):
        TrayFunctionsPopup.setObjectName("TrayFunctionsPopup")
        TrayFunctionsPopup.resize(440, 483)
        TrayFunctionsPopup.setStyleSheet("\n"
"QPushButton {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #2C87B2, stop: 1 #57A3D2);\n"
"    font-size: 15px;\n"
"    font-weight: bold;\n"
"    border: 2px solid #287599;\n"
"    color: white;\n"
"    min-height: 40px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #287599;\n"
"    border: 2px solid #287599;\n"
"}\n"
"\n"
"QPushButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: None;\n"
"}\n"
"\n"
"#g_cover_frame, \n"
"#__g_cover_frame {\n"
"    background-color: rgb(0,0,0, 25%);\n"
"}\n"
"\n"
"#g_container_frame, \n"
"#__g_container_frame {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"    border: 1px solid #073c83;\n"
"    border-radius: 10px;\n"
"    background-color: #DDE4E9;\n"
"}\n"
"\n"
"#g_cancel_button,\n"
"#__g_cancel_button {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #e50012, stop: 1 #ed3940);\n"
"    border: 2px solid #c80319;\n"
"    min-width: 120px;\n"
"    font-size: 15px;\n"
"}\n"
"\n"
"#g_cancel_button:pressed,\n"
"#__g_cancel_button:pressed {\n"
"    background: #c80319;\n"
"}\n"
"\n"
"#g_title_label {\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}")
        self.verticalLayout = QtWidgets.QVBoxLayout(TrayFunctionsPopup)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.g_cover_frame = QtWidgets.QFrame(TrayFunctionsPopup)
        self.g_cover_frame.setFocusPolicy(QtCore.Qt.StrongFocus)
        self.g_cover_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_cover_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_cover_frame.setObjectName("g_cover_frame")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.g_cover_frame)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        spacerItem = QtWidgets.QSpacerItem(20, 67, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem1)
        self.g_container_frame = QtWidgets.QFrame(self.g_cover_frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.g_container_frame.sizePolicy().hasHeightForWidth())
        self.g_container_frame.setSizePolicy(sizePolicy)
        self.g_container_frame.setMinimumSize(QtCore.QSize(250, 0))
        self.g_container_frame.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.g_container_frame.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_container_frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.g_container_frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.g_container_frame.setObjectName("g_container_frame")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.g_container_frame)
        self.verticalLayout_3.setContentsMargins(11, 11, 11, 11)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.g_title_label = QtWidgets.QLabel(self.g_container_frame)
        self.g_title_label.setMaximumSize(QtCore.QSize(16777215, 19))
        font = QtGui.QFont()
        font.setPointSize(-1)
        font.setBold(True)
        font.setWeight(75)
        self.g_title_label.setFont(font)
        self.g_title_label.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_title_label.setObjectName("g_title_label")
        self.verticalLayout_3.addWidget(self.g_title_label)
        spacerItem2 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_3.addItem(spacerItem2)
        self.g_container_frame_layout = QtWidgets.QVBoxLayout()
        self.g_container_frame_layout.setContentsMargins(18, -1, 18, -1)
        self.g_container_frame_layout.setObjectName("g_container_frame_layout")
        self.verticalLayout_3.addLayout(self.g_container_frame_layout)
        spacerItem3 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.verticalLayout_3.addItem(spacerItem3)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setContentsMargins(-1, -1, 0, -1)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem4)
        self.g_cancel_button = QtWidgets.QPushButton(self.g_container_frame)
        self.g_cancel_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_cancel_button.setFocusPolicy(QtCore.Qt.NoFocus)
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/editproductline_popup/cancel"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_cancel_button.setIcon(icon)
        self.g_cancel_button.setIconSize(QtCore.QSize(24, 24))
        self.g_cancel_button.setObjectName("g_cancel_button")
        self.horizontalLayout_2.addWidget(self.g_cancel_button)
        self.verticalLayout_3.addLayout(self.horizontalLayout_2)
        self.horizontalLayout_3.addWidget(self.g_container_frame)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem5)
        self.verticalLayout_4.addLayout(self.horizontalLayout_3)
        spacerItem6 = QtWidgets.QSpacerItem(20, 66, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem6)
        self.verticalLayout.addWidget(self.g_cover_frame)

        self.retranslateUi(TrayFunctionsPopup)
        QtCore.QMetaObject.connectSlotsByName(TrayFunctionsPopup)

    def retranslateUi(self, TrayFunctionsPopup):
        _translate = QtCore.QCoreApplication.translate
        TrayFunctionsPopup.setWindowTitle(_translate("TrayFunctionsPopup", "Dialog"))
        self.g_title_label.setText(_translate("TrayFunctionsPopup", "Select Action"))
        self.g_cancel_button.setText(_translate("TrayFunctionsPopup", "  Cancel"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    TrayFunctionsPopup = QtWidgets.QDialog()
    ui = Ui_TrayFunctionsPopup()
    ui.setupUi(TrayFunctionsPopup)
    TrayFunctionsPopup.show()
    sys.exit(app.exec_())
