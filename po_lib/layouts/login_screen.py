# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'login_screen.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_LoginScreen(object):
    def setupUi(self, LoginScreen):
        LoginScreen.setObjectName("LoginScreen")
        LoginScreen.resize(730, 508)
        LoginScreen.setWindowTitle("Form")
        LoginScreen.setStyleSheet("\n"
"#LoginScreen {\n"
"    background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #e7eff4, stop: 1 #d9edf8);\n"
"}\n"
"\n"
"QCheckBox {\n"
"    font-size: 13px;\n"
"}\n"
"QCheckBox::indicator {\n"
"    width:20px;\n"
"    height:20px;\n"
"    margin-top:-7px;\n"
"}\n"
"\n"
"QComboBox::drop-down:enabled {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/login_screen/combobox_down_arrow);\n"
"    height: 31px;\n"
"    width: 20px;\n"
"}\n"
"\n"
"QComboBox::drop-down:disabled {\n"
"    subcontrol-origin: margin;\n"
"    background-image: url(:/login_screen/disabled_combobox_down_arrow);\n"
"    height: 31px;\n"
"    width: 20px;\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(\':/login_screen/unchecked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(\':/login_screen/checked\');\n"
"    border:0px;\n"
"}\n"
"\n"
"QPushButton {\n"
"    background: #009c00;\n"
"    border: 2px solid #007f00;\n"
"    color:white;\n"
"    font-weight:bold;\n"
"    font-size: 13px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: #007f00;\n"
"}\n"
"\n"
"QPushButton:disabled {\n"
"    background: rgb(164, 164, 164);\n"
"    border: 2px solid rgb(164, 164, 164);\n"
"}\n"
"\n"
"")
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/main_window/login"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        LoginScreen.setProperty("login_toolbutton_icon", icon)
        icon1 = QtGui.QIcon()
        icon1.addPixmap(QtGui.QPixmap(":/main_window/logout"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        LoginScreen.setProperty("logout_toolbutton_icon", icon1)
        LoginScreen.setProperty("logout_toolbutton_stylesheet_text", "\n"
"QToolButton {\n"
"    background: #ee1111;\n"
"    border: 2px solid #CA0E0E;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #CA0E0E;\n"
"}\n"
"\n"
"QToolButton:disabled {\n"
"    background: grey;\n"
"    border: 2px solid grey;\n"
"}\n"
"\n"
"\n"
"")
        LoginScreen.setProperty("logout_indicator_stylesheet_text", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"            the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #CA0E0E;\n"
"    border: 0;\n"
"}\n"
"\n"
"")
        LoginScreen.setProperty("login_english_value_text", "en_US")
        LoginScreen.setProperty("login_spanish_value_text", "es_MX")
        self.gridLayout = QtWidgets.QGridLayout(LoginScreen)
        self.gridLayout.setContentsMargins(0, 0, 0, 0)
        self.gridLayout.setSpacing(0)
        self.gridLayout.setObjectName("gridLayout")
        self.g_logo_label = QtWidgets.QLabel(LoginScreen)
        self.g_logo_label.setMinimumSize(QtCore.QSize(450, 69))
        self.g_logo_label.setStyleSheet("")
        self.g_logo_label.setText("")
        self.g_logo_label.setPixmap(QtGui.QPixmap(":/login_screen/logo"))
        self.g_logo_label.setObjectName("g_logo_label")
        self.gridLayout.addWidget(self.g_logo_label, 1, 0, 1, 1)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.gridLayout.addItem(spacerItem, 3, 0, 1, 1)
        self.frame = QtWidgets.QFrame(LoginScreen)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame.sizePolicy().hasHeightForWidth())
        self.frame.setSizePolicy(sizePolicy)
        self.frame.setMinimumSize(QtCore.QSize(400, 314))
        self.frame.setMaximumSize(QtCore.QSize(344, 314))
        self.frame.setStyleSheet("/*#frame {\n"
"background: QLinearGradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #d9edf8, stop: 1 #e7eff4);\n"
"}*/\n"
"\n"
"#frame QComboBox {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"}\n"
"\n"
"#frame QLineEdit {\n"
"    border: 1px solid #aaa;\n"
"    border-radius:5px;\n"
"    background-color:#FFF;\n"
"}\n"
"\n"
"#frame QLabel {\n"
"    font-weight: bold;\n"
"    font-size: 14px;\n"
"}\n"
"\n"
"")
        self.frame.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.frame.setFrameShadow(QtWidgets.QFrame.Plain)
        self.frame.setLineWidth(0)
        self.frame.setObjectName("frame")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.frame)
        self.gridLayout_2.setContentsMargins(50, 50, 50, 50)
        self.gridLayout_2.setSpacing(15)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.g_login_button = QtWidgets.QPushButton(self.frame)
        self.g_login_button.setMinimumSize(QtCore.QSize(60, 40))
        self.g_login_button.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_login_button.setFocusPolicy(QtCore.Qt.NoFocus)
        self.g_login_button.setStyleSheet("")
        icon2 = QtGui.QIcon()
        icon2.addPixmap(QtGui.QPixmap(":/login_screen/key"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.g_login_button.setIcon(icon2)
        self.g_login_button.setIconSize(QtCore.QSize(32, 32))
        self.g_login_button.setObjectName("g_login_button")
        self.gridLayout_2.addWidget(self.g_login_button, 3, 1, 1, 1)
        self.g_password_lineedit = QtWidgets.QLineEdit(self.frame)
        self.g_password_lineedit.setMinimumSize(QtCore.QSize(0, 31))
        self.g_password_lineedit.setInputMethodHints(QtCore.Qt.ImhHiddenText|QtCore.Qt.ImhNoAutoUppercase|QtCore.Qt.ImhNoPredictiveText|QtCore.Qt.ImhSensitiveData)
        self.g_password_lineedit.setText("")
        self.g_password_lineedit.setEchoMode(QtWidgets.QLineEdit.Password)
        self.g_password_lineedit.setClearButtonEnabled(True)
        self.g_password_lineedit.setObjectName("g_password_lineedit")
        self.gridLayout_2.addWidget(self.g_password_lineedit, 0, 1, 1, 1)
        self.g_language_label = QtWidgets.QLabel(self.frame)
        self.g_language_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_language_label.setObjectName("g_language_label")
        self.gridLayout_2.addWidget(self.g_language_label, 1, 0, 1, 1)
        self.g_password_label = QtWidgets.QLabel(self.frame)
        self.g_password_label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.g_password_label.setObjectName("g_password_label")
        self.gridLayout_2.addWidget(self.g_password_label, 0, 0, 1, 1)
        self.g_language_combobox = QtWidgets.QComboBox(self.frame)
        self.g_language_combobox.setEnabled(True)
        self.g_language_combobox.setMinimumSize(QtCore.QSize(0, 31))
        self.g_language_combobox.setObjectName("g_language_combobox")
        self.g_language_combobox.addItem("")
        self.g_language_combobox.addItem("")
        self.gridLayout_2.addWidget(self.g_language_combobox, 1, 1, 1, 1)
        self.g_save_credentials_checkbox = QtWidgets.QCheckBox(self.frame)
        self.g_save_credentials_checkbox.setMinimumSize(QtCore.QSize(0, 27))
        font = QtGui.QFont()
        font.setPointSize(-1)
        self.g_save_credentials_checkbox.setFont(font)
        self.g_save_credentials_checkbox.setCursor(QtGui.QCursor(QtCore.Qt.PointingHandCursor))
        self.g_save_credentials_checkbox.setStyleSheet("")
        self.g_save_credentials_checkbox.setChecked(False)
        self.g_save_credentials_checkbox.setTristate(False)
        self.g_save_credentials_checkbox.setObjectName("g_save_credentials_checkbox")
        self.gridLayout_2.addWidget(self.g_save_credentials_checkbox, 2, 1, 1, 1)
        self.gridLayout.addWidget(self.frame, 2, 0, 1, 1)
        spacerItem1 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.gridLayout.addItem(spacerItem1, 0, 0, 1, 1)

        self.retranslateUi(LoginScreen)
        QtCore.QMetaObject.connectSlotsByName(LoginScreen)

    def retranslateUi(self, LoginScreen):
        _translate = QtCore.QCoreApplication.translate
        LoginScreen.setProperty("login_english_option_text", _translate("LoginScreen", "English"))
        LoginScreen.setProperty("login_spanish_option_text", _translate("LoginScreen", "Spanish"))
        LoginScreen.setProperty("login_toolbutton_tooltip_text", _translate("LoginScreen", "Sign into Pinogy POS"))
        LoginScreen.setProperty("login_toolbutton_title_text", _translate("LoginScreen", "Sign In"))
        LoginScreen.setProperty("login_toolbutton_stylesheet_text", _translate("LoginScreen", "\n"
"QToolButton {\n"
"    background: #009c00;\n"
"    border: 2px solid #007f00;\n"
"}\n"
"\n"
"QToolButton:checked {\n"
"    background: #007f00;\n"
"}\n"
"\n"
"QToolButton:disabled {\n"
"    background: grey;\n"
"    border: 2px solid grey;\n"
"}\n"
""))
        LoginScreen.setProperty("login_indicator_stylesheet_text", _translate("LoginScreen", "\n"
"Line, QFrame {\n"
"    /* A \"line\" widget also contains a QFrame widget for which the following directives must be set, otherwise\n"
"            the line will appear transparent because it is using the same styling as the main toolbar frame. */\n"
"    background-color: #007f00;\n"
"    border: 0;\n"
"}"))
        LoginScreen.setProperty("logout_toolbutton_tooltip_text", _translate("LoginScreen", "Sign out of Pinogy POS"))
        LoginScreen.setProperty("logout_toolbutton_title_text", _translate("LoginScreen", "Sign Out"))
        LoginScreen.setProperty("login_success_msg_text", _translate("LoginScreen", "Login successful. Welcome %(username)s."))
        LoginScreen.setProperty("login_dev_title_text", _translate("LoginScreen", "Development Server"))
        LoginScreen.setProperty("login_dev_warning_msg_text", _translate("LoginScreen", "You are logged into the Pinogy POS development server. All data in this server is for testing and training purposes only."))
        LoginScreen.setProperty("login_password_required_msg_text", _translate("LoginScreen", "Please enter a password to login."))
        LoginScreen.setProperty("login_busy_msg_text", _translate("LoginScreen", "Logging into Pinogy POS ..."))
        LoginScreen.setProperty("login_failure_msg_text", _translate("LoginScreen", "Login incorrect"))
        LoginScreen.setProperty("login_ended_msg_text", _translate("LoginScreen", "Your login session has ended. Goodbye %(username)s."))
        LoginScreen.setProperty("logout_busy_msg_text", _translate("LoginScreen", "Logging out of Pinogy POS ..."))
        LoginScreen.setProperty("login_disallowed_msg_text", _translate("LoginScreen", "This user account cannot be used with this application. Please contact Pinogy support at\n"
"             877-360-7381.\n"
"         "))
        LoginScreen.setProperty("sessions_url_text", _translate("LoginScreen", "/apps/any/sessions"))
        LoginScreen.setProperty("location_disallowed_msg_text", _translate("LoginScreen", "This user account cannot be used from this location. Please contact Pinogy support at 877-360-7381.\n"
"         "))
        LoginScreen.setProperty("login_rc_warning_msg_text", _translate("LoginScreen", "You are logged into the Pinogy POS Release Candidate server."))
        LoginScreen.setProperty("login_beta_warning_msg_text", _translate("LoginScreen", "You are logged into the Pinogy POS Beta server."))
        self.g_login_button.setText(_translate("LoginScreen", "Login"))
        self.g_language_label.setText(_translate("LoginScreen", "Language"))
        self.g_password_label.setText(_translate("LoginScreen", "Password"))
        self.g_language_combobox.setProperty("spanish_entry", _translate("LoginScreen", "Spanish"))
        self.g_language_combobox.setProperty("english_entry", _translate("LoginScreen", "English"))
        self.g_language_combobox.setItemText(0, _translate("LoginScreen", "English"))
        self.g_language_combobox.setItemText(1, _translate("LoginScreen", "Spanish"))
        self.g_save_credentials_checkbox.setText(_translate("LoginScreen", "Save login credentials"))
from . import resources_rc


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    LoginScreen = QtWidgets.QWidget()
    ui = Ui_LoginScreen()
    ui.setupUi(LoginScreen)
    LoginScreen.show()
    sys.exit(app.exec_())
