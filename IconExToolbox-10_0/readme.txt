README for IconExperience Toolbox v10.0, provided by INCORS GmbH

-----

The IconExperience Toolbox provides a set of features for using the IconExperience icons. Current features are

- adding badges like "add", "edit", "preferences" to PNG files

- converting PNG images to GIF or BMP files

- adding an opaque background and/or changing the foreground color for PNG images

- adding an empty padding around an icon 

- converting PNG images to hot or disabled state images


The software is self explaining, other documentation than this file is not available.

-----

Included files:

IconExToolbox_10_0.exe - The IconExperience Toolbox software as a Windows exe file
IconExToolbox_10_0.jar - The IconExperience Toolbox software as an executable JAR file (all operating systems)
license.txt - The license terms for the IconExperience Toolbox
readme.txt - This file

-----

System requirements:

- Java(tm) Runtime Environment version 1.6 or higher. A Java(tm) Runtime Environment can be downloaded for free from www.java.com

-----

Support for this software is only provided for customers of IconExperience icon products. For questions regarding this software, please send an <NAME_EMAIL>

-----

Copyright (C) INCORS GmbH - All rights reserved